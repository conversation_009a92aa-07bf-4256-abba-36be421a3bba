/**
 * \file IfxCan_bf.h
 * \brief
 * \copyright Copyright (c) 2016 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC23XADAS_UM_V1.1.R1
 * Specification: tc23xadas_um_sfrs_MCSFR.xml (Revision: UM_V1.1)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Can_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Can
 * 
 */
#ifndef IFXCAN_BF_H
#define IFXCAN_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Can_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN0 */
#define IFX_CAN_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN0 */
#define IFX_CAN_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN0 */
#define IFX_CAN_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN10 */
#define IFX_CAN_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN10 */
#define IFX_CAN_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN10 */
#define IFX_CAN_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN11 */
#define IFX_CAN_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN11 */
#define IFX_CAN_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN11 */
#define IFX_CAN_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN12 */
#define IFX_CAN_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN12 */
#define IFX_CAN_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN12 */
#define IFX_CAN_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN13 */
#define IFX_CAN_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN13 */
#define IFX_CAN_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN13 */
#define IFX_CAN_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN14 */
#define IFX_CAN_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN14 */
#define IFX_CAN_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN14 */
#define IFX_CAN_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN15 */
#define IFX_CAN_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN15 */
#define IFX_CAN_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN15 */
#define IFX_CAN_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN16 */
#define IFX_CAN_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN16 */
#define IFX_CAN_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN16 */
#define IFX_CAN_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN17 */
#define IFX_CAN_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN17 */
#define IFX_CAN_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN17 */
#define IFX_CAN_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN18 */
#define IFX_CAN_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN18 */
#define IFX_CAN_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN18 */
#define IFX_CAN_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN19 */
#define IFX_CAN_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN19 */
#define IFX_CAN_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN19 */
#define IFX_CAN_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN1 */
#define IFX_CAN_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN1 */
#define IFX_CAN_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN1 */
#define IFX_CAN_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN20 */
#define IFX_CAN_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN20 */
#define IFX_CAN_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN20 */
#define IFX_CAN_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN21 */
#define IFX_CAN_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN21 */
#define IFX_CAN_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN21 */
#define IFX_CAN_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN22 */
#define IFX_CAN_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN22 */
#define IFX_CAN_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN22 */
#define IFX_CAN_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN23 */
#define IFX_CAN_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN23 */
#define IFX_CAN_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN23 */
#define IFX_CAN_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN24 */
#define IFX_CAN_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN24 */
#define IFX_CAN_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN24 */
#define IFX_CAN_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN25 */
#define IFX_CAN_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN25 */
#define IFX_CAN_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN25 */
#define IFX_CAN_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN26 */
#define IFX_CAN_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN26 */
#define IFX_CAN_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN26 */
#define IFX_CAN_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN27 */
#define IFX_CAN_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN27 */
#define IFX_CAN_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN27 */
#define IFX_CAN_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN28 */
#define IFX_CAN_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN28 */
#define IFX_CAN_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN28 */
#define IFX_CAN_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN29 */
#define IFX_CAN_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN29 */
#define IFX_CAN_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN29 */
#define IFX_CAN_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN2 */
#define IFX_CAN_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN2 */
#define IFX_CAN_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN2 */
#define IFX_CAN_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN30 */
#define IFX_CAN_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN30 */
#define IFX_CAN_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN30 */
#define IFX_CAN_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN31 */
#define IFX_CAN_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN31 */
#define IFX_CAN_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN31 */
#define IFX_CAN_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN3 */
#define IFX_CAN_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN3 */
#define IFX_CAN_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN3 */
#define IFX_CAN_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN4 */
#define IFX_CAN_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN4 */
#define IFX_CAN_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN4 */
#define IFX_CAN_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN5 */
#define IFX_CAN_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN5 */
#define IFX_CAN_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN5 */
#define IFX_CAN_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN6 */
#define IFX_CAN_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN6 */
#define IFX_CAN_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN6 */
#define IFX_CAN_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN7 */
#define IFX_CAN_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN7 */
#define IFX_CAN_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN7 */
#define IFX_CAN_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN8 */
#define IFX_CAN_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN8 */
#define IFX_CAN_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN8 */
#define IFX_CAN_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_CAN_ACCEN0_Bits.EN9 */
#define IFX_CAN_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_CAN_ACCEN0_Bits.EN9 */
#define IFX_CAN_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_ACCEN0_Bits.EN9 */
#define IFX_CAN_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_CAN_CLC_Bits.DISR */
#define IFX_CAN_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_CAN_CLC_Bits.DISR */
#define IFX_CAN_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_CLC_Bits.DISR */
#define IFX_CAN_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_CAN_CLC_Bits.DISS */
#define IFX_CAN_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_CAN_CLC_Bits.DISS */
#define IFX_CAN_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_CLC_Bits.DISS */
#define IFX_CAN_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_CAN_CLC_Bits.EDIS */
#define IFX_CAN_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_CAN_CLC_Bits.EDIS */
#define IFX_CAN_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_CLC_Bits.EDIS */
#define IFX_CAN_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_CAN_FDR_Bits.DM */
#define IFX_CAN_FDR_DM_LEN (2u)

/** \brief  Mask for Ifx_CAN_FDR_Bits.DM */
#define IFX_CAN_FDR_DM_MSK (0x3u)

/** \brief  Offset for Ifx_CAN_FDR_Bits.DM */
#define IFX_CAN_FDR_DM_OFF (14u)

/** \brief  Length for Ifx_CAN_FDR_Bits.STEP */
#define IFX_CAN_FDR_STEP_LEN (10u)

/** \brief  Mask for Ifx_CAN_FDR_Bits.STEP */
#define IFX_CAN_FDR_STEP_MSK (0x3ffu)

/** \brief  Offset for Ifx_CAN_FDR_Bits.STEP */
#define IFX_CAN_FDR_STEP_OFF (0u)

/** \brief  Length for Ifx_CAN_ID_Bits.MODNUMBER */
#define IFX_CAN_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_CAN_ID_Bits.MODNUMBER */
#define IFX_CAN_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_CAN_ID_Bits.MODNUMBER */
#define IFX_CAN_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_CAN_ID_Bits.MODREV */
#define IFX_CAN_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_CAN_ID_Bits.MODREV */
#define IFX_CAN_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_ID_Bits.MODREV */
#define IFX_CAN_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_CAN_ID_Bits.MODTYPE */
#define IFX_CAN_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_CAN_ID_Bits.MODTYPE */
#define IFX_CAN_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_ID_Bits.MODTYPE */
#define IFX_CAN_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_CAN_KRST0_Bits.RST */
#define IFX_CAN_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_CAN_KRST0_Bits.RST */
#define IFX_CAN_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_KRST0_Bits.RST */
#define IFX_CAN_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_CAN_KRST0_Bits.RSTSTAT */
#define IFX_CAN_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_CAN_KRST0_Bits.RSTSTAT */
#define IFX_CAN_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_KRST0_Bits.RSTSTAT */
#define IFX_CAN_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_CAN_KRST1_Bits.RST */
#define IFX_CAN_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_CAN_KRST1_Bits.RST */
#define IFX_CAN_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_KRST1_Bits.RST */
#define IFX_CAN_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_CAN_KRSTCLR_Bits.CLR */
#define IFX_CAN_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_CAN_KRSTCLR_Bits.CLR */
#define IFX_CAN_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_KRSTCLR_Bits.CLR */
#define IFX_CAN_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_CAN_LIST_Bits.BEGIN */
#define IFX_CAN_LIST_BEGIN_LEN (8u)

/** \brief  Mask for Ifx_CAN_LIST_Bits.BEGIN */
#define IFX_CAN_LIST_BEGIN_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_LIST_Bits.BEGIN */
#define IFX_CAN_LIST_BEGIN_OFF (0u)

/** \brief  Length for Ifx_CAN_LIST_Bits.EMPTY */
#define IFX_CAN_LIST_EMPTY_LEN (1u)

/** \brief  Mask for Ifx_CAN_LIST_Bits.EMPTY */
#define IFX_CAN_LIST_EMPTY_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_LIST_Bits.EMPTY */
#define IFX_CAN_LIST_EMPTY_OFF (24u)

/** \brief  Length for Ifx_CAN_LIST_Bits.END */
#define IFX_CAN_LIST_END_LEN (8u)

/** \brief  Mask for Ifx_CAN_LIST_Bits.END */
#define IFX_CAN_LIST_END_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_LIST_Bits.END */
#define IFX_CAN_LIST_END_OFF (8u)

/** \brief  Length for Ifx_CAN_LIST_Bits.SIZE */
#define IFX_CAN_LIST_SIZE_LEN (8u)

/** \brief  Mask for Ifx_CAN_LIST_Bits.SIZE */
#define IFX_CAN_LIST_SIZE_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_LIST_Bits.SIZE */
#define IFX_CAN_LIST_SIZE_OFF (16u)

/** \brief  Length for Ifx_CAN_MCR_Bits.CLKSEL */
#define IFX_CAN_MCR_CLKSEL_LEN (4u)

/** \brief  Mask for Ifx_CAN_MCR_Bits.CLKSEL */
#define IFX_CAN_MCR_CLKSEL_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_MCR_Bits.CLKSEL */
#define IFX_CAN_MCR_CLKSEL_OFF (0u)

/** \brief  Length for Ifx_CAN_MCR_Bits.DXCM */
#define IFX_CAN_MCR_DXCM_LEN (1u)

/** \brief  Mask for Ifx_CAN_MCR_Bits.DXCM */
#define IFX_CAN_MCR_DXCM_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MCR_Bits.DXCM */
#define IFX_CAN_MCR_DXCM_OFF (8u)

/** \brief  Length for Ifx_CAN_MCR_Bits.MPSEL */
#define IFX_CAN_MCR_MPSEL_LEN (4u)

/** \brief  Mask for Ifx_CAN_MCR_Bits.MPSEL */
#define IFX_CAN_MCR_MPSEL_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_MCR_Bits.MPSEL */
#define IFX_CAN_MCR_MPSEL_OFF (12u)

/** \brief  Length for Ifx_CAN_MECR_Bits.ANYED */
#define IFX_CAN_MECR_ANYED_LEN (1u)

/** \brief  Mask for Ifx_CAN_MECR_Bits.ANYED */
#define IFX_CAN_MECR_ANYED_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MECR_Bits.ANYED */
#define IFX_CAN_MECR_ANYED_OFF (24u)

/** \brief  Length for Ifx_CAN_MECR_Bits.CAPEIE */
#define IFX_CAN_MECR_CAPEIE_LEN (1u)

/** \brief  Mask for Ifx_CAN_MECR_Bits.CAPEIE */
#define IFX_CAN_MECR_CAPEIE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MECR_Bits.CAPEIE */
#define IFX_CAN_MECR_CAPEIE_OFF (25u)

/** \brief  Length for Ifx_CAN_MECR_Bits.DEPTH */
#define IFX_CAN_MECR_DEPTH_LEN (3u)

/** \brief  Mask for Ifx_CAN_MECR_Bits.DEPTH */
#define IFX_CAN_MECR_DEPTH_MSK (0x7u)

/** \brief  Offset for Ifx_CAN_MECR_Bits.DEPTH */
#define IFX_CAN_MECR_DEPTH_OFF (27u)

/** \brief  Length for Ifx_CAN_MECR_Bits.INP */
#define IFX_CAN_MECR_INP_LEN (4u)

/** \brief  Mask for Ifx_CAN_MECR_Bits.INP */
#define IFX_CAN_MECR_INP_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_MECR_Bits.INP */
#define IFX_CAN_MECR_INP_OFF (16u)

/** \brief  Length for Ifx_CAN_MECR_Bits.NODE */
#define IFX_CAN_MECR_NODE_LEN (3u)

/** \brief  Mask for Ifx_CAN_MECR_Bits.NODE */
#define IFX_CAN_MECR_NODE_MSK (0x7u)

/** \brief  Offset for Ifx_CAN_MECR_Bits.NODE */
#define IFX_CAN_MECR_NODE_OFF (20u)

/** \brief  Length for Ifx_CAN_MECR_Bits.SOF */
#define IFX_CAN_MECR_SOF_LEN (1u)

/** \brief  Mask for Ifx_CAN_MECR_Bits.SOF */
#define IFX_CAN_MECR_SOF_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MECR_Bits.SOF */
#define IFX_CAN_MECR_SOF_OFF (30u)

/** \brief  Length for Ifx_CAN_MECR_Bits.TH */
#define IFX_CAN_MECR_TH_LEN (16u)

/** \brief  Mask for Ifx_CAN_MECR_Bits.TH */
#define IFX_CAN_MECR_TH_MSK (0xffffu)

/** \brief  Offset for Ifx_CAN_MECR_Bits.TH */
#define IFX_CAN_MECR_TH_OFF (0u)

/** \brief  Length for Ifx_CAN_MESTAT_Bits.CAPE */
#define IFX_CAN_MESTAT_CAPE_LEN (1u)

/** \brief  Mask for Ifx_CAN_MESTAT_Bits.CAPE */
#define IFX_CAN_MESTAT_CAPE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MESTAT_Bits.CAPE */
#define IFX_CAN_MESTAT_CAPE_OFF (17u)

/** \brief  Length for Ifx_CAN_MESTAT_Bits.CAPRED */
#define IFX_CAN_MESTAT_CAPRED_LEN (1u)

/** \brief  Mask for Ifx_CAN_MESTAT_Bits.CAPRED */
#define IFX_CAN_MESTAT_CAPRED_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MESTAT_Bits.CAPRED */
#define IFX_CAN_MESTAT_CAPRED_OFF (16u)

/** \brief  Length for Ifx_CAN_MESTAT_Bits.CAPT */
#define IFX_CAN_MESTAT_CAPT_LEN (16u)

/** \brief  Mask for Ifx_CAN_MESTAT_Bits.CAPT */
#define IFX_CAN_MESTAT_CAPT_MSK (0xffffu)

/** \brief  Offset for Ifx_CAN_MESTAT_Bits.CAPT */
#define IFX_CAN_MESTAT_CAPT_OFF (0u)

/** \brief  Length for Ifx_CAN_MITR_Bits.IT */
#define IFX_CAN_MITR_IT_LEN (16u)

/** \brief  Mask for Ifx_CAN_MITR_Bits.IT */
#define IFX_CAN_MITR_IT_MSK (0xffffu)

/** \brief  Offset for Ifx_CAN_MITR_Bits.IT */
#define IFX_CAN_MITR_IT_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_AMR_Bits.AM */
#define IFX_CAN_MO_AMR_AM_LEN (29u)

/** \brief  Mask for Ifx_CAN_MO_AMR_Bits.AM */
#define IFX_CAN_MO_AMR_AM_MSK (0x1fffffffu)

/** \brief  Offset for Ifx_CAN_MO_AMR_Bits.AM */
#define IFX_CAN_MO_AMR_AM_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_AMR_Bits.MIDE */
#define IFX_CAN_MO_AMR_MIDE_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_AMR_Bits.MIDE */
#define IFX_CAN_MO_AMR_MIDE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_AMR_Bits.MIDE */
#define IFX_CAN_MO_AMR_MIDE_OFF (29u)

/** \brief  Length for Ifx_CAN_MO_AR_Bits.ID */
#define IFX_CAN_MO_AR_ID_LEN (29u)

/** \brief  Mask for Ifx_CAN_MO_AR_Bits.ID */
#define IFX_CAN_MO_AR_ID_MSK (0x1fffffffu)

/** \brief  Offset for Ifx_CAN_MO_AR_Bits.ID */
#define IFX_CAN_MO_AR_ID_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_AR_Bits.IDE */
#define IFX_CAN_MO_AR_IDE_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_AR_Bits.IDE */
#define IFX_CAN_MO_AR_IDE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_AR_Bits.IDE */
#define IFX_CAN_MO_AR_IDE_OFF (29u)

/** \brief  Length for Ifx_CAN_MO_AR_Bits.PRI */
#define IFX_CAN_MO_AR_PRI_LEN (2u)

/** \brief  Mask for Ifx_CAN_MO_AR_Bits.PRI */
#define IFX_CAN_MO_AR_PRI_MSK (0x3u)

/** \brief  Offset for Ifx_CAN_MO_AR_Bits.PRI */
#define IFX_CAN_MO_AR_PRI_OFF (30u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.RESDIR */
#define IFX_CAN_MO_CTR_RESDIR_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.RESDIR */
#define IFX_CAN_MO_CTR_RESDIR_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.RESDIR */
#define IFX_CAN_MO_CTR_RESDIR_OFF (11u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.RESMSGLST */
#define IFX_CAN_MO_CTR_RESMSGLST_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.RESMSGLST */
#define IFX_CAN_MO_CTR_RESMSGLST_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.RESMSGLST */
#define IFX_CAN_MO_CTR_RESMSGLST_OFF (4u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.RESMSGVAL */
#define IFX_CAN_MO_CTR_RESMSGVAL_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.RESMSGVAL */
#define IFX_CAN_MO_CTR_RESMSGVAL_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.RESMSGVAL */
#define IFX_CAN_MO_CTR_RESMSGVAL_OFF (5u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.RESNEWDAT */
#define IFX_CAN_MO_CTR_RESNEWDAT_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.RESNEWDAT */
#define IFX_CAN_MO_CTR_RESNEWDAT_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.RESNEWDAT */
#define IFX_CAN_MO_CTR_RESNEWDAT_OFF (3u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.RESRTSEL */
#define IFX_CAN_MO_CTR_RESRTSEL_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.RESRTSEL */
#define IFX_CAN_MO_CTR_RESRTSEL_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.RESRTSEL */
#define IFX_CAN_MO_CTR_RESRTSEL_OFF (6u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.RESRXEN */
#define IFX_CAN_MO_CTR_RESRXEN_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.RESRXEN */
#define IFX_CAN_MO_CTR_RESRXEN_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.RESRXEN */
#define IFX_CAN_MO_CTR_RESRXEN_OFF (7u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.RESRXPND */
#define IFX_CAN_MO_CTR_RESRXPND_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.RESRXPND */
#define IFX_CAN_MO_CTR_RESRXPND_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.RESRXPND */
#define IFX_CAN_MO_CTR_RESRXPND_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.RESRXUPD */
#define IFX_CAN_MO_CTR_RESRXUPD_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.RESRXUPD */
#define IFX_CAN_MO_CTR_RESRXUPD_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.RESRXUPD */
#define IFX_CAN_MO_CTR_RESRXUPD_OFF (2u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.RESTXEN0 */
#define IFX_CAN_MO_CTR_RESTXEN0_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.RESTXEN0 */
#define IFX_CAN_MO_CTR_RESTXEN0_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.RESTXEN0 */
#define IFX_CAN_MO_CTR_RESTXEN0_OFF (9u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.RESTXEN1 */
#define IFX_CAN_MO_CTR_RESTXEN1_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.RESTXEN1 */
#define IFX_CAN_MO_CTR_RESTXEN1_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.RESTXEN1 */
#define IFX_CAN_MO_CTR_RESTXEN1_OFF (10u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.RESTXPND */
#define IFX_CAN_MO_CTR_RESTXPND_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.RESTXPND */
#define IFX_CAN_MO_CTR_RESTXPND_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.RESTXPND */
#define IFX_CAN_MO_CTR_RESTXPND_OFF (1u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.RESTXRQ */
#define IFX_CAN_MO_CTR_RESTXRQ_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.RESTXRQ */
#define IFX_CAN_MO_CTR_RESTXRQ_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.RESTXRQ */
#define IFX_CAN_MO_CTR_RESTXRQ_OFF (8u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.SETDIR */
#define IFX_CAN_MO_CTR_SETDIR_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.SETDIR */
#define IFX_CAN_MO_CTR_SETDIR_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.SETDIR */
#define IFX_CAN_MO_CTR_SETDIR_OFF (27u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.SETMSGLST */
#define IFX_CAN_MO_CTR_SETMSGLST_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.SETMSGLST */
#define IFX_CAN_MO_CTR_SETMSGLST_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.SETMSGLST */
#define IFX_CAN_MO_CTR_SETMSGLST_OFF (20u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.SETMSGVAL */
#define IFX_CAN_MO_CTR_SETMSGVAL_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.SETMSGVAL */
#define IFX_CAN_MO_CTR_SETMSGVAL_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.SETMSGVAL */
#define IFX_CAN_MO_CTR_SETMSGVAL_OFF (21u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.SETNEWDAT */
#define IFX_CAN_MO_CTR_SETNEWDAT_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.SETNEWDAT */
#define IFX_CAN_MO_CTR_SETNEWDAT_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.SETNEWDAT */
#define IFX_CAN_MO_CTR_SETNEWDAT_OFF (19u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.SETRTSEL */
#define IFX_CAN_MO_CTR_SETRTSEL_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.SETRTSEL */
#define IFX_CAN_MO_CTR_SETRTSEL_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.SETRTSEL */
#define IFX_CAN_MO_CTR_SETRTSEL_OFF (22u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.SETRXEN */
#define IFX_CAN_MO_CTR_SETRXEN_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.SETRXEN */
#define IFX_CAN_MO_CTR_SETRXEN_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.SETRXEN */
#define IFX_CAN_MO_CTR_SETRXEN_OFF (23u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.SETRXPND */
#define IFX_CAN_MO_CTR_SETRXPND_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.SETRXPND */
#define IFX_CAN_MO_CTR_SETRXPND_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.SETRXPND */
#define IFX_CAN_MO_CTR_SETRXPND_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.SETRXUPD */
#define IFX_CAN_MO_CTR_SETRXUPD_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.SETRXUPD */
#define IFX_CAN_MO_CTR_SETRXUPD_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.SETRXUPD */
#define IFX_CAN_MO_CTR_SETRXUPD_OFF (18u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.SETTXEN0 */
#define IFX_CAN_MO_CTR_SETTXEN0_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.SETTXEN0 */
#define IFX_CAN_MO_CTR_SETTXEN0_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.SETTXEN0 */
#define IFX_CAN_MO_CTR_SETTXEN0_OFF (25u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.SETTXEN1 */
#define IFX_CAN_MO_CTR_SETTXEN1_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.SETTXEN1 */
#define IFX_CAN_MO_CTR_SETTXEN1_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.SETTXEN1 */
#define IFX_CAN_MO_CTR_SETTXEN1_OFF (26u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.SETTXPND */
#define IFX_CAN_MO_CTR_SETTXPND_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.SETTXPND */
#define IFX_CAN_MO_CTR_SETTXPND_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.SETTXPND */
#define IFX_CAN_MO_CTR_SETTXPND_OFF (17u)

/** \brief  Length for Ifx_CAN_MO_CTR_Bits.SETTXRQ */
#define IFX_CAN_MO_CTR_SETTXRQ_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_CTR_Bits.SETTXRQ */
#define IFX_CAN_MO_CTR_SETTXRQ_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_CTR_Bits.SETTXRQ */
#define IFX_CAN_MO_CTR_SETTXRQ_OFF (24u)

/** \brief  Length for Ifx_CAN_MO_DATAH_Bits.DB4 */
#define IFX_CAN_MO_DATAH_DB4_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_DATAH_Bits.DB4 */
#define IFX_CAN_MO_DATAH_DB4_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_DATAH_Bits.DB4 */
#define IFX_CAN_MO_DATAH_DB4_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_DATAH_Bits.DB5 */
#define IFX_CAN_MO_DATAH_DB5_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_DATAH_Bits.DB5 */
#define IFX_CAN_MO_DATAH_DB5_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_DATAH_Bits.DB5 */
#define IFX_CAN_MO_DATAH_DB5_OFF (8u)

/** \brief  Length for Ifx_CAN_MO_DATAH_Bits.DB6 */
#define IFX_CAN_MO_DATAH_DB6_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_DATAH_Bits.DB6 */
#define IFX_CAN_MO_DATAH_DB6_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_DATAH_Bits.DB6 */
#define IFX_CAN_MO_DATAH_DB6_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_DATAH_Bits.DB7 */
#define IFX_CAN_MO_DATAH_DB7_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_DATAH_Bits.DB7 */
#define IFX_CAN_MO_DATAH_DB7_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_DATAH_Bits.DB7 */
#define IFX_CAN_MO_DATAH_DB7_OFF (24u)

/** \brief  Length for Ifx_CAN_MO_DATAL_Bits.DB0 */
#define IFX_CAN_MO_DATAL_DB0_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_DATAL_Bits.DB0 */
#define IFX_CAN_MO_DATAL_DB0_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_DATAL_Bits.DB0 */
#define IFX_CAN_MO_DATAL_DB0_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_DATAL_Bits.DB1 */
#define IFX_CAN_MO_DATAL_DB1_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_DATAL_Bits.DB1 */
#define IFX_CAN_MO_DATAL_DB1_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_DATAL_Bits.DB1 */
#define IFX_CAN_MO_DATAL_DB1_OFF (8u)

/** \brief  Length for Ifx_CAN_MO_DATAL_Bits.DB2 */
#define IFX_CAN_MO_DATAL_DB2_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_DATAL_Bits.DB2 */
#define IFX_CAN_MO_DATAL_DB2_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_DATAL_Bits.DB2 */
#define IFX_CAN_MO_DATAL_DB2_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_DATAL_Bits.DB3 */
#define IFX_CAN_MO_DATAL_DB3_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_DATAL_Bits.DB3 */
#define IFX_CAN_MO_DATAL_DB3_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_DATAL_Bits.DB3 */
#define IFX_CAN_MO_DATAL_DB3_OFF (24u)

/** \brief  Length for Ifx_CAN_MO_EDATA0_Bits.DB0 */
#define IFX_CAN_MO_EDATA0_DB0_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA0_Bits.DB0 */
#define IFX_CAN_MO_EDATA0_DB0_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA0_Bits.DB0 */
#define IFX_CAN_MO_EDATA0_DB0_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_EDATA0_Bits.DB1 */
#define IFX_CAN_MO_EDATA0_DB1_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA0_Bits.DB1 */
#define IFX_CAN_MO_EDATA0_DB1_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA0_Bits.DB1 */
#define IFX_CAN_MO_EDATA0_DB1_OFF (8u)

/** \brief  Length for Ifx_CAN_MO_EDATA0_Bits.DB2 */
#define IFX_CAN_MO_EDATA0_DB2_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA0_Bits.DB2 */
#define IFX_CAN_MO_EDATA0_DB2_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA0_Bits.DB2 */
#define IFX_CAN_MO_EDATA0_DB2_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_EDATA0_Bits.DB3 */
#define IFX_CAN_MO_EDATA0_DB3_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA0_Bits.DB3 */
#define IFX_CAN_MO_EDATA0_DB3_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA0_Bits.DB3 */
#define IFX_CAN_MO_EDATA0_DB3_OFF (24u)

/** \brief  Length for Ifx_CAN_MO_EDATA1_Bits.DB0 */
#define IFX_CAN_MO_EDATA1_DB0_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA1_Bits.DB0 */
#define IFX_CAN_MO_EDATA1_DB0_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA1_Bits.DB0 */
#define IFX_CAN_MO_EDATA1_DB0_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_EDATA1_Bits.DB1 */
#define IFX_CAN_MO_EDATA1_DB1_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA1_Bits.DB1 */
#define IFX_CAN_MO_EDATA1_DB1_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA1_Bits.DB1 */
#define IFX_CAN_MO_EDATA1_DB1_OFF (8u)

/** \brief  Length for Ifx_CAN_MO_EDATA1_Bits.DB2 */
#define IFX_CAN_MO_EDATA1_DB2_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA1_Bits.DB2 */
#define IFX_CAN_MO_EDATA1_DB2_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA1_Bits.DB2 */
#define IFX_CAN_MO_EDATA1_DB2_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_EDATA1_Bits.DB3 */
#define IFX_CAN_MO_EDATA1_DB3_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA1_Bits.DB3 */
#define IFX_CAN_MO_EDATA1_DB3_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA1_Bits.DB3 */
#define IFX_CAN_MO_EDATA1_DB3_OFF (24u)

/** \brief  Length for Ifx_CAN_MO_EDATA2_Bits.DB0 */
#define IFX_CAN_MO_EDATA2_DB0_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA2_Bits.DB0 */
#define IFX_CAN_MO_EDATA2_DB0_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA2_Bits.DB0 */
#define IFX_CAN_MO_EDATA2_DB0_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_EDATA2_Bits.DB1 */
#define IFX_CAN_MO_EDATA2_DB1_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA2_Bits.DB1 */
#define IFX_CAN_MO_EDATA2_DB1_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA2_Bits.DB1 */
#define IFX_CAN_MO_EDATA2_DB1_OFF (8u)

/** \brief  Length for Ifx_CAN_MO_EDATA2_Bits.DB2 */
#define IFX_CAN_MO_EDATA2_DB2_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA2_Bits.DB2 */
#define IFX_CAN_MO_EDATA2_DB2_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA2_Bits.DB2 */
#define IFX_CAN_MO_EDATA2_DB2_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_EDATA2_Bits.DB3 */
#define IFX_CAN_MO_EDATA2_DB3_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA2_Bits.DB3 */
#define IFX_CAN_MO_EDATA2_DB3_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA2_Bits.DB3 */
#define IFX_CAN_MO_EDATA2_DB3_OFF (24u)

/** \brief  Length for Ifx_CAN_MO_EDATA3_Bits.DB0 */
#define IFX_CAN_MO_EDATA3_DB0_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA3_Bits.DB0 */
#define IFX_CAN_MO_EDATA3_DB0_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA3_Bits.DB0 */
#define IFX_CAN_MO_EDATA3_DB0_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_EDATA3_Bits.DB1 */
#define IFX_CAN_MO_EDATA3_DB1_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA3_Bits.DB1 */
#define IFX_CAN_MO_EDATA3_DB1_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA3_Bits.DB1 */
#define IFX_CAN_MO_EDATA3_DB1_OFF (8u)

/** \brief  Length for Ifx_CAN_MO_EDATA3_Bits.DB2 */
#define IFX_CAN_MO_EDATA3_DB2_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA3_Bits.DB2 */
#define IFX_CAN_MO_EDATA3_DB2_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA3_Bits.DB2 */
#define IFX_CAN_MO_EDATA3_DB2_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_EDATA3_Bits.DB3 */
#define IFX_CAN_MO_EDATA3_DB3_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA3_Bits.DB3 */
#define IFX_CAN_MO_EDATA3_DB3_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA3_Bits.DB3 */
#define IFX_CAN_MO_EDATA3_DB3_OFF (24u)

/** \brief  Length for Ifx_CAN_MO_EDATA4_Bits.DB0 */
#define IFX_CAN_MO_EDATA4_DB0_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA4_Bits.DB0 */
#define IFX_CAN_MO_EDATA4_DB0_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA4_Bits.DB0 */
#define IFX_CAN_MO_EDATA4_DB0_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_EDATA4_Bits.DB1 */
#define IFX_CAN_MO_EDATA4_DB1_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA4_Bits.DB1 */
#define IFX_CAN_MO_EDATA4_DB1_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA4_Bits.DB1 */
#define IFX_CAN_MO_EDATA4_DB1_OFF (8u)

/** \brief  Length for Ifx_CAN_MO_EDATA4_Bits.DB2 */
#define IFX_CAN_MO_EDATA4_DB2_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA4_Bits.DB2 */
#define IFX_CAN_MO_EDATA4_DB2_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA4_Bits.DB2 */
#define IFX_CAN_MO_EDATA4_DB2_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_EDATA4_Bits.DB3 */
#define IFX_CAN_MO_EDATA4_DB3_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA4_Bits.DB3 */
#define IFX_CAN_MO_EDATA4_DB3_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA4_Bits.DB3 */
#define IFX_CAN_MO_EDATA4_DB3_OFF (24u)

/** \brief  Length for Ifx_CAN_MO_EDATA5_Bits.DB0 */
#define IFX_CAN_MO_EDATA5_DB0_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA5_Bits.DB0 */
#define IFX_CAN_MO_EDATA5_DB0_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA5_Bits.DB0 */
#define IFX_CAN_MO_EDATA5_DB0_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_EDATA5_Bits.DB1 */
#define IFX_CAN_MO_EDATA5_DB1_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA5_Bits.DB1 */
#define IFX_CAN_MO_EDATA5_DB1_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA5_Bits.DB1 */
#define IFX_CAN_MO_EDATA5_DB1_OFF (8u)

/** \brief  Length for Ifx_CAN_MO_EDATA5_Bits.DB2 */
#define IFX_CAN_MO_EDATA5_DB2_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA5_Bits.DB2 */
#define IFX_CAN_MO_EDATA5_DB2_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA5_Bits.DB2 */
#define IFX_CAN_MO_EDATA5_DB2_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_EDATA5_Bits.DB3 */
#define IFX_CAN_MO_EDATA5_DB3_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA5_Bits.DB3 */
#define IFX_CAN_MO_EDATA5_DB3_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA5_Bits.DB3 */
#define IFX_CAN_MO_EDATA5_DB3_OFF (24u)

/** \brief  Length for Ifx_CAN_MO_EDATA6_Bits.DB0 */
#define IFX_CAN_MO_EDATA6_DB0_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA6_Bits.DB0 */
#define IFX_CAN_MO_EDATA6_DB0_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA6_Bits.DB0 */
#define IFX_CAN_MO_EDATA6_DB0_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_EDATA6_Bits.DB1 */
#define IFX_CAN_MO_EDATA6_DB1_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA6_Bits.DB1 */
#define IFX_CAN_MO_EDATA6_DB1_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA6_Bits.DB1 */
#define IFX_CAN_MO_EDATA6_DB1_OFF (8u)

/** \brief  Length for Ifx_CAN_MO_EDATA6_Bits.DB2 */
#define IFX_CAN_MO_EDATA6_DB2_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA6_Bits.DB2 */
#define IFX_CAN_MO_EDATA6_DB2_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA6_Bits.DB2 */
#define IFX_CAN_MO_EDATA6_DB2_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_EDATA6_Bits.DB3 */
#define IFX_CAN_MO_EDATA6_DB3_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_EDATA6_Bits.DB3 */
#define IFX_CAN_MO_EDATA6_DB3_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_EDATA6_Bits.DB3 */
#define IFX_CAN_MO_EDATA6_DB3_OFF (24u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.BRS */
#define IFX_CAN_MO_FCR_BRS_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.BRS */
#define IFX_CAN_MO_FCR_BRS_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.BRS */
#define IFX_CAN_MO_FCR_BRS_OFF (5u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.DATC */
#define IFX_CAN_MO_FCR_DATC_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.DATC */
#define IFX_CAN_MO_FCR_DATC_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.DATC */
#define IFX_CAN_MO_FCR_DATC_OFF (11u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.DLC */
#define IFX_CAN_MO_FCR_DLC_LEN (4u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.DLC */
#define IFX_CAN_MO_FCR_DLC_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.DLC */
#define IFX_CAN_MO_FCR_DLC_OFF (24u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.DLCC */
#define IFX_CAN_MO_FCR_DLCC_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.DLCC */
#define IFX_CAN_MO_FCR_DLCC_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.DLCC */
#define IFX_CAN_MO_FCR_DLCC_OFF (10u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.FDF */
#define IFX_CAN_MO_FCR_FDF_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.FDF */
#define IFX_CAN_MO_FCR_FDF_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.FDF */
#define IFX_CAN_MO_FCR_FDF_OFF (6u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.FRREN */
#define IFX_CAN_MO_FCR_FRREN_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.FRREN */
#define IFX_CAN_MO_FCR_FRREN_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.FRREN */
#define IFX_CAN_MO_FCR_FRREN_OFF (20u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.GDFS */
#define IFX_CAN_MO_FCR_GDFS_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.GDFS */
#define IFX_CAN_MO_FCR_GDFS_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.GDFS */
#define IFX_CAN_MO_FCR_GDFS_OFF (8u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.IDC */
#define IFX_CAN_MO_FCR_IDC_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.IDC */
#define IFX_CAN_MO_FCR_IDC_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.IDC */
#define IFX_CAN_MO_FCR_IDC_OFF (9u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.MMC */
#define IFX_CAN_MO_FCR_MMC_LEN (4u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.MMC */
#define IFX_CAN_MO_FCR_MMC_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.MMC */
#define IFX_CAN_MO_FCR_MMC_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.OVIE */
#define IFX_CAN_MO_FCR_OVIE_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.OVIE */
#define IFX_CAN_MO_FCR_OVIE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.OVIE */
#define IFX_CAN_MO_FCR_OVIE_OFF (18u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.RMM */
#define IFX_CAN_MO_FCR_RMM_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.RMM */
#define IFX_CAN_MO_FCR_RMM_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.RMM */
#define IFX_CAN_MO_FCR_RMM_OFF (21u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.RXIE */
#define IFX_CAN_MO_FCR_RXIE_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.RXIE */
#define IFX_CAN_MO_FCR_RXIE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.RXIE */
#define IFX_CAN_MO_FCR_RXIE_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.RXTOE */
#define IFX_CAN_MO_FCR_RXTOE_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.RXTOE */
#define IFX_CAN_MO_FCR_RXTOE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.RXTOE */
#define IFX_CAN_MO_FCR_RXTOE_OFF (4u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.SDT */
#define IFX_CAN_MO_FCR_SDT_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.SDT */
#define IFX_CAN_MO_FCR_SDT_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.SDT */
#define IFX_CAN_MO_FCR_SDT_OFF (22u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.STT */
#define IFX_CAN_MO_FCR_STT_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.STT */
#define IFX_CAN_MO_FCR_STT_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.STT */
#define IFX_CAN_MO_FCR_STT_OFF (23u)

/** \brief  Length for Ifx_CAN_MO_FCR_Bits.TXIE */
#define IFX_CAN_MO_FCR_TXIE_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_FCR_Bits.TXIE */
#define IFX_CAN_MO_FCR_TXIE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_FCR_Bits.TXIE */
#define IFX_CAN_MO_FCR_TXIE_OFF (17u)

/** \brief  Length for Ifx_CAN_MO_FGPR_Bits.BOT */
#define IFX_CAN_MO_FGPR_BOT_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_FGPR_Bits.BOT */
#define IFX_CAN_MO_FGPR_BOT_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_FGPR_Bits.BOT */
#define IFX_CAN_MO_FGPR_BOT_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_FGPR_Bits.CUR */
#define IFX_CAN_MO_FGPR_CUR_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_FGPR_Bits.CUR */
#define IFX_CAN_MO_FGPR_CUR_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_FGPR_Bits.CUR */
#define IFX_CAN_MO_FGPR_CUR_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_FGPR_Bits.SEL */
#define IFX_CAN_MO_FGPR_SEL_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_FGPR_Bits.SEL */
#define IFX_CAN_MO_FGPR_SEL_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_FGPR_Bits.SEL */
#define IFX_CAN_MO_FGPR_SEL_OFF (24u)

/** \brief  Length for Ifx_CAN_MO_FGPR_Bits.TOP */
#define IFX_CAN_MO_FGPR_TOP_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_FGPR_Bits.TOP */
#define IFX_CAN_MO_FGPR_TOP_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_FGPR_Bits.TOP */
#define IFX_CAN_MO_FGPR_TOP_OFF (8u)

/** \brief  Length for Ifx_CAN_MO_IPR_Bits.CFCVAL */
#define IFX_CAN_MO_IPR_CFCVAL_LEN (16u)

/** \brief  Mask for Ifx_CAN_MO_IPR_Bits.CFCVAL */
#define IFX_CAN_MO_IPR_CFCVAL_MSK (0xffffu)

/** \brief  Offset for Ifx_CAN_MO_IPR_Bits.CFCVAL */
#define IFX_CAN_MO_IPR_CFCVAL_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_IPR_Bits.MPN */
#define IFX_CAN_MO_IPR_MPN_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_IPR_Bits.MPN */
#define IFX_CAN_MO_IPR_MPN_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_IPR_Bits.MPN */
#define IFX_CAN_MO_IPR_MPN_OFF (8u)

/** \brief  Length for Ifx_CAN_MO_IPR_Bits.RXINP */
#define IFX_CAN_MO_IPR_RXINP_LEN (4u)

/** \brief  Mask for Ifx_CAN_MO_IPR_Bits.RXINP */
#define IFX_CAN_MO_IPR_RXINP_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_MO_IPR_Bits.RXINP */
#define IFX_CAN_MO_IPR_RXINP_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_IPR_Bits.TXINP */
#define IFX_CAN_MO_IPR_TXINP_LEN (4u)

/** \brief  Mask for Ifx_CAN_MO_IPR_Bits.TXINP */
#define IFX_CAN_MO_IPR_TXINP_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_MO_IPR_Bits.TXINP */
#define IFX_CAN_MO_IPR_TXINP_OFF (4u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.DIR */
#define IFX_CAN_MO_STAT_DIR_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.DIR */
#define IFX_CAN_MO_STAT_DIR_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.DIR */
#define IFX_CAN_MO_STAT_DIR_OFF (11u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.LIST */
#define IFX_CAN_MO_STAT_LIST_LEN (4u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.LIST */
#define IFX_CAN_MO_STAT_LIST_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.LIST */
#define IFX_CAN_MO_STAT_LIST_OFF (12u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.MSGLST */
#define IFX_CAN_MO_STAT_MSGLST_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.MSGLST */
#define IFX_CAN_MO_STAT_MSGLST_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.MSGLST */
#define IFX_CAN_MO_STAT_MSGLST_OFF (4u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.MSGVAL */
#define IFX_CAN_MO_STAT_MSGVAL_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.MSGVAL */
#define IFX_CAN_MO_STAT_MSGVAL_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.MSGVAL */
#define IFX_CAN_MO_STAT_MSGVAL_OFF (5u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.NEWDAT */
#define IFX_CAN_MO_STAT_NEWDAT_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.NEWDAT */
#define IFX_CAN_MO_STAT_NEWDAT_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.NEWDAT */
#define IFX_CAN_MO_STAT_NEWDAT_OFF (3u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.PNEXT */
#define IFX_CAN_MO_STAT_PNEXT_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.PNEXT */
#define IFX_CAN_MO_STAT_PNEXT_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.PNEXT */
#define IFX_CAN_MO_STAT_PNEXT_OFF (24u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.PPREV */
#define IFX_CAN_MO_STAT_PPREV_LEN (8u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.PPREV */
#define IFX_CAN_MO_STAT_PPREV_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.PPREV */
#define IFX_CAN_MO_STAT_PPREV_OFF (16u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.RTSEL */
#define IFX_CAN_MO_STAT_RTSEL_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.RTSEL */
#define IFX_CAN_MO_STAT_RTSEL_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.RTSEL */
#define IFX_CAN_MO_STAT_RTSEL_OFF (6u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.RXEN */
#define IFX_CAN_MO_STAT_RXEN_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.RXEN */
#define IFX_CAN_MO_STAT_RXEN_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.RXEN */
#define IFX_CAN_MO_STAT_RXEN_OFF (7u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.RXPND */
#define IFX_CAN_MO_STAT_RXPND_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.RXPND */
#define IFX_CAN_MO_STAT_RXPND_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.RXPND */
#define IFX_CAN_MO_STAT_RXPND_OFF (0u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.RXUPD */
#define IFX_CAN_MO_STAT_RXUPD_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.RXUPD */
#define IFX_CAN_MO_STAT_RXUPD_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.RXUPD */
#define IFX_CAN_MO_STAT_RXUPD_OFF (2u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.TXEN0 */
#define IFX_CAN_MO_STAT_TXEN0_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.TXEN0 */
#define IFX_CAN_MO_STAT_TXEN0_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.TXEN0 */
#define IFX_CAN_MO_STAT_TXEN0_OFF (9u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.TXEN1 */
#define IFX_CAN_MO_STAT_TXEN1_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.TXEN1 */
#define IFX_CAN_MO_STAT_TXEN1_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.TXEN1 */
#define IFX_CAN_MO_STAT_TXEN1_OFF (10u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.TXPND */
#define IFX_CAN_MO_STAT_TXPND_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.TXPND */
#define IFX_CAN_MO_STAT_TXPND_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.TXPND */
#define IFX_CAN_MO_STAT_TXPND_OFF (1u)

/** \brief  Length for Ifx_CAN_MO_STAT_Bits.TXRQ */
#define IFX_CAN_MO_STAT_TXRQ_LEN (1u)

/** \brief  Mask for Ifx_CAN_MO_STAT_Bits.TXRQ */
#define IFX_CAN_MO_STAT_TXRQ_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_MO_STAT_Bits.TXRQ */
#define IFX_CAN_MO_STAT_TXRQ_OFF (8u)

/** \brief  Length for Ifx_CAN_MSID_Bits.INDEX */
#define IFX_CAN_MSID_INDEX_LEN (6u)

/** \brief  Mask for Ifx_CAN_MSID_Bits.INDEX */
#define IFX_CAN_MSID_INDEX_MSK (0x3fu)

/** \brief  Offset for Ifx_CAN_MSID_Bits.INDEX */
#define IFX_CAN_MSID_INDEX_OFF (0u)

/** \brief  Length for Ifx_CAN_MSIMASK_Bits.IM */
#define IFX_CAN_MSIMASK_IM_LEN (32u)

/** \brief  Mask for Ifx_CAN_MSIMASK_Bits.IM */
#define IFX_CAN_MSIMASK_IM_MSK (0xffffffffu)

/** \brief  Offset for Ifx_CAN_MSIMASK_Bits.IM */
#define IFX_CAN_MSIMASK_IM_OFF (0u)

/** \brief  Length for Ifx_CAN_MSPND_Bits.PND */
#define IFX_CAN_MSPND_PND_LEN (32u)

/** \brief  Mask for Ifx_CAN_MSPND_Bits.PND */
#define IFX_CAN_MSPND_PND_MSK (0xffffffffu)

/** \brief  Offset for Ifx_CAN_MSPND_Bits.PND */
#define IFX_CAN_MSPND_PND_OFF (0u)

/** \brief  Length for Ifx_CAN_N_BTEVR_Bits.BRP */
#define IFX_CAN_N_BTEVR_BRP_LEN (6u)

/** \brief  Mask for Ifx_CAN_N_BTEVR_Bits.BRP */
#define IFX_CAN_N_BTEVR_BRP_MSK (0x3fu)

/** \brief  Offset for Ifx_CAN_N_BTEVR_Bits.BRP */
#define IFX_CAN_N_BTEVR_BRP_OFF (0u)

/** \brief  Length for Ifx_CAN_N_BTEVR_Bits.DIV8 */
#define IFX_CAN_N_BTEVR_DIV8_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_BTEVR_Bits.DIV8 */
#define IFX_CAN_N_BTEVR_DIV8_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_BTEVR_Bits.DIV8 */
#define IFX_CAN_N_BTEVR_DIV8_OFF (15u)

/** \brief  Length for Ifx_CAN_N_BTEVR_Bits.SJW */
#define IFX_CAN_N_BTEVR_SJW_LEN (4u)

/** \brief  Mask for Ifx_CAN_N_BTEVR_Bits.SJW */
#define IFX_CAN_N_BTEVR_SJW_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_N_BTEVR_Bits.SJW */
#define IFX_CAN_N_BTEVR_SJW_OFF (8u)

/** \brief  Length for Ifx_CAN_N_BTEVR_Bits.TSEG1 */
#define IFX_CAN_N_BTEVR_TSEG1_LEN (6u)

/** \brief  Mask for Ifx_CAN_N_BTEVR_Bits.TSEG1 */
#define IFX_CAN_N_BTEVR_TSEG1_MSK (0x3fu)

/** \brief  Offset for Ifx_CAN_N_BTEVR_Bits.TSEG1 */
#define IFX_CAN_N_BTEVR_TSEG1_OFF (22u)

/** \brief  Length for Ifx_CAN_N_BTEVR_Bits.TSEG2 */
#define IFX_CAN_N_BTEVR_TSEG2_LEN (5u)

/** \brief  Mask for Ifx_CAN_N_BTEVR_Bits.TSEG2 */
#define IFX_CAN_N_BTEVR_TSEG2_MSK (0x1fu)

/** \brief  Offset for Ifx_CAN_N_BTEVR_Bits.TSEG2 */
#define IFX_CAN_N_BTEVR_TSEG2_OFF (16u)

/** \brief  Length for Ifx_CAN_N_BTR_Bits.BRP */
#define IFX_CAN_N_BTR_BRP_LEN (6u)

/** \brief  Mask for Ifx_CAN_N_BTR_Bits.BRP */
#define IFX_CAN_N_BTR_BRP_MSK (0x3fu)

/** \brief  Offset for Ifx_CAN_N_BTR_Bits.BRP */
#define IFX_CAN_N_BTR_BRP_OFF (0u)

/** \brief  Length for Ifx_CAN_N_BTR_Bits.DIV8 */
#define IFX_CAN_N_BTR_DIV8_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_BTR_Bits.DIV8 */
#define IFX_CAN_N_BTR_DIV8_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_BTR_Bits.DIV8 */
#define IFX_CAN_N_BTR_DIV8_OFF (15u)

/** \brief  Length for Ifx_CAN_N_BTR_Bits.SJW */
#define IFX_CAN_N_BTR_SJW_LEN (2u)

/** \brief  Mask for Ifx_CAN_N_BTR_Bits.SJW */
#define IFX_CAN_N_BTR_SJW_MSK (0x3u)

/** \brief  Offset for Ifx_CAN_N_BTR_Bits.SJW */
#define IFX_CAN_N_BTR_SJW_OFF (6u)

/** \brief  Length for Ifx_CAN_N_BTR_Bits.TSEG1 */
#define IFX_CAN_N_BTR_TSEG1_LEN (4u)

/** \brief  Mask for Ifx_CAN_N_BTR_Bits.TSEG1 */
#define IFX_CAN_N_BTR_TSEG1_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_N_BTR_Bits.TSEG1 */
#define IFX_CAN_N_BTR_TSEG1_OFF (8u)

/** \brief  Length for Ifx_CAN_N_BTR_Bits.TSEG2 */
#define IFX_CAN_N_BTR_TSEG2_LEN (3u)

/** \brief  Mask for Ifx_CAN_N_BTR_Bits.TSEG2 */
#define IFX_CAN_N_BTR_TSEG2_MSK (0x7u)

/** \brief  Offset for Ifx_CAN_N_BTR_Bits.TSEG2 */
#define IFX_CAN_N_BTR_TSEG2_OFF (12u)

/** \brief  Length for Ifx_CAN_N_CR_Bits.ALIE */
#define IFX_CAN_N_CR_ALIE_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_CR_Bits.ALIE */
#define IFX_CAN_N_CR_ALIE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_CR_Bits.ALIE */
#define IFX_CAN_N_CR_ALIE_OFF (3u)

/** \brief  Length for Ifx_CAN_N_CR_Bits.CALM */
#define IFX_CAN_N_CR_CALM_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_CR_Bits.CALM */
#define IFX_CAN_N_CR_CALM_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_CR_Bits.CALM */
#define IFX_CAN_N_CR_CALM_OFF (7u)

/** \brief  Length for Ifx_CAN_N_CR_Bits.CANDIS */
#define IFX_CAN_N_CR_CANDIS_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_CR_Bits.CANDIS */
#define IFX_CAN_N_CR_CANDIS_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_CR_Bits.CANDIS */
#define IFX_CAN_N_CR_CANDIS_OFF (4u)

/** \brief  Length for Ifx_CAN_N_CR_Bits.CCE */
#define IFX_CAN_N_CR_CCE_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_CR_Bits.CCE */
#define IFX_CAN_N_CR_CCE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_CR_Bits.CCE */
#define IFX_CAN_N_CR_CCE_OFF (6u)

/** \brief  Length for Ifx_CAN_N_CR_Bits.FDEN */
#define IFX_CAN_N_CR_FDEN_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_CR_Bits.FDEN */
#define IFX_CAN_N_CR_FDEN_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_CR_Bits.FDEN */
#define IFX_CAN_N_CR_FDEN_OFF (9u)

/** \brief  Length for Ifx_CAN_N_CR_Bits.INIT */
#define IFX_CAN_N_CR_INIT_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_CR_Bits.INIT */
#define IFX_CAN_N_CR_INIT_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_CR_Bits.INIT */
#define IFX_CAN_N_CR_INIT_OFF (0u)

/** \brief  Length for Ifx_CAN_N_CR_Bits.LECIE */
#define IFX_CAN_N_CR_LECIE_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_CR_Bits.LECIE */
#define IFX_CAN_N_CR_LECIE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_CR_Bits.LECIE */
#define IFX_CAN_N_CR_LECIE_OFF (2u)

/** \brief  Length for Ifx_CAN_N_CR_Bits.SUSEN */
#define IFX_CAN_N_CR_SUSEN_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_CR_Bits.SUSEN */
#define IFX_CAN_N_CR_SUSEN_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_CR_Bits.SUSEN */
#define IFX_CAN_N_CR_SUSEN_OFF (8u)

/** \brief  Length for Ifx_CAN_N_CR_Bits.TRIE */
#define IFX_CAN_N_CR_TRIE_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_CR_Bits.TRIE */
#define IFX_CAN_N_CR_TRIE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_CR_Bits.TRIE */
#define IFX_CAN_N_CR_TRIE_OFF (1u)

/** \brief  Length for Ifx_CAN_N_CR_Bits.TXDIS */
#define IFX_CAN_N_CR_TXDIS_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_CR_Bits.TXDIS */
#define IFX_CAN_N_CR_TXDIS_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_CR_Bits.TXDIS */
#define IFX_CAN_N_CR_TXDIS_OFF (5u)

/** \brief  Length for Ifx_CAN_N_ECNT_Bits.EWRNLVL */
#define IFX_CAN_N_ECNT_EWRNLVL_LEN (8u)

/** \brief  Mask for Ifx_CAN_N_ECNT_Bits.EWRNLVL */
#define IFX_CAN_N_ECNT_EWRNLVL_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_N_ECNT_Bits.EWRNLVL */
#define IFX_CAN_N_ECNT_EWRNLVL_OFF (16u)

/** \brief  Length for Ifx_CAN_N_ECNT_Bits.LEINC */
#define IFX_CAN_N_ECNT_LEINC_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_ECNT_Bits.LEINC */
#define IFX_CAN_N_ECNT_LEINC_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_ECNT_Bits.LEINC */
#define IFX_CAN_N_ECNT_LEINC_OFF (25u)

/** \brief  Length for Ifx_CAN_N_ECNT_Bits.LETD */
#define IFX_CAN_N_ECNT_LETD_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_ECNT_Bits.LETD */
#define IFX_CAN_N_ECNT_LETD_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_ECNT_Bits.LETD */
#define IFX_CAN_N_ECNT_LETD_OFF (24u)

/** \brief  Length for Ifx_CAN_N_ECNT_Bits.REC */
#define IFX_CAN_N_ECNT_REC_LEN (8u)

/** \brief  Mask for Ifx_CAN_N_ECNT_Bits.REC */
#define IFX_CAN_N_ECNT_REC_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_N_ECNT_Bits.REC */
#define IFX_CAN_N_ECNT_REC_OFF (0u)

/** \brief  Length for Ifx_CAN_N_ECNT_Bits.TEC */
#define IFX_CAN_N_ECNT_TEC_LEN (8u)

/** \brief  Mask for Ifx_CAN_N_ECNT_Bits.TEC */
#define IFX_CAN_N_ECNT_TEC_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_N_ECNT_Bits.TEC */
#define IFX_CAN_N_ECNT_TEC_OFF (8u)

/** \brief  Length for Ifx_CAN_N_FBTR_Bits.FBRP */
#define IFX_CAN_N_FBTR_FBRP_LEN (6u)

/** \brief  Mask for Ifx_CAN_N_FBTR_Bits.FBRP */
#define IFX_CAN_N_FBTR_FBRP_MSK (0x3fu)

/** \brief  Offset for Ifx_CAN_N_FBTR_Bits.FBRP */
#define IFX_CAN_N_FBTR_FBRP_OFF (0u)

/** \brief  Length for Ifx_CAN_N_FBTR_Bits.FSJW */
#define IFX_CAN_N_FBTR_FSJW_LEN (2u)

/** \brief  Mask for Ifx_CAN_N_FBTR_Bits.FSJW */
#define IFX_CAN_N_FBTR_FSJW_MSK (0x3u)

/** \brief  Offset for Ifx_CAN_N_FBTR_Bits.FSJW */
#define IFX_CAN_N_FBTR_FSJW_OFF (6u)

/** \brief  Length for Ifx_CAN_N_FBTR_Bits.FTSEG1 */
#define IFX_CAN_N_FBTR_FTSEG1_LEN (4u)

/** \brief  Mask for Ifx_CAN_N_FBTR_Bits.FTSEG1 */
#define IFX_CAN_N_FBTR_FTSEG1_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_N_FBTR_Bits.FTSEG1 */
#define IFX_CAN_N_FBTR_FTSEG1_OFF (8u)

/** \brief  Length for Ifx_CAN_N_FBTR_Bits.FTSEG2 */
#define IFX_CAN_N_FBTR_FTSEG2_LEN (3u)

/** \brief  Mask for Ifx_CAN_N_FBTR_Bits.FTSEG2 */
#define IFX_CAN_N_FBTR_FTSEG2_MSK (0x7u)

/** \brief  Offset for Ifx_CAN_N_FBTR_Bits.FTSEG2 */
#define IFX_CAN_N_FBTR_FTSEG2_OFF (12u)

/** \brief  Length for Ifx_CAN_N_FCR_Bits.CFC */
#define IFX_CAN_N_FCR_CFC_LEN (16u)

/** \brief  Mask for Ifx_CAN_N_FCR_Bits.CFC */
#define IFX_CAN_N_FCR_CFC_MSK (0xffffu)

/** \brief  Offset for Ifx_CAN_N_FCR_Bits.CFC */
#define IFX_CAN_N_FCR_CFC_OFF (0u)

/** \brief  Length for Ifx_CAN_N_FCR_Bits.CFCIE */
#define IFX_CAN_N_FCR_CFCIE_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_FCR_Bits.CFCIE */
#define IFX_CAN_N_FCR_CFCIE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_FCR_Bits.CFCIE */
#define IFX_CAN_N_FCR_CFCIE_OFF (22u)

/** \brief  Length for Ifx_CAN_N_FCR_Bits.CFCOV */
#define IFX_CAN_N_FCR_CFCOV_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_FCR_Bits.CFCOV */
#define IFX_CAN_N_FCR_CFCOV_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_FCR_Bits.CFCOV */
#define IFX_CAN_N_FCR_CFCOV_OFF (23u)

/** \brief  Length for Ifx_CAN_N_FCR_Bits.CFMOD */
#define IFX_CAN_N_FCR_CFMOD_LEN (2u)

/** \brief  Mask for Ifx_CAN_N_FCR_Bits.CFMOD */
#define IFX_CAN_N_FCR_CFMOD_MSK (0x3u)

/** \brief  Offset for Ifx_CAN_N_FCR_Bits.CFMOD */
#define IFX_CAN_N_FCR_CFMOD_OFF (19u)

/** \brief  Length for Ifx_CAN_N_FCR_Bits.CFSEL */
#define IFX_CAN_N_FCR_CFSEL_LEN (3u)

/** \brief  Mask for Ifx_CAN_N_FCR_Bits.CFSEL */
#define IFX_CAN_N_FCR_CFSEL_MSK (0x7u)

/** \brief  Offset for Ifx_CAN_N_FCR_Bits.CFSEL */
#define IFX_CAN_N_FCR_CFSEL_OFF (16u)

/** \brief  Length for Ifx_CAN_N_IPR_Bits.ALINP */
#define IFX_CAN_N_IPR_ALINP_LEN (4u)

/** \brief  Mask for Ifx_CAN_N_IPR_Bits.ALINP */
#define IFX_CAN_N_IPR_ALINP_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_N_IPR_Bits.ALINP */
#define IFX_CAN_N_IPR_ALINP_OFF (0u)

/** \brief  Length for Ifx_CAN_N_IPR_Bits.CFCINP */
#define IFX_CAN_N_IPR_CFCINP_LEN (4u)

/** \brief  Mask for Ifx_CAN_N_IPR_Bits.CFCINP */
#define IFX_CAN_N_IPR_CFCINP_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_N_IPR_Bits.CFCINP */
#define IFX_CAN_N_IPR_CFCINP_OFF (12u)

/** \brief  Length for Ifx_CAN_N_IPR_Bits.LECINP */
#define IFX_CAN_N_IPR_LECINP_LEN (4u)

/** \brief  Mask for Ifx_CAN_N_IPR_Bits.LECINP */
#define IFX_CAN_N_IPR_LECINP_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_N_IPR_Bits.LECINP */
#define IFX_CAN_N_IPR_LECINP_OFF (4u)

/** \brief  Length for Ifx_CAN_N_IPR_Bits.TEINP */
#define IFX_CAN_N_IPR_TEINP_LEN (4u)

/** \brief  Mask for Ifx_CAN_N_IPR_Bits.TEINP */
#define IFX_CAN_N_IPR_TEINP_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_N_IPR_Bits.TEINP */
#define IFX_CAN_N_IPR_TEINP_OFF (16u)

/** \brief  Length for Ifx_CAN_N_IPR_Bits.TRINP */
#define IFX_CAN_N_IPR_TRINP_LEN (4u)

/** \brief  Mask for Ifx_CAN_N_IPR_Bits.TRINP */
#define IFX_CAN_N_IPR_TRINP_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_N_IPR_Bits.TRINP */
#define IFX_CAN_N_IPR_TRINP_OFF (8u)

/** \brief  Length for Ifx_CAN_N_PCR_Bits.LBM */
#define IFX_CAN_N_PCR_LBM_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_PCR_Bits.LBM */
#define IFX_CAN_N_PCR_LBM_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_PCR_Bits.LBM */
#define IFX_CAN_N_PCR_LBM_OFF (8u)

/** \brief  Length for Ifx_CAN_N_PCR_Bits.RXSEL */
#define IFX_CAN_N_PCR_RXSEL_LEN (3u)

/** \brief  Mask for Ifx_CAN_N_PCR_Bits.RXSEL */
#define IFX_CAN_N_PCR_RXSEL_MSK (0x7u)

/** \brief  Offset for Ifx_CAN_N_PCR_Bits.RXSEL */
#define IFX_CAN_N_PCR_RXSEL_OFF (0u)

/** \brief  Length for Ifx_CAN_N_SR_Bits.ALERT */
#define IFX_CAN_N_SR_ALERT_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_SR_Bits.ALERT */
#define IFX_CAN_N_SR_ALERT_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_SR_Bits.ALERT */
#define IFX_CAN_N_SR_ALERT_OFF (5u)

/** \brief  Length for Ifx_CAN_N_SR_Bits.BOFF */
#define IFX_CAN_N_SR_BOFF_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_SR_Bits.BOFF */
#define IFX_CAN_N_SR_BOFF_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_SR_Bits.BOFF */
#define IFX_CAN_N_SR_BOFF_OFF (7u)

/** \brief  Length for Ifx_CAN_N_SR_Bits.EWRN */
#define IFX_CAN_N_SR_EWRN_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_SR_Bits.EWRN */
#define IFX_CAN_N_SR_EWRN_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_SR_Bits.EWRN */
#define IFX_CAN_N_SR_EWRN_OFF (6u)

/** \brief  Length for Ifx_CAN_N_SR_Bits.FLEC */
#define IFX_CAN_N_SR_FLEC_LEN (3u)

/** \brief  Mask for Ifx_CAN_N_SR_Bits.FLEC */
#define IFX_CAN_N_SR_FLEC_MSK (0x7u)

/** \brief  Offset for Ifx_CAN_N_SR_Bits.FLEC */
#define IFX_CAN_N_SR_FLEC_OFF (12u)

/** \brief  Length for Ifx_CAN_N_SR_Bits.LEC */
#define IFX_CAN_N_SR_LEC_LEN (3u)

/** \brief  Mask for Ifx_CAN_N_SR_Bits.LEC */
#define IFX_CAN_N_SR_LEC_MSK (0x7u)

/** \brief  Offset for Ifx_CAN_N_SR_Bits.LEC */
#define IFX_CAN_N_SR_LEC_OFF (0u)

/** \brief  Length for Ifx_CAN_N_SR_Bits.LLE */
#define IFX_CAN_N_SR_LLE_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_SR_Bits.LLE */
#define IFX_CAN_N_SR_LLE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_SR_Bits.LLE */
#define IFX_CAN_N_SR_LLE_OFF (8u)

/** \brief  Length for Ifx_CAN_N_SR_Bits.LOE */
#define IFX_CAN_N_SR_LOE_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_SR_Bits.LOE */
#define IFX_CAN_N_SR_LOE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_SR_Bits.LOE */
#define IFX_CAN_N_SR_LOE_OFF (9u)

/** \brief  Length for Ifx_CAN_N_SR_Bits.RESI */
#define IFX_CAN_N_SR_RESI_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_SR_Bits.RESI */
#define IFX_CAN_N_SR_RESI_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_SR_Bits.RESI */
#define IFX_CAN_N_SR_RESI_OFF (11u)

/** \brief  Length for Ifx_CAN_N_SR_Bits.RXOK */
#define IFX_CAN_N_SR_RXOK_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_SR_Bits.RXOK */
#define IFX_CAN_N_SR_RXOK_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_SR_Bits.RXOK */
#define IFX_CAN_N_SR_RXOK_OFF (4u)

/** \brief  Length for Ifx_CAN_N_SR_Bits.SUSACK */
#define IFX_CAN_N_SR_SUSACK_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_SR_Bits.SUSACK */
#define IFX_CAN_N_SR_SUSACK_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_SR_Bits.SUSACK */
#define IFX_CAN_N_SR_SUSACK_OFF (10u)

/** \brief  Length for Ifx_CAN_N_SR_Bits.TXOK */
#define IFX_CAN_N_SR_TXOK_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_SR_Bits.TXOK */
#define IFX_CAN_N_SR_TXOK_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_SR_Bits.TXOK */
#define IFX_CAN_N_SR_TXOK_OFF (3u)

/** \brief  Length for Ifx_CAN_N_TCCR_Bits.TPSC */
#define IFX_CAN_N_TCCR_TPSC_LEN (4u)

/** \brief  Mask for Ifx_CAN_N_TCCR_Bits.TPSC */
#define IFX_CAN_N_TCCR_TPSC_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_N_TCCR_Bits.TPSC */
#define IFX_CAN_N_TCCR_TPSC_OFF (8u)

/** \brief  Length for Ifx_CAN_N_TCCR_Bits.TRIGSRC */
#define IFX_CAN_N_TCCR_TRIGSRC_LEN (3u)

/** \brief  Mask for Ifx_CAN_N_TCCR_Bits.TRIGSRC */
#define IFX_CAN_N_TCCR_TRIGSRC_MSK (0x7u)

/** \brief  Offset for Ifx_CAN_N_TCCR_Bits.TRIGSRC */
#define IFX_CAN_N_TCCR_TRIGSRC_OFF (18u)

/** \brief  Length for Ifx_CAN_N_TDCR_Bits.TDC */
#define IFX_CAN_N_TDCR_TDC_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_TDCR_Bits.TDC */
#define IFX_CAN_N_TDCR_TDC_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_TDCR_Bits.TDC */
#define IFX_CAN_N_TDCR_TDC_OFF (15u)

/** \brief  Length for Ifx_CAN_N_TDCR_Bits.TDCO */
#define IFX_CAN_N_TDCR_TDCO_LEN (4u)

/** \brief  Mask for Ifx_CAN_N_TDCR_Bits.TDCO */
#define IFX_CAN_N_TDCR_TDCO_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_N_TDCR_Bits.TDCO */
#define IFX_CAN_N_TDCR_TDCO_OFF (8u)

/** \brief  Length for Ifx_CAN_N_TDCR_Bits.TDCV */
#define IFX_CAN_N_TDCR_TDCV_LEN (5u)

/** \brief  Mask for Ifx_CAN_N_TDCR_Bits.TDCV */
#define IFX_CAN_N_TDCR_TDCV_MSK (0x1fu)

/** \brief  Offset for Ifx_CAN_N_TDCR_Bits.TDCV */
#define IFX_CAN_N_TDCR_TDCV_OFF (0u)

/** \brief  Length for Ifx_CAN_N_TRTR_Bits.RELOAD */
#define IFX_CAN_N_TRTR_RELOAD_LEN (16u)

/** \brief  Mask for Ifx_CAN_N_TRTR_Bits.RELOAD */
#define IFX_CAN_N_TRTR_RELOAD_MSK (0xffffu)

/** \brief  Offset for Ifx_CAN_N_TRTR_Bits.RELOAD */
#define IFX_CAN_N_TRTR_RELOAD_OFF (0u)

/** \brief  Length for Ifx_CAN_N_TRTR_Bits.TE */
#define IFX_CAN_N_TRTR_TE_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_TRTR_Bits.TE */
#define IFX_CAN_N_TRTR_TE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_TRTR_Bits.TE */
#define IFX_CAN_N_TRTR_TE_OFF (23u)

/** \brief  Length for Ifx_CAN_N_TRTR_Bits.TEIE */
#define IFX_CAN_N_TRTR_TEIE_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_TRTR_Bits.TEIE */
#define IFX_CAN_N_TRTR_TEIE_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_TRTR_Bits.TEIE */
#define IFX_CAN_N_TRTR_TEIE_OFF (22u)

/** \brief  Length for Ifx_CAN_N_TTTR_Bits.RELOAD */
#define IFX_CAN_N_TTTR_RELOAD_LEN (16u)

/** \brief  Mask for Ifx_CAN_N_TTTR_Bits.RELOAD */
#define IFX_CAN_N_TTTR_RELOAD_MSK (0xffffu)

/** \brief  Offset for Ifx_CAN_N_TTTR_Bits.RELOAD */
#define IFX_CAN_N_TTTR_RELOAD_OFF (0u)

/** \brief  Length for Ifx_CAN_N_TTTR_Bits.STRT */
#define IFX_CAN_N_TTTR_STRT_LEN (1u)

/** \brief  Mask for Ifx_CAN_N_TTTR_Bits.STRT */
#define IFX_CAN_N_TTTR_STRT_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_N_TTTR_Bits.STRT */
#define IFX_CAN_N_TTTR_STRT_OFF (24u)

/** \brief  Length for Ifx_CAN_N_TTTR_Bits.TXMO */
#define IFX_CAN_N_TTTR_TXMO_LEN (8u)

/** \brief  Mask for Ifx_CAN_N_TTTR_Bits.TXMO */
#define IFX_CAN_N_TTTR_TXMO_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_N_TTTR_Bits.TXMO */
#define IFX_CAN_N_TTTR_TXMO_OFF (16u)

/** \brief  Length for Ifx_CAN_OCS_Bits.SUS */
#define IFX_CAN_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_CAN_OCS_Bits.SUS */
#define IFX_CAN_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_CAN_OCS_Bits.SUS */
#define IFX_CAN_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_CAN_OCS_Bits.SUS_P */
#define IFX_CAN_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_CAN_OCS_Bits.SUS_P */
#define IFX_CAN_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_OCS_Bits.SUS_P */
#define IFX_CAN_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_CAN_OCS_Bits.SUSSTA */
#define IFX_CAN_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_CAN_OCS_Bits.SUSSTA */
#define IFX_CAN_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_OCS_Bits.SUSSTA */
#define IFX_CAN_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_CAN_OCS_Bits.TG_P */
#define IFX_CAN_OCS_TG_P_LEN (1u)

/** \brief  Mask for Ifx_CAN_OCS_Bits.TG_P */
#define IFX_CAN_OCS_TG_P_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_OCS_Bits.TG_P */
#define IFX_CAN_OCS_TG_P_OFF (3u)

/** \brief  Length for Ifx_CAN_OCS_Bits.TGB */
#define IFX_CAN_OCS_TGB_LEN (1u)

/** \brief  Mask for Ifx_CAN_OCS_Bits.TGB */
#define IFX_CAN_OCS_TGB_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_OCS_Bits.TGB */
#define IFX_CAN_OCS_TGB_OFF (2u)

/** \brief  Length for Ifx_CAN_OCS_Bits.TGS */
#define IFX_CAN_OCS_TGS_LEN (2u)

/** \brief  Mask for Ifx_CAN_OCS_Bits.TGS */
#define IFX_CAN_OCS_TGS_MSK (0x3u)

/** \brief  Offset for Ifx_CAN_OCS_Bits.TGS */
#define IFX_CAN_OCS_TGS_OFF (0u)

/** \brief  Length for Ifx_CAN_PANCTR_Bits.BUSY */
#define IFX_CAN_PANCTR_BUSY_LEN (1u)

/** \brief  Mask for Ifx_CAN_PANCTR_Bits.BUSY */
#define IFX_CAN_PANCTR_BUSY_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_PANCTR_Bits.BUSY */
#define IFX_CAN_PANCTR_BUSY_OFF (8u)

/** \brief  Length for Ifx_CAN_PANCTR_Bits.PANAR1 */
#define IFX_CAN_PANCTR_PANAR1_LEN (8u)

/** \brief  Mask for Ifx_CAN_PANCTR_Bits.PANAR1 */
#define IFX_CAN_PANCTR_PANAR1_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_PANCTR_Bits.PANAR1 */
#define IFX_CAN_PANCTR_PANAR1_OFF (16u)

/** \brief  Length for Ifx_CAN_PANCTR_Bits.PANAR2 */
#define IFX_CAN_PANCTR_PANAR2_LEN (8u)

/** \brief  Mask for Ifx_CAN_PANCTR_Bits.PANAR2 */
#define IFX_CAN_PANCTR_PANAR2_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_PANCTR_Bits.PANAR2 */
#define IFX_CAN_PANCTR_PANAR2_OFF (24u)

/** \brief  Length for Ifx_CAN_PANCTR_Bits.PANCMD */
#define IFX_CAN_PANCTR_PANCMD_LEN (8u)

/** \brief  Mask for Ifx_CAN_PANCTR_Bits.PANCMD */
#define IFX_CAN_PANCTR_PANCMD_MSK (0xffu)

/** \brief  Offset for Ifx_CAN_PANCTR_Bits.PANCMD */
#define IFX_CAN_PANCTR_PANCMD_OFF (0u)

/** \brief  Length for Ifx_CAN_PANCTR_Bits.RBUSY */
#define IFX_CAN_PANCTR_RBUSY_LEN (1u)

/** \brief  Mask for Ifx_CAN_PANCTR_Bits.RBUSY */
#define IFX_CAN_PANCTR_RBUSY_MSK (0x1u)

/** \brief  Offset for Ifx_CAN_PANCTR_Bits.RBUSY */
#define IFX_CAN_PANCTR_RBUSY_OFF (9u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXCAN_BF_H */
