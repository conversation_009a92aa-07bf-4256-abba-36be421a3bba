///*============================================================================*/
///*  Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
// *
// *  All rights reserved. This software is iSOFT property. Duplication
// *  or disclosure without iSOFT written authorization is prohibited.
// *
// *  @file       <Mcu.c>
// *  @brief      <This is Mcu C file>
// *
// *  <Compiler: TASKING3.5    MCU:TC1782>
// *
// *  <AUTHOR>
// *  @date       <2014-5-30>
// */
///*============================================================================*/
//
///*=======[R E V I S I O N   H I S T O R Y]====================================*/
///*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
// *  V1.0.0       20140407   jjq        Initial version
// *  V1.0.1       20140530   jianan.liu fix it for bootloader
// */
///*============================================================================*/
//
///*=======[I N C L U D E S]====================================================*/
//#include "Mcu.h"
//#include "wdg.h"
//#include "mcal_src/IfxScu_reg.h"
//#include "mcal_src/IfxScu_bf.h"
//#include "mcal_src/IfxCpu_reg.h"
//#include "mcal_src/IfxCpu_bf.h"
//
//
//
//
//#define EXTCLK		(20000000)	/* external oscillator clock (20MHz) */
//
//static unsigned long system_GetIntClock(void);
//static unsigned long system_GetPllClock(void);
///*=======[M A C R O S]========================================================*/
//#define SCU_RSTCON   (*((volatile uint32*)0xf0036058))
///* 200/100 MHz @ 20MHz ext. clock */
//const TPllInitValue g_PllInitValue_200_100 =
//{
//	/* OSCCON,	PLLCON0,	PLLCON1,	CCUCON0,	CCUCON1,	CCUCON2 */
//	/* 0x000F0118, 0x01017600, 0x00020002, 0x52250101, 0x50012211, 0x40000202 */
//	/* OSCCON,	PLLCON0,	PLLCON1,	CCUCON0,	CCUCON1,	CCUCON2 */
//	0x00070118, 0x00013A00, 0x00000502, 0x02220122U, 0x58212215U, 0x00000002U
//};
//static Ifx_SCU * const pSCU = (Ifx_SCU *)&MODULE_SCU;
//
///*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/
//unsigned long SYSTEM_GetStmClock(void)
//{
//	unsigned long frequency = system_GetIntClock();
//	unsigned long divider = pSCU->CCUCON1.B.STMDIV;
//	if (0 == divider)
//		return 0;
//	return (frequency / divider);
//}
//static unsigned long system_GetIntClock(void)
//{
//	unsigned long frequency = 0;
//	switch (pSCU->CCUCON0.B.CLKSEL)
//	{
//		default:
//		case 0:  /* back-up clock (typ. 100 MHz) */
//			frequency = 100000000ul;
//			break;
//		case 1:	 /* fPLL */
//			frequency = system_GetPllClock();
//			break;
//	}
//	return frequency;
//}
//static unsigned long system_GetPllClock(void)
//{
//	unsigned int frequency = EXTCLK;	/* fOSC */
//
//	Ifx_SCU_PLLSTAT pllstat = pSCU->PLLSTAT;
//	Ifx_SCU_PLLCON0 pllcon0 = pSCU->PLLCON0;
//	Ifx_SCU_PLLCON1 pllcon1 = pSCU->PLLCON1;
//
//	if (0 == (pllstat.B.VCOBYST))
//	{
//		if (0 == (pllstat.B.FINDIS))
//		{
//			/* normal mode */
//			frequency *= (pllcon0.B.NDIV + 1);		/* fOSC*N */
//			frequency /= (pllcon0.B.PDIV + 1);		/* .../P  */
//			frequency /= (pllcon1.B.K2DIV + 1);		/* .../K2 */
//		}
//		else	/* freerunning mode */
//		{
//			frequency = 800000000;		/* fVCOBASE 800 MHz (???) */
//			frequency /= (pllcon1.B.K2DIV + 1);		/* .../K2 */
//		}
//	}
//	else	/* prescaler mode */
//	{
//		frequency /= (pllcon1.B.K1DIV + 1);		/* fOSC/K1 */
//	}
//
//	return (unsigned long)frequency;
//}
//
///******************************************************************************/
///*
// * Brief               <Mcu_Init>
// * Sync/Async          <Synchronous>
// * Reentrancy          <Non-Reentrant>
// * Param-Name[in]      <ConfigPtr>
// * Param-Name[out]     <None>
// * Param-Name[in/out]  <None>
// * Return              <None>
// * PreCondition        <None>
// * CallByAPI           <APIName>
// */
///******************************************************************************/
//void Mcu_Init(void)
//{
//	const PPllInitValue pPllInitValue = &g_PllInitValue_200_100;
//
//	unlock_safety_wdtcon();
//
//	pSCU->OSCCON.U = pPllInitValue->uiOSCCON;//0x000F0118
//
//	while (pSCU->CCUCON1.B.LCK);
//	pSCU->CCUCON1.U = pPllInitValue->uiCCUCON1 | (1 << IFX_SCU_CCUCON1_UP_OFF);
//
//	pSCU->PLLCON0.U = pPllInitValue->uiPLLCON0 | (1 << IFX_SCU_PLLCON0_SETFINDIS_OFF);
//
//	pSCU->PLLCON1.U = pPllInitValue->uiPLLCON1;	//0x00020002			/* set K1,K2 divider */    //	K2 =2 k3=0 k1= 2
//	pSCU->PLLCON0.U = pPllInitValue->uiPLLCON0 | (1 << IFX_SCU_PLLCON0_CLRFINDIS_OFF);
//
//	lock_safety_wdtcon();
//
//	while (0 == pSCU->PLLSTAT.B.VCOLOCK);
//
//	unlock_safety_wdtcon();
//
//	pSCU->CCUCON0.U = pPllInitValue->uiCCUCON0;
//	pSCU->CCUCON1.U = pPllInitValue->uiCCUCON1;
//	pSCU->CCUCON1.U = pPllInitValue->uiCCUCON1 | (1 << IFX_SCU_CCUCON1_UP_OFF);//0x50012211  Fcan=Fsource  Fstm=Fsource/2 Fgtm=Fsource/2 OSC is clock source for PLL
//	while (pSCU->CCUCON1.B.LCK);
//
//	pSCU->CCUCON2.U = pPllInitValue->uiCCUCON2;
//	pSCU->CCUCON2.U = pPllInitValue->uiCCUCON2 | (1 << IFX_SCU_CCUCON2_UP_OFF);//0x40000202 Fbbb
//	while (pSCU->CCUCON2.B.LCK);
//
//	pSCU->CCUCON0.U = pPllInitValue->uiCCUCON0 | 0x10000000u | (1 << IFX_SCU_CCUCON0_UP_OFF);//0x52250101    1010010001001010000000100000001  pll is used as clock Fsource
//	while (pSCU->CCUCON0.B.LCK);
//
//	lock_safety_wdtcon();
//
//	unlock_safety_wdtcon();
//	SCU_RSTCON = 0x00000140;
//	lock_safety_wdtcon();
//}
//
///******************************************************************************/
///*
// * Brief               <Mcu_Deinit>
// * Sync/Async          <Synchronous>
// * Reentrancy          <Non-Reentrant>
// * Param-Name[in]      <ConfigPtr>
// * Param-Name[out]     <None>
// * Param-Name[in/out]  <None>
// * Return              <None>
// * PreCondition        <None>
// * CallByAPI           <APIName>
// */
///******************************************************************************/
////void Mcu_Deinit(void)
////{
////
////}
///*=======[E N D   O F   F I L E]==============================================*/
//
