/*******************************************************************************
 * Copyright (C) 2023 Technomous. All rights reserved         *
 ******************************************************************************/
/**
 *  \file
 *      Secure.h
 *  \brief
 *      The secure boot.
 *	\author
 *		Ou Hengyue
 */

#ifndef SECURE_H
#define SECURE_H
/*****************************************************************************
*   Include Files
*****************************************************************************/
#include "Secure_Types.h"
/*****************************************************************************
*   Global Define Definitions
*****************************************************************************/


/*****************************************************************************
*   Global Type Definitions
*****************************************************************************/
typedef enum {
    SECURE_VERIFICATION_STATE = 0,
    SECURE_DOWNLOAD_STATE,
    SECURE_CHECKING_STATE
} Secure_StateTypes;

/*****************************************************************************
*   Global Macros Definitions
*****************************************************************************/

/*****************************************************************************
*   Global Data Declarations
*****************************************************************************/
extern Secure_SignHeaderType SecureSignHeader;
/*****************************************************************************
*   Global Function Declarations
*****************************************************************************/
void Secure_Init(void);
Std_ReturnType Secure_BypassChk(void);
Std_ReturnType Secure_ChkByPassFlag(void);
Std_ReturnType Secure_SaveSignHeader(uint8 *data, uint16 length, uint8 headerType);
uint8 Secure_GetDownloadIdx(void);
Std_ReturnType Secure_SignHeaderChk(uint8* fileIdx);
Std_ReturnType Secure_CheckSumForHeader(uint16 * crcValPtr);
Std_ReturnType Secure_HashVerification(uint32 address, uint32 length, uint8 index);
Std_ReturnType Secure_RootPublickeyHashVerification(uint32 address, uint32 length);
#endif/* endif SECURE_H */