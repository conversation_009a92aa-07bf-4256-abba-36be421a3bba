/*
 * Dem_Data.h
 *
 *  Created on: 2021-1-11
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#ifndef DEM_DATA_H_
#define DEM_DATA_H_
#include "Std_Types.h"
typedef struct Dem_Cfg_AdminDataType_s {
  uint16 ImplementationVersion;
  uint16 ConfigurationId;
  uint16 CycleCounter[ 1 /*DEM_CFG_GLOBAL_CYCLE_COUNT*/];
  uint8  MemoryOverflow;                                                        /* (DEM_CFG_DATA_OVFLIND == STD_ON) || (DEM_CFG_SUPPORT_OVFLIND_API == STD_ON) */
}                                                                     Dem_Cfg_AdminDataType;


typedef struct Dem_Cfg_StatusDataType_s {
  uint16         FirstFailedEvent;
  uint16         FirstConfirmedEvent;
  uint16         MostRecentFailedEvent;
  uint16         MostRecentConfmdEvent;
  uint8          TripCount[ 31 /*DEM_G_NUMBER_OF_EVENTS (incl. #0)*/];          /* (DEM_CFG_SUPPORT_MULTITRIP == STD_ON) || use HealingCycleCounter */
  volatile uint8 EventStatus[ 31 /*DEM_G_NUMBER_OF_EVENTS (incl. #0)*/];
}                                                                     Dem_Cfg_StatusDataType;

typedef struct Dem_Cfg_PrimaryEntryType_s {
  uint32 Timestamp;
  uint16 AgingCounter;
  uint16 EventId;
  uint8  ExtendedData[ 1 /*DEM_CFG_MAX_NUM_EXT_RECORDS*/][ 1 /*DEM_CFG_MAX_SIZE_EXT_RECORD*/];  /* (DEM_CFG_SUPPORT_USER_ERECS == STD_ON) */
  uint8  SnapshotData[ 1 /*DEM_CFG_MAX_NUM_SNAPSHOTS*/][ 55 /*DEM_CFG_MAX_SIZE_SNAPSHOT*/];     /* (DEM_CFG_SUPPORT_SRECS == STD_ON) */
  uint8  ExtendedHeader;                                                                        /* (DEM_CFG_SUPPORT_USER_ERECS == STD_ON) */
  uint8  SnapshotHeader;                                                                        /* (DEM_CFG_SUPPORT_SRECS == STD_ON) */
}                                                                     Dem_Cfg_PrimaryEntryType;


extern Dem_Cfg_AdminDataType Dem_Cfg_AdminData;
extern Dem_Cfg_StatusDataType Dem_Cfg_StatusData;
extern Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_0;
extern Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_1;
extern Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_2;
extern Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_3;
extern Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_4;
extern Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_5;
extern Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_6;
extern Dem_Cfg_PrimaryEntryType Dem_Cfg_PrimaryEntry_7;

extern const Dem_Cfg_PrimaryEntryType Dem_Cfg_MemoryEntryInit;
extern void Dem_NvM_InitAdminData(void);
extern void Dem_NvM_JobFinished(void);
extern void Dem_NvM_InitStatusData(void);
#endif /* DEM_DATA_H_ */
