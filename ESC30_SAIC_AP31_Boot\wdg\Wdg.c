    /*============================================================================*/
/*  Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *
 *  @file       <Wdg.C>
 *  @brief      <This is Wdg C file>
 *
 *  <Compiler: TASKING3.5    MCU:TC1782>
 *
 *  <AUTHOR>
 *  @date       <2014-5-30>
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>      <REVISION LOG>
 *  V1.0.0       20140530   jianan.liu   Initial version
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/
#include "Wdg.h"
#include "Mcu.h"
//#include <machine/intrinsics.h>
//#include <machine/cint.h>
#include "IfxScu_reg.h"
#include "IfxScu_bf.h"
#include "IfxCpu_reg.h"
#include "IfxCpu_bf.h"
#include "wdtcon.h"
//#include "Can.h"
#include "Mcal_Compiler.h"

//extern void unlock_wdtcon (void);

#define WDG0_BASE                          (Ifx_SCU_WDTCPU *)0xF0036100u

/** Input frequency request control */
typedef enum
{
    IfxScu_WDTCON1_IR_divBy16384 = 0,
    IfxScu_WDTCON1_IR_divBy256   = 1,
    IfxScu_WDTCON1_IR_divBy64    = 2
} IfxScu_WDTCON1_IR;

typedef struct
{
    uint16            password;                      /**< \brief password for access to WDTxCON0 reg */
    uint16            reload;                        /**< \brief WDT reload value */
    IfxScu_WDTCON1_IR inputFrequency;                /**< \brief input frequency of the WDT */
    boolean           disableWatchdog;               /**< \brief Disable Request Control Bit */
    boolean           enableSmuRestriction;          /**< \brief Unlock Restriction Request Control Bit */
    boolean           enableAutomaticPasswordChange; /**< \brief Password Auto-sequence Request Bit */
    boolean           enableTimerCheck;              /**< \brief Counter Check Request Bit */
    boolean           enableTimerCheckTolerance;     /**< \brief Timer Check Tolerance Request */
    boolean           clrInternalResetFlag;          /**< \brief Clear Internal Reset Flag */
} IfxScuWdt_Config;

IfxScuWdt_Config Wdg_Cfg;

/*=======[M A C R O S]========================================================*/

/*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/
/**
 * \brief Reset password of watchdog module.
 */


uint8 IfxCpu_getCoreId(void)
{
    Ifx_CPU_CORE_ID reg;
    reg.U = __mfcr(CPU_CORE_ID);
    return (uint8)reg.B.CORE_ID;
}



static Ifx_SCU_WDTCPU *GetWdgBase(void)
{
    switch (MFCR(CPU_CORE_ID) & IFX_CPU_CORE_ID_CORE_ID_MSK)
    {
        default:
        case 0: return WDG0_BASE;
        //case 1: return STM1_BASE;
        //case 2: return STM2_BASE;
    }
}

/******************************************************************************/
/*                           Function prototypes                              */
/******************************************************************************/
void IfxScuWdt_initConfig(IfxScuWdt_Config *p_cfg)
{
    /*Fwdg = (200/5)M  timeout = 1.6s*/
    p_cfg->password                      = IFXSCUWDT_RESET_PASSWORD;
    p_cfg->reload                        = 0xFB00;
    p_cfg->inputFrequency                = IfxScu_WDTCON1_IR_divBy16384;
    p_cfg->disableWatchdog               = FALSE;
    p_cfg->enableSmuRestriction          = FALSE;
    p_cfg->enableAutomaticPasswordChange = FALSE;
    p_cfg->enableTimerCheck              = FALSE;
    p_cfg->enableTimerCheckTolerance     = FALSE;
    p_cfg->clrInternalResetFlag          = FALSE;
}


void IfxScuWdt_initCpuWatchdog(Ifx_SCU_WDTCPU *wdt, IfxScuWdt_Config *p_cfg)
{
    Ifx_SCU_WDTCPU_CON0 wdt_con0;
    Ifx_SCU_WDTCPU_CON1 wdt_con1;

    /* Read Config_0 register and clear wdt_con1 variable */
    wdt_con0.U = wdt->CON0.U;
    wdt_con1.U = 0;

    if (wdt_con0.B.LCK)
    {
        /* see Table 1 (Password Access Bit Pattern Requirements) */
        wdt_con0.B.ENDINIT = 1;
        wdt_con0.B.LCK     = 0;
        wdt_con0.B.PW     ^= 0x003F;

        /* Password ready. Store it to WDT_CON0 to unprotect the register */
        wdt->CON0.U = wdt_con0.U;
    }

    /* Initialize CON0 register, with modify access, with user defined parameters
     * Clear ENDINT bit to unprotect CON1 register for initialization
     * see Table 3 (Modify Access Bit Pattern Requirements) */
    wdt_con0.B.ENDINIT = 0;
    wdt_con0.B.LCK     = 1;
    wdt_con0.B.PW      = p_cfg->password; //user defined password
    wdt_con0.B.REL     = p_cfg->reload;   //user defined reload value

    /* Modify access ready - write WDT_CON0 register */
    wdt->CON0.U = wdt_con0.U;

    /* read back ENDINIT and wait until it has been cleared */
    while (wdt->CON0.B.ENDINIT == 1)
    {}

    /* Initialize CON1 register */
    switch (p_cfg->inputFrequency)
    {
    case IfxScu_WDTCON1_IR_divBy16384:
        wdt_con1.B.IR0 = 0;
        wdt_con1.B.IR1 = 0;
        break;
    case IfxScu_WDTCON1_IR_divBy256:
        wdt_con1.B.IR0 = 1;
        wdt_con1.B.IR1 = 0;
        break;
    case IfxScu_WDTCON1_IR_divBy64:
        wdt_con1.B.IR0 = 0;
        wdt_con1.B.IR1 = 1;
        break;
    }

    wdt_con1.B.DR   = p_cfg->disableWatchdog ? 1 : 0;
    wdt_con1.B.UR   = p_cfg->enableSmuRestriction ? 1 : 0;
    wdt_con1.B.PAR  = p_cfg->enableAutomaticPasswordChange ? 1 : 0;
    wdt_con1.B.TCR  = p_cfg->enableTimerCheck ? 1 : 0;
    wdt_con1.B.TCTR = p_cfg->enableTimerCheckTolerance ? 1 : 0;

    /* Finally write CON1 with user defined configuration */
    wdt->CON1.U = wdt_con1.U;

    /* Initialization finished - set CPU ENDINIT protection */
    IfxScuWdt_setCpuEndinit(p_cfg->password);
}

void IfxScuWdt_clearCpuEndinitInline(Ifx_SCU_WDTCPU *watchdog, uint16 password)
{
    /* Read Config_0 register */
    Ifx_SCU_WDTCPU_CON0 wdt_con0;
    wdt_con0.U = watchdog->CON0.U;

    if (wdt_con0.B.LCK)
    {
        /* see Table 1 (Pass.word Access Bit Pattern Requirements) */
        wdt_con0.B.ENDINIT = 1;
        wdt_con0.B.LCK     = 0;
        wdt_con0.B.PW      = password;

        /* Password ready. Store it to WDT_CON0 to unprotect the register */
        watchdog->CON0.U = wdt_con0.U;
    }

    /* Clear ENDINT and set LCK bit in Config_0 register */
    wdt_con0.B.ENDINIT = 0;
    wdt_con0.B.LCK     = 1;
    watchdog->CON0.U   = wdt_con0.U;

    /* read back ENDINIT and wait until it has been cleared */
    while (watchdog->CON0.B.ENDINIT == 1)
    {}
}


/**
 * \brief SCUWDT Inline API to Set ENDINIT bit provided by CPU WDT Hardware module.
 */
void IfxScuWdt_setCpuEndinitInline(Ifx_SCU_WDTCPU *watchdog, uint16 password)
{
    /* Read Config_0 register */
    Ifx_SCU_WDTCPU_CON0 wdt_con0;
    wdt_con0.U = watchdog->CON0.U;

    if (wdt_con0.B.LCK)
    {
        /* see Table 1 (Password Access Bit Pattern Requirements) */
        wdt_con0.B.ENDINIT = 1;
        wdt_con0.B.LCK     = 0;
        wdt_con0.B.PW      = password;

        /* Password ready. Store it to WDT_CON0 to unprotect the register */
        watchdog->CON0.U = wdt_con0.U;
    }

    /* Set ENDINT and set LCK bit in Config_0 register */
    wdt_con0.B.ENDINIT = 1;
    wdt_con0.B.LCK     = 1;
    watchdog->CON0.U   = wdt_con0.U;

    /* read back ENDINIT and wait until it has been set */
    while (watchdog->CON0.B.ENDINIT == 0)
    {}

    // FIXME: old version: removed this line after check: watchdog->CON0.U; /* read is required */
}

void IfxScuWdt_clearCpuEndinit(uint16 password)
{
    IfxScuWdt_clearCpuEndinitInline(&MODULE_SCU.WDTCPU[IfxCpu_getCoreId()], password);
}

void IfxScuWdt_setCpuEndinit(uint16 password)
{
    IfxScuWdt_setCpuEndinitInline(&MODULE_SCU.WDTCPU[IfxCpu_getCoreId()], password);
}


void IfxScuWdt_serviceCpuWatchdog(uint16 password)
{
    IfxScuWdt_setCpuEndinit(password);
}

/******************************************************************************/
/*
 * Brief               <Initializes the WDG driver>
 * Sync/Async          <Synchronous>
 * Reentrancy          <Non-Reentrant>
 * Param-Name[in]      <None>
 * Param-Name[out]     <None>
 * Param-Name[in/out]  <None>
 * Return              <None>
 * PreCondition        <None>
 * CallByAPI           <APIName>
 */
/******************************************************************************/
void Wdg_Start(void)
{
    Ifx_SCU_WDTCPU * wdgAddr = GetWdgBase();
    IfxScuWdt_initConfig(&Wdg_Cfg);
    IfxScuWdt_initCpuWatchdog(wdgAddr, &Wdg_Cfg);
    return;
}

/******************************************************************************/
/*
 * Brief               <Stop the WDG driver>
 * Sync/Async          <Synchronous>
 * Reentrancy          <Non-Reentrant>
 * Param-Name[in]      <None>
 * Param-Name[out]     <None>
 * Param-Name[in/out]  <None>
 * Return              <None>
 * PreCondition        <None>
 * CallByAPI           <APIName>
 */
/******************************************************************************/
void Wdg_Stop(void)
{
    unlock_safety_wdtcon();
    unlock_wdtcon();
    *((volatile uint32 *)0xF0036104) |= 0x00000008u;
    return;
}
/******************************************************************************/
/*
 * Brief               <This Funtion Triggers the Watchdog Hardware>
 * Sync/Async          <Synchronous>
 * Reentrancy          <Non-Reentrant>
 * Param-Name[in]      <None>
 * Param-Name[out]     <None>
 * Param-Name[in/out]  <None>
 * Return              <None>
 * PreCondition        <None>
 * CallByAPI           <APIName>
 */
/******************************************************************************/
void Wdg_Kick(void)
{
    IfxScuWdt_serviceCpuWatchdog(IFXSCUWDT_RESET_PASSWORD);
    return;
}
/******************************************************************************/
/*
 * Brief               <This Funtion resets the Wdg module>
 * Sync/Async          <Synchronous>
 * Reentrancy          <Non-Reentrant>
 * Param-Name[in]      <None>
 * Param-Name[out]     <None>
 * Param-Name[in/out]  <None>
 * Return              <None>
 * PreCondition        <None>
 * CallByAPI           <APIName>
 */
/******************************************************************************/
void Wdg_SystemReset(void)
{
	uint32 counter=0;
    unlock_safety_wdtcon();
    unlock_wdtcon();
    *((volatile uint32 *)0xF0036060) = 0x00000002u;
    lock_safety_wdtcon();
    lock_wdtcon();
    while(1)
    {
    	counter++;
    }
    return;
}

