/**
 * \file IfxInt_bf.h
 * \brief
 * \copyright Copyright (c) 2016 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC23XADAS_UM_V1.1.R1
 * Specification: tc23xadas_um_sfrs_MCSFR.xml (Revision: UM_V1.1)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Int_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Int
 * 
 */
#ifndef IFXINT_BF_H
#define IFXINT_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Int_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN0 */
#define IFX_INT_ACCEN00_EN0_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN0 */
#define IFX_INT_ACCEN00_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN0 */
#define IFX_INT_ACCEN00_EN0_OFF (0u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN10 */
#define IFX_INT_ACCEN00_EN10_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN10 */
#define IFX_INT_ACCEN00_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN10 */
#define IFX_INT_ACCEN00_EN10_OFF (10u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN11 */
#define IFX_INT_ACCEN00_EN11_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN11 */
#define IFX_INT_ACCEN00_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN11 */
#define IFX_INT_ACCEN00_EN11_OFF (11u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN12 */
#define IFX_INT_ACCEN00_EN12_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN12 */
#define IFX_INT_ACCEN00_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN12 */
#define IFX_INT_ACCEN00_EN12_OFF (12u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN13 */
#define IFX_INT_ACCEN00_EN13_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN13 */
#define IFX_INT_ACCEN00_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN13 */
#define IFX_INT_ACCEN00_EN13_OFF (13u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN14 */
#define IFX_INT_ACCEN00_EN14_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN14 */
#define IFX_INT_ACCEN00_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN14 */
#define IFX_INT_ACCEN00_EN14_OFF (14u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN15 */
#define IFX_INT_ACCEN00_EN15_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN15 */
#define IFX_INT_ACCEN00_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN15 */
#define IFX_INT_ACCEN00_EN15_OFF (15u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN16 */
#define IFX_INT_ACCEN00_EN16_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN16 */
#define IFX_INT_ACCEN00_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN16 */
#define IFX_INT_ACCEN00_EN16_OFF (16u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN17 */
#define IFX_INT_ACCEN00_EN17_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN17 */
#define IFX_INT_ACCEN00_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN17 */
#define IFX_INT_ACCEN00_EN17_OFF (17u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN18 */
#define IFX_INT_ACCEN00_EN18_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN18 */
#define IFX_INT_ACCEN00_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN18 */
#define IFX_INT_ACCEN00_EN18_OFF (18u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN19 */
#define IFX_INT_ACCEN00_EN19_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN19 */
#define IFX_INT_ACCEN00_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN19 */
#define IFX_INT_ACCEN00_EN19_OFF (19u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN1 */
#define IFX_INT_ACCEN00_EN1_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN1 */
#define IFX_INT_ACCEN00_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN1 */
#define IFX_INT_ACCEN00_EN1_OFF (1u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN20 */
#define IFX_INT_ACCEN00_EN20_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN20 */
#define IFX_INT_ACCEN00_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN20 */
#define IFX_INT_ACCEN00_EN20_OFF (20u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN21 */
#define IFX_INT_ACCEN00_EN21_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN21 */
#define IFX_INT_ACCEN00_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN21 */
#define IFX_INT_ACCEN00_EN21_OFF (21u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN22 */
#define IFX_INT_ACCEN00_EN22_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN22 */
#define IFX_INT_ACCEN00_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN22 */
#define IFX_INT_ACCEN00_EN22_OFF (22u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN23 */
#define IFX_INT_ACCEN00_EN23_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN23 */
#define IFX_INT_ACCEN00_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN23 */
#define IFX_INT_ACCEN00_EN23_OFF (23u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN24 */
#define IFX_INT_ACCEN00_EN24_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN24 */
#define IFX_INT_ACCEN00_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN24 */
#define IFX_INT_ACCEN00_EN24_OFF (24u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN25 */
#define IFX_INT_ACCEN00_EN25_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN25 */
#define IFX_INT_ACCEN00_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN25 */
#define IFX_INT_ACCEN00_EN25_OFF (25u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN26 */
#define IFX_INT_ACCEN00_EN26_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN26 */
#define IFX_INT_ACCEN00_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN26 */
#define IFX_INT_ACCEN00_EN26_OFF (26u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN27 */
#define IFX_INT_ACCEN00_EN27_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN27 */
#define IFX_INT_ACCEN00_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN27 */
#define IFX_INT_ACCEN00_EN27_OFF (27u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN28 */
#define IFX_INT_ACCEN00_EN28_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN28 */
#define IFX_INT_ACCEN00_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN28 */
#define IFX_INT_ACCEN00_EN28_OFF (28u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN29 */
#define IFX_INT_ACCEN00_EN29_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN29 */
#define IFX_INT_ACCEN00_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN29 */
#define IFX_INT_ACCEN00_EN29_OFF (29u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN2 */
#define IFX_INT_ACCEN00_EN2_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN2 */
#define IFX_INT_ACCEN00_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN2 */
#define IFX_INT_ACCEN00_EN2_OFF (2u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN30 */
#define IFX_INT_ACCEN00_EN30_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN30 */
#define IFX_INT_ACCEN00_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN30 */
#define IFX_INT_ACCEN00_EN30_OFF (30u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN31 */
#define IFX_INT_ACCEN00_EN31_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN31 */
#define IFX_INT_ACCEN00_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN31 */
#define IFX_INT_ACCEN00_EN31_OFF (31u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN3 */
#define IFX_INT_ACCEN00_EN3_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN3 */
#define IFX_INT_ACCEN00_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN3 */
#define IFX_INT_ACCEN00_EN3_OFF (3u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN4 */
#define IFX_INT_ACCEN00_EN4_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN4 */
#define IFX_INT_ACCEN00_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN4 */
#define IFX_INT_ACCEN00_EN4_OFF (4u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN5 */
#define IFX_INT_ACCEN00_EN5_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN5 */
#define IFX_INT_ACCEN00_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN5 */
#define IFX_INT_ACCEN00_EN5_OFF (5u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN6 */
#define IFX_INT_ACCEN00_EN6_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN6 */
#define IFX_INT_ACCEN00_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN6 */
#define IFX_INT_ACCEN00_EN6_OFF (6u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN7 */
#define IFX_INT_ACCEN00_EN7_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN7 */
#define IFX_INT_ACCEN00_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN7 */
#define IFX_INT_ACCEN00_EN7_OFF (7u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN8 */
#define IFX_INT_ACCEN00_EN8_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN8 */
#define IFX_INT_ACCEN00_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN8 */
#define IFX_INT_ACCEN00_EN8_OFF (8u)

/** \brief  Length for Ifx_INT_ACCEN00_Bits.EN9 */
#define IFX_INT_ACCEN00_EN9_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN00_Bits.EN9 */
#define IFX_INT_ACCEN00_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN00_Bits.EN9 */
#define IFX_INT_ACCEN00_EN9_OFF (9u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN0 */
#define IFX_INT_ACCEN10_EN0_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN0 */
#define IFX_INT_ACCEN10_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN0 */
#define IFX_INT_ACCEN10_EN0_OFF (0u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN10 */
#define IFX_INT_ACCEN10_EN10_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN10 */
#define IFX_INT_ACCEN10_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN10 */
#define IFX_INT_ACCEN10_EN10_OFF (10u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN11 */
#define IFX_INT_ACCEN10_EN11_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN11 */
#define IFX_INT_ACCEN10_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN11 */
#define IFX_INT_ACCEN10_EN11_OFF (11u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN12 */
#define IFX_INT_ACCEN10_EN12_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN12 */
#define IFX_INT_ACCEN10_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN12 */
#define IFX_INT_ACCEN10_EN12_OFF (12u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN13 */
#define IFX_INT_ACCEN10_EN13_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN13 */
#define IFX_INT_ACCEN10_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN13 */
#define IFX_INT_ACCEN10_EN13_OFF (13u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN14 */
#define IFX_INT_ACCEN10_EN14_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN14 */
#define IFX_INT_ACCEN10_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN14 */
#define IFX_INT_ACCEN10_EN14_OFF (14u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN15 */
#define IFX_INT_ACCEN10_EN15_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN15 */
#define IFX_INT_ACCEN10_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN15 */
#define IFX_INT_ACCEN10_EN15_OFF (15u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN16 */
#define IFX_INT_ACCEN10_EN16_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN16 */
#define IFX_INT_ACCEN10_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN16 */
#define IFX_INT_ACCEN10_EN16_OFF (16u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN17 */
#define IFX_INT_ACCEN10_EN17_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN17 */
#define IFX_INT_ACCEN10_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN17 */
#define IFX_INT_ACCEN10_EN17_OFF (17u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN18 */
#define IFX_INT_ACCEN10_EN18_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN18 */
#define IFX_INT_ACCEN10_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN18 */
#define IFX_INT_ACCEN10_EN18_OFF (18u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN19 */
#define IFX_INT_ACCEN10_EN19_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN19 */
#define IFX_INT_ACCEN10_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN19 */
#define IFX_INT_ACCEN10_EN19_OFF (19u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN1 */
#define IFX_INT_ACCEN10_EN1_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN1 */
#define IFX_INT_ACCEN10_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN1 */
#define IFX_INT_ACCEN10_EN1_OFF (1u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN20 */
#define IFX_INT_ACCEN10_EN20_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN20 */
#define IFX_INT_ACCEN10_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN20 */
#define IFX_INT_ACCEN10_EN20_OFF (20u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN21 */
#define IFX_INT_ACCEN10_EN21_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN21 */
#define IFX_INT_ACCEN10_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN21 */
#define IFX_INT_ACCEN10_EN21_OFF (21u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN22 */
#define IFX_INT_ACCEN10_EN22_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN22 */
#define IFX_INT_ACCEN10_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN22 */
#define IFX_INT_ACCEN10_EN22_OFF (22u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN23 */
#define IFX_INT_ACCEN10_EN23_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN23 */
#define IFX_INT_ACCEN10_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN23 */
#define IFX_INT_ACCEN10_EN23_OFF (23u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN24 */
#define IFX_INT_ACCEN10_EN24_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN24 */
#define IFX_INT_ACCEN10_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN24 */
#define IFX_INT_ACCEN10_EN24_OFF (24u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN25 */
#define IFX_INT_ACCEN10_EN25_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN25 */
#define IFX_INT_ACCEN10_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN25 */
#define IFX_INT_ACCEN10_EN25_OFF (25u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN26 */
#define IFX_INT_ACCEN10_EN26_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN26 */
#define IFX_INT_ACCEN10_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN26 */
#define IFX_INT_ACCEN10_EN26_OFF (26u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN27 */
#define IFX_INT_ACCEN10_EN27_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN27 */
#define IFX_INT_ACCEN10_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN27 */
#define IFX_INT_ACCEN10_EN27_OFF (27u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN28 */
#define IFX_INT_ACCEN10_EN28_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN28 */
#define IFX_INT_ACCEN10_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN28 */
#define IFX_INT_ACCEN10_EN28_OFF (28u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN29 */
#define IFX_INT_ACCEN10_EN29_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN29 */
#define IFX_INT_ACCEN10_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN29 */
#define IFX_INT_ACCEN10_EN29_OFF (29u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN2 */
#define IFX_INT_ACCEN10_EN2_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN2 */
#define IFX_INT_ACCEN10_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN2 */
#define IFX_INT_ACCEN10_EN2_OFF (2u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN30 */
#define IFX_INT_ACCEN10_EN30_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN30 */
#define IFX_INT_ACCEN10_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN30 */
#define IFX_INT_ACCEN10_EN30_OFF (30u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN31 */
#define IFX_INT_ACCEN10_EN31_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN31 */
#define IFX_INT_ACCEN10_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN31 */
#define IFX_INT_ACCEN10_EN31_OFF (31u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN3 */
#define IFX_INT_ACCEN10_EN3_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN3 */
#define IFX_INT_ACCEN10_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN3 */
#define IFX_INT_ACCEN10_EN3_OFF (3u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN4 */
#define IFX_INT_ACCEN10_EN4_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN4 */
#define IFX_INT_ACCEN10_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN4 */
#define IFX_INT_ACCEN10_EN4_OFF (4u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN5 */
#define IFX_INT_ACCEN10_EN5_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN5 */
#define IFX_INT_ACCEN10_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN5 */
#define IFX_INT_ACCEN10_EN5_OFF (5u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN6 */
#define IFX_INT_ACCEN10_EN6_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN6 */
#define IFX_INT_ACCEN10_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN6 */
#define IFX_INT_ACCEN10_EN6_OFF (6u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN7 */
#define IFX_INT_ACCEN10_EN7_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN7 */
#define IFX_INT_ACCEN10_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN7 */
#define IFX_INT_ACCEN10_EN7_OFF (7u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN8 */
#define IFX_INT_ACCEN10_EN8_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN8 */
#define IFX_INT_ACCEN10_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN8 */
#define IFX_INT_ACCEN10_EN8_OFF (8u)

/** \brief  Length for Ifx_INT_ACCEN10_Bits.EN9 */
#define IFX_INT_ACCEN10_EN9_LEN (1u)

/** \brief  Mask for Ifx_INT_ACCEN10_Bits.EN9 */
#define IFX_INT_ACCEN10_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ACCEN10_Bits.EN9 */
#define IFX_INT_ACCEN10_EN9_OFF (9u)

/** \brief  Length for Ifx_INT_ICU_ECR_Bits.ECC */
#define IFX_INT_ICU_ECR_ECC_LEN (6u)

/** \brief  Mask for Ifx_INT_ICU_ECR_Bits.ECC */
#define IFX_INT_ICU_ECR_ECC_MSK (0x3fu)

/** \brief  Offset for Ifx_INT_ICU_ECR_Bits.ECC */
#define IFX_INT_ICU_ECR_ECC_OFF (10u)

/** \brief  Length for Ifx_INT_ICU_ECR_Bits.EOV */
#define IFX_INT_ICU_ECR_EOV_LEN (1u)

/** \brief  Mask for Ifx_INT_ICU_ECR_Bits.EOV */
#define IFX_INT_ICU_ECR_EOV_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ICU_ECR_Bits.EOV */
#define IFX_INT_ICU_ECR_EOV_OFF (30u)

/** \brief  Length for Ifx_INT_ICU_ECR_Bits.ID */
#define IFX_INT_ICU_ECR_ID_LEN (10u)

/** \brief  Mask for Ifx_INT_ICU_ECR_Bits.ID */
#define IFX_INT_ICU_ECR_ID_MSK (0x3ffu)

/** \brief  Offset for Ifx_INT_ICU_ECR_Bits.ID */
#define IFX_INT_ICU_ECR_ID_OFF (16u)

/** \brief  Length for Ifx_INT_ICU_ECR_Bits.PN */
#define IFX_INT_ICU_ECR_PN_LEN (8u)

/** \brief  Mask for Ifx_INT_ICU_ECR_Bits.PN */
#define IFX_INT_ICU_ECR_PN_MSK (0xffu)

/** \brief  Offset for Ifx_INT_ICU_ECR_Bits.PN */
#define IFX_INT_ICU_ECR_PN_OFF (0u)

/** \brief  Length for Ifx_INT_ICU_ECR_Bits.STAT */
#define IFX_INT_ICU_ECR_STAT_LEN (1u)

/** \brief  Mask for Ifx_INT_ICU_ECR_Bits.STAT */
#define IFX_INT_ICU_ECR_STAT_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ICU_ECR_Bits.STAT */
#define IFX_INT_ICU_ECR_STAT_OFF (31u)

/** \brief  Length for Ifx_INT_ICU_LASR_Bits.ECC */
#define IFX_INT_ICU_LASR_ECC_LEN (6u)

/** \brief  Mask for Ifx_INT_ICU_LASR_Bits.ECC */
#define IFX_INT_ICU_LASR_ECC_MSK (0x3fu)

/** \brief  Offset for Ifx_INT_ICU_LASR_Bits.ECC */
#define IFX_INT_ICU_LASR_ECC_OFF (10u)

/** \brief  Length for Ifx_INT_ICU_LASR_Bits.ID */
#define IFX_INT_ICU_LASR_ID_LEN (10u)

/** \brief  Mask for Ifx_INT_ICU_LASR_Bits.ID */
#define IFX_INT_ICU_LASR_ID_MSK (0x3ffu)

/** \brief  Offset for Ifx_INT_ICU_LASR_Bits.ID */
#define IFX_INT_ICU_LASR_ID_OFF (16u)

/** \brief  Length for Ifx_INT_ICU_LASR_Bits.PN */
#define IFX_INT_ICU_LASR_PN_LEN (8u)

/** \brief  Mask for Ifx_INT_ICU_LASR_Bits.PN */
#define IFX_INT_ICU_LASR_PN_MSK (0xffu)

/** \brief  Offset for Ifx_INT_ICU_LASR_Bits.PN */
#define IFX_INT_ICU_LASR_PN_OFF (0u)

/** \brief  Length for Ifx_INT_ICU_LWSR_Bits.ECC */
#define IFX_INT_ICU_LWSR_ECC_LEN (6u)

/** \brief  Mask for Ifx_INT_ICU_LWSR_Bits.ECC */
#define IFX_INT_ICU_LWSR_ECC_MSK (0x3fu)

/** \brief  Offset for Ifx_INT_ICU_LWSR_Bits.ECC */
#define IFX_INT_ICU_LWSR_ECC_OFF (10u)

/** \brief  Length for Ifx_INT_ICU_LWSR_Bits.ID */
#define IFX_INT_ICU_LWSR_ID_LEN (10u)

/** \brief  Mask for Ifx_INT_ICU_LWSR_Bits.ID */
#define IFX_INT_ICU_LWSR_ID_MSK (0x3ffu)

/** \brief  Offset for Ifx_INT_ICU_LWSR_Bits.ID */
#define IFX_INT_ICU_LWSR_ID_OFF (16u)

/** \brief  Length for Ifx_INT_ICU_LWSR_Bits.PN */
#define IFX_INT_ICU_LWSR_PN_LEN (8u)

/** \brief  Mask for Ifx_INT_ICU_LWSR_Bits.PN */
#define IFX_INT_ICU_LWSR_PN_MSK (0xffu)

/** \brief  Offset for Ifx_INT_ICU_LWSR_Bits.PN */
#define IFX_INT_ICU_LWSR_PN_OFF (0u)

/** \brief  Length for Ifx_INT_ICU_LWSR_Bits.STAT */
#define IFX_INT_ICU_LWSR_STAT_LEN (1u)

/** \brief  Mask for Ifx_INT_ICU_LWSR_Bits.STAT */
#define IFX_INT_ICU_LWSR_STAT_MSK (0x1u)

/** \brief  Offset for Ifx_INT_ICU_LWSR_Bits.STAT */
#define IFX_INT_ICU_LWSR_STAT_OFF (31u)

/** \brief  Length for Ifx_INT_ID_Bits.MODNUMBER */
#define IFX_INT_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_INT_ID_Bits.MODNUMBER */
#define IFX_INT_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_INT_ID_Bits.MODNUMBER */
#define IFX_INT_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_INT_ID_Bits.MODREV */
#define IFX_INT_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_INT_ID_Bits.MODREV */
#define IFX_INT_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_INT_ID_Bits.MODREV */
#define IFX_INT_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_INT_ID_Bits.MODTYPE */
#define IFX_INT_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_INT_ID_Bits.MODTYPE */
#define IFX_INT_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_INT_ID_Bits.MODTYPE */
#define IFX_INT_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_INT_OIT_Bits.OE0 */
#define IFX_INT_OIT_OE0_LEN (1u)

/** \brief  Mask for Ifx_INT_OIT_Bits.OE0 */
#define IFX_INT_OIT_OE0_MSK (0x1u)

/** \brief  Offset for Ifx_INT_OIT_Bits.OE0 */
#define IFX_INT_OIT_OE0_OFF (7u)

/** \brief  Length for Ifx_INT_OIT_Bits.OE1 */
#define IFX_INT_OIT_OE1_LEN (1u)

/** \brief  Mask for Ifx_INT_OIT_Bits.OE1 */
#define IFX_INT_OIT_OE1_MSK (0x1u)

/** \brief  Offset for Ifx_INT_OIT_Bits.OE1 */
#define IFX_INT_OIT_OE1_OFF (15u)

/** \brief  Length for Ifx_INT_OIT_Bits.TOS0 */
#define IFX_INT_OIT_TOS0_LEN (2u)

/** \brief  Mask for Ifx_INT_OIT_Bits.TOS0 */
#define IFX_INT_OIT_TOS0_MSK (0x3u)

/** \brief  Offset for Ifx_INT_OIT_Bits.TOS0 */
#define IFX_INT_OIT_TOS0_OFF (0u)

/** \brief  Length for Ifx_INT_OIT_Bits.TOS1 */
#define IFX_INT_OIT_TOS1_LEN (2u)

/** \brief  Mask for Ifx_INT_OIT_Bits.TOS1 */
#define IFX_INT_OIT_TOS1_MSK (0x3u)

/** \brief  Offset for Ifx_INT_OIT_Bits.TOS1 */
#define IFX_INT_OIT_TOS1_OFF (8u)

/** \brief  Length for Ifx_INT_OIXMS_Bits.MIRQ */
#define IFX_INT_OIXMS_MIRQ_LEN (10u)

/** \brief  Mask for Ifx_INT_OIXMS_Bits.MIRQ */
#define IFX_INT_OIXMS_MIRQ_MSK (0x3ffu)

/** \brief  Offset for Ifx_INT_OIXMS_Bits.MIRQ */
#define IFX_INT_OIXMS_MIRQ_OFF (0u)

/** \brief  Length for Ifx_INT_OIXS0_Bits.IRQ0 */
#define IFX_INT_OIXS0_IRQ0_LEN (10u)

/** \brief  Mask for Ifx_INT_OIXS0_Bits.IRQ0 */
#define IFX_INT_OIXS0_IRQ0_MSK (0x3ffu)

/** \brief  Offset for Ifx_INT_OIXS0_Bits.IRQ0 */
#define IFX_INT_OIXS0_IRQ0_OFF (0u)

/** \brief  Length for Ifx_INT_OIXS0_Bits.IRQ1 */
#define IFX_INT_OIXS0_IRQ1_LEN (10u)

/** \brief  Mask for Ifx_INT_OIXS0_Bits.IRQ1 */
#define IFX_INT_OIXS0_IRQ1_MSK (0x3ffu)

/** \brief  Offset for Ifx_INT_OIXS0_Bits.IRQ1 */
#define IFX_INT_OIXS0_IRQ1_OFF (16u)

/** \brief  Length for Ifx_INT_OIXS1_Bits.IRQ2 */
#define IFX_INT_OIXS1_IRQ2_LEN (10u)

/** \brief  Mask for Ifx_INT_OIXS1_Bits.IRQ2 */
#define IFX_INT_OIXS1_IRQ2_MSK (0x3ffu)

/** \brief  Offset for Ifx_INT_OIXS1_Bits.IRQ2 */
#define IFX_INT_OIXS1_IRQ2_OFF (0u)

/** \brief  Length for Ifx_INT_OIXS1_Bits.IRQ3 */
#define IFX_INT_OIXS1_IRQ3_LEN (10u)

/** \brief  Mask for Ifx_INT_OIXS1_Bits.IRQ3 */
#define IFX_INT_OIXS1_IRQ3_MSK (0x3ffu)

/** \brief  Offset for Ifx_INT_OIXS1_Bits.IRQ3 */
#define IFX_INT_OIXS1_IRQ3_OFF (16u)

/** \brief  Length for Ifx_INT_OIXTS_Bits.OBS */
#define IFX_INT_OIXTS_OBS_LEN (2u)

/** \brief  Mask for Ifx_INT_OIXTS_Bits.OBS */
#define IFX_INT_OIXTS_OBS_MSK (0x3u)

/** \brief  Offset for Ifx_INT_OIXTS_Bits.OBS */
#define IFX_INT_OIXTS_OBS_OFF (8u)

/** \brief  Length for Ifx_INT_OIXTS_Bits.TGS */
#define IFX_INT_OIXTS_TGS_LEN (2u)

/** \brief  Mask for Ifx_INT_OIXTS_Bits.TGS */
#define IFX_INT_OIXTS_TGS_MSK (0x3u)

/** \brief  Offset for Ifx_INT_OIXTS_Bits.TGS */
#define IFX_INT_OIXTS_TGS_OFF (0u)

/** \brief  Length for Ifx_INT_OMISN_Bits.OTGB0 */
#define IFX_INT_OMISN_OTGB0_LEN (16u)

/** \brief  Mask for Ifx_INT_OMISN_Bits.OTGB0 */
#define IFX_INT_OMISN_OTGB0_MSK (0xffffu)

/** \brief  Offset for Ifx_INT_OMISN_Bits.OTGB0 */
#define IFX_INT_OMISN_OTGB0_OFF (0u)

/** \brief  Length for Ifx_INT_OMISN_Bits.OTGB1 */
#define IFX_INT_OMISN_OTGB1_LEN (16u)

/** \brief  Mask for Ifx_INT_OMISN_Bits.OTGB1 */
#define IFX_INT_OMISN_OTGB1_MSK (0xffffu)

/** \brief  Offset for Ifx_INT_OMISN_Bits.OTGB1 */
#define IFX_INT_OMISN_OTGB1_OFF (16u)

/** \brief  Length for Ifx_INT_OMISP_Bits.OTGB0 */
#define IFX_INT_OMISP_OTGB0_LEN (16u)

/** \brief  Mask for Ifx_INT_OMISP_Bits.OTGB0 */
#define IFX_INT_OMISP_OTGB0_MSK (0xffffu)

/** \brief  Offset for Ifx_INT_OMISP_Bits.OTGB0 */
#define IFX_INT_OMISP_OTGB0_OFF (0u)

/** \brief  Length for Ifx_INT_OMISP_Bits.OTGB1 */
#define IFX_INT_OMISP_OTGB1_LEN (16u)

/** \brief  Mask for Ifx_INT_OMISP_Bits.OTGB1 */
#define IFX_INT_OMISP_OTGB1_MSK (0xffffu)

/** \brief  Offset for Ifx_INT_OMISP_Bits.OTGB1 */
#define IFX_INT_OMISP_OTGB1_OFF (16u)

/** \brief  Length for Ifx_INT_OOBS_Bits.OTGB0 */
#define IFX_INT_OOBS_OTGB0_LEN (16u)

/** \brief  Mask for Ifx_INT_OOBS_Bits.OTGB0 */
#define IFX_INT_OOBS_OTGB0_MSK (0xffffu)

/** \brief  Offset for Ifx_INT_OOBS_Bits.OTGB0 */
#define IFX_INT_OOBS_OTGB0_OFF (0u)

/** \brief  Length for Ifx_INT_OOBS_Bits.OTGB1 */
#define IFX_INT_OOBS_OTGB1_LEN (16u)

/** \brief  Mask for Ifx_INT_OOBS_Bits.OTGB1 */
#define IFX_INT_OOBS_OTGB1_MSK (0xffffu)

/** \brief  Offset for Ifx_INT_OOBS_Bits.OTGB1 */
#define IFX_INT_OOBS_OTGB1_OFF (16u)

/** \brief  Length for Ifx_INT_OSSIC_Bits.TGB */
#define IFX_INT_OSSIC_TGB_LEN (1u)

/** \brief  Mask for Ifx_INT_OSSIC_Bits.TGB */
#define IFX_INT_OSSIC_TGB_MSK (0x1u)

/** \brief  Offset for Ifx_INT_OSSIC_Bits.TGB */
#define IFX_INT_OSSIC_TGB_OFF (2u)

/** \brief  Length for Ifx_INT_OSSIC_Bits.TGS */
#define IFX_INT_OSSIC_TGS_LEN (2u)

/** \brief  Mask for Ifx_INT_OSSIC_Bits.TGS */
#define IFX_INT_OSSIC_TGS_MSK (0x3u)

/** \brief  Offset for Ifx_INT_OSSIC_Bits.TGS */
#define IFX_INT_OSSIC_TGS_OFF (0u)

/** \brief  Length for Ifx_INT_SRB_Bits.TRIG0 */
#define IFX_INT_SRB_TRIG0_LEN (1u)

/** \brief  Mask for Ifx_INT_SRB_Bits.TRIG0 */
#define IFX_INT_SRB_TRIG0_MSK (0x1u)

/** \brief  Offset for Ifx_INT_SRB_Bits.TRIG0 */
#define IFX_INT_SRB_TRIG0_OFF (0u)

/** \brief  Length for Ifx_INT_SRB_Bits.TRIG1 */
#define IFX_INT_SRB_TRIG1_LEN (1u)

/** \brief  Mask for Ifx_INT_SRB_Bits.TRIG1 */
#define IFX_INT_SRB_TRIG1_MSK (0x1u)

/** \brief  Offset for Ifx_INT_SRB_Bits.TRIG1 */
#define IFX_INT_SRB_TRIG1_OFF (1u)

/** \brief  Length for Ifx_INT_SRB_Bits.TRIG2 */
#define IFX_INT_SRB_TRIG2_LEN (1u)

/** \brief  Mask for Ifx_INT_SRB_Bits.TRIG2 */
#define IFX_INT_SRB_TRIG2_MSK (0x1u)

/** \brief  Offset for Ifx_INT_SRB_Bits.TRIG2 */
#define IFX_INT_SRB_TRIG2_OFF (2u)

/** \brief  Length for Ifx_INT_SRB_Bits.TRIG3 */
#define IFX_INT_SRB_TRIG3_LEN (1u)

/** \brief  Mask for Ifx_INT_SRB_Bits.TRIG3 */
#define IFX_INT_SRB_TRIG3_MSK (0x1u)

/** \brief  Offset for Ifx_INT_SRB_Bits.TRIG3 */
#define IFX_INT_SRB_TRIG3_OFF (3u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXINT_BF_H */
