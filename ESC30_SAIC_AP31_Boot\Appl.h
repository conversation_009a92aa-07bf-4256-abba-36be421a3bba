/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  
 *  @file       <Appl.h>
 *  @brief      <function decalrations for Appl>
 *  
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *  
 *  <AUTHOR> Chen>
 *  @date       <2012-12-27>
 */
/*============================================================================*/

#ifndef APPL_H
#define APPL_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/* <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20121227    Gary       Initial version
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/

#include "Std_Types.h"
#include "FL.h"
#include "FL_Cfg.h"
#include "IfxStm_reg.h"
#define Stm0_GetSysTickTime()     STM0_TIM0.U
#define TIMER_10MS_COUNTER 100000U //1000000U

/*=======[E X T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
extern FL_ResultType Appl_EcuStartup(void);

extern FL_ResultType Appl_FlStartup(void);

#if (FL_SLEEP_TIMER > 0)
extern void Appl_EcuShutdown (void);
#endif

extern void Appl_EcuReset(void);

extern FL_ResultType Appl_CheckValidation(void);

extern void Appl_UpdateTriggerCondition(void);
extern void Appl_UpdateTriggerConditionImmediate(uint8 count);
extern void Appl_Memcpy(uint8 * dest,
    const uint8 *source,
    uint32 length);

extern void Appl_Memset(uint8 * dest,
    const uint8 source,
    uint32 length);
extern void  Appl_Period_10ms(void);
extern uint8 GetTicks10ms(void);
#endif/* endof APPL_H */

/*=======[E N D   O F   F I L E]==============================================*/

