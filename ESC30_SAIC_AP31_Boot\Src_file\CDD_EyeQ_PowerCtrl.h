/*******************************************************************************
|  File Name:  SM_WdgMng.h
|  Description:  Implementation of the Wdg Manage
|-------------------------------------------------------------------------------
| (c) This software is the proprietary of DIAS.
|     All rights are reserved by DIAS.
|-------------------------------------------------------------------------------
| Initials      Name                   Company
| --------      --------------------   -----------------------------------------
| XG           XiaoGang           		DIAS
|-------------------------------------------------------------------------------
|               R E V I S I O N   H I S T O R Y
|-------------------------------------------------------------------------------
| Date          Version      Author    Description
| ------------  --------     -------   ------------------------------------
| 2020-04-10    01.00.00     XiaoGang       Creation
|******************************************************************************/
#ifndef SM_WDGMNG_H
# define SM_WDGMNG_H

/*******************************************************************************
|    Other Header File Inclusion
|******************************************************************************/
#include "Std_Types.h"
#include "Dio.h"
#include "EcuM.h"
#include "EcuM_Cfg.h"
#include "Stm.h"
#include "Gtm.h"
#include "Gtm_Cfg.h"
/*******************************************************************************
|    Macro Definition
|******************************************************************************/
#define   IIC_A                    ((uint8)0)
#define   IIC_B                    ((uint8)1)
#define   SCL_A                    DIO_CHANNEL_33_1/*O_F_SLA_M*/
#define   SCL_B                    DIO_CHANNEL_34_3/*O_F_SLB_M*/
#define   SDA_A                    DIO_CHANNEL_33_2/*B_D_SDAA_M*/
#define   SDA_B                    DIO_CHANNEL_33_0/*B_D_SDAB_M*/
#define   PortInput_Mode           0x00
#define   PortOutput_Mode          0x80
/*******************************************************************************
|    Enum Definition
|******************************************************************************/

/*******************************************************************************
|    Typedef Definition
|******************************************************************************/

/*******************************************************************************
|    Extern variables and functions declaration
|******************************************************************************/

extern uint8 SM_EthH_Flag;
extern uint8 EyeQPower_Flag;
extern void CDD_IIC_COM_Delay(uint32 Delaytime);
extern uint8 SM_PwrInit(void);
extern uint8 SM_EyeQPwrMainFunc(void);
extern uint8 SM_EyeQPwrDiagMainFunc(void);
extern uint8 SM_EyeQPwrUpMainFunc(void);
extern uint8 SM_EyeQPwrDownMainFunc(void);
extern uint8 SM_EyeQPwrOnEnMainFunc(void);
extern void SM_EthEnable(void);

#endif /* SM_PWRMNG_H */
