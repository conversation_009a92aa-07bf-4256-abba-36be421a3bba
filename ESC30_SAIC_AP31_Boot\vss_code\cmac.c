
#include "errid.h"
#include "cmac.h"
#include "vssapi.h"
#include "vsscommon.h"

extern const vss_uint8 cmac_Rb[16];
extern const vss_uint8 vss_ZERO[32];

#pragma section code "vss_api_code" 

__attribute__((used)) vss_uint32 CMacData(vss_uint8 alg, vss_uint32 key_len, vss_uint8 *key, vss_uint8 *input, vss_uint32 length, vss_uint8 *mac )
{
	vss_uint8 X[16];
	vss_uint8 Y[16];
	vss_uint8 M_last[16];
	vss_uint8 padded[16];
	vss_uint8 K1[16];
	vss_uint8 K2[16];
	vss_uint32 n;
	vss_uint32 i;
	vss_uint32 flag;

	TSymmCtx ctx;

	mem_set8(X, 0, 16);
	mem_set8(Y, 0, 16);
	mem_set8(M_last, 0, 16);
	mem_set8(padded, 0, 16);
	mem_set8(K1, 0, 16);
	mem_set8(K2, 0, 16);

	if (alg == ALG_GM)
	{

		sm4_setkey_enc(&ctx.sm4_ctx, key);
		cmac_generate_subkey(alg, &ctx, K1, K2);
	}
	else
	{
#if (defined (_ENABLE_MIZAR_AES_)&&(_ENABLE_MIZAR_AES_ == 1U))
		aes_set_key(&ctx.aes_ctx, key);
		cmac_generate_subkey(alg, &ctx, K1, K2);
#else
		return ERR_NOT_SUPPORT;
#endif
	}	

	n = (length+15)/16;

	if (n == 0)
	{
		n = 1;
		flag = 0;
	} 
	else 
	{
		if ( (length%16) == 0 )
		{ 
			flag = 1;
		} 
		else 
		{ 
			flag = 0;
		}
	}

	if (flag) 
	{ 
		cmac_xor_128(&input[16*(n-1)], K1, M_last);
	} 
	else 
	{
		cmac_padding(&input[16*(n-1)], padded, length%16);
		cmac_xor_128(padded, K2, M_last);
	}

	for ( i = 0; i < (n-1); i++ ) 
	{
		cmac_xor_128(X, &input[16*i], Y);
		if (alg == 2)
		{
			sm4_crypt_ecb(&ctx.sm4_ctx, TT_SM4_ENCRYPT, 16, Y, X);
		}
		else
		{
#if (defined (_ENABLE_MIZAR_AES_)&&(_ENABLE_MIZAR_AES_ == 1U))		
			aes_ecb_encrypt(Y, X, 16, &ctx.aes_ctx, TT_AES_ENCRYPT);
#else
			return ERR_NOT_SUPPORT;
#endif
		}
	}

	cmac_xor_128(X, M_last, Y);

	if (alg == 2)
	{
			sm4_crypt_ecb(&ctx.sm4_ctx, TT_SM4_ENCRYPT, 16, Y, mac);
	}	
	else
	{
#if (defined (_ENABLE_MIZAR_AES_)&&(_ENABLE_MIZAR_AES_ == 1U))		
		aes_ecb_encrypt(Y, mac, 16, &ctx.aes_ctx, TT_AES_ENCRYPT);
#else
		return ERR_NOT_SUPPORT;
#endif
	}
	return 0;
}

__attribute__((used)) void cmac_padding(vss_uint8 *lastb, vss_uint8 *pad, vss_uint32 length)
{
	vss_uint32 j;
	
	for (j = 0; j < 16; j++ ) 
	{
		if (j < length) 
		{
			pad[j] = lastb[j];
		}
		else if (j == length) 
		{
			pad[j] = 0x80;
		} 
		else 
		{
			pad[j] = 0x00;
		}
	}
}

__attribute__((used)) void cmac_leftshift_onebit(vss_uint8 *input, vss_uint8 *output)
{
	vss_sint32 i;
	vss_uint8 overflow = 0;
	
	for ( i=15; i>=0; i-- ) 
	{
		output[i] = input[i] << 1;
		output[i] |= overflow;
		if (input[i] & 0x80)
			overflow = 1;
		else
			overflow = 0;			
	}
	return;
}

__attribute__((used)) void cmac_xor_128(vss_uint8* input1, vss_uint8* input2, vss_uint8* output)
{
	vss_uint32 i = 0;
	for (i = 0; i < 16; i++)
	{
		output[i] = input1[i] ^ input2[i];
	}
}

__attribute__((used)) vss_uint32 cmac_generate_subkey(vss_uint8 alg, TSymmCtx* ctx, vss_uint8 *K1, vss_uint8 *K2)
{
	vss_uint8 L[32];
	vss_uint8 tmp[32];

	mem_set8(L, 0, 32);
	mem_set8(tmp, 0, 32);

	if (alg == 2)
	{

		sm4_crypt_ecb(&ctx->sm4_ctx, TT_SM4_ENCRYPT, 16, (vss_uint8*)vss_ZERO, L);

	}
	else
	{
#if (defined (_ENABLE_MIZAR_AES_)&&(_ENABLE_MIZAR_AES_ == 1U))			
		aes_ecb_encrypt((vss_uint8*)vss_ZERO, L, 16, &ctx->aes_ctx, TT_AES_ENCRYPT);
#else
		return ERR_NOT_SUPPORT;
#endif		
	}
	
	if ( (L[0] & 0x80) == 0 ) { /* If MSB(L) = 0, then K1 = L << 1 */
		cmac_leftshift_onebit(L,K1);
	} else {    /* Else K1 = ( L << 1 ) (+) Rb */
		cmac_leftshift_onebit(L,tmp);
		cmac_xor_128(tmp,(vss_uint8*)cmac_Rb,K1);
	}
	
	if ( (K1[0] & 0x80) == 0 ) {
		cmac_leftshift_onebit(K1,K2);
	} else {
		cmac_leftshift_onebit(K1,tmp);
		cmac_xor_128(tmp,(vss_uint8*)cmac_Rb,K2);
	}
	return 0;
}


#pragma section code restore



