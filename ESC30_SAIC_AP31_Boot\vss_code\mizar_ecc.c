#include "mizar_ecc.h"
#include "errid.h"
#include "string.h"
#include "vsscommon.h"



#pragma section code "vss_api_code" 

#if (defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))
extern const vss_uint32 mizar_ecc_g_ecdsa_para_a[8];
extern const vss_uint32 mizar_ecc_g_ecdsa_para_b[8];
extern const vss_uint32 mizar_ecc_g_ecdsa_para_p[8];
extern const vss_uint32 mizar_ecc_g_ecdsa_para_n[8];
extern const vss_uint32 mizar_ecc_g_ecdsa_para_G[16];
extern const vss_uint32 mizar_ecc_g_ecdsa_para_h;
extern const vss_uint8 mizar_ecc_vss_ecc_rand[32];
extern const vss_uint8 mizar_ecc_k[32];
extern const vss_uint8 mizar_ecc_rand1[32];
								
vss_uint32 ecc_pointmul_little(ecc_point_a *result, ecc_point_a *a, vss_uint32 *k, ecc_fp *para)
{
	vss_uint32 blen = (para->wlen) * 4;
	ecc_fp para_buf;
	vss_uint32 rand_buf[8];
	vss_uint32 a_buf[8];  
	vss_uint32 b_buf[8]; 
	vss_uint32 p_buf[8];
	vss_uint32 n_buf[8];
	ecc_point_a G_buf;
	vss_uint32 G_bufx[8];
	vss_uint32 G_bufy[8];
	vss_uint32 ret = 0;	
	ecc_point_a R;
	vss_uint32 R_bufx[8];
	vss_uint32 R_bufy[8];
	
	para_buf.a = a_buf;
	para_buf.b = b_buf;
	para_buf.p = p_buf;
	para_buf.n = n_buf;
	para_buf.wlen = para->wlen;
	G_buf.x = G_bufx;
	G_buf.y = G_bufy;
	R.x = R_bufx;
	R.y = R_bufy;
	calc_bigtolit((vss_uint8*)rand_buf, (vss_uint8*)k, blen);
	calc_bigtolit((vss_uint8*)para_buf.a, (vss_uint8 *)para->a, blen);
	calc_bigtolit((vss_uint8*)para_buf.b, (vss_uint8 *)para->b, blen);
	calc_bigtolit((vss_uint8*)para_buf.p, (vss_uint8 *)para->p, blen);
	calc_bigtolit((vss_uint8*)para_buf.n, (vss_uint8 *)para->n, blen);
	
	calc_bigtolit((vss_uint8*)G_buf.x, (vss_uint8 *)a->x, blen);
	calc_bigtolit((vss_uint8*)G_buf.y, (vss_uint8 *)a->y, blen);	
	
	ret = ecc_pointmul(&R, &G_buf, rand_buf, &para_buf);
	
	calc_bigtolit((vss_uint8*)result->x, (vss_uint8 *)R.x, blen);
	calc_bigtolit((vss_uint8*)result->y, (vss_uint8 *)R.y, blen);	
	return ret;
}

vss_uint32 ecc_pointadd_little(ecc_point_a *result, ecc_point_a *op_a, ecc_point_a *op_b, ecc_fp *para)
{
	vss_uint32 blen = (para->wlen) * 4;
	ecc_fp para_buf;
	vss_uint32 a_buf[8];  
	vss_uint32 b_buf[8]; 
	vss_uint32 p_buf[8];
	vss_uint32 n_buf[8];
	ecc_point_a PA, PB;
	vss_uint32 PA_bufx[8];
	vss_uint32 PA_bufy[8];
	vss_uint32 PB_bufx[8];
	vss_uint32 PB_bufy[8];
	vss_uint32 ret = 0;	
	ecc_point_a R;
	vss_uint32 R_bufx[8];
	vss_uint32 R_bufy[8];	
	
	para_buf.a = a_buf;
	para_buf.b = b_buf;
	para_buf.p = p_buf;
	para_buf.n = n_buf;
	para_buf.wlen = para->wlen;
	PA.x = PA_bufx;
	PA.y = PA_bufy;
	PB.x = PB_bufx;
	PB.y = PB_bufy;
	R.x = R_bufx;
	R.y = R_bufy;
	
	calc_bigtolit((vss_uint8*)para_buf.a, (vss_uint8 *)para->a, blen);
	calc_bigtolit((vss_uint8*)para_buf.b, (vss_uint8 *)para->b, blen);
	calc_bigtolit((vss_uint8*)para_buf.p, (vss_uint8 *)para->p, blen);
	calc_bigtolit((vss_uint8*)para_buf.n, (vss_uint8 *)para->n, blen);
	
	calc_bigtolit((vss_uint8*)PA.x, (vss_uint8 *)op_a->x, blen);
	calc_bigtolit((vss_uint8*)PA.y, (vss_uint8 *)op_a->y, blen);	
	calc_bigtolit((vss_uint8*)PB.x, (vss_uint8 *)op_b->x, blen);
	calc_bigtolit((vss_uint8*)PB.y, (vss_uint8 *)op_b->y, blen);	
	
	ret = ecc_pointadd(&R, &PA, &PB, &para_buf);
	
	calc_bigtolit((vss_uint8*)result->x, (vss_uint8 *)R.x, blen);
	calc_bigtolit((vss_uint8*)result->y, (vss_uint8 *)R.y, blen);	
	return ret;
}

vss_uint32 ecc_pointinv_little(ecc_point_a *result, ecc_point_a *a, ecc_fp *para)
{
	vss_uint32 blen = (para->wlen) * 4;
	ecc_fp para_buf;
	vss_uint32 a_buf[8];  
	vss_uint32 b_buf[8]; 
	vss_uint32 p_buf[8];
	vss_uint32 n_buf[8];
	ecc_point_a G_buf;
	vss_uint32 G_bufx[8];
	vss_uint32 G_bufy[8];
	vss_uint32 ret = 0;	
	ecc_point_a R;
	vss_uint32 R_bufx[8];
	vss_uint32 R_bufy[8];

	para_buf.a = a_buf;
	para_buf.b = b_buf;
	para_buf.p = p_buf;
	para_buf.n = n_buf;
	para_buf.wlen = para->wlen;
	G_buf.x = G_bufx;
	G_buf.y = G_bufy;
	R.x = R_bufx;
	R.y = R_bufy;
	
	calc_bigtolit((vss_uint8*)para_buf.a, (vss_uint8 *)para->a, blen);
	calc_bigtolit((vss_uint8*)para_buf.b, (vss_uint8 *)para->b, blen);
	calc_bigtolit((vss_uint8*)para_buf.p, (vss_uint8 *)para->p, blen);
	calc_bigtolit((vss_uint8*)para_buf.n, (vss_uint8 *)para->n, blen);
	
	calc_bigtolit((vss_uint8*)G_buf.x, (vss_uint8 *)a->x, blen);
	calc_bigtolit((vss_uint8*)G_buf.y, (vss_uint8 *)a->y, blen);	
	
	ret = ecc_pointinv(&R, &G_buf, &para_buf);
	calc_bigtolit((vss_uint8*)result->x, (vss_uint8 *)R.x, blen);
	calc_bigtolit((vss_uint8*)result->y, (vss_uint8 *)R.y, blen);	
	return ret;
}

vss_uint8 ecc_pri_dec(vss_uint32* priKey, vss_uint8 wlen, vss_uint8* in, vss_uint32 inLen, vss_uint8* out)
{
	vss_uint8* temp;
	vss_uint32* wtemp;
	ecc_point_a R;
	ecc_point_a B;
	ecc_point_a B1;
	ecc_point_a R1;
	ecc_point_a R2;
	ecc_fp para;
	vss_uint32 result_data_R1[16];
	vss_uint32 result_data_R2[16];
	vss_uint32 result_data_B1[16];
	vss_uint32 ret;
	
	wtemp = (vss_uint32*)in;
	R.x = wtemp;
	R.y = R.x + wlen;
	B.x = R.y + wlen;
	B.y = B.x + wlen;
	
	para.wlen = wlen;
	para.n = (vss_uint32*)mizar_ecc_g_ecdsa_para_n;
	para.p = (vss_uint32*)mizar_ecc_g_ecdsa_para_p;
	para.a = (vss_uint32*)mizar_ecc_g_ecdsa_para_a;
	para.b = (vss_uint32*)mizar_ecc_g_ecdsa_para_b;		
	
	R1.x = result_data_R1;
	R1.y = result_data_R1 + para.wlen;
	R2.x = result_data_R2;
	R2.y = result_data_R2 + para.wlen;
	B1.x = result_data_B1;
	B1.y = result_data_B1 + para.wlen;
	
	ret = ecc_pointmul_little(&R1, &R, (vss_uint32*)priKey, &para);

	if(RET_ECC_IMPLEMENT_SUCCESS != ret)
		return ERR_CALC_FAIL;
	
	ret = ecc_pointinv_little(&R2, &R1, &para);

	if(RET_ECC_IMPLEMENT_SUCCESS != ret)
		return ERR_CALC_FAIL;
	
	ret = ecc_pointadd_little(&B1, &B, &R2, &para);
	if(RET_ECC_IMPLEMENT_SUCCESS != ret)
		return ERR_CALC_FAIL;
	
	temp = out;
	mem_cpy8(temp, (vss_uint8*)result_data_B1, 64);
	return 0;
}

vss_uint8 ecc_pub_enc(vss_uint32* x, vss_uint32* y, vss_uint8 wlen, vss_uint8* in, vss_uint32 inLen, vss_uint8* out)
{
	vss_uint8* temp;
	ecc_point_a R;
	ecc_point_a B;
	ecc_point_a B1;
	ecc_point_a pk;
	ecc_point_a g; 
	ecc_point_a d;

	ecc_fp para;
	vss_uint32 result_data_R[16];
	vss_uint32 result_data_B[16];
	vss_uint32 result_data_B1[16];	
	vss_uint32 ret;	
	g.x = (vss_uint32*)mizar_ecc_g_ecdsa_para_G;
	g.y = (vss_uint32*)mizar_ecc_g_ecdsa_para_G + wlen; 
	para.wlen = wlen;
	para.n = (vss_uint32*)mizar_ecc_g_ecdsa_para_n;
	para.p = (vss_uint32*)mizar_ecc_g_ecdsa_para_p;
	para.a = (vss_uint32*)mizar_ecc_g_ecdsa_para_a;
	para.b = (vss_uint32*)mizar_ecc_g_ecdsa_para_b;	
	para.h = (vss_uint32*)&mizar_ecc_g_ecdsa_para_h;
	para.a_n3 = 1;							
	
	R.x = result_data_R;
	R.y = result_data_R + para.wlen;
	B.x = result_data_B;
	B.y = result_data_B + para.wlen;
	B1.x = result_data_B1;
	B1.y = result_data_B1 + para.wlen;
	pk.x = x;
	pk.y = y;
	d.x = (vss_uint32*)in;
	d.y = d.x + wlen;

	ret = ecc_pointmul_little(&R, &g, (vss_uint32*)mizar_ecc_k, &para);

	if(RET_ECC_IMPLEMENT_SUCCESS != ret)
	{		
		return ERR_CALC_FAIL;
	}
	
	ret = ecc_pointmul_little(&B, &pk, (vss_uint32*)mizar_ecc_k, &para);

	if(RET_ECC_IMPLEMENT_SUCCESS != ret)
		return ERR_CALC_FAIL;
	
	ret = ecc_pointadd_little(&B1, &d, &B, &para);
	if(RET_ECC_IMPLEMENT_SUCCESS != ret)
		return ERR_CALC_FAIL;
	
	temp = out;
	mem_cpy8(temp, (vss_uint8*)result_data_R, 64);
	temp += 64;
	mem_cpy8(temp, (vss_uint8*)result_data_B1, 64);
	return 0;
}

vss_uint32 ecc_gen_key(vss_uint8* sk, vss_uint8* x, vss_uint8* y)
{
	vss_uint32 ret;
	ecdsa_point pa;
	ecdsa_point g;
	ecdsa_DomainPara para;
	vss_uint8 modlen;
	vss_uint8 wlen; 
	vss_uint32 ecdsa_private_key[8];
	vss_uint32 ecdsa_public_key[16];

	modlen = 32;
	wlen = modlen/4;
	
	g.x = (vss_uint32*)mizar_ecc_g_ecdsa_para_G;
	g.y = (vss_uint32*)mizar_ecc_g_ecdsa_para_G + wlen; 
	para.field = ECDSA_FP_FIELD;
	para.G = &g;
	para.n = (vss_uint32*)mizar_ecc_g_ecdsa_para_n;
	para.p = (vss_uint32*)mizar_ecc_g_ecdsa_para_p;
	para.a = (vss_uint32*)mizar_ecc_g_ecdsa_para_a;
	para.b = (vss_uint32*)mizar_ecc_g_ecdsa_para_b;	
	
	pa.x = (vss_uint32*)ecdsa_public_key;
	pa.y = (vss_uint32*)ecdsa_public_key + wlen;
	
	ret =  ecdsa_genkey(&pa, (vss_uint32*)ecdsa_private_key, (vss_uint32*)mizar_ecc_vss_ecc_rand, wlen, &para);
	if(RET_ECDSA_GENKEY_SUCCESS != ret)
	{
		return ERR_CALC_FAIL;
	}	

	mem_cpy8(sk, (vss_uint8*)ecdsa_private_key, 32);
	mem_cpy8(x, (vss_uint8*)pa.x, 32);
	mem_cpy8(y, (vss_uint8*)pa.y, 32);
	return 0;
}

vss_uint32 my_ecc_sign(vss_uint8* sk, vss_uint8* data, vss_uint8* sig_data)
{
	ecdsa_point g;
	ecdsa_DomainPara para;
	ecdsa_sig sig;
	vss_uint32 ret;
	vss_uint8 wlen = 8; 

	g.x = (vss_uint32*)mizar_ecc_g_ecdsa_para_G;
	g.y = (vss_uint32*)mizar_ecc_g_ecdsa_para_G + wlen; 
	para.field = ECDSA_FP_FIELD;
	para.G = &g;
	para.n = (vss_uint32*)mizar_ecc_g_ecdsa_para_n;
	para.p = (vss_uint32*)mizar_ecc_g_ecdsa_para_p;
	para.a = (vss_uint32*)mizar_ecc_g_ecdsa_para_a;
	para.b = (vss_uint32*)mizar_ecc_g_ecdsa_para_b;		
	
	sig.r = (vss_uint32*)sig_data;
	sig.s = sig.r + wlen;
	ret = ecdsa_gensig(&sig, (vss_uint32*)data, wlen, (vss_uint32*)sk, (vss_uint32*)mizar_ecc_rand1, wlen, &para);
	if(RET_ECDSA_GENSIG_SUCCESS != ret)
	{
		return ERR_CALC_FAIL;
	}
	return 0;
}

vss_uint32 my_ecc_verify(vss_uint8* x, vss_uint8* y, vss_uint8* data, vss_uint8* sig_data)
{
	ecdsa_point g;
	ecdsa_DomainPara para;
	ecdsa_sig sig;
	vss_uint8 wlen = 8; 
	ecdsa_point pa;
	vss_uint32 ret;

	sig.r = (vss_uint32*)sig_data;
	sig.s = sig.r + wlen;

	g.x = (vss_uint32*)mizar_ecc_g_ecdsa_para_G;
	g.y = (vss_uint32*)mizar_ecc_g_ecdsa_para_G + wlen; 	
	para.field = ECDSA_FP_FIELD;
	para.G = &g;
	para.n = (vss_uint32*)mizar_ecc_g_ecdsa_para_n;
	para.p = (vss_uint32*)mizar_ecc_g_ecdsa_para_p;
	para.a = (vss_uint32*)mizar_ecc_g_ecdsa_para_a;
	para.b = (vss_uint32*)mizar_ecc_g_ecdsa_para_b;			
	pa.x = (vss_uint32*)x;
	pa.y = (vss_uint32*)y;
	ret = ecdsa_verifysig(&sig, (vss_uint32*)data, wlen, &pa, wlen, &para);
	if(RET_ECDSA_VERIFYSIG_SUCCESS != ret)
	{
		return ERR_CALC_FAIL;
	}

	return 0;
}

#endif



#pragma section code restore



