ODIR			=./obj
EDIR			=./bin
LDIR			=./bin
INC_DIR=./

CC		= g++ 
AR      = ar -r
LD      = ld
RM		= rm -f
cc 		= gcc
STRIP	= strip

MKDIR 		= mkdir -p

INCLUDES		= -I$(INC_DIR) -D_PRINT_DEBUG_
CCFLAGS			= -g $(INCLUDES) -fPIC
#CCFLAGS			= -pipe -g -w  $(INCLUDES)
LIBS 			=  -lc -ldl -lcryptoapi -lm -lgcc_s -pthread

SRC_DIR = ./
ALL_DIR = -I$(INC_DIR)

LIBAPI_SO = $(LDIR)/libcryptoapi.so
TEST = $(EDIR)/test_mcu

PROGRAM = $(LIBAPI_SO) $(TEST)

########## object files #############
LIBAPI_OBJ	=$(ODIR)/bignum.o \
	$(ODIR)/bignum_mem_op.o \
	$(ODIR)/sm3.o \
	$(ODIR)/sm4.o \
	$(ODIR)/bignum_wrapper.o \
	$(ODIR)/ecdsa.o \
	$(ODIR)/ecc.o \
	$(ODIR)/cert.o \
	$(ODIR)/sm2.o \
	$(ODIR)/aes.o \
	$(ODIR)/kzuc.o \
	$(ODIR)/cmac.o \
	$(ODIR)/mizar_ecc.o \
	$(ODIR)/sha256.o \
	$(ODIR)/ftlsmsg.o \
	$(ODIR)/vssvar.o \
	$(ODIR)/vssapi.o \
	$(ODIR)/vsskeym.o

TEST_OBJ	=$(ODIR)/Crypto_123_20.o \
	$(ODIR)/test_common.o \
	$(ODIR)/test_data.o \
	$(ODIR)/test_init.o \
	$(ODIR)/test_key.o \
	$(ODIR)/test_cert.o \
	$(ODIR)/test_fota.o \
	$(ODIR)/test_crypto.o \
	$(ODIR)/test_autosar.o \
	$(ODIR)/test.o
       
###############################################
all:	$(PROGRAM)
force:
$(LIBAPI_SO)::	$(LIBAPI_OBJ)
	@echo ------ Linking...	------
	$(LD) -shared -o $(LIBAPI_SO) $(LIBAPI_OBJ) 
	@echo ------ make $@ OK. ------

$(TEST)::	$(TEST_OBJ)
	@echo ------ Linking...	------
	$(CC) -o $(TEST) $(TEST_OBJ) $(CCFLAGS) -L$(LDIR) $(LIBS)
	@echo ------ make $@ OK. ------
	
$(FTLS)::	$(FTLS_OBJ)
	@echo ------ Linking...	------
	$(CC) -o $(FTLS) $(FTLS_OBJ) $(CCFLAGS) -L$(LDIR) $(LIBS)
	@echo ------ make $@ OK. ------

clean::
	@$(RM) $(LIBAPI_OBJ) $(TEST_OBJ) $(TEST) $(PROGRAM) 
cleanbin::
	@$(RM) $(PROGRAM)

.SUFFIXES: .cpp .c .o .so .a
##################### common #################### 
$(ODIR)/%.o:$(SRC_DIR)/%.c
	$(cc) $(CCFLAGS) $< $(ALL_DIR) -c -o $@		
$(ODIR)/%.o:$(SRC_DIR)/test/%.c
	$(cc) $(CCFLAGS) $< $(ALL_DIR) -c -o $@		
