/**
 * \file Ifx_reg.h
 * \brief
 * \copyright Copyright (c) 2012 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC23XADAS_UM_V1.1.R1
 * Specification: Refer to module specific file heading
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 */
#ifndef IFX_REG_H
#define IFX_REG_H 1

#include "IfxAsclin_reg.h"
#include "IfxCan_reg.h"
#include "IfxCcu6_reg.h"
#include "IfxCpu_reg.h"
#include "IfxDma_reg.h"
#include "IfxEbcu_reg.h"
#include "IfxEmem_reg.h"
#include "IfxEray_reg.h"
#include "IfxEth_reg.h"
#include "IfxFft_reg.h"
#include "IfxFlash_reg.h"
#include "IfxGpt12_reg.h"
#include "IfxGtm_reg.h"
#include "IfxInt_reg.h"
#include "IfxIom_reg.h"
#include "IfxLmu_reg.h"
#include "IfxMc_reg.h"
#include "IfxMtu_reg.h"
#include "IfxOvc_reg.h"
#include "IfxPmu_reg.h"
#include "IfxPort_reg.h"
#include "IfxQspi_reg.h"
#include "IfxSbcu_reg.h"
#include "IfxScu_reg.h"
#include "IfxSent_reg.h"
#include "IfxSmu_reg.h"
#include "IfxSrc_reg.h"
#include "IfxStm_reg.h"
#include "IfxVadc_reg.h"
#include "IfxXbar_reg.h"

#endif /*IFX_REG_H*/

