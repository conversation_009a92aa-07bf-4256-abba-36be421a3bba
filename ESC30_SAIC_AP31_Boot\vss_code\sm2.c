/**
sm2.c
Author: <PERSON>
*/

#include "calc.h"
#include "sm2.h"
#include "sm3.h"
#include "errid.h"
#include "vsscommon.h"



#pragma section code "vss_api_code" 

#if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U)) 
#define SM2_VERSION 0x00000001

extern const vss_uint32 sm2_G_SM2_Gx[8];
extern const vss_uint32 sm2_G_SM2_Gy[8];
extern const vss_uint32 sm2_G_SM2_a[8];
extern const vss_uint32 sm2_G_SM2_b[8];
extern const vss_uint32 sm2_G_SM2_p[8];
extern const vss_uint32 sm2_G_SM2_n[8];
extern vss_uint8 sm2_pub_enc_rand[32];
extern vss_uint32 sm2_pri_dec_rand[8];
extern vss_uint8 sm2_kdf_len;

vss_uint32 sm2_version(void)
{
	return SM2_VERSION;
}

/***********************************************
Description:	SM2  generate key pair

Return Value:	RET_SM2_RAND_ERROR: the return value denotes the sm2_random data is illegal
				RET_SM2_GENKEY_ERROR: the return value denotes there is some fault happen
				RET_SM2_GENKEY_SUCCESS: the return value denotes this function opetate successfully
Input:			sm2_rand: 	320 bits sm2_random data
Output:			keypair->pa: user's public key
				keypair->da: user's private key
Note:			none
***********************************************/
vss_uint32 sm2_genkey(sm2_point *pa, vss_uint32 *da, vss_uint32 *sm2_rand)
{
	volatile vss_uint32 ret = RET_SM2_GENKEY_ERROR;
	
	vss_uint32 sm2_rand_buf[8];
	sm2_fp_para para_buf;
	vss_uint32 a_buf[8];  
	vss_uint32 b_buf[8]; 
	vss_uint32 p_buf[8];
	vss_uint32 n_buf[8];
	sm2_point G_buf;
	vss_uint32 G_bufx[8];
	vss_uint32 G_bufy[8];	
	sm2_point g_G;
	sm2_fp_para g_para;
	vss_uint32 blen = 0;

	g_G.x = (vss_uint32*)sm2_G_SM2_Gx;
	g_G.y = (vss_uint32*)sm2_G_SM2_Gy;
	
	g_para.a = (vss_uint32*)sm2_G_SM2_a;
	g_para.b = (vss_uint32*)sm2_G_SM2_b;
	g_para.p = (vss_uint32*)sm2_G_SM2_p;
	g_para.n = (vss_uint32*)sm2_G_SM2_n;
	g_para.wlen = SM2_PARA_WLEN;
	
	blen = (g_para.wlen) * 4;
	
	calc_bigtolit((vss_uint8*)sm2_rand_buf, (vss_uint8*)sm2_rand, blen);
	
	para_buf.a = a_buf;
	para_buf.b = b_buf;
	para_buf.p = p_buf;
	para_buf.n = n_buf;
	para_buf.wlen = g_para.wlen;
	
	G_buf.x = G_bufx;
	G_buf.y = G_bufy;
	
	calc_bigtolit((vss_uint8*)para_buf.a, (vss_uint8 *)g_para.a, blen);
	calc_bigtolit((vss_uint8*)para_buf.b, (vss_uint8 *)g_para.b, blen);
	calc_bigtolit((vss_uint8*)para_buf.p, (vss_uint8 *)g_para.p, blen);
	calc_bigtolit((vss_uint8*)para_buf.n, (vss_uint8 *)g_para.n, blen);
	
	calc_bigtolit((vss_uint8*)G_buf.x, (vss_uint8 *)g_G.x, blen);
	calc_bigtolit((vss_uint8*)G_buf.y, (vss_uint8 *)g_G.y, blen);

	calc_mod(da, sm2_rand_buf, g_para.wlen, para_buf.n, g_para.wlen);
	
	ret = ecc_pointmul(pa, &G_buf, da, &para_buf);

	if(ret != RET_ECC_IMPLEMENT_SUCCESS)
	{
	 	return RET_SM2_GENKEY_ERROR;
	}

	calc_bigtolit((vss_uint8*)da, (vss_uint8 *)da, blen);
	
	calc_bigtolit((vss_uint8*)pa->x, (vss_uint8 *)pa->x, blen);
	calc_bigtolit((vss_uint8*)pa->y, (vss_uint8 *)pa->y, blen);
 	
 	return RET_SM2_GENKEY_SUCCESS; 
}	

/***********************************************
Description:	sm2_gensig SM2  generate signature data

Return Value:	RET_SM2_RAND_ERROR: the return value denotes the random data is wrong
				RET_SM2_GENSIG_ERROR: the return value denotes there is some fault happen 
				RET_SM2_GENSIG_SUCCESS: the return value denotes the function operate successfully
Input:			da: a pointer point to the private key
				rand:  256 bits random data
				content: a pointer point to the context which is e = Hv(M), the length of the content
											is 256 bits, and it will be signed after this function operation
Output:			sig: a pointer point to the signatue data structure
Note:			none
***********************************************/
vss_uint32 sm2_gensig(sm2_sig *sig, vss_uint32* content, vss_uint32 *da, vss_uint32 *sm2_rand)
{
	volatile vss_uint32 ret=RET_SM2_GENSIG_ERROR;

	ecc_point_a kG;
	vss_uint32 kG_x[8],kG_y[8],tmp[8],k[8];

	
	vss_uint32 PriKey_buf[8];
	vss_uint32 sm2_rand_buf[8];
	sm2_fp_para para_buf;
	vss_uint32 a_buf[8];  
	vss_uint32 b_buf[8]; 
	vss_uint32 p_buf[8];
	vss_uint32 n_buf[8];
	sm2_point G_buf;
	vss_uint32 G_bufx[8];
	vss_uint32 G_bufy[8];	
	vss_uint32 HashBuf[8];
	vss_uint32 blen = 0;
	sm2_fp_para g_para;
	
	g_para.a = (vss_uint32*)sm2_G_SM2_a;
	g_para.b = (vss_uint32*)sm2_G_SM2_b;
	g_para.p = (vss_uint32*)sm2_G_SM2_p;
	g_para.n = (vss_uint32*)sm2_G_SM2_n;
	g_para.wlen = SM2_PARA_WLEN;

	blen = (g_para.wlen) * 4;

	calc_bigtolit((vss_uint8*)HashBuf, (vss_uint8*)content, blen);
	calc_bigtolit((vss_uint8*)PriKey_buf, (vss_uint8*)da, blen);
	calc_bigtolit((vss_uint8*)sm2_rand_buf, (vss_uint8*)sm2_rand, blen);
	
	para_buf.a = a_buf;
	para_buf.b = b_buf;
	para_buf.p = p_buf;
	para_buf.n = n_buf;
	para_buf.wlen = g_para.wlen;
	
	G_buf.x = G_bufx;
	G_buf.y = G_bufy;
	
	calc_bigtolit((vss_uint8*)para_buf.a, (vss_uint8 *)g_para.a, blen);
	calc_bigtolit((vss_uint8*)para_buf.b, (vss_uint8 *)g_para.b, blen);
	calc_bigtolit((vss_uint8*)para_buf.p, (vss_uint8 *)g_para.p, blen);
	calc_bigtolit((vss_uint8*)para_buf.n, (vss_uint8 *)g_para.n, blen);
	
	calc_bigtolit((vss_uint8*)G_buf.x, (vss_uint8 *)G_buf.x, blen);
	calc_bigtolit((vss_uint8*)G_buf.y, (vss_uint8 *)G_buf.y, blen);	
	
	kG.x = kG_x;
	kG.y = kG_y;
	
	calc_mod(k, sm2_rand_buf, g_para.wlen, para_buf.n, g_para.wlen);
  		  	

	ret = ecc_pointmul4G(&kG, &G_buf, k, &para_buf);
  		
	if(ret!=RET_ECC_IMPLEMENT_SUCCESS)
	{			
		return RET_SM2_GENSIG_ERROR;
	}			
	

	calc_modadd(sig->r, HashBuf, kG.x, para_buf.n, WORDLENGTH_8);


	if(calc_mem_cmp2(sig->r, 0, WORDLENGTH_8) == CALC_EQUAL)
	{
		return RET_SM2_RAND_ERROR;
	}
	else
	{
		calc_add(kG_y, sig->r, WORDLENGTH_8, k, WORDLENGTH_8);
		if(calc_mem_cmp(kG_y, para_buf.n, WORDLENGTH_8) == CALC_EQUAL)
		{
			return RET_SM2_RAND_ERROR;
		}
	}		

	
	calc_modmul(kG_y, k, PriKey_buf, para_buf.n, WORDLENGTH_8);
	
	calc_modadd(kG_x, kG_y, k, para_buf.n, WORDLENGTH_8);
	
	calc_modinv(kG_x, kG_x, WORDLENGTH_8, para_buf.n, WORDLENGTH_8);
	


	calc_modmul(kG_y, sig->r, kG_y, para_buf.n, WORDLENGTH_8);
	
	calc_modmul(tmp, k, k, para_buf.n, WORDLENGTH_8);
	
	calc_modsub(kG_y, tmp, kG_y, para_buf.n, WORDLENGTH_8);
	

	
	calc_modmul(sig->s, kG_x, kG_y, para_buf.n, WORDLENGTH_8);
	
	if(calc_mem_cmp2(sig->s,0,WORDLENGTH_8)==CALC_EQUAL)
	{
		return RET_SM2_RAND_ERROR;	
	}

	calc_bigtolit((vss_uint8*)sig->r, (vss_uint8*)sig->r, blen);
	calc_bigtolit((vss_uint8*)sig->s, (vss_uint8*)sig->s, blen);
	
	return RET_SM2_GENSIG_SUCCESS;
}
/***********************************************
Description:	SM2  check signature data 

Return Value:	RET_SM2_CHECKSIG_ERROR: the return value denotes the signature data is illegal
				RET_SM2_CHECKSIG_SUCCESS: the return value denotes the signhature data is legal
Input:			*sig:	the address of signature

Output:			none
Note:			N/A
***********************************************/

vss_uint32 sm2_checksig (sm2_sig *sig, sm2_fp_para *para)
{

    vss_uint32 wlen = 8;

    if (calc_mem_cmp2(sig->r, 0, wlen)==CALC_EQUAL || calc_mem_cmp2(sig->s, 0, wlen)==CALC_EQUAL)
    {
        return RET_SM2_CHECKSIG_ERROR;
    }
    if ((calc_mem_cmp(sig->r, para->n, wlen) != CALC_SMALLER) || (calc_mem_cmp(sig->s, para->n, wlen) != CALC_SMALLER))
    {
        return RET_SM2_CHECKSIG_ERROR;
    }
 	  
    return RET_SM2_CHECKSIG_SUCCESS;
	
}

/***********************************************
Description:	SM2  verify signature

Return Value:	RET_SM2_VERIFYSIG_ERROR: 
				RET_SM2_VERIFYSIG_SUCCESS:

Input:			*content:  the data used for signature
				*pb: user's public key
				*sig:	the address of signature
Output:			none
Note:			N/A
***********************************************/

vss_uint32  sm2_verifysig(sm2_sig *sig, vss_uint32 *content, sm2_point *pa)
{
	volatile vss_uint32 ret=RET_SM2_VERIFYSIG_ERROR;
	vss_uint32 Temp[8],SG_x[8],SG_y[8],TP_x[8],TP_y[8];
	ecc_point_a SG,TP;

	vss_uint32 HashBuf[8];
	sm2_sig sig_buf;
	vss_uint32 sig_bufr[8];
	vss_uint32 sig_bufs[8];	
	sm2_point PubKey_buf;
	vss_uint32 PubKey_bufx[8];
	vss_uint32 PubKey_bufy[8];	
	sm2_fp_para para_buf;
	vss_uint32 a_buf[8];  
	vss_uint32 b_buf[8]; 
	vss_uint32 p_buf[8];
	vss_uint32 n_buf[8];
	sm2_point G_buf;
	vss_uint32 G_bufx[8];
	vss_uint32 G_bufy[8];	

	vss_uint32 blen = 0;
	sm2_point g_G;
	sm2_fp_para g_para;

	g_G.x = (vss_uint32*)sm2_G_SM2_Gx;
	g_G.y = (vss_uint32*)sm2_G_SM2_Gy;
	
	g_para.a = (vss_uint32*)sm2_G_SM2_a;
	g_para.b = (vss_uint32*)sm2_G_SM2_b;
	g_para.p = (vss_uint32*)sm2_G_SM2_p;
	g_para.n = (vss_uint32*)sm2_G_SM2_n;
	g_para.wlen = SM2_PARA_WLEN;

	blen = (g_para.wlen) * 4;

	sig_buf.r = sig_bufr;
	sig_buf.s = sig_bufs;

	PubKey_buf.x = PubKey_bufx;
	PubKey_buf.y = PubKey_bufy;

	para_buf.a = a_buf;
	para_buf.b = b_buf;
	para_buf.p = p_buf;
	para_buf.n = n_buf;
	para_buf.wlen = g_para.wlen;
	
	G_buf.x = G_bufx;
	G_buf.y = G_bufy;

	calc_bigtolit((vss_uint8*)sig_buf.r, (vss_uint8*)sig->r, blen);
	calc_bigtolit((vss_uint8*)sig_buf.s, (vss_uint8*)sig->s, blen);

	calc_bigtolit((vss_uint8*)HashBuf, (vss_uint8*)content, blen);

	calc_bigtolit((vss_uint8*)PubKey_buf.x, (vss_uint8*)pa->x, blen);
	calc_bigtolit((vss_uint8*)PubKey_buf.y, (vss_uint8*)pa->y, blen);

	calc_bigtolit((vss_uint8*)para_buf.a, (vss_uint8 *)g_para.a, blen);
	calc_bigtolit((vss_uint8*)para_buf.b, (vss_uint8 *)g_para.b, blen);
	calc_bigtolit((vss_uint8*)para_buf.p, (vss_uint8 *)g_para.p, blen);
	calc_bigtolit((vss_uint8*)para_buf.n, (vss_uint8 *)g_para.n, blen);
	
	calc_bigtolit((vss_uint8*)G_buf.x, (vss_uint8 *)g_G.x, blen);
	calc_bigtolit((vss_uint8*)G_buf.y, (vss_uint8 *)g_G.y, blen);
	
	SG.x = SG_x;
	SG.y = SG_y;
	TP.x = TP_x;
	TP.y = TP_y;

	ret = sm2_checksig (&sig_buf, &para_buf);
	if(ret != RET_SM2_CHECKSIG_SUCCESS)
	{	
		return RET_SM2_VERIFYSIG_ERROR;
	}	
	

	calc_modadd(Temp, sig_buf.s, sig_buf.r, para_buf.n, WORDLENGTH_8);

	if(calc_mem_cmp2(Temp, 0, WORDLENGTH_8)==CALC_EQUAL)
	{
		return RET_SM2_VERIFYSIG_ERROR;
	}

	ret = ecc_pointmul4G(&SG, &G_buf, sig_buf.s, &para_buf);
	
	if(ret!=RET_ECC_IMPLEMENT_SUCCESS)
	{
		return RET_SM2_VERIFYSIG_ERROR;
	}

	ret = ecc_pointmul(&TP, &PubKey_buf, Temp, &para_buf);
	
	if(ret!=RET_ECC_IMPLEMENT_SUCCESS)
	{
		return RET_SM2_VERIFYSIG_ERROR;
	}
			
	if((calc_mem_cmp(SG.x, TP.x, WORDLENGTH_8) == CALC_EQUAL) && (calc_mem_cmp(SG.y, TP.y, WORDLENGTH_8) == CALC_EQUAL))
	{
		ret = ecc_pointdbl(&TP, &SG, &para_buf);
		
		if(ret!=RET_ECC_IMPLEMENT_SUCCESS)
		{
			return RET_SM2_VERIFYSIG_ERROR;
		}
	}	
	else
	{
		ret = ecc_pointadd (&TP, &TP, &SG, &para_buf);
		
		if(ret!=RET_ECC_IMPLEMENT_SUCCESS)
		{
			return RET_SM2_VERIFYSIG_ERROR;
		}
	}
		

	calc_modadd(Temp, HashBuf, TP.x, para_buf.n, WORDLENGTH_8);
	
	if(calc_mem_cmp(Temp, sig_buf.r, WORDLENGTH_8) != CALC_EQUAL)
	{		
		return RET_SM2_VERIFYSIG_ERROR;
	}	
	
	return RET_SM2_VERIFYSIG_SUCCESS;
	
}
/***********************************************
Description:	SM2  encrypt funtion, the step of generate C1 data
				and generate (x2|y2)

Return Value:	RET_SM2_RAND_ERROR:
				RET_SM2_ENC_ERROR:
				RET_SM2_ENC_SUCCESS:
Input:			*pb: user's public key
				*rand: 256 bits random data
				
Output:			*c1: the address of C1
				*kdf_para:	the address of (x2|y2)
Note:			N/A
***********************************************/
vss_uint32 sm2_enc_c1_calc(sm2_point *c1, sm2_point *kdf_para, sm2_point *pb, vss_uint32 *sm2_rand, \
sm2_point *G, sm2_fp_para *para)
{
	volatile vss_uint32 ret=RET_SM2_ENC_ERROR;
	ecc_point_a ecc_result,ecc_buf,ecc_pb;
	vss_uint32 k[8];
	
	ecc_result.x = c1->x;
	ecc_result.y = c1->y;
	ecc_buf.x = kdf_para->x;
	ecc_buf.y = kdf_para->y;
	ecc_pb.x = pb->x;
	ecc_pb.y = pb->y;



	calc_mod(k, sm2_rand, para->wlen, para->n, para->wlen);


  		
	ret = ecc_pointmul(&ecc_result, G, k, para);
	
	if(ret != RET_ECC_IMPLEMENT_SUCCESS)
	{
		return RET_SM2_ENC_ERROR;
	}	



	ret = ecc_pointmul(&ecc_buf, &ecc_pb, k, para);
	
	if(ret != RET_ECC_IMPLEMENT_SUCCESS)
	{
		return RET_SM2_ENC_ERROR;
	}	

	return RET_SM2_ENC_SUCCESS;
	
}

vss_uint32 sm2_enc_c1(sm2_point *c1, sm2_point *kdf_para, sm2_point *pb, vss_uint32 *sm2_rand)
{
	vss_uint32 ret;
	sm2_point pb_buf;
	vss_uint32 pb_bufx[8];
	vss_uint32 pb_bufy[8];
	vss_uint32 sm2_rand_buf[8];
	sm2_point G_buf;
	vss_uint32 G_bufx[8];
	vss_uint32 G_bufy[8];
	sm2_fp_para para_buf;
	vss_uint32 a_buf[8];  
	vss_uint32 b_buf[8]; 
	vss_uint32 p_buf[8];
	vss_uint32 n_buf[8];
	sm2_point g_G;
	sm2_fp_para g_para;
	vss_uint32 blen = 0;

	g_G.x = (vss_uint32*)sm2_G_SM2_Gx;
	g_G.y = (vss_uint32*)sm2_G_SM2_Gy;
	
	g_para.a = (vss_uint32*)sm2_G_SM2_a;
	g_para.b = (vss_uint32*)sm2_G_SM2_b;
	g_para.p = (vss_uint32*)sm2_G_SM2_p;
	g_para.n = (vss_uint32*)sm2_G_SM2_n;
	g_para.wlen = SM2_PARA_WLEN;

	blen = (g_para.wlen) * 4;
	
	pb_buf.x = pb_bufx;
	pb_buf.y = pb_bufy;

	G_buf.x = G_bufx;
	G_buf.y = G_bufy;

	para_buf.a = a_buf;
	para_buf.b = b_buf;
	para_buf.p = p_buf;
	para_buf.n = n_buf;
	para_buf.wlen = g_para.wlen;

	calc_bigtolit((vss_uint8*)pb_buf.x, (vss_uint8*)pb->x, blen);
	calc_bigtolit((vss_uint8*)pb_buf.y, (vss_uint8*)pb->y, blen);

	calc_bigtolit((vss_uint8*)sm2_rand_buf, (vss_uint8*)sm2_rand, blen);

	calc_bigtolit((vss_uint8*)para_buf.a, (vss_uint8 *)g_para.a, blen);
	calc_bigtolit((vss_uint8*)para_buf.b, (vss_uint8 *)g_para.b, blen);
	calc_bigtolit((vss_uint8*)para_buf.p, (vss_uint8 *)g_para.p, blen);
	calc_bigtolit((vss_uint8*)para_buf.n, (vss_uint8 *)g_para.n, blen);
	
	calc_bigtolit((vss_uint8*)G_buf.x, (vss_uint8 *)g_G.x, blen);
	calc_bigtolit((vss_uint8*)G_buf.y, (vss_uint8 *)g_G.y, blen);	
	
	ret = sm2_enc_c1_calc(c1, kdf_para, &pb_buf, sm2_rand_buf, &G_buf, &para_buf);

	calc_bigtolit((vss_uint8*)c1->x, (vss_uint8*)c1->x, blen);
	calc_bigtolit((vss_uint8*)c1->y, (vss_uint8*)c1->y, blen);
	
	calc_bigtolit((vss_uint8*)kdf_para->x, (vss_uint8*)kdf_para->x, blen);
	calc_bigtolit((vss_uint8*)kdf_para->y, (vss_uint8*)kdf_para->y, blen);
	
	return ret;
}

/***********************************************
function: Key Derivation Functions. KDF(Z,klen)
param: - input a pointer to the input shared data Z.
           -ilen  the length of input data in bytes.  
           -olen the bit length of output key (klen).
           -output a pointer to the output key.
return:  none
note: 1. olen must be multiple of 8. The maximum ilen is 128.
        2. If ilen is not the multiple of 4, we must pad 0 after the last byte.
              e.g. original data:        xxxxxxxx xxxxxxxx xx
              input data:            xxxxxxxx xxxxxxxx xx000000
              ilen: 9
              ilen  <= 64byte
        3. output buffer[0] is LSB.

***********************************************/
static void sm2_kdf(vss_uint8 *output, vss_uint32 olen, sm2_point *input, vss_uint32 *ct)
{
	vss_uint8 i,j;
	vss_uint8 tmp[72];
	vss_uint8 *p, *r;
	vss_uint32 result[8];
	vss_uint32 ilen = 64;
	
	SM3_CTX_T context;


	if(olen != 0)
		sm2_kdf_len = 1;
	else
		return;

	mem_set8(tmp, 0, 72);
	mem_set(result, 0, 8);

	sm2_kdf_len = sm2_kdf_len +( olen/256);

	r = (vss_uint8*)input->x;
	for(j=0;j<32;j++)

		tmp[j] = r[j];
	r = (vss_uint8*)input->y;
	for(;j<64; j++)

		tmp[j] = r[j-32];		

	tmp[ilen] = (vss_uint8)((*ct)>>24);
	tmp[ilen + 1] = (vss_uint8)((*ct)>>16);
	tmp[ilen + 2] = (vss_uint8)((*ct)>>8);
	tmp[ilen + 3] = (vss_uint8)(*ct);

	p=output;
	r=(vss_uint8 *)result;

	for(i=1;i<=sm2_kdf_len;i++)
	{		
		if(i==sm2_kdf_len)
		{
			sm3_init(&context);
			sm3_update(&context, tmp, ilen + 4);
			sm3_final((vss_uint8*)result, &context);
			for(j=0;j<(olen%256)/8;j++)
			{
				p[j] = r[j];
			}
		}
		else
		{
			sm3_init(&context);
			sm3_update(&context, tmp, ilen + 4);
			sm3_final((vss_uint8*)result, &context);
			for(j=0;j<32;j++)
				p[j] = r[j];
			p = p + 32;
			*ct = *ct + 1;
			tmp[ilen] = (vss_uint8)((*ct)>>24);
			tmp[ilen + 1] = (vss_uint8)((*ct)>>16);
			tmp[ilen + 2] = (vss_uint8)((*ct)>>8);
			tmp[ilen + 3] = (vss_uint8)(*ct);		
		}		
	}
}

/***********************************************
Description:	SM2�ֲ����ܣ����²������Ĳ�
				�õ���һ���ֵ�������ݡ�

Return Value:	RET_SM2_ENC_ERROR:
				RET_SM2_ENC_SUCCESS:
Input:			*content:	�����������ָ�룬(���)
				data_bitlen: 	���ĳ���(bit)  <= 2048bit
				kdf_para: the address of (x2|y2)
Output:			*c2:	���ֵ��������ָ��(C2), (���).
Note:			�ò������ĵ���ݳ��ȱ���С�ڻ����2048bit
***********************************************/
vss_uint32 sm2_enc_c2(vss_uint32 *c2, vss_uint32 *content, vss_uint32 data_bitlen, sm2_point *kdf_para, vss_uint32 *ct)
{
	volatile vss_uint32 ret = RET_SM2_ENC_ERROR;
	ret = sm2_c2(c2, content, data_bitlen, kdf_para, ct);

	if( ret!= RET_SM2_SUCCESS)
	{
		return RET_SM2_ENC_ERROR;
	}
		
	return RET_SM2_ENC_SUCCESS;
}
/***********************************************
Description:	SM2 decrypt verify C1 data and generate (x2|y2)

Return Value:	0:�ɹ���-1:ʧ��
Input:			*c1:  the struct point of C1
				*da:	user's privatekey, use for generate (x2|y2)
				*rand: 32 bits random data
Output:			*intBuf:	the address  for (x2|y2)
Note:			C1 is a ECC_point.
***********************************************/

vss_uint32  sm2_dec_c1_calc(sm2_point *kdf_para, sm2_point *c1, vss_uint32 *da, vss_uint32 *sm2_rand, \
sm2_point *G, sm2_fp_para *para)
{
	volatile vss_uint32 ret = RET_SM2_DEC_ERROR;
	ecc_point_a ecc_content,ecc_result;

	ecc_content.x = c1->x;
	ecc_content.y = c1->y;
	ecc_result.x = kdf_para->x;
	ecc_result.y = kdf_para->y;

	

	ret = ecc_verifypoint(para, &ecc_content);
	
	if(ret != RET_ECC_IMPLEMENT_SUCCESS)
	{
		return RET_SM2_DEC_ERROR;
	}
	

	ret = ecc_pointmul(&ecc_result, &ecc_content, da, para);	
	
	if(ret != RET_ECC_IMPLEMENT_SUCCESS)
	{
		return RET_SM2_DEC_ERROR;
	}

	return RET_SM2_DEC_SUCCESS;
	
}

vss_uint32  sm2_dec_c1(sm2_point *kdf_para, sm2_point *c1, vss_uint32 *da, vss_uint32 *sm2_rand)
{
	vss_uint32 ret;
	sm2_point c1_buf;
	vss_uint32 c1_bufx[8];
	vss_uint32 c1_bufy[8];
	vss_uint32 da_buf[8];
	sm2_point G_buf;
	vss_uint32 G_bufx[8];
	vss_uint32 G_bufy[8];
	sm2_fp_para para_buf;
	vss_uint32 a_buf[8];  
	vss_uint32 b_buf[8]; 
	vss_uint32 p_buf[8];
	vss_uint32 n_buf[8];
	sm2_point g_G;
	sm2_fp_para g_para;
	vss_uint32 blen = 0;

	g_G.x = (vss_uint32*)sm2_G_SM2_Gx;
	g_G.y = (vss_uint32*)sm2_G_SM2_Gy;
	
	g_para.a = (vss_uint32*)sm2_G_SM2_a;
	g_para.b = (vss_uint32*)sm2_G_SM2_b;
	g_para.p = (vss_uint32*)sm2_G_SM2_p;
	g_para.n = (vss_uint32*)sm2_G_SM2_n;
	g_para.wlen = SM2_PARA_WLEN;

	blen = (g_para.wlen) * 4;

	c1_buf.x = c1_bufx;
	c1_buf.y = c1_bufy;
	
	G_buf.x = G_bufx;
	G_buf.y = G_bufy;

	para_buf.a = a_buf;
	para_buf.b = b_buf;
	para_buf.p = p_buf;
	para_buf.n = n_buf;
	para_buf.wlen = g_para.wlen;

	calc_bigtolit((vss_uint8*)c1_buf.x, (vss_uint8 *)c1->x, blen);
	calc_bigtolit((vss_uint8*)c1_buf.y, (vss_uint8 *)c1->y, blen);	

	calc_bigtolit((vss_uint8*)da_buf, (vss_uint8 *)da, blen);

	calc_bigtolit((vss_uint8*)G_buf.x, (vss_uint8 *)g_G.x, blen);
	calc_bigtolit((vss_uint8*)G_buf.y, (vss_uint8 *)g_G.y, blen);	

	calc_bigtolit((vss_uint8*)para_buf.a, (vss_uint8 *)g_para.a, blen);
	calc_bigtolit((vss_uint8*)para_buf.b, (vss_uint8 *)g_para.b, blen);
	calc_bigtolit((vss_uint8*)para_buf.p, (vss_uint8 *)g_para.p, blen);
	calc_bigtolit((vss_uint8*)para_buf.n, (vss_uint8 *)g_para.n, blen);	
	
	ret = sm2_dec_c1_calc(kdf_para, &c1_buf, da_buf, sm2_rand, &G_buf, &para_buf);
	
	calc_bigtolit((vss_uint8*)kdf_para->x, (vss_uint8*)kdf_para->x, blen);
	calc_bigtolit((vss_uint8*)kdf_para->y, (vss_uint8*)kdf_para->y, blen);
	
	return ret;	
}	

/***********************************************
Description:	SM2 decrypt generate plaintext

Return Value:	0:�ɹ���-1:ʧ��
Input:			*c2:  the address of C2
				data_bitLen: the bit length of C2
				*kdf_para:	the address  for (x2|y2)

Output:			*result:	the address of paintext
Note:			������ĵĳ����Ϊ2048bits
***********************************************/
vss_uint32 sm2_dec_c2(vss_uint32 *result,  vss_uint32 *c2, vss_uint32 data_bitlen, sm2_point *kdf_para, vss_uint32 *ct)
{
	volatile vss_uint32 ret = RET_SM2_DEC_ERROR;
	ret = sm2_c2(result, c2, data_bitlen, kdf_para, ct);

	if( ret != RET_SM2_SUCCESS)
	{
		return RET_SM2_DEC_ERROR;
	}
	
	return RET_SM2_DEC_SUCCESS;
}


vss_uint32 sm2_c2(vss_uint32 *result, vss_uint32 *content, vss_uint32 data_bitlen, sm2_point *buf, vss_uint32 *ct)
{
	vss_uint32 t[8];
	vss_uint32 idx;
	vss_uint32 i;

	vss_uint32 blocknum = data_bitlen/256;
	vss_uint32 remained_bit = data_bitlen%256;
	vss_uint32 *res,*con;
	
	mem_set(t, 0xffffffff, 8);
	res = result;
	con = content;

    for(i = 0; i < blocknum; i++)
    {	
      
    	sm2_kdf((vss_uint8*)t, 256, buf, ct);


    	if(calc_mem_cmp0(t, 8) == CALC_EQUAL)
    		return RET_SM2_FAILED;
    		

    	for(idx= 0; idx < 8; idx++)
    	{
    		res[idx] = t[idx] ^ con[idx];
    	}
    	con = con + 8;
    	res = res + 8;
    }	
    if(remained_bit)
    {
        blocknum = remained_bit/32;
        if(remained_bit%32)
            blocknum++;
      
    	sm2_kdf((vss_uint8*)t, remained_bit, buf, ct);

    	

    	
	    if(calc_mem_cmp0(t, blocknum) == CALC_EQUAL)
	    	return RET_SM2_FAILED;
	    		

    	for(idx= 0; idx < blocknum; idx++)
    	{
    		res[idx] = t[idx] ^ con[idx];
    	}
    }
	
	calc_mem_set(t, 0, 8);
	
	return RET_SM2_SUCCESS;
}

vss_uint8 sm2_pri_dec(vss_uint8 sn, vss_uint32* priKey, vss_uint8* in, vss_uint32 inLen, vss_uint8* out)
{
	sm2_point c1;
	sm2_point kdf_para;
	vss_uint32 ret;
	vss_uint8 *temp;
	vss_uint32 data_bitlen;
	vss_uint32 data_len;
	vss_uint8 *c2;
	vss_uint8 *data = out; 
	vss_uint8 c3[32];
	vss_uint8 hash[32];
	vss_uint32 c1_data[16];
	vss_uint32 kdf_para_data[16];
	vss_uint32 sm2_ct;				
	SM3_CTX_T sm3_ctx;
	
	temp = in;

	data_len = inLen;
	if (data_len <= 96 || data_len > 160)
	{
		return ERR_LEN_INVALID;
	}
		
	data_len = data_len - 96;
	
	mem_cpy8((vss_uint8*)c1_data, temp, 64);	
	temp += 64;
	c2 = temp;
	temp += data_len;
	mem_cpy8((vss_uint8*)c3, temp, 32);
	
	c1.x = (vss_uint32*)c1_data;
	c1.y = (vss_uint32*)(c1_data + SM2_PARA_WLEN);
	kdf_para.x = (vss_uint32*)kdf_para_data;
	kdf_para.y = (vss_uint32*)(kdf_para_data + SM2_PARA_WLEN);
	ret = sm2_dec_c1(&kdf_para, &c1, priKey, sm2_pri_dec_rand);
	
	if(RET_SM2_DEC_SUCCESS != ret)
	{
		return ERR_CALC_FAIL;
	}
	
	sm2_ct = 1;
	data_bitlen = data_len*8;
	ret = sm2_dec_c2((vss_uint32*)data, (vss_uint32*)c2, data_bitlen, &kdf_para, &sm2_ct);
	if(RET_SM2_DEC_SUCCESS != ret)
	{
		return ERR_CALC_FAIL;
	}

	sm3_init(&sm3_ctx);
	sm3_update(&sm3_ctx, (vss_uint8*)kdf_para.x, 32);
	sm3_update(&sm3_ctx, (vss_uint8*)data, data_len);
	sm3_update(&sm3_ctx, (vss_uint8*)kdf_para.y, 32);
	sm3_final(hash, &sm3_ctx);	

	if (mem_cmp8(hash, c3, 32) != 0)
	{
		return ERR_DATA_INVALID;
	}
	
	return 0;
}

vss_uint8 sm2_pub_enc(vss_uint8 sn, vss_uint32* x, vss_uint32* y, vss_uint8* in, vss_uint32 inLen, vss_uint8* out)
{
	sm2_point c1;
	sm2_point kdf_para;
	sm2_point pa;
	vss_uint32 ret;
	vss_uint8 c2[64];
	vss_uint8 c3[64];
	vss_uint8 *temp;
	vss_uint32 data_bitlen;
	vss_uint32 c1_data[16];
	vss_uint32 kdf_para_data[16];
	vss_uint32 sm2_ct;
	SM3_CTX_T sm3_ctx;


	temp = out;
		
	if (inLen > 64)
		return ERR_LEN_INVALID;
	
	c1.x = (vss_uint32*)c1_data;
	c1.y = (vss_uint32*)(c1_data + SM2_PARA_WLEN);
	kdf_para.x = (vss_uint32*)kdf_para_data;
	kdf_para.y = (vss_uint32*)(kdf_para_data + SM2_PARA_WLEN);
	pa.x = (vss_uint32*)x;
	pa.y = (vss_uint32*)y;
	
	ret = sm2_enc_c1(&c1, &kdf_para, &pa, (vss_uint32*)sm2_pub_enc_rand);
	if(RET_SM2_ENC_SUCCESS != ret)
	{
		return ERR_CALC_FAIL;
	}	
	
	sm2_ct = 1;
	data_bitlen = inLen * 8;
	mem_set8(c2, 0, 64);
	ret = sm2_enc_c2((vss_uint32*)c2, (vss_uint32 *)in, data_bitlen, &kdf_para, (vss_uint32*)&sm2_ct);
	if(RET_SM2_ENC_SUCCESS != ret)
	{
		return ERR_CALC_FAIL;
	}

	sm3_init(&sm3_ctx);
	sm3_update(&sm3_ctx, (vss_uint8*)kdf_para.x, 32);
	sm3_update(&sm3_ctx, (vss_uint8*)in, inLen);
	sm3_update(&sm3_ctx, (vss_uint8*)kdf_para.y, 32);
	sm3_final(c3, &sm3_ctx);

	temp = out;
	mem_cpy8(temp, (vss_uint8*)c1_data, 64);
	temp += 64;
	mem_cpy8(temp, (vss_uint8*)c2, inLen);
	temp += inLen;
	mem_cpy8(temp, (vss_uint8*)c3, 32);
	return 0;
}

#endif

#pragma section



