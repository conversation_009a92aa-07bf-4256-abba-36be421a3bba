<?xml version='1.0'?>
<datamodel version="3.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/08/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/08/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd" >
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Wdg" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Wdg" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/AURIX/Wdg"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuildSelectable">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="WdgGeneral" type="IDENTIFIABLE">
                <d:var name="WdgDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="WdgDisableAllowed" type="BOOLEAN" value="true"/>
                <d:var name="WdgIndex" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="WdgTriggerLocation" type="FUNCTION-NAME" >
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="WdgVersionInfoApi" type="BOOLEAN" value="true"/>
                <d:var name="WdgPBFixedAddress" type="BOOLEAN" value="true"/>
                <d:var name="WdgMaxTimeout" type="FLOAT" value="50.0"/>
                <d:var name="WdgInitialTimeout" type="FLOAT" value="40.0"/>
                <d:var name="WdgDebugSupport" type="BOOLEAN" value="true"/>
                <d:var name="WdgRunArea" type="ENUMERATION" value="ROM">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="WdgUnlockRestriction" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="WdgRunningInUser0Mode" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="WdgUserModeInitApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="WdgUserModeRuntimeApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:lst name="WdgSettingsConfig" type="MAP">
                <d:ctr name="WdgSettingsConfig_0" type="IDENTIFIABLE">
                  <d:ref name="WdgSystemClockRef" type="REFERENCE" 
                         value="ASPath:/Mcu/Mcu/McuModuleConfiguration_0/McuClockSettingConfig_0/McuClockReferencePoint"/>
                  <d:var name="WdgDefaultMode" type="ENUMERATION" 
                         value="WDGIF_SLOW_MODE"/>
                  <d:ctr name="WdgSettingsFast" type="IDENTIFIABLE">
                    <d:var name="Wdg0FastModeTimeoutValue" type="FLOAT" 
                           value="110.0"/>
                  </d:ctr>
                  <d:ctr name="WdgSettingsOff" type="IDENTIFIABLE"/>
                  <d:ctr name="WdgSettingsSlow" type="IDENTIFIABLE">
                    <d:var name="Wdg0SlowModeTimeoutValue" type="FLOAT" 
                           value="500.0"/>
                  </d:ctr>
                  <d:ctr name="WdgInitialPassword" type="IDENTIFIABLE">
                    <d:var name="Wdg0InitialPassword" type="INTEGER" value="240"/>
                  </d:ctr>
                  <d:ctr name="WdgTimerCheckTolerance" type="IDENTIFIABLE">
                    <d:var name="Wdg0TimerTolerance" type="INTEGER" value="100"/>
                  </d:ctr>
                  <d:ctr name="WdgGtmSettings" type="IDENTIFIABLE">
                    <d:var name="Wdg0SlowServiceGtmCbkTime" type="FLOAT" 
                           value="15.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="Wdg0FastServiceGtmCbkTime" type="FLOAT" 
                           value="7.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="Wdg0ServiceGtmChannelRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration_0/GtmConfiguration_0/Tom_0/TomChannel_15"/>
                  </d:ctr>
                  <d:lst name="WdgExternalConfiguration" type="MAP"/>
                </d:ctr>
              </d:lst>
              <d:ctr name="WdgSafety" type="IDENTIFIABLE">
                <d:var name="Wdg0SafetyEnable" type="BOOLEAN" value="false"/>
                <d:var name="WdgSetOffModeApi" type="BOOLEAN" value="false"/>
                <d:var name="WdgEnableTimecheck" type="BOOLEAN" value="false"/>
              </d:ctr>
              <d:lst name="WdgDemEventParameterRefs" type="MAP"/>
              <d:ctr name="WdgPublishedInformation" type="IDENTIFIABLE">
                <d:var name="WdgTriggerMode" type="ENUMERATION" 
                       value="WDG_TOGGLE">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArMajorVersion" type="INTEGER" value="2">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArMinorVersion" type="INTEGER" value="5">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="3">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="5">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="102">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="17">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="Scu">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="Release" type="STRING" value="_TRICORE_TC234">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
