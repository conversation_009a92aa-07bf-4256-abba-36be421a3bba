#ifndef _CRYPTO_123_20_CFG_H_
#define _CRYPTO_123_20_CFG_H_

#include "vsstype.h"
#include "sm4.h"
#include "aes.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef union
{
	sm4_context sm4_ctx;
	aes_context aes_ctx;
}TSymmCtx;

__attribute__((used)) void cmac_padding(vss_uint8 *lastb, vss_uint8 *pad, vss_uint32 length);
__attribute__((used)) void cmac_leftshift_onebit(vss_uint8 *input,vss_uint8 *output);
__attribute__((used)) void cmac_xor_128(vss_uint8* input1, vss_uint8* input2, vss_uint8* output);

__attribute__((used)) vss_uint32 CMacData(vss_uint8 alg, vss_uint32 key_len, vss_uint8 *key, vss_uint8 *input, vss_uint32 length, vss_uint8 *mac );
__attribute__((used)) vss_uint32 cmac_generate_subkey(vss_uint8 alg, TSymmCtx* ctx, vss_uint8 *K1, vss_uint8 *K2);


#ifdef __cplusplus
}
#endif
#endif



