
// Include the CPU specific .lsl file
// The CPU is specified by the __CPU__ macro
#ifndef __CPU__
# error No CPU defined, please link with -D__CPU__=<cpu>
#endif

#define BOOTLOADER 0x80000020
#define BOOTLOADER_SIZE 0x20000-0x20

#define RESERVED_FLASH 0x80020000
#define RESERVED_FLASH_SIZE 0x20000

#define CAL1_RUN_ADDR 0x70020000
#define CAL1_LOAD_ADDR 0x80040000
#define CAL1_RESERVE_SIZE 0x8000

#define CAL2_RUN_ADDR 0x70028000
#define CAL2_LOAD_ADDR 0x80048000
#define CAL2_RESERVE_SIZE 0x8000

#define PMU_PFLASH0 0x80070000
#define APP_PFLASH0_SIZE 300k

#define EOL_ADDR 0x801C0000
#define EOL_RESERVE_SIZE 256k


#define CPU0_PMI_PSPR 0x70100100


// 100 bytes of DSPR is reserved for sbst_tc0_dspr
#define CPU0_DMI_DSPR 0x70000100



#define __QUOTE_ME2(x) #x
#define __QUOTE_ME(x) __QUOTE_ME2(x)
#define CPULSL_FILE __QUOTE_ME(__CPU__.lsl)

#include CPULSL_FILE

// Define default external memory
// These memory definitions can be replaced by the Debug Target Configuration Wizard

#ifndef HAS_ON_CHIP_FLASH       // On-chip flash is located at 0x[8a]0000000
memory xrom_a (tag="External ROM",tag="dtc")
{
        mau = 8;
        size = 1M;
        type = rom;
         map     cached (dest=bus:sri, dest_offset=0x80000000,           size=1M);
         map not_cached (dest=bus:sri, dest_offset=0xa0000000, reserved, size=1M);
}
#endif

section_layout mpe:tc0:linear
{
  
  group  (ordered, run_addr=0x80000000)
  {
    select ".rodata.BMD_HDR_CONST_FAR_UNSPECIFIED";
  }
}

//section_layout :tc0:linear
//{
// 
//  group BOOT_CODE (ordered, run_addr=BOOTLOADER)
//  {
//     reserved "bootload" (alloc_allowed = absolute, size = BOOTLOADER_SIZE);
//  } 
//}

section_layout :tc0:linear
{
  group BOOT_CODE (ordered, run_addr=BOOTLOADER)
  {
     reserved "reserved_bootload" (alloc_allowed = absolute, size = BOOTLOADER_SIZE);
  } 
  
  group RESERVED_GROUP (ordered, run_addr=RESERVED_FLASH)
  {
     reserved "reserved_flash" (alloc_allowed = absolute, size = RESERVED_FLASH_SIZE);
  } 
  
   group RESERVED_CAL1_LOAD (ordered, run_addr=CAL1_LOAD_ADDR)
  {
     reserved "reserved_cal1" (alloc_allowed = absolute, size = CAL1_RESERVE_SIZE);
  } 
  
  group RESERVED_CAL2_LOAD (ordered, run_addr=CAL2_LOAD_ADDR)
  {
     reserved "reserved_cal2" (alloc_allowed = absolute, size = CAL2_RESERVE_SIZE);
  } 
  group RESERVED_EOL (ordered, run_addr=EOL_ADDR)
  {
     reserved "reserved_eol" (alloc_allowed = absolute, size = EOL_RESERVE_SIZE);
  } 
}




section_layout mpe:tc0:linear
{
	group MY_CAL1_RAM(ordered, run_addr=CAL1_RUN_ADDR)
	{
		section "mycal1_ram" ( size = CAL1_RESERVE_SIZE, attributes = rw )
		{
			select ".data.CAL_AREA1*";
		}
	
	}
	group MY_CAL1_FLASH (ordered, load_addr=CAL1_LOAD_ADDR)
	{
			select ".data.CAL_AREA1*";
	}
}

section_layout mpe:tc0:linear
{
	group MY_CAL2_RAM(ordered, run_addr=CAL2_RUN_ADDR)
	{
		section "mycal2_ram" ( size = CAL2_RESERVE_SIZE-0x2000, attributes = rw )
		{
			select ".data.CAL_AREA2*";
		}
	
	}
	group MY_CAL2_FLASH (ordered, load_addr=CAL2_LOAD_ADDR)
	{
		select ".data.CAL_AREA2*";
	}
}


section_layout mpe:tc0:linear
{
  group CPU0_DATA (ordered, load_addr=0x80056000)
  {
    //section "myoutput_data" ( size = 16k,attributes = r)
    //{  
      select ".data*";  
    //}
  }

}

section_layout mpe:tc0:linear
{
  group CPU0_PRIVATE_CODE (ordered, run_addr=PMU_PFLASH0)
  {
    section "myoutput" ( size = APP_PFLASH0_SIZE, attributes = rx ,fill=0x00)
    {
//      select ".text.CPU0.Private*";
      select ".text*"; 
      select ".rodata*";   
    }
  }

}


//CORE SPECIFIC DATA

section_layout :tc0:linear
{
  group  (ordered, align = 64, attributes=rw, run_addr=(0xd0000000)) 
          reserved "sbst_tc0_dspr" (size = 0x100);                
  group  (ordered, align = 64, attributes=rw, run_addr=(0xc0000000)) 
          reserved "sbst_tc0_pspr" (size = 0x40);                
}

section_layout :tc0:linear
{
  group  (ordered, align = 32, attributes=rw, run_addr=(0x70002000))
            reserved "standby_ram_dspr0" (size = 0x44);   
}


section_layout :tc0:linear
{
  group  (ordered, run_addr=0x70001080)
  {
    select ".data.FLS_RESERVED";
  }
}


section_layout :tc0:linear
{
  group( run_addr = mem:mpe:dspr0, ordered ) stack "ustack_tc0" (size = (USTACK_TC0));
  group( run_addr = mem:mpe:dspr0, ordered ) stack "istack_tc0" (size = (ISTACK_TC0));
}

section_layout mpe:tc0:linear
{
  group CPU0_PRIVATE_DATA (ordered, run_addr=mem:mpe:dspr0  )
  {
    select ".data.CPU0.Private*";
    select ".zdata.CPU0.Private*";
    select ".zdata*";
    select ".bss.CPU0.Private*";
  }
}

section_layout mpe:tc0:abs18
{
  group DEFAULT_ZBSS (ordered, run_addr=mem:mpe:dspr0  )
  {
    select ".zbss*";
  }
}
section_layout mpe:tc0:linear
{
  group DEFAULT_SECTION (ordered, run_addr=mem:mpe:dspr0  )
  {
    select ".bss.Dma_Demo.ExtBuffer";
    select ".data*";
    select ".zdata*";
    select ".bss*";
  }
}


