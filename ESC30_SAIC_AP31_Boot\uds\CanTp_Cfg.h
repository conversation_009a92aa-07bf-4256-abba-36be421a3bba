/*============================================================================*/
/*** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  
 *  @file       <CanTp_Cfg.h>
 *  @brief      <The CanTp Configration file>
 *  
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *  
 *  <AUTHOR> Yu>
 *  @date       <2012-12-28>
 */
/*============================================================================*/

#ifndef CANTP_CFG_H
#define CANTP_CFG_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20121228    Tommy Yu     Initial version
 */
/*============================================================================*/

/* to get the GPT_PERIOD_TIME */
//#include "Gpt_Cfg.h"

/*=======[M A C R O S]========================================================*/

/** Minimum time the sender shall wait between transmissions of two N-PDU */
/* @type:uint8 range:0~255 note:uint-CF frame number */
#define CANTP_BS_SIZE     ((uint8)0u)

/* @type:uint8 range:0~255 note:uint-ms */
#define CANTP_ST_MIN     ((uint8)0u)

/** N_As timeout for transmission of any CAN frame */
/* @type:uint8 range:0~255 note:uint-ms */
#define CANTP_N_AS       ((uint8)25u)	

/** Timeout for transmission of a CAN frame (ms) */
/* @type:uint8 range:0~255 note:uint-ms */
#define CANTP_N_AR       ((uint8)25u)

/** N_Bs timeout */
/* @type:uint8 range:0~255 note:uint-ms */
#define CANTP_N_BS       ((uint8)25u)//������ݴ�ͨˢ�¹淶������� 2019-5-20

/** Time out for consecutive frames (ms) */
/* @type:uint8 range:0~255 note:uint-ms */
#define CANTP_N_CR       ((uint8)30u)  //20191106 ��25�ĵ�30

/* @type:define range:GPT_PERIOD_TIME note:auto generate */
#ifndef GPT_PERIOD_TIME
#define GPT_PERIOD_TIME    5u   //10u
#endif
#define CANTP_MAIN_TICK GPT_PERIOD_TIME

/** padding value */
/* @type:uint8 range:0x00~0xFF note:NONE */
#define CANTP_PADDING_VALUE (0xAAu)

/* @type:NONE range:NONE note:auto generate */
#define CANTP_PADDING_ON

/* @type:uint8 range:1~255 note:reference to num of CanTp_TxSdu */
#define CANTP_TX_CHANNEL_NUM 	1u

/* @type:uint8 range:1~255 note:reference to num of CanTp_RxSdu */
#define CANTP_RX_CHANNEL_NUM 	2u

#define CANTP_SUPPORT_CANFD FALSE
#endif

/*=======[E N D   O F   F I L E]==============================================*/

