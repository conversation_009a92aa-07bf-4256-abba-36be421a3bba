/*
 * main.c
 *
 *  Created on: 2019-7-1
 *      Author: gaoguanlong
 */

#include "Std_Types.h"
#include "Dio.h"
#include "EcuM.h"
#include "EcuM_Cfg.h"
#include "Stm.h"
#include "Gtm.h"
#include "Gtm_Cfg.h"
#include "Dio_Cfg.h"
#include "Port.h"
#include "CDD_EyeQ_PowerCtrl.h"
#include "CDD_Wdg.h"
#include "main.h"
#include "Spi.h"
#include "Dma.h"
#include "Can_17_MCanP.h"
#include "Irq.h"
#include "Std_Types.h"
#include "Platform_Types.h"
#include "CDD_SpiSlave.h"
#include "EyeQFls_Drive.h"
#include "CanTp.h"
#include "Dcm.h"
#include "Appl.h"
#include "Uds_CanIf.h"
#include "Wdg.h"
#include "EyeQFls_Drive.h"
#include "Fls.h"
#include "CDD_Adc.h"
#include "Vss.h"
#include "Mcu.h"


uint32 u16t_L9960_InitCount;
uint32 u16t_L9960_InitCount1;
uint32 u16t_L9960_WhileCount;
uint8 Wdg_Flag=0;
unsigned int cnt_test=0;
unsigned int cnt_test1=0;
unsigned int cnt_test2=0;
unsigned int cnt_test5=0;
unsigned int cnt_test10=0;
uint32 cnt_test20=0;
uint32 cnt_test50=0;
uint8 Test_status=0;
uint8  FeedDog_Flag=0;
uint8  EyeQ_Flag=0;
uint8  Recover_Flag=0;
uint8 After_Pin=0;
uint16 cnt_test3=0;
uint8 counter_test=0;
uint16 Adc_Res3[8]={0};
uint16 Adc_Res4[8]={0};
uint16 Inr_counter1=0;
uint16 Inr_counter2=0;
uint16 Inr_counter3=0;
uint16 Inr_counter4=0;
uint16 Inr_counter5=0;
uint16 Inr_counter6=0;
uint8  EyeQ_Check=0;
uint8  WDG_FLAG=0;
uint8  AfterRun_Flag=1;
uint16 AfterRun_Count=0;
uint8  Sleep_flag=1;//锟斤拷锟斤拷使锟斤拷时锟斤拷0锟斤拷锟斤拷锟斤拷为锟剿诧拷锟皆达拷锟斤拷某锟斤拷锟�
uint8  EyeQPower=0;
uint8  Can_sta1=0;
uint8  Can_sta2=0;
uint8  EyeQPower_Status=0;
uint8  recover2=0;
uint8  recover3=0;


uint8  Micron_EN_Count=0;


#include "wdg.h"
#include "mcal_src/IfxScu_reg.h"
#include "mcal_src/IfxScu_bf.h"
#include "mcal_src/IfxCpu_reg.h"
#include "mcal_src/IfxCpu_bf.h"
#include "mcal_src/IfxStm_reg.h"

#include "Std_Types.h"
#include "Mcu_Cfg.h"

/*=======[M A C R O S]========================================================*/


/*=======[T Y P E   D E F I N I T I O N S]====================================*/
typedef struct tagPllInitValue
{
	unsigned int uiOSCCON;
	unsigned int uiPLLCON0;
	unsigned int uiPLLCON1;
	unsigned int uiCCUCON0;
	unsigned int uiCCUCON1;
	unsigned int uiCCUCON2;
} TPllInitValue, *PPllInitValue;


#define EXTCLK		(20000000)	/* external oscillator clock (20MHz) */
#define CFG_STM0_CMP0_VALUE		2*500000U //50000U//500000U//500000U // STM0ê±?ó?μ?ê50MHz

static unsigned long system_GetIntClock(void);
static unsigned long system_GetPllClock(void);
/*=======[M A C R O S]========================================================*/
#define SCU_RSTCON   (*((volatile uint32*)0xf0036058))
/* 200/100 MHz @ 20MHz ext. clock */
const TPllInitValue g_PllInitValue_200_100 =
{
	/* OSCCON,	PLLCON0,	PLLCON1,	CCUCON0,	CCUCON1,	CCUCON2 */
	/* 0x000F0118, 0x01017600, 0x00020002, 0x52250101, 0x50012211, 0x40000202 */
	/* OSCCON,	PLLCON0,	PLLCON1,	CCUCON0,	CCUCON1,	CCUCON2 */
	0x00070118, 0x00013A00, 0x00000502, 0x02220122U, 0x58212215U, 0x00000002U
};
static Ifx_SCU * const pSCU = (Ifx_SCU *)&MODULE_SCU;

/*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/
unsigned long SYSTEM_GetStmClock(void)
{
	unsigned long frequency = system_GetIntClock();
	unsigned long divider = pSCU->CCUCON1.B.STMDIV;
	if (0 == divider)
		return 0;
	return (frequency / divider);
}
static unsigned long system_GetIntClock(void)
{
	unsigned long frequency = 0;
	switch (pSCU->CCUCON0.B.CLKSEL)
	{
		default:
		case 0:  /* back-up clock (typ. 100 MHz) */
			frequency = 100000000ul;
			break;
		case 1:	 /* fPLL */
			frequency = system_GetPllClock();
			break;
	}
	return frequency;
}
static unsigned long system_GetPllClock(void)
{
	unsigned int frequency = EXTCLK;	/* fOSC */

	Ifx_SCU_PLLSTAT pllstat = pSCU->PLLSTAT;
	Ifx_SCU_PLLCON0 pllcon0 = pSCU->PLLCON0;
	Ifx_SCU_PLLCON1 pllcon1 = pSCU->PLLCON1;

	if (0 == (pllstat.B.VCOBYST))
	{
		if (0 == (pllstat.B.FINDIS))
		{
			/* normal mode */
			frequency *= (pllcon0.B.NDIV + 1);		/* fOSC*N */
			frequency /= (pllcon0.B.PDIV + 1);		/* .../P  */
			frequency /= (pllcon1.B.K2DIV + 1);		/* .../K2 */
		}
		else	/* freerunning mode */
		{
			frequency = 800000000;		/* fVCOBASE 800 MHz (???) */
			frequency /= (pllcon1.B.K2DIV + 1);		/* .../K2 */
		}
	}
	else	/* prescaler mode */
	{
		frequency /= (pllcon1.B.K1DIV + 1);		/* fOSC/K1 */
	}

	return (unsigned long)frequency;
}

/******************************************************************************/
/*
 * Brief               <Mcu_Init>
 * Sync/Async          <Synchronous>
 * Reentrancy          <Non-Reentrant>
 * Param-Name[in]      <ConfigPtr>
 * Param-Name[out]     <None>
 * Param-Name[in/out]  <None>
 * Return              <None>
 * PreCondition        <None>
 * CallByAPI           <APIName>
 */
/******************************************************************************/
void Mcu11_Init(void)
{
	const PPllInitValue pPllInitValue = &g_PllInitValue_200_100;

	unlock_safety_wdtcon();

	pSCU->OSCCON.U = pPllInitValue->uiOSCCON;//0x000F0118

	while (pSCU->CCUCON1.B.LCK);
	pSCU->CCUCON1.U = pPllInitValue->uiCCUCON1 | (1 << IFX_SCU_CCUCON1_UP_OFF);

	pSCU->PLLCON0.U = pPllInitValue->uiPLLCON0 | (1 << IFX_SCU_PLLCON0_SETFINDIS_OFF);

	pSCU->PLLCON1.U = pPllInitValue->uiPLLCON1;	//0x00020002			/* set K1,K2 divider */    //	K2 =2 k3=0 k1= 2
	pSCU->PLLCON0.U = pPllInitValue->uiPLLCON0 | (1 << IFX_SCU_PLLCON0_CLRFINDIS_OFF);

	lock_safety_wdtcon();

	while (0 == pSCU->PLLSTAT.B.VCOLOCK);

	unlock_safety_wdtcon();

	pSCU->CCUCON0.U = pPllInitValue->uiCCUCON0;
	pSCU->CCUCON1.U = pPllInitValue->uiCCUCON1;
	pSCU->CCUCON1.U = pPllInitValue->uiCCUCON1 | (1 << IFX_SCU_CCUCON1_UP_OFF);//0x50012211  Fcan=Fsource  Fstm=Fsource/2 Fgtm=Fsource/2 OSC is clock source for PLL
	while (pSCU->CCUCON1.B.LCK);

	pSCU->CCUCON2.U = pPllInitValue->uiCCUCON2;
	pSCU->CCUCON2.U = pPllInitValue->uiCCUCON2 | (1 << IFX_SCU_CCUCON2_UP_OFF);//0x40000202 Fbbb
	while (pSCU->CCUCON2.B.LCK);

	pSCU->CCUCON0.U = pPllInitValue->uiCCUCON0 | 0x10000000u | (1 << IFX_SCU_CCUCON0_UP_OFF);//0x52250101    1010010001001010000000100000001  pll is used as clock Fsource
	while (pSCU->CCUCON0.B.LCK);

	lock_safety_wdtcon();

	unlock_safety_wdtcon();
	SCU_RSTCON = 0x00000140;
	lock_safety_wdtcon();
}
void Task_STM_Init(void)
{
//	uint32 Ticksvalue=0x10000;
//	Stm_EnableModule(0);
	unlock_safety_wdtcon();

	while(0U != (SCU_CCUCON1.U >> 31));
	{   /*Wait till ccucon registers can be written with new value */
		/*No "timeout" required, because if it hangs, Safety Endinit will give a trap */
	}
	/*set .UP to 1,.INSEL to 1,fstm = fsource/2*/
	SCU_CCUCON1.U &= 0xFFFFF0FF;
	SCU_CCUCON1.U |= 0x50000200;

//	_safety_endinit_set();
	lock_safety_wdtcon();

	/* prepare compare register */
    STM0_CMP0.U  = STM0_TIM0.U + CFG_STM0_CMP0_VALUE;
    /* Compare Match cfg */
    STM0_CMCON.U |= 0x0000001F;
    /* Interrupt on compare match with CMP0 enabled */
    STM0_ICR.U   |= 0x00000001;
    /* reset interrupt flag */
    STM0_ISCR.U = STM0_ISCR.U | 0x00000001;
}
boolean Gpt_GetFlag(void)
{
//	boolean flag = FALSE;
//
//	Ifx_STM *StmBase = systime_GetStmBase();
//
//	if ((StmBase->ICR.U & 0x00000002UL) != 0x0ul)
//	{
//		StmBase->ISCR.U |= 0x01ul;
//		flag = TRUE;
//		StmBase->CMP[0].U += systime_Reload;
//	}
//
//	return flag;
	  boolean timOut = FALSE;
	  uint32 initialTimerValue, finalTimerValue; /* Judge whether a time-out occured */
	  if (0x02 == (STM0_ICR.U & 0x00000002))
	  {
	    timOut = TRUE; /* Safely reload value for compare, taking care of potential timer wrap-around */
	    do {
	         initialTimerValue = STM0_TIM0.U; // Read initial timer value
	         STM0_CMP0.U = CFG_STM0_CMP0_VALUE + initialTimerValue; // Set new compare value
	         finalTimerValue = STM0_TIM0.U; // Read timer value again to check for wrap-around
	                                        // If the timer value has changed significantly, we may have hit a wrap-around, so try again
	       } while(finalTimerValue < initialTimerValue); /* clear interrupt flag */
	       STM0_ISCR.U = STM0_ISCR.U | 0x00000001;
	   }
	    return timOut;
}
void EyeQ_ErrInr(void)
{
	Inr_counter1++;
}

void CAN1_ErrInr(void)
{
	Inr_counter2++;
}
void LP875701_ErrInr(void)
{
	Inr_counter4++;
}

void LP87563_ErrInr(void)
{
	Inr_counter5++;
}

void Can_InitPort(void)
{
	Dio_WriteChannel(DIO_CHANNEL_15_0,STD_HIGH);     /*CAN1 enable*/
	Dio_WriteChannel(DIO_CHANNEL_15_1,STD_HIGH);

	CDD_IIC_COM_Delay(2000);
	Can_17_MCanP_SetControllerMode (0,CAN_T_START);//锟斤拷锟斤拷使锟斤拷CAN
	Can_17_MCanP_SetControllerMode (1,CAN_T_START);//锟斤拷锟斤拷使锟斤拷CAN
	Can_17_MCanP_SetControllerMode (2,CAN_T_START);//锟斤拷锟斤拷使锟斤拷CAN

}



void  ecal_period_1ms(uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal)
{

	cnt_test1++;


}

void  ecal_period_2ms(uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal)
{

	cnt_test2++;
}

void  ecal_period_5ms(uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal)
{
	cnt_test5++;
}

void  ecal_period_10ms(uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal)
{


	cnt_test10++;
}

void  ecal_period_20ms(uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal)
{

	cnt_test20++;
}

void  ecal_period_50ms(uint8 ModuleType, uint8 ModuleNo, uint8 ChannelNo,uint16 IrqNotifVal)
{
	cnt_test50++;
}

void CDD_UartTransmit(void)
{

}
void CDD_UartRecive(void)
{

}
//extern void Mcu_Init(void);
void main(void)
{

    uint8 i=0;
    Mcu11_Init();
//	EcuM_ConfigType EcuM_ConfigPtr=EcuM_ConfigAlternative[0];
//    Mcu_Init();
//	EcuM_Init(&EcuM_ConfigPtr);

	// Can_17_MCanP_Init(&Can_17_MCanP_ConfigRoot[0]);
	Task_STM_Init();

//	CDD_Wdg_Dis();
	// Can_InitPort();
	CanTp_Init();
    Dcm_Init();

	Vss_Init();
    CanIf_Init();
	Appl_EcuStartup();
//	Irq_ClearAllInterruptFlags();
//	EyeQPower=SM_EyeQPwrUpMainFunc();
//	uint32 count200ms=0;
	while(1)
	{
//		Irq_ClearAllInterruptFlags();
		Uds_CanIf_MainFunction();
//		if(GetTicks10ms()==TRUE)  //Gpt_GetFlag
	    if (TRUE == Gpt_GetFlag())
		{
//			count200ms++;
			Appl_Period_10ms();
//			if((count200ms%20)==0)
//			{
//				CDD_AdcTrigger();
//				CDD_AdcRead();
//			}
		}
//		EEP_MainFunction();
		FL_MainFunction();
	}
}









