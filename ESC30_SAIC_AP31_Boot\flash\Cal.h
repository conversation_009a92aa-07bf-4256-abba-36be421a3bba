/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <Cal.h>
 *  @brief      < function decalrations for Cal>
 *  
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *
 *  <AUTHOR> Chen>
 *  @date       <2012-12-27>
 */
/*============================================================================*/

#ifndef CAL_H
#define CAL_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20121227    Gary       Initial version
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/

#include "Std_Types.h"
#include "SecM.h"
/*37 01 response crc type,0x16=crc16,0x32=crc32*/
#define SECM_S37_CRC_TYPE 0x16
#define SECM_S37_CRC_INIT 0xFFFFu
#define SECM_S37_CRC_XOR  0x00u

/*=======[E X T E R N A L   D A T A]==========================================*/

/*=======[E X T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
extern void Cal_CrcInit(SecM_CRCType *curCrc);

extern void Cal_CrcCal(SecM_CRCType *curCrc,
    const uint8 *buf,
    const uint32 size);

extern void Cal_CrcFinalize(SecM_CRCType *curCrc);


#endif/* endof CAL_H */

/*=======[E N D   O F   F I L E]==============================================*/
