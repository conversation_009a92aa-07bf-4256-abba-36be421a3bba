{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "d:/PGM/PGM/saic-csuv-pgm/CSUV_PGM_FBL", "program": "d:/PGM/PGM/saic-csuv-pgm/CSUV_PGM_FBL/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}