<?xml version='1.0'?>
<datamodel version="3.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/08/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/08/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd" >
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Fee" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Fee" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/AURIX/Fee"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPreCompile">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:lst name="FeeBlockConfiguration" type="MAP">
                <d:ctr name="FeeBlock_ConfigBlock" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="176"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="4"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false"/>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_ConfigBlock_cons0" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="177"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="4"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false"/>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DemAdmin" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="16"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="12"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false"/>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DemPrimary0" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="32"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="30"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false"/>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DemPrimary1" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="48"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="30"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false"/>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DemPrimary2" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="64"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="30"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false"/>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DemPrimary3" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="80"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="30"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false"/>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DemPrimary4" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="96"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="30"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false"/>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DemPrimary5" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="112"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="30"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false"/>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DemPrimary6" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="128"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="30"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false"/>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DemPrimary7" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="144"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="30"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false"/>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DemStatus" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="160"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="40"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false"/>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_CALDATA" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="192"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="14"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_CPUMaxLoad_Core0" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="208"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="4"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_CPUMaxLoad_Core1" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="224"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="4"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_CPUMaxLoad_Core2" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="240"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="4"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_AFFC" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="256"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="4"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_B002" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="272"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="3"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_C001" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="288"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="5"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_C002" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="304"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="4"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_C003" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="320"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="4"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_C004" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="336"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="3"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_C005" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="352"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="3"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F1A8" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="368"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="22"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F1A9" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="384"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F1AA" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="400"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F18A" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="416"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F18B" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="432"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="5"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F18C" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="448"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="18"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F110" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="464"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="18"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F120" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="480"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="18"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F121" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="496"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="18"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F187" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="512"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F190" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="528"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="19"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F191" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="544"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F192" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="560"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="12"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DID_F198" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="576"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="13"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_DrivingSide" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="592"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="3"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_PEF" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="608"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="3"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_ReservedData01" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="624"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_ReservedData02" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="640"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_ReservedData03" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="656"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_ReservedData04" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="672"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_ReservedData05" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="688"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_ReservedData06" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="704"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_ReservedData07" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="720"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_ReservedData08" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="736"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_ReservedData09" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="752"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_ReservedData10" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="768"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="7">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlock_ResetSource" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="784"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="3"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlockSAFE_PER_DATA" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="816"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="26"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
                <d:ctr name="FeeBlockYSRSTINFO_NVMBLOCK" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="800"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="18"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="true"/>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
              </d:lst>
              <d:ctr name="FeeGeneral" type="IDENTIFIABLE">
                <d:var name="FeeDevErrorDetect" type="BOOLEAN" value="false"/>
                <d:var name="FeeIndex" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="FeeNvmJobEndNotification">
                  <d:var type="FUNCTION-NAME" value="NvM_JobEndNotification"/>
                </d:lst>
                <d:lst name="FeeNvmJobErrorNotification">
                  <d:var type="FUNCTION-NAME" value="NvM_JobErrorNotification"/>
                </d:lst>
                <d:var name="FeePollingMode" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeSetModeSupported" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeVersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeVirtualPageSize" type="INTEGER" value="8">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeDebugSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="FeePublishedInformation" type="IDENTIFIABLE">
                <d:var name="FeeBlockOverhead" type="INTEGER" value="17">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeMaximumBlockingTime" type="FLOAT" value="10.0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeePageOverhead" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="FeeIfxSpecificConfig" type="IDENTIFIABLE">
                <d:var name="FeeThresholdValue" type="INTEGER" value="2288"/>
                <d:var name="FeeMaxBlockCount" type="INTEGER" value="56"/>
                <d:var name="FeeUseEraseSuspend" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeStateVarStructure" type="STRING" 
                       value="Fee_StateVar">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeUnConfigBlock" type="ENUMERATION" 
                       value="FEE_UNCONFIG_BLOCK_IGNORE">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeUnConfigBlkOverflowHandle" type="ENUMERATION" 
                       value="FEE_CONTINUE">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeGcRestart" type="ENUMERATION" 
                       value="FEE_GC_RESTART_INIT">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeGetCycleCountApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeEraseAllEnable" type="BOOLEAN" value="true"/>
                <d:var name="FeeGetPrevDataApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeMaxBytesPerCycle" type="ENUMERATION" 
                       value="FEE_MAX_BYTES_512">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeIllegalStateNotification" type="FUNCTION-NAME" 
                       value="FeeIllegalStateNotification"/>
                <d:var name="FeeVirginFlashIllegalState" type="BOOLEAN" 
                       value="true"/>
              </d:ctr>
              <d:lst name="FeeDemEventParameterRefs" type="MAP"/>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArMajorVersion" type="INTEGER" value="2"/>
                <d:var name="ArMinorVersion" type="INTEGER" value="0"/>
                <d:var name="ArPatchVersion" type="INTEGER" value="0"/>
                <d:var name="SwMajorVersion" type="INTEGER" value="2"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="6"/>
                <d:var name="SwPatchVersion" type="INTEGER" value="0"/>
                <d:var name="ModuleId" type="INTEGER" value="21">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="17">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="Release" type="STRING" value="_TRICORE_TC234">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
