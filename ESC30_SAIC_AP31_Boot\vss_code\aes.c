#include "vsscommon.h"
#include "vssconf.h"
#include "aes.h"


#pragma section code "vss_api_code" 

#if (defined (_ENABLE_MIZAR_AES_)&&(_ENABLE_MIZAR_AES_ == 1U))
extern const vss_uint8 aes_sbox[256];
extern const vss_uint8 aes_contrary_sbox[256];                          
extern const vss_uint8 aes_Rcon[10];
     
/*������*2�˷� The x2time() function */     
static vss_uint8 x2time(vss_uint8 x)
{    
	if (x&0x80)                             
	{
		return (((x<<1)^0x1B)&0xFF);        
	}
	return x<<1;                            
}    
/*������*3�˷� The x2time() function */     
static vss_uint8 x3time(vss_uint8 x)
{    
	return (x2time(x)^x);                   
}    
/*������*4�˷� The x4time() function */     
static vss_uint8 x4time(vss_uint8 x)
{    
	return ( x2time(x2time(x)) );           
}    
/*������*8�˷� The x8time() function */     
static vss_uint8 x8time(vss_uint8 x)
{    
	return ( x2time(x2time(x2time(x))) );   
}    
/*������9�˷� The x9time() function */      
static vss_uint8 x9time(vss_uint8 x)
{    
	return ( x8time(x)^x );                 
}    
/*������*B�˷� The xBtime() function */     
static vss_uint8 xBtime(vss_uint8 x)
{    
	return ( x8time(x)^x2time(x)^x );       
}    
/*������*D�˷� The xDtime() function */     
static vss_uint8 xDtime(vss_uint8 x)
{    
	return ( x8time(x)^x4time(x)^x );       
}    
/*������*E�˷� The xEtime() function */     
static vss_uint8 xEtime(vss_uint8 x)
{    
	return ( x8time(x)^x4time(x)^x2time(x) );                              
}    
     
/****************************************************************************************************************/
/*������������л�ϲ��� MixColumns: Process the entire block*/            
static void MixColumns(vss_uint8 *col)
{    
	vss_uint8 tmp[4];             
	vss_uint32 i;                                  
	for(i=0;i<4;i++,col+=4)
	{
		tmp[0]=x2time(col[0])^x3time(col[1])^col[2]^col[3];
		tmp[1]=col[0]^x2time(col[1])^x3time(col[2])^col[3];
		tmp[2]=col[0]^col[1]^x2time(col[2])^x3time(col[3]);
		tmp[3]=x3time(col[0])^col[1]^col[2]^x2time(col[3]);
		col[0]=tmp[0];                      
		col[1]=tmp[1];                      
		col[2]=tmp[2];                      
		col[3]=tmp[3];                      
	}
}    

static void Contrary_MixColumns(vss_uint8 *col)                        
{    
	vss_uint8 tmp[4];                   
	vss_uint32 x;                                  
	for(x=0;x<4;x++,col+=4)                 
	{
		tmp[0]=xEtime(col[0])^xBtime(col[1])^xDtime(col[2])^x9time(col[3]);
		tmp[1]=x9time(col[0])^xEtime(col[1])^xBtime(col[2])^xDtime(col[3]);
		tmp[2]=xDtime(col[0])^x9time(col[1])^xEtime(col[2])^xBtime(col[3]);
		tmp[3]=xBtime(col[0])^xDtime(col[1])^x9time(col[2])^xEtime(col[3]);
		col[0]=tmp[0];                      
		col[1]=tmp[1];                      
		col[2]=tmp[2];                      
		col[3]=tmp[3];                      
	}
}    
/*�ڶ������������λ������ѭ����λ ShiftRows:Shifts the entire block*/     
static void ShiftRows(vss_uint8 *col)
{    
	/*                                      
		1 5  9 13				5  9 13 1   
		2 6 10 14				10 14 2 6   
		3 7 11 15				15 3 7 11   
		4 8 12 16				16 4 8 12   
	*/                                      
	vss_uint8 t;                        
	/*1nd row*/
	t=col[1];col[1]=col[5];col[5]=col[9];col[9]=col[13];col[13]=t;         
	/*2rd row*/
	t=col[2];col[2]=col[10];col[10]=t;      
	t=col[6];col[6]=col[14];col[14]=t;      
	/*3th row*/
	t=col[15];col[15]=col[11];col[11]=col[7];col[7]=col[3];col[3]=t;       
	/*4th row*/	
}    
static void Contrary_ShiftRows(vss_uint8 *col)                         
{    
	vss_uint8 t;                        
	/*1nd row*/                             
	t=col[13];col[13]=col[9];col[9]=col[5];col[5]=col[1];col[1]=t;         
	/*2rd row*/                             
	t=col[2];col[2]=col[10];col[10]=t;      
	t=col[6];col[6]=col[14];col[14]=t;      
	/*3th row*/                             
	t=col[3];col[3]=col[7];col[7]=col[11];col[11]=col[15];col[15]=t;       
	/*4th row*/	
}    
/*��һ�������s���ֽڴ��滻 SubBytes*/    
static void SubBytes(vss_uint8 *col)
{    
	vss_uint32 x;                                  
	for(x=0;x<16;x++)                       
	{
		col[x]=aes_sbox[col[x]];                
	}
}    

static void Contrary_SubBytes(vss_uint8 *col)                          
{    
	vss_uint32 x;                                  
	for(x=0;x<16;x++)                       
	{
		col[x]=aes_contrary_sbox[col[x]];       
	}
}    
/*���������������Կ�� AddRoundKey*/        
static void AddRoundKey(vss_uint8 *col,vss_uint8 *expansionkey,vss_uint32 round)
{    
	vss_uint32 x;				                    
	for(x=0;x<16;x++)
	{
		col[x]^=expansionkey[(round<<4)+x]; 
	}
}    
/* AES�����ܺ��� 10��4����� Encrypt a single block with Nr Rounds(10,12,14)*/                                    
void AesEncrypt(vss_uint8 *blk,vss_uint8 *expansionkey,vss_uint32 Nr)
{    
	vss_uint32 round;                              
	AddRoundKey(blk,expansionkey,0);        
	for(round=1;round<=(Nr-1);round++)	    
	{
		SubBytes(blk);
		ShiftRows(blk);	
		MixColumns(blk);
		AddRoundKey(blk,expansionkey,round);
	}

	SubBytes(blk);                          
	ShiftRows(blk);                         
	AddRoundKey(blk,expansionkey,Nr);       
}    

void Contrary_AesEncrypt(vss_uint8 *blk,vss_uint8 *expansionkey,vss_uint32 Nr)                                   
{    
	vss_uint32 x;                                  
	/* vss_uint8 *contrary_key=key;     
	for(x=0;x<11;x++,key+=16)               
	Contrary_MixColumns(key);*/             
	AddRoundKey(blk,expansionkey,Nr);       
	Contrary_ShiftRows(blk);                
	Contrary_SubBytes(blk);                 
	for(x=(Nr-1);x>=1;x--)                  
	{
		AddRoundKey(blk,expansionkey,x);    
		Contrary_MixColumns(blk);           
		Contrary_ShiftRows(blk);            
		Contrary_SubBytes(blk);             
	}
	AddRoundKey(blk,expansionkey,0);        
}    

  
void ScheduleKey(vss_uint8 *inkey,vss_uint8 *outkey,vss_uint32 Nk,vss_uint32 Nr)
{    
	vss_uint8 temp[4],t;                
	vss_uint32 x,i;                                
	for(i=0;i<(4*Nk);i++)                   
	{
		outkey[i]=inkey[i];                 
	}

	i=Nk;                                   
	while(i<(4*(Nr+1)))
	{
		for(x=0;x<4;x++)                    
			temp[x]=outkey[(4*(i-1))+x];

		if(i%Nk==0)                         
		{                                   
			/*��ѭ����ѭ������1�ֽ� RotWord()*/                            
			t=temp[0];temp[0]=temp[1];temp[1]=temp[2];temp[2]=temp[3];temp[3]=t;                                  
			/*�ֽڴ�SubWord()*/         
			for(x=0;x<4;x++)                
			{                               
				temp[x]=aes_sbox[temp[x]];      
			}                               
			/*�ֳ������Rcon[j]*/         
			temp[0]^=aes_Rcon[(i/Nk)-1];        
		}                                   
     
		/*w[i] = w[i-4]^w[i-1]*/            
		for(x=0;x<4;x++)                    
		{                                   
			outkey[(4*i)+x]=outkey[(4*(i-Nk))+x]^temp[x];                  
		}                                   
		++i;                                
	}
}    

vss_uint32 aes_set_key(aes_context* ctx, vss_uint8 *key)
{
	ScheduleKey(key, ctx->expansionkey, 4, 10);
	return 0;
}

vss_uint32 aes_ecb_encrypt(vss_uint8 *in, vss_uint8 *out, vss_uint32 length, aes_context* ctx, vss_uint32 enc)
{
	vss_uint8 tempBuf[16];
	
	while( length > 0 )
	{
		mem_cpy8(tempBuf, in, 16);
		if (enc == TT_AES_ENCRYPT)
		{			
			AesEncrypt(tempBuf, ctx->expansionkey, 10);			
		}
		else
		{
			Contrary_AesEncrypt(tempBuf, ctx->expansionkey, 10);
		}
		mem_cpy8(out, tempBuf, 16);
		in  += 16;
		out += 16;
		length -= 16;
	}

	return 0;
}

#endif

#pragma section code restore



