cmake_minimum_required(VERSION 3.16)

# 项目名称和语言设置 (基于Debug/makefile中的PROJ = AP31_ESC_PBL)
project(AP31_ESC_PBL C ASM)

# 生成compile_commands.json供clangd使用
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 设置C标准 (基于Tasking编译器的--iso=99设置)
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# 包含Tasking编译器配置文件
include(${CMAKE_CURRENT_SOURCE_DIR}/cmake/tasking-definitions.cmake)

# 设置构建类型为Debug以匹配原始配置
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug)
endif()

# 定义源文件目录
set(PROJECT_ROOT ${CMAKE_CURRENT_SOURCE_DIR}/ESC30_SAIC_AP31_Boot)

# 包含目录设置 (基于Tasking编译器的-I参数)
include_directories(
    # 主项目目录
    ${PROJECT_ROOT}

    # 核心模块目录
    ${PROJECT_ROOT}/MCU
    ${PROJECT_ROOT}/eeprom
    ${PROJECT_ROOT}/Secure
    ${PROJECT_ROOT}/Src_file

    # Flash相关目录
    ${PROJECT_ROOT}/flash
    ${PROJECT_ROOT}/flash/flsloader

    # 通信协议目录
    ${PROJECT_ROOT}/uds

    # 看门狗目录
    ${PROJECT_ROOT}/wdg

    # MCAL配置和源码目录
    ${PROJECT_ROOT}/mcal_cfg
    ${PROJECT_ROOT}/mcal_src

    # DMA Infineon TriCore模块
    ${PROJECT_ROOT}/mcal_src/dma_infineon_tricore
    ${PROJECT_ROOT}/mcal_src/dma_infineon_tricore/inc
    ${PROJECT_ROOT}/mcal_src/dma_infineon_tricore/src

    # 通用集成模块
    ${PROJECT_ROOT}/mcal_src/integration_general
    ${PROJECT_ROOT}/mcal_src/integration_general/inc
    ${PROJECT_ROOT}/mcal_src/integration_general/src

    # SPI Infineon TriCore模块
    ${PROJECT_ROOT}/mcal_src/spi_infineon_tricore
    ${PROJECT_ROOT}/mcal_src/spi_infineon_tricore/inc
    ${PROJECT_ROOT}/mcal_src/spi_infineon_tricore/src
)

# 收集所有源文件
file(GLOB_RECURSE MAIN_SOURCES
    "${PROJECT_ROOT}/*.c"
    "${PROJECT_ROOT}/*.cpp"
    "${PROJECT_ROOT}/*.h"
    "${PROJECT_ROOT}/*.hpp"
)

# 排除Debug目录中的文件
list(FILTER MAIN_SOURCES EXCLUDE REGEX "${PROJECT_ROOT}/Debug/.*")

# 分类源文件
set(C_SOURCES "")
set(CPP_SOURCES "")
set(HEADER_FILES "")

foreach(SOURCE_FILE ${MAIN_SOURCES})
    get_filename_component(FILE_EXT ${SOURCE_FILE} EXT)
    if(FILE_EXT STREQUAL ".c")
        list(APPEND C_SOURCES ${SOURCE_FILE})
    elseif(FILE_EXT STREQUAL ".cpp" OR FILE_EXT STREQUAL ".cxx" OR FILE_EXT STREQUAL ".cc")
        list(APPEND CPP_SOURCES ${SOURCE_FILE})
    elseif(FILE_EXT STREQUAL ".h" OR FILE_EXT STREQUAL ".hpp")
        list(APPEND HEADER_FILES ${SOURCE_FILE})
    endif()
endforeach()

# 创建一个虚拟目标用于IDE代码索引 (模拟AP31_ESC_PBL.elf)
add_library(AP31_ESC_PBL_IDE STATIC
    ${C_SOURCES}
    ${HEADER_FILES}
)

# 设置目标属性
set_target_properties(AP31_ESC_PBL_IDE PROPERTIES
    LINKER_LANGUAGE C
    EXCLUDE_FROM_ALL TRUE
)

# 目标不需要额外的编译定义，因为已经在tasking-definitions.cmake中定义了
# 但是可以添加一些目标特有的定义
target_compile_definitions(AP31_ESC_PBL_IDE PRIVATE
    # IDE特有标识
    CMAKE_IDE_BUILD=1

    # 调试构建标识
    DEBUG_BUILD=1
)

# 添加子目录的CMakeLists.txt文件
add_subdirectory(ESC30_SAIC_AP31_Boot)

# 计算源文件数量
list(LENGTH C_SOURCES C_SOURCES_COUNT)
list(LENGTH HEADER_FILES HEADER_FILES_COUNT)

# 打印配置信息
message(STATUS "=== Tasking TriCore Bootloader Project Configuration ===")
message(STATUS "Project: ${PROJECT_NAME}")
message(STATUS "Target: AP31_ESC_PBL (Primary Bootloader)")
message(STATUS "C Standard: ISO ${CMAKE_C_STANDARD}")
message(STATUS "Compiler: Tasking TriCore v4.2r2 (Simulated)")
message(STATUS "CPU: TC23x TriCore Architecture")
message(STATUS "Found ${C_SOURCES_COUNT} C source files")
message(STATUS "Found ${HEADER_FILES_COUNT} header files")
message(STATUS "Project root: ${PROJECT_ROOT}")
message(STATUS "Optimization: -O2 with tradeoff=4")
message(STATUS "Memory model: Near=0, A0=0, A1=0")
message(STATUS "=== Configuration Complete ===")

# 创建一个自定义目标来显示Tasking编译器信息
add_custom_target(tasking_info
    COMMAND ${CMAKE_COMMAND} -E echo "Tasking TriCore Compiler Information:"
    COMMAND ${CMAKE_COMMAND} -E echo "  PRODDIR: C:/Program Files (x86)/TASKING/TriCore v4.2r2/ctc"
    COMMAND ${CMAKE_COMMAND} -E echo "  Compiler: cctc"
    COMMAND ${CMAKE_COMMAND} -E echo "  Linker: cctc with LSL script"
    COMMAND ${CMAKE_COMMAND} -E echo "  Target CPU: userdef16x (TC23x)"
    COMMAND ${CMAKE_COMMAND} -E echo "  Output: AP31_ESC_PBL.elf, AP31_ESC_PBL.hex"
    COMMENT "Displaying Tasking compiler configuration"
)
