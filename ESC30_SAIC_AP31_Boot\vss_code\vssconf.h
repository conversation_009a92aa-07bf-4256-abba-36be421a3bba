#ifndef VSSCONF_H
#define VSSCONF_H

#define _ENABLE_MIZAR_SM2_ 1
#define _ENABLE_MIZAR_SM3_ 1
#define _ENABLE_MIZAR_SHA256_ 1
#define _ENABLE_MIZAR_ECC_ 1
#define _ENABLE_MIZAR_AES_ 1
#define _ENABLE_MIZAR_ZUC_ 1
#define _ENABLE_MIZAR_FOTA_ 1
#define _ENABLE_MIZAR_FTLS_ 0
#define _BIG_ENDIAN_ 0
#define _MPU_LINUX_ 0
/*0 表示mcu ,1 表示mpu*/
#define _ENVFLAG_ 0	
#define _ENABLE_FLASH_BUFF_ 1
#define MBEDTLS_NO_UDBL_DIVISION 1

#endif

