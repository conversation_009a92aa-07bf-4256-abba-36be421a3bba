/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <Dcm_Dsp.c>
 *  @brief      <UDS Service - ISO14229>
 *  
 *  <This Diagnostic Communication Manager file contained UDS services
 *   which used for bootloader project>
 *
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *
 *  <AUTHOR>
 *  @date       <2013-09-13>
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20121109    Gary       Initial version
 *
 *  V1.1    20130913    ccl        update
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/
#include "Dcm_Dsp.h"
#include "Dcm_Types.h"
#include "Dcm_Internel.h"
#include "FL.h"
#include "EcuM.h"
#include "Mcal_Compiler.h"
#include "Seedkey.h"
#include "stdio.h"
#include "SecM.h"
#include "CDD_Adc.h"
//#include "Nvm_Did_Cfg.h"
/*=======[T Y P E   D E F I N I T I O N S]====================================*/
#pragma section code "vss_cfg_data_code"
__attribute__((section (".rodata.vss_cfg_data_codeF180")))
  const uint8 BLVer[5] = {0x56,0x31, 0x2E, 0x30, 0x33};
#pragma section code restore
/* data structure for program */
typedef struct
{
    /* data program start address */
    uint32 address;

    /* data program block Index(compare with parameter transfered by test) */
    uint8 blockId;

    /* if program condition is passed */
    boolean condition;

} Dcm_DspProgramType;

/* data structure for security access */
typedef struct
{
    /* DCM security access attempt num counter */
    uint8 attempt;

    /* if seed has requested */
    boolean seedReq;

    /* the last requested seed Id */
    uint8 seedIdReq;

    /* seed which generated by SecM module */
    SecM_SeedType seed;

} Dcm_DspSecurityType;
typedef struct
{
	SecM_CRCType crcResult;
	SecM_CRCType crcDefault;
	boolean  crcStatus;

}DCM_DspS37CRCType;
/*=======[I N T E R N A L   D A T A]==========================================*/
STATIC Dcm_DspProgramType dcmDspProgram;
STATIC Dcm_DspSecurityType dcmDspSec;
/*=======[I N T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
STATIC uint32 Dcm_Get4Byte(const uint8* data);

STATIC void Dcm_Set4Byte(uint8* destData,
    const uint32 sourceData);
STATIC boolean Dcm_CheckSubFuncSession(const Dcm_SessionType sessionSupportMask);
STATIC boolean Dcm_CheckRoutineIDSession(const Dcm_SessionType sessionSupportMask);
STATIC boolean Dcm_CheckMsgLength(const uint16 minlength,
    const uint16 receivedLength,boolean isfixlength);
STATIC boolean Dcm_CheckSecurityCondition(const Dcm_SecurityType securitySupportMask);
STATIC boolean Dcm_CheckSubFuncFind(const boolean find,
    const Dcm_BuffType * rxBuff);
STATIC boolean Dcm_SessionChangeConditionCheck(Dcm_SessionType sessionValue);
STATIC boolean Dcm_ResetConditionCheck(void);
STATIC boolean Dcm_CheckReqOutOfRange(const boolean find,const Dcm_BuffType * rxBuff);

STATIC void Dcm_RequestSeed(const Dcm_SecurityRowType * secTablePtr,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);
STATIC boolean Dcm_CheckVoltageCondition(void);
STATIC void Dcm_SendKey(const Dcm_SecurityRowType * secTablePtr,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern FL_ResultType FL_CheckSumFor37(SecM_CRCType* crc);
/*=======[I N T E R N A L   D A T A]==========================================*/
boolean pendingReq;

STATIC DCM_DspS37CRCType dcms37_crcResult={0x00,0x00,FALSE};

extern uint8 SecurityErrorFlag;
extern uint8 blsDIDF187WriteOnlyOnceFlag;
uint8 FailReason =0;
uint8 SecurityLog_trigger=0;
/*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/
/******************************************************************************/
/**
 * @brief               <DCM module dsp initialize>
 * 
 * <DCM module dsp initialize> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_DspInit(void)
{
    /* set program condition is allowed */
    dcmDspProgram.condition = TRUE;
    
    /* clear erase block id */
    dcmDspProgram.blockId = 0x00u;
    
    /* clear program address */
    dcmDspProgram.address = 0x00uL;

    /* clear security access attempt num */
    dcmDspSec.attempt = 0x00u;
    
    /* set seed has not requested */
    dcmDspSec.seedReq = FALSE;
    
    return;
}

/******************************************************************************/
/**
 * @brief               <handle service 0x10 session control>
 * 
 * <handle service 0x10 session control> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_RecvMsg10(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
#if (STD_ON == DCM_SERVICE_10_SUPPRESS_POS_SUPPORT)
    boolean positiveRspReq = TRUE;
#endif
    Dcm_SessionType sessionValue = DCM_SISSION_RESEVED;
    uint8 sessionTableIndex = DCM_SESSION_NUM;
    const Dcm_SessionRowType * sessionTablePtr = Dcm_SessionRow;
    boolean sessionFind = FALSE;
    boolean processContinue = TRUE;
    Dcm_SessionType curSession;
    /*convert subFunc to session mask value
    * default session:0x01->0x01
    * program session:0x02->0x02
    * extend session: 0x03->0x04
    */
    uint8 sessionConvertTable[DCM_SESSION_SUPPORT_NUM + 1] = \
    {DCM_SISSION_RESEVED, DCM_SESSION_DEFAULT,\
    DCM_SESSION_PROGRAMMING, DCM_SESSION_EXTEND};

	/* check if received message length is right, other wise send NRC */
     processContinue = Dcm_CheckMsgLength(0x02u, rxBuff->pduInfo.SduLength,TRUE);

    /* get subfunction */
	if( (rxBuff->pduInfo.SduDataPtr[1] & DCM_RSP_CLEAR_REQUIRED) <= DCM_SESSION_SUPPORT_NUM )
	{
		sessionValue = sessionConvertTable[(rxBuff->pduInfo.SduDataPtr[1] & DCM_RSP_CLEAR_REQUIRED)];
	}
#if (STD_ON == DCM_SERVICE_10_SUPPRESS_POS_SUPPORT)
    /* check if response is needed */
    if ((rxBuff->pduInfo.SduDataPtr[1] & DCM_RSP_NOT_REQUIRED) > 0x00u)
    {
        positiveRspReq = FALSE;
    }
#endif
    /* find session mode subfunction */
    while ((sessionTableIndex > 0) && (FALSE == sessionFind))
    {
        sessionTableIndex--;
        if (sessionValue == sessionTablePtr->sessionType)
        {
            sessionFind = TRUE;
        }
        else
        {
            sessionTablePtr++;
        }
    }
    if (TRUE == processContinue)
    {
		/* if did not find subfunciton,send NRC */
		processContinue = Dcm_CheckSubFuncFind(sessionFind, rxBuff);
    }
    
    if (TRUE == processContinue)
    {
        /* check if subfunciton is supportted in current session, other wise send NRC */
        processContinue = Dcm_CheckSubFuncSession(sessionTablePtr->sessionSupportMask);
    }
    

#if 0
    /* SESSION_PROGRAMMING is not supported in function address */
    if (TRUE == processContinue)
    {
        if ((DCM_SESSION_PROGRAMMING == sessionValue)
            && (DCM_RX_FUNC_PDU_ID == rxBuff->pduId)) 
        {
            Dcm_ServiceFinish();
            processContinue = FALSE;
        }
    }
#endif

#if (STD_ON == DCM_SID_NRC_SUPPORT)    
    if (TRUE == processContinue)
    {
        /* check if subfunciton is supportted in current security level, other wise send NRC */
        processContinue = Dcm_CheckSecurityCondition(
            sessionTablePtr->securitySupportMask);
    }
#endif
    
    if (TRUE == processContinue)
    {
        /* check if condition is correct, other wise send NRC */
        processContinue = Dcm_SessionChangeConditionCheck(sessionValue);
    }

    if (TRUE == processContinue)
    {
        /* if from prog. session to default session, then not change, but reset */
        curSession = Dcm_GetSessionMode();
        if ((DCM_SESSION_PROGRAMMING == curSession)
            && (DCM_SESSION_DEFAULT == sessionValue)
            && (STD_OFF == dcmDummyDefault))
        {
              Dcm_StartResetTimer(DCM_P2MAX_TIME * 0x02u);
//            *(uint8 *)FL_BOOT_DEFAULT = FL_BOOT_DEFAULT_FROM_PROG;
        	  Dcm_SetSessionMode(sessionValue);

				/* Init security level */
				Dcm_SetSecurityLevel(DCM_SECURITY_LOCKED);
				Dcm_DspInit();

				/* Init FL state */
				FL_InitState();

        	#if (STD_ON == DCM_SERVICE_10_SUPPRESS_POS_SUPPORT)
        	            if (TRUE == positiveRspReq)
        	            {
        	#endif
				/* set response message */
				txBuff->pduInfo.SduDataPtr[0] = 0x50u;
				txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
				txBuff->pduInfo.SduDataPtr[2] = (uint8)(((uint16)(DCM_P2MAX_TIME_TX))>> 0x08u);
				txBuff->pduInfo.SduDataPtr[3] = (uint8)(DCM_P2MAX_TIME_TX);
				txBuff->pduInfo.SduDataPtr[4] = (uint8)(((uint16)(DCM_P2SMAX_TIME/10))>> 0x08u);
				txBuff->pduInfo.SduDataPtr[5] = (uint8)(DCM_P2SMAX_TIME/10);
				txBuff->pduInfo.SduLength = 0x06u;
				Dcm_SendRsp();
        	#if (STD_ON == DCM_SERVICE_10_SUPPRESS_POS_SUPPORT)
        	            }
        	            else
        	            {
        	                /* reset service process */
        	                Dcm_ServiceFinish();
        	            }
        	#endif
        }
        else
        {
            dcmDummyDefault = STD_OFF;
            Dcm_SetSessionMode(sessionValue);
            
            /* Init security level */
            Dcm_SetSecurityLevel(DCM_SECURITY_LOCKED);
            Dcm_DspInit();
            
            /* Init FL state */
            FL_InitState();
            
#if (STD_ON == DCM_SERVICE_10_SUPPRESS_POS_SUPPORT)
            if (TRUE == positiveRspReq)
            {
#endif
                /* set response message */
                txBuff->pduInfo.SduDataPtr[0] = 0x50u;
                txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
                txBuff->pduInfo.SduDataPtr[2] = (uint8)(((uint16)(DCM_P2MAX_TIME_TX))>> 0x08u);
                txBuff->pduInfo.SduDataPtr[3] = (uint8)(DCM_P2MAX_TIME_TX);
                txBuff->pduInfo.SduDataPtr[4] = (uint8)(((uint16)(DCM_P2SMAX_TIME/10))>> 0x08u);
                txBuff->pduInfo.SduDataPtr[5] = (uint8)(DCM_P2SMAX_TIME/10);
                txBuff->pduInfo.SduLength = 0x06u;
                Dcm_SendRsp();
#if (STD_ON == DCM_SERVICE_10_SUPPRESS_POS_SUPPORT)
            }
            else
            {
                /* reset service process */
                Dcm_ServiceFinish();
            }
#endif
        }
    }
    
    return;
}

#if(DCM_SERVICE_11_ENABLE == STD_ON)
/******************************************************************************/
/**
 * @brief               <handle service 0x11 ECU reset>
 * 
 * <handle service 0x11 ECU reset> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_RecvMsg11(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
#if (STD_ON == DCM_SERVICE_11_SUPPRESS_POS_SUPPORT)
    boolean positiveRspReq = TRUE;
#endif
    Dcm_ResetType resetValue;
    uint8 resetTableIndex = DCM_RESET_NUM;
    const Dcm_ResetRowType * resetTablePtr = Dcm_ResetRow;
    boolean resetFind = FALSE;
    boolean processContinue = TRUE;
    
    processContinue = Dcm_CheckMsgLength(0x02u, rxBuff->pduInfo.SduLength,TRUE);
    /* get subfunction */
    resetValue = (rxBuff->pduInfo.SduDataPtr[1] & DCM_RSP_CLEAR_REQUIRED);
#if (STD_ON == DCM_SERVICE_11_SUPPRESS_POS_SUPPORT)
    /* check if response is needed */
    if ((rxBuff->pduInfo.SduDataPtr[1] & DCM_RSP_NOT_REQUIRED) > 0x00u)
    {
        positiveRspReq = FALSE;
    }
#endif
    if (TRUE == processContinue)
    {
		/* find reset mode subfunction */
		while ((resetTableIndex > 0) && (FALSE == resetFind))
		{
			resetTableIndex--;
			if (resetValue == resetTablePtr->resetType)
			{
				resetFind = TRUE;
			}
			else
			{
				resetTablePtr++;
			}
		}
		/* if did not find subfunciton,send NRC */
		processContinue = Dcm_CheckSubFuncFind(resetFind, rxBuff);
   }
#if (STD_ON == DCM_SID_NRC_SUPPORT)    
    if (TRUE == processContinue)
    {
        /* check if subfunciton is supportted in current session, other wise send NRC */
        processContinue = Dcm_CheckSubFuncSession(resetTablePtr->sessionSupportMask);
    }
#endif
   

#if (STD_ON == DCM_SID_NRC_SUPPORT)    
    if (TRUE == processContinue)
    {
        /* check if subfunciton is supportted in current security level, other wise send NRC */
        processContinue = Dcm_CheckSecurityCondition(resetTablePtr->securitySupportMask);
    }
#endif

    if (TRUE == processContinue)
    {
        /* check if condition is correct, other wise send NRC */
        processContinue = Dcm_ResetConditionCheck();
    }

    if (TRUE == processContinue)
    {
#if (STD_ON == DCM_SERVICE_11_SUPPRESS_POS_SUPPORT)
        if (TRUE == positiveRspReq)
        {
#endif
 #if 0
            /* set response message */
            if (FL_APPL_UPDATED != *(uint8 *)FL_APPL_UPDATE)
            {
                txBuff->pduInfo.SduDataPtr[0] = 0x51u;
                txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
                txBuff->pduInfo.SduLength = 0x02u;
                Dcm_SendRsp();
            }
#endif

                txBuff->pduInfo.SduDataPtr[0] = 0x51u;
                txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
                txBuff->pduInfo.SduLength = 0x02u;
                Dcm_SendRsp();
#if (STD_ON == DCM_SERVICE_11_SUPPRESS_POS_SUPPORT)
        }
        else
        {
            /* reset service process */
            Dcm_ServiceFinish();
        }
#endif
		/* ECU will reset after 100ms */
		Dcm_StartResetTimer(DCM_P2MAX_TIME * 0x02u);
    }
    
    return;
}
#endif
#if((DCM_READDID_NUM > 0) && (DCM_SERVICE_22_ENABLE == STD_ON))
/******************************************************************************/
/**
 * @brief               <handle service 0x22 read data by identifier>
 * 
 * <handle service 0x22 read data by identifier> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_RecvMsg22(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    uint16 readDid;
    uint8 readTableIndex = DCM_READDID_NUM;
    const Dcm_ReadDidRowType * readTablePtr = Dcm_ReadDidRow;
    boolean didFind = FALSE;
    boolean processContinue = TRUE;

    /* get read data Id */
    readDid = ((uint16)rxBuff->pduInfo.SduDataPtr[1]) << 0x08u;
    readDid += (uint16)rxBuff->pduInfo.SduDataPtr[2];
    
    /* check if received message length is right, other wise send NRC */
    processContinue = Dcm_CheckMsgLength(0x03u, rxBuff->pduInfo.SduLength,TRUE);

    if (TRUE == processContinue)
    {
    	/* find read data Id in configered read data table */
		while ((readTableIndex > 0) && (FALSE == didFind))
		{
			readTableIndex--;
			if (readDid == readTablePtr->DID)
			{
				didFind = TRUE;
			}
			else
			{
				readTablePtr++;
			}
		}

		/* if did not find DID,send NRC */
		processContinue = Dcm_CheckReqOutOfRange(didFind, rxBuff);
    }


#if (STD_ON == DCM_SID_NRC_SUPPORT)
    if (TRUE == processContinue)
    {
        /* check if DID is supportted in current security level, other wise send NRC */
        processContinue = Dcm_CheckSecurityCondition(
            readTablePtr->securitySupportMask);
    }
#endif

    if (TRUE == processContinue)
    {
        uint16 readLength;
        
        /* excute read API,return read length */
        readLength = readTablePtr->readDataFct(&txBuff->pduInfo.SduDataPtr[3],readDid);
        {
        	txBuff->pduInfo.SduDataPtr[0] = 0x62u;
			txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
			txBuff->pduInfo.SduDataPtr[2] = rxBuff->pduInfo.SduDataPtr[2];
			txBuff->pduInfo.SduLength = 0x03u + readLength;
			Dcm_SendRsp();
        }

    }
    
    return;
}
#endif

/******************************************************************************/
/**
 * @brief               <handle service 0x2E write data by identifier>
 * 
 * <handle service 0x2E write data by identifier> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_RecvMsg2E(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    uint16 writeDid;
    uint8 writeTableIndex = DCM_WRITEDID_NUM;
    const Dcm_WriteDidRowType * writeTablePtr = Dcm_WriteDidRow;
    boolean didFind = FALSE;
    boolean processContinue = TRUE;
    FL_ResultType writeRet;

    /* get write data Id */
    writeDid = ((uint16)rxBuff->pduInfo.SduDataPtr[1]) << 0x08u;
    writeDid += (uint16)rxBuff->pduInfo.SduDataPtr[2];
	/* find write data Id in configered write data table */
	while ((writeTableIndex > 0) && (FALSE == didFind))
	{
		writeTableIndex--;
		if (writeDid == writeTablePtr->DID)
		{
			didFind = TRUE;
		}
		else
		{
			writeTablePtr++;
		}
	}

	if(TRUE==didFind)
	{
		/* check if received message length is right, other wise send NRC */
		processContinue = Dcm_CheckMsgLength(writeTablePtr->dataLength + 0x03u, rxBuff->pduInfo.SduLength,TRUE);

		if (TRUE == processContinue)
		{
			/* check if DID is supportted in current security level, other wise send NRC */
			processContinue = Dcm_CheckSecurityCondition(writeTablePtr->securitySupportMask);
		}
	}
	if(TRUE == processContinue)
	{
		/* if did not find DID,send NRC */
		processContinue = Dcm_CheckReqOutOfRange(didFind, rxBuff);
	}


    if (TRUE == processContinue)
    {

        /* excute write API,return read length */
    	if(writeTablePtr->writeDataFct!=NULL)
    	{
    		 writeRet = writeTablePtr->writeDataFct(&rxBuff->pduInfo.SduDataPtr[1],
    		            writeTablePtr->dataLength);
    		 /* check if FL step is correct, otherwise send NRC */
			 if (FL_ERR_SEQUENCE == writeRet)
			 {
				 Dcm_SendNcr(DCM_E_REQUEST_OUT_OF_RANGE);
			 }
			 else if(writeRet==FL_WRITEMOREONETIME)
			 {
				 txBuff->pduInfo.SduDataPtr[0] = 0x7Fu;
				 txBuff->pduInfo.SduDataPtr[1] = 0x2Eu;
				 txBuff->pduInfo.SduDataPtr[2] = 0x22u;
				 txBuff->pduInfo.SduDataPtr[3] = 0x01u;
				 txBuff->pduInfo.SduLength = 0x04u;
				 Dcm_SendRsp();
			 }
			 else
			 {
				 /* if write data acceptted, wait for write pending */
				 if (FL_OK != writeRet)
				 {
					 /* program finger print failure */
					 Dcm_SendNcr(DCM_E_GENERAL_PROGRAMMING_FAILURE);
				 }
			 }
    	}
    	else
    	{
    		txBuff->pduInfo.SduDataPtr[0] = 0x6Eu;
			txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
			txBuff->pduInfo.SduDataPtr[2] = rxBuff->pduInfo.SduDataPtr[2];
			txBuff->pduInfo.SduLength = 0x03u;
			Dcm_SendRsp();

    	}
    }
    
    return;
}

/******************************************************************************/
/**
 * @brief               <handle service 0x27 securtiy access>
 * 
 * <handle service 0x27 securtiy access> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_RecvMsg27(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{

    Dcm_SessionType secId = rxBuff->pduInfo.SduDataPtr[1];
    uint8 secTableIndex = DCM_SECURITY_NUM;
    const Dcm_SecurityRowType * secTablePtr = Dcm_SecurityRow;
    boolean secIdFind = FALSE;
    boolean processContinue = TRUE;
     uint16 checkLength;

    /* find subfunction Id in configered security table */
    while ((secTableIndex > 0) && (FALSE == secIdFind))
    {
        secTableIndex--;
        if ((secId == secTablePtr->reqSeedId) ||
            (secId == secTablePtr->sendKeyId))
        {
            secIdFind = TRUE;
        }
        else
        {
            secTablePtr++;
        }
    }
    
    processContinue = Dcm_CheckMsgLength(2,rxBuff->pduInfo.SduLength,FALSE);
    if(TRUE==processContinue)
    {
    	if(TRUE==secIdFind)
		{
			if (secId == secTablePtr->reqSeedId)
			{
				/* check if received message length is right, other wise send NRC */
			   checkLength=2;
			   processContinue = Dcm_CheckMsgLength(checkLength,rxBuff->pduInfo.SduLength,TRUE);
			}
			else
			{
				/*length error will return NRC 0x35,this will be done in Dcm_SendKey*/
			}
		}
		else
		{
			/* if did not find subfunction,send NRC */
			processContinue = Dcm_CheckSubFuncFind(secIdFind, rxBuff);
		}
    }

    if (TRUE == processContinue)
    {
        /* check if subfunciton is supportted in current session, other wise send NRC */
        processContinue = Dcm_CheckSubFuncSession(secTablePtr->sessionSupportMask);
    }

#if (STD_ON == DCM_SID_NRC_SUPPORT)
    if (TRUE == processContinue)
    {
        /* check if subfunction is supportted in current security level, other wise send NRC */
        processContinue = Dcm_CheckSecurityCondition(secTablePtr->securitySupportMask);
    }
#endif
    
    if (TRUE == processContinue)
    {
        /* check if security timer is expired */
        if (FALSE == Dcm_GetSecurityTimerExpired())
        {
            /* security access timer delay is not expired */
            Dcm_SendNcr(DCM_E_REQUIRED_TIME_DELAY_NOT_EXPIRED);
            FailReason = DCM_E_REQUIRED_TIME_DELAY_NOT_EXPIRED;
            SecurityLog_trigger = 0x1;
           }
        else
        {
            if (secId == secTablePtr->reqSeedId)
            {
                Dcm_RequestSeed(secTablePtr, rxBuff, txBuff);
            }
            else
            {
                Dcm_SendKey(secTablePtr, rxBuff, txBuff);
            }
        }
    }

    return;
}

#if (DCM_COM_CONTROL_NUM > 0)
/******************************************************************************/
/**
 * @brief               <handle service 0x28 communication control>
 * 
 * <handle service 0x28 communication control> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_RecvMsg28(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    Dcm_ComControlType controlValue = rxBuff->pduInfo.SduDataPtr[1];
    uint8 comTableIndex = DCM_COM_CONTROL_NUM;
    const Dcm_ComControlRowType * comTablePtr = Dcm_ComControlRow;
    boolean controlTypeFind = FALSE;
    boolean processContinue = TRUE;

    /* find communication control type in configered table */
    while ((comTableIndex > 0) && (FALSE == controlTypeFind))
    {
        comTableIndex--;
        
        if (controlValue == comTablePtr->controlType)
        {
            controlTypeFind = TRUE;
        }
        else
        {
            comTablePtr++;
        }
    }
    
    /* if did not find subfunciton,send NRC */
    processContinue = Dcm_CheckSubFuncFind(controlTypeFind, rxBuff);

#if (STD_ON == DCM_SID_NRC_SUPPORT)
    if (TRUE == processContinue)
    {
        /* check if subfunciton is supportted in current session, other wise send NRC */
        processContinue = Dcm_CheckSubFuncSession(comTablePtr->sessionSupportMask);
    }
#endif
    
    if (TRUE == processContinue)
    {
        /* check if received message length is right, other wise send NRC */
        processContinue = Dcm_CheckMsgLength(0x03u, rxBuff->pduInfo.SduLength,TRUE);
    }

#if (STD_ON == DCM_SID_NRC_SUPPORT)
    if (TRUE == processContinue)
    {
        /* check if subfunction is supportted in current security level, other wise send NRC */
        processContinue = Dcm_CheckSecurityCondition(comTablePtr->securitySupportMask);
    }
#endif

    if (TRUE == processContinue)
    {
        Dcm_CommunicationType communicationType = rxBuff->pduInfo.SduDataPtr[2];
        
        /* check if communication type is correct */
        if ((DCM_NORMAL_COM_MESSAGES == communicationType)
        || (DCM_NMANDNORMAL_COM_MESSAGES == communicationType))
        {
            /* set response message */
            txBuff->pduInfo.SduDataPtr[0] = 0x68u;
            txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
            txBuff->pduInfo.SduLength = 0x02u;
            Dcm_SendRsp();
        }
        else
        {
            /* Request out of range */
            Dcm_SendNcr(DCM_E_REQUEST_OUT_OF_RANGE);
        }
    }
    
    return;
}
#endif

/******************************************************************************/
/**
 * @brief               <handle service 0x34 request download>
 * 
 * <handle service 0x34 request download> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/

void Dcm_RecvMsg34(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    boolean processContinue = TRUE;
    uint32 programSize;
    FL_ResultType requestRet;
    uint8 formatId;
    uint8 dataFormat;

    /*锟斤拷锟斤拷欠锟斤拷诘锟窖癸拷锟轿э拷锟�*/

    if (TRUE == processContinue)
    {    /* check if received message length is right, other wise send NRC */
    	processContinue = Dcm_CheckMsgLength(0x0Bu, rxBuff->pduInfo.SduLength,TRUE);
    }

    if (TRUE == processContinue)
    {
        /* check if service is supportted in current security level, other wise send NRC */
        processContinue = Dcm_CheckSecurityCondition(
            Dcm_DownloadRow.securitySupportMask);
    }

    if (TRUE == processContinue)
    {
        formatId = rxBuff->pduInfo.SduDataPtr[1];
        dataFormat = rxBuff->pduInfo.SduDataPtr[2];
        
        /* check if format Id and data format is correct */
        if ((0x00u == formatId) && (0x44u == dataFormat))
        {
            /* set program index to 0, for service 0x36 */
            dcmDspProgram.blockId = 0x00u;

            /* get program address */
            dcmDspProgram.address = Dcm_Get4Byte(&rxBuff->pduInfo.SduDataPtr[3]);
            if(dcmDspProgram.address!=0x0)
            {
            	dcmDspProgram.address=dcmDspProgram.address|0xa0000000;
            }
            else
            {

            }

            /* get program length */
            programSize = Dcm_Get4Byte(&rxBuff->pduInfo.SduDataPtr[7]);
//            if(dcmDspProgram.address==0xa0080000)
//            {
//            	requestRet =FL_OK;
//            	dcmDspProgram.address=0xa0080000;
//            }
            /* check if program address and length is correct */
            requestRet =FL_OK;
            if (FL_OK == requestRet)
            {
            	 processContinue=Dcm_CheckVoltageCondition();

            	 if(TRUE ==processContinue)
            	 {
            		 Dcm_RawEraseMemory(dcmDspProgram.address,programSize);
            	 }

            }
            else if (FL_ERR_SEQUENCE == requestRet)
            {
                /* 0x34,0x36,0x37 service sequence is not correct */
                Dcm_SendNcr(DCM_E_CONDITION_NOT_CORRECT);
            }
            else if (FL_NO_FINGERPRINT == requestRet)
            {
                /* fingerprint is not written */
                Dcm_SendNcr(DCM_E_UPLOAD_DOWNLOAD_NOT_ACCEPTED);
            }
            else
            {
                /* Request out of range */
                Dcm_SendNcr(DCM_E_REQUEST_OUT_OF_RANGE);
            }
        }
        else
        {
            /* Request out of range */
            Dcm_SendNcr(DCM_E_REQUEST_OUT_OF_RANGE);
        }
    }

    return;
}

/******************************************************************************/
/**
 * @brief               <handle service 0x36 transfer data>
 * 
 * <handle service 0x36 transfer data> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
uint32 headRemainDataLength = 0;
void Dcm_RecvMsg36(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    boolean processContinue = TRUE;
    uint32 programSize;
    FL_ResultType programRet;
    uint8 dataBlockId;



    if (TRUE == processContinue)
    {
    	/* check if received message length is right, other wise send NRC */
    	 processContinue = Dcm_CheckMsgLength(0x03u, rxBuff->pduInfo.SduLength,FALSE);
    }



#if (STD_ON == DCM_SID_NRC_SUPPORT)
    if (TRUE == processContinue)
    {
        /* check if service is supportted in current security level, other wise send NRC */
        processContinue = Dcm_CheckSecurityCondition(
            Dcm_DownloadRow.securitySupportMask);
    }
#endif
    
    if (TRUE == processContinue)
    {
    /* get program block index */
        dataBlockId = rxBuff->pduInfo.SduDataPtr[1];
        /*receive the same dataBlockId again*/
        if (dcmDspProgram.blockId == dataBlockId)
        {
            txBuff->pduInfo.SduDataPtr[0] = 0x76u;
            txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
            txBuff->pduInfo.SduLength = 0x02u;
            Dcm_SendRsp();
            processContinue = FALSE;
        }
    }

    if(TRUE == processContinue)
    {
    	 processContinue=Dcm_CheckVoltageCondition();
    }

    if (TRUE == processContinue)
    {        
        /* index programmed block index */
        if (0xFFu == dcmDspProgram.blockId)
        {
            dcmDspProgram.blockId = 0;
        }
        else
        {
            dcmDspProgram.blockId++;
        }
        
        if (dcmDspProgram.blockId != dataBlockId)
        {
            /* program block index is not correct */
            Dcm_SendNcr(DCM_E_WRONG_BLOCK_SEQUENCE_COUNTER);
        }
        else
        {
            programSize = (uint32)rxBuff->pduInfo.SduLength - 0x02uL;
#if 0
        	if ((FL_BlkInfo[0].address <= dcmDspProgram.address) && (FL_BlkInfo[0].address + FL_BlkInfo[0].length >= dcmDspProgram.address))
        	{
        		headRemainDataLength = headRemainDataLength - programSize;
        		if (0 == headRemainDataLength)
        		{
        			FL_SetExitTransferStep();
        		}
        		programRet = FL_OK;
        	}
        	else
#endif
        	{
                /* excute program routine */
                programRet = FL_FlashProgramRegion(dcmDspProgram.address,
                    &rxBuff->pduInfo.SduDataPtr[2],
                    programSize);
        	}

            /* index program address */
            dcmDspProgram.address += programSize;
            
            if (FL_ERR_SEQUENCE == programRet)
            {
                /* 0x34,0x36,0x37 service sequence is not correct */
                Dcm_SendNcr(DCM_E_REQUEST_SEQUENCE_ERROR);
            }
            else if (FL_ERR_ADDR_LENGTH == programRet)
            {
                /* program length is not the same as received in 0x34 service */
                Dcm_SendNcr(DCM_E_TRANSFER_DATA_SUSPENDED);
            }
            else
            {
                /* wait for pending */
                Dcm_SendNcr(DCM_E_PENDING);
                pendingReq = TRUE;
            }
        }
    }
    
    return;
}

/******************************************************************************/
/**
 * @brief               <handle service 0x37 exit transfer data>
 * 
 * <handle service 0x37 exit transfer data> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_RecvMsg37(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    boolean processContinue = TRUE;
    FL_ResultType programRet,crcret;
    uint8 crcType=rxBuff->pduInfo.SduDataPtr[1];
    uint8 rxLength=rxBuff->pduInfo.SduLength;
    //FL_ResultType crcret;
    SecM_CRCType crc=0;
#if (STD_ON == DCM_SID_NRC_SUPPORT)
    
    /* check if received message length is right, other wise send NRC */
    processContinue = Dcm_CheckMsgLength(0x01u, rxBuff->pduInfo.SduLength);

    if (TRUE == processContinue)
    {
        /* check if service is supportted in current security level, other wise send NRC */
        processContinue = Dcm_CheckSecurityCondition(
            Dcm_DownloadRow.securitySupportMask);
    }
#endif
    processContinue = Dcm_CheckMsgLength(0x02u, rxBuff->pduInfo.SduLength,TRUE);

    if(TRUE == processContinue)
    {
    	if((0x00!=crcType)&&(0x01!=crcType))
		{
			 Dcm_SendNcr(DCM_E_SUBFUNC_NOT_SUPPORTED);
			 processContinue=FALSE;
		}
    }

    if (TRUE == processContinue)
    { 
        if(FL_SignVerifFlags() == FBL_TRUE)
        {
            /*Sign Verify*/
            Dcm_SendNcr(DCM_E_PENDING);
            pendingReq = TRUE;
        }
        else
        {
            /* execute tranfer data exit */
            programRet = FL_ExitTransferData();

            if (FL_OK == programRet)
            {
                dcmDspProgram.blockId = 0;
                
                /* set response message */
                txBuff->pduInfo.SduDataPtr[0] = 0x77u;

                switch(crcType)
                {
                    case 0x00: //CRC not required
                    {
                        txBuff->pduInfo.SduDataPtr[1] = crcType;
                        txBuff->pduInfo.SduLength = 0x02u;
                        Dcm_SendRsp();
						blsDIDF187WriteOnlyOnceFlag=0x01;
                        break;
                    }
                    case 0x01://CRC required
                    {
                        /*calculate CRC*/
                        Dcm_SendNcr(DCM_E_PENDING);
                        pendingReq = TRUE;

                        SetS37JobStatusBusy();
                        crcret=FBL_CheckSumFor37(&crc);
                        dcms37_crcResult.crcStatus=TRUE;
                        dcms37_crcResult.crcResult=crc;
                        GetS37JobStatusResult(crcret);
                        blsDIDF187WriteOnlyOnceFlag=0x01;
                        break;
                    }
                    default:
                    {
                        break;
                    }
                }
            }
            else
            {
                /* 0x34,0x36,0x37 service sequence is not correct */
                Dcm_SendNcr(DCM_E_REQUEST_SEQUENCE_ERROR);
            }
        }

    }
    
    return;
}

/******************************************************************************/
/**
 * @brief               <handle service 0x31 routine control>
 * 
 * <handle service 0x31 routine control> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_RecvMsg31(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    boolean processContinue = TRUE;
    uint16 routineIdValue;
    const Dcm_RoutineControlRowType * routineTablePtr = Dcm_RoutineControlRow;
    uint8 routineTableIndex;
    boolean routineIdFind;
    
    /* get routine control type */
    uint8 routineControlValue = rxBuff->pduInfo.SduDataPtr[1];

	routineTableIndex = DCM_ROUTINE_CONTROL_NUM;
	routineIdFind = FALSE;

	/* get routine Id */
	routineIdValue = ((uint16)rxBuff->pduInfo.SduDataPtr[2]) << 0x08u;
	routineIdValue += (uint16)rxBuff->pduInfo.SduDataPtr[3];

	/* find routine Id in routine table */
	while ((routineTableIndex > 0) && (FALSE == routineIdFind))
	{
		routineTableIndex--;
		if ((routineIdValue == routineTablePtr->routineId) &&
			((DCM_RX_PHY_PDU_ID == rxBuff->pduId) ||
				((DCM_RX_FUNC_PDU_ID == rxBuff->pduId) &&
					(TRUE == routineTablePtr->funcAddrSupp))))
		{
			routineIdFind = TRUE;
		}
		else
		{
			routineTablePtr++;
		}
	}

	if(TRUE==routineIdFind)
	{
		processContinue = Dcm_CheckMsgLength(
					0x04u + (uint16)routineTablePtr->optionLength,
					rxBuff->pduInfo.SduLength,TRUE);
	}
	if(TRUE == processContinue)
	{
		/* check if subfunction is start routine */
		if (DCM_START_ROUTINE != routineControlValue)
		{
			/* if did not find subfunciton,send NRC */
			processContinue = Dcm_CheckSubFuncFind(FALSE, rxBuff);
		}

	}

	if ((TRUE == processContinue)&&(TRUE==routineIdFind))
	{
		/* check if subfunciton is supportted in current security level, other wise send NRC */
		processContinue = Dcm_CheckSecurityCondition(
			routineTablePtr->securitySupportMask);
	}


	if(TRUE == processContinue)
	{
		/* if routine Id is not find,send NRC */
		processContinue = Dcm_CheckReqOutOfRange(routineIdFind, rxBuff);
	}

#if (STD_ON == DCM_SID_NRC_SUPPORT)
    if (TRUE == processContinue)
    {
        /* check if subfunciton is supportted in current session, other wise send NRC */
        processContinue = Dcm_CheckSubFuncSession(routineTablePtr->sessionSupportMask);
    }
 #endif
    

    if (TRUE == processContinue)
    {
        /* check if RoutineID is supportted in current session, other wise send NRC */
        processContinue = Dcm_CheckRoutineIDSession(routineTablePtr->sessionSupportMask);
    }
    


    if (TRUE == processContinue)
    {
        /* set response message */
        txBuff->pduInfo.SduDataPtr[0] = 0x71u;
        txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
        txBuff->pduInfo.SduDataPtr[2] = rxBuff->pduInfo.SduDataPtr[2];
        txBuff->pduInfo.SduDataPtr[3] = rxBuff->pduInfo.SduDataPtr[3];
        
        /* execute routine */
        routineTablePtr->routineControl(rxBuff, txBuff);
    }
    
    return;
}

/******************************************************************************/
/**
 * @brief               <handle service 0x31 check program pre-condition>
 * 
 * <handle service 0x31 check program pre-condition> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_CheckProgPreCondition(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    uint8 conditionLength;
    
    /* execute routine for check programming pre-conditions */
    conditionLength = FL_CheckProgPreCondition(&txBuff->pduInfo.SduDataPtr[4]);

    if (0x00u == conditionLength)
    {
        /* programming pre-conditions is acceptted */
        dcmDspProgram.condition = TRUE;
    }
    else
    {
        /* programming pre-conditions is not acceptted */
        dcmDspProgram.condition = FALSE;
    }
    
    /* set response message */
    txBuff->pduInfo.SduLength = (PduLengthType)conditionLength + 0x05u;
    Dcm_SendRsp();

    return;
}

/******************************************************************************/
/**
 * @brief               <handle service 0x31 check dependency>
 * 
 * <handle service 0x31 check dependency> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_CheckProgDependencies(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{

    uint8 errorValue=0;
    /* execute routine for check application software dependency */

    Dcm_SendNcr(DCM_E_PENDING);
    pendingReq = TRUE;
    FL_CheckCPBRoutine();
    return;
}


/******************************************************************************/
/**
 * @brief               <handle service 0x31 check software verification>
 *
 * <handle service 0x31 check dependency> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>
 */
/******************************************************************************/
void Dcm_CheckSWVerification(const Dcm_BuffType * rxBuff,Dcm_BuffType * txBuff)
{


	txBuff->pduInfo.SduDataPtr[4]=rxBuff->pduInfo.SduDataPtr[4];
	/*by default check successfully always.  by fhq*/
	txBuff->pduInfo.SduDataPtr[5]=0x00;
	switch(rxBuff->pduInfo.SduDataPtr[4])
	{
		case 0:
		{
			/*do not update the SW verification information*/
			if(FL_CheckSWVerification(&txBuff->pduInfo.SduDataPtr[6]))
			{
				/*check fail*/
				txBuff->pduInfo.SduDataPtr[5]=0x01;
			}
			else
			{
				/*check ok*/
				txBuff->pduInfo.SduDataPtr[5]=0x00;
			}

			break;
		}
		case 1:
		{
			/* update the SW verification information*/
			if(FL_CheckSWVerification(&txBuff->pduInfo.SduDataPtr[6]))
			{
				/*check fail*/
				txBuff->pduInfo.SduDataPtr[5]=0x01;
			}
			else
			{
				/*check ok*/
				txBuff->pduInfo.SduDataPtr[5]=0x00;
			}
			break;
		}
		default:
		{
			break;
		}
	}
}


/******************************************************************************/
/**
 * @brief               <handle service 0x31 checksum>
 * 
 * <handle service 0x31 checksum> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_CheckMemory(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{

    FL_ResultType checksumRet;

    /* excute checksum routine */
    // checksumRet = FL_CheckSumRoutine(&rxBuff->pduInfo.SduDataPtr[4]);
    checksumRet = FBL_IntegrityCheck();
    if (FL_OK == checksumRet)
    {
        /* wait for pending */

    	/*20200623 add by fhq*/
    	 Dcm_SendNcr(DCM_E_PENDING);
    	 pendingReq = TRUE;
         Appl_UpdateTriggerConditionImmediate(1);
    }
    else if (FL_ERR_SEQUENCE == checksumRet)
    {
        /* checksum sequence error,no fingerprint or download data */
        Dcm_SendNcr(DCM_E_REQUEST_SEQUENCE_ERROR);
    }
    else
    {
        Dcm_SendNcr(DCM_E_CONDITION_NOT_CORRECT);
    }

    return;
}

/******************************************************************************/
/**
 * @brief               <handle service 0x31 erase block>
 * 
 * <handle service 0x31 erase block> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_EraseMemory(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    FL_ResultType eraseRet = FL_INVALID_DATA;
    uint8 parameterLen;
    uint32 eraseAddress;
    uint32 eraseLength;
    uint8 index;

    parameterLen = rxBuff->pduInfo.SduDataPtr[4];
    eraseAddress = Dcm_Get4Byte(&rxBuff->pduInfo.SduDataPtr[5]);
    eraseLength = Dcm_Get4Byte(&rxBuff->pduInfo.SduDataPtr[9]);

    if (0x44u == parameterLen)
    {
        for (index = 0; index < FL_NUM_LOGICAL_BLOCKS; index++)
        {
//            if((eraseAddress == FL_BlkInfo[index].address) && (eraseLength <= FL_BlkInfo[index].length))
        	if((eraseAddress == FL_BlkInfo[index].address)&&((eraseAddress+eraseLength) <= (FL_BlkInfo[index].address+FL_BlkInfo[index].length)))
            {
               /* excute erase routine */
            	CurrentProgrammingBlock=index;
            	CurrentErasingAddress=eraseAddress;
            	CurrentErasingLength=eraseLength;
               eraseRet=FL_EraseRoutine(index);
            }
        }

        if (FL_OK == eraseRet)
        {
            /* wait for pending */
            Dcm_SendNcr(DCM_E_PENDING);
            pendingReq = TRUE;
        }
        else if ((FL_NO_FINGERPRINT == eraseRet) ||
            (FL_ERR_SEQUENCE == eraseRet) ||
            (FL_NO_FLASHDRIVER == eraseRet))
        {
            /* erase sequence error,no fingerprint or download data */
            Dcm_SendNcr(DCM_E_REQUEST_SEQUENCE_ERROR);
        }
        else if (FL_INVALID_DATA == eraseRet)
        {
            Dcm_SendNcr(DCM_E_REQUEST_OUT_OF_RANGE);
        }
        else
        /* FL_FAILED == eraseRet */
        {
            Dcm_SendNcr(DCM_E_GENERAL_PROGRAMMING_FAILURE);
        }
    }
    else
    {
        Dcm_SendNcr(DCM_E_REQUEST_OUT_OF_RANGE);
    }

    return;
}

/******************************************************************************/
/**
 * @brief               <handle service 0x34 erase block>
 *
 * <handle service 0x31 erase block> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>
 */
/******************************************************************************/
void Dcm_RawEraseMemory(const uint32 startAdd,const uint32 length)
{
    FL_ResultType eraseRet = FL_INVALID_DATA;
    uint32 eraseAddress;
    uint32 eraseLength;
    uint8 index;


    eraseAddress = startAdd;
    eraseLength = length;


	for (index = 0; index < FL_NUM_LOGICAL_BLOCKS; index++)
	{
        if((eraseAddress == FL_BlkInfo[index].address) && (eraseLength <= FL_BlkInfo[index].length))
		{
 		    /* excute erase routine */
    		CurrentProgrammingBlock=index;
 			CurrentErasingAddress=eraseAddress;
 			CurrentErasingLength=eraseLength;

        	if (0 == index)
        	{
        		headRemainDataLength = FL_BlkInfo[index].length;
        		FL_SetHeadBlockErased();
        		eraseRet = FL_OK;
        	}
        	else
        	{
     		    eraseRet=FL_EraseRoutine(index);
        	}
		}
	}

	if (FL_OK == eraseRet)
	{
		/* wait for pending */
		Dcm_SendNcr(DCM_E_PENDING);
		pendingReq = TRUE;
	}
	else if ((FL_NO_FINGERPRINT == eraseRet) ||
		(FL_ERR_SEQUENCE == eraseRet) ||
		(FL_NO_FLASHDRIVER == eraseRet))
	{
		/* erase sequence error,no fingerprint or download data */
		Dcm_SendNcr(DCM_E_REQUEST_SEQUENCE_ERROR);
	}
	else if (FL_INVALID_DATA == eraseRet)
	{
		Dcm_SendNcr(DCM_E_REQUEST_OUT_OF_RANGE);
	}
	else
	/* FL_FAILED == eraseRet */
	{
		Dcm_SendNcr(DCM_E_GENERAL_PROGRAMMING_FAILURE);
	}


    return;
}


/******************************************************************************/
/**
 * @brief               <handle service 0x31 erase block>
 *
 * <handle service 0x31 erase block> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>
 */
/******************************************************************************/
void Dcm_DIDEraseMemory(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    FL_ResultType eraseRet = FL_INVALID_DATA;
    uint8 parameterLen;
    uint32 eraseAddress;
    uint32 eraseLength;
    uint8 index;

    parameterLen = rxBuff->pduInfo.SduDataPtr[4];
    eraseAddress = Dcm_Get4Byte(&rxBuff->pduInfo.SduDataPtr[5]);
    eraseLength = Dcm_Get4Byte(&rxBuff->pduInfo.SduDataPtr[9]);

    if (0x44u == parameterLen)
    {
            index = 0;

            if((eraseAddress == 0xAF010000) && (eraseLength <= (64*1024)))
            {
               /* excute erase routine */
//              eraseRet = FL_DIDEraseRoutine(index);
            }
    }
    else
    {
        Dcm_SendNcr(DCM_E_REQUEST_OUT_OF_RANGE);
    }

    return;
}


/******************************************************************************/
/**
 * @brief               <handle service 0x3E test precent>
 * 
 * <handle service 0x3E test precent> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_RecvMsg3E(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
#if (STD_ON == DCM_SERVICE_3E_SUPPRESS_POS_SUPPORT)
    boolean positiveRspReq = TRUE;
#endif

    boolean processContinue = TRUE;
    boolean testPresentFind = FALSE;
    uint8 testPresentTableIndex = DCM_TESTPRESENT_NUM;
    uint8 zeroSubFunc = (rxBuff->pduInfo.SduDataPtr[1] & DCM_RSP_CLEAR_REQUIRED);
    const Dcm_testPresentRowType *testPresentTablePtr = Dcm_testPresentRow;

#if (STD_ON == DCM_SERVICE_3E_SUPPRESS_POS_SUPPORT)
    /* check if response is needed */
    if ((rxBuff->pduInfo.SduDataPtr[1] & DCM_RSP_NOT_REQUIRED) > 0x00u)
    {
        positiveRspReq = FALSE;
    }
#endif

    /* find testpresent subfunction */
    while ((testPresentTableIndex > 0) && (FALSE == testPresentFind))
    {
        testPresentTableIndex--;
        if (zeroSubFunc == testPresentTablePtr->zeroSubFunc)
        {
            testPresentFind = TRUE;
        }
        else
        {
            testPresentTablePtr++;
        }
    }
    
    if (TRUE == processContinue)
	{
		/* check if received message length is right, other wise send NRC */
		processContinue = Dcm_CheckMsgLength(0x02u, rxBuff->pduInfo.SduLength,TRUE);
	}

    if(TRUE == processContinue)
    {
    	/* if did not find subfunciton, send NRC */
		processContinue = Dcm_CheckSubFuncFind(testPresentFind, rxBuff);
    }

    
    

    if (TRUE == processContinue)
    {
        /* set response message */
#if (STD_ON == DCM_SERVICE_3E_SUPPRESS_POS_SUPPORT)
            if (TRUE == positiveRspReq)
            {
#endif

        txBuff->pduInfo.SduDataPtr[0] = 0x7Eu;
        txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
        txBuff->pduInfo.SduLength = 0x02u;
        Dcm_SendRsp();
#if (STD_ON == DCM_SERVICE_3E_SUPPRESS_POS_SUPPORT)
            }
            else
            {
                /* reset service process */
                Dcm_ServiceFinish();
            }
#endif

    }
    
    return;
}

#if (DCM_DTC_SET_NUM > 0)
/******************************************************************************/
/**
 * @brief               <handle service 0x85 set DTC control>
 * 
 * <handle service 0x85 set DTC control> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_RecvMsg85(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    Dcm_DTCSettingType DTCsetType = rxBuff->pduInfo.SduDataPtr[1];
    uint8 DTCsetTableIndex = DCM_DTC_SET_NUM;
    const Dcm_DTCSettingRowType * DTCsetTablePtr = Dcm_DTCSettingRow;
    boolean DTCsetTypeFind = FALSE;
    boolean processContinue = TRUE;

    /* find DTC setting subfunction */
    while ((DTCsetTableIndex > 0) && (FALSE == DTCsetTypeFind))
    {
        DTCsetTableIndex--;
        if (DTCsetType == DTCsetTablePtr->DTCSet)
        {
            DTCsetTypeFind = TRUE;
        }
        else
        {
            DTCsetTablePtr++;
        }
    }
    
    /* if did not find subfunciton,send NRC */
    processContinue = Dcm_CheckSubFuncFind(DTCsetTypeFind, rxBuff);
    
#if (STD_ON == DCM_SID_NRC_SUPPORT)
    if (TRUE == processContinue)
    {
        /* check if subfunciton is supportted in current session, other wise send NRC */
        processContinue = Dcm_CheckSubFuncSession(DTCsetTablePtr->sessionSupportMask);
    }
#endif
    
    if (TRUE == processContinue)
    {
        /* check if received message length is right, other wise send NRC */
        processContinue = Dcm_CheckMsgLength(0x02u, rxBuff->pduInfo.SduLength,TRUE);
    }

#if (STD_ON == DCM_SID_NRC_SUPPORT)
    if (TRUE == processContinue)
    {
        /* check if DID is supportted in current security level, other wise send NRC */
        processContinue = Dcm_CheckSecurityCondition(
            DTCsetTablePtr->securitySupportMask);
    }
#endif

    if (TRUE == processContinue)
    {
        /* set response message */
        txBuff->pduInfo.SduDataPtr[0] = 0xC5u;
        txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
        txBuff->pduInfo.SduLength = 0x02u;
        Dcm_SendRsp();
    }
    
    return;
}
#endif
/******************************************************************************/
/**
 * @brief               <handle service 0x2E write data pending finish>
 * 
 * <handle service 0x2E write data pending finish> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <errorCode (IN),rxBuff (IN),>
 * @param[out]          <NONE>
 * @param[in/out]       <txBuff (IN/OUT)>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_Pending27(const FL_ResultType errorCode,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    const Dcm_SecurityRowType * secTablePtr = Dcm_SecurityRow;
    if(FL_GetSecErrFlag() == 0)
    {
		/* set to requested security level */
		Dcm_SetSecurityLevel(secTablePtr->secAccessLevel);

		/* clear security access attempt num */
		dcmDspSec.attempt = 0x00u;

		/* set response message */
		txBuff->pduInfo.SduDataPtr[0] = 0x67u;
		txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
		txBuff->pduInfo.SduLength = 0x02u;
		Dcm_SendRsp();

    }
    else if(FL_GetSecErrFlag() == 1)
    {
		/* compare key fialed, increat security access attempt num */
		dcmDspSec.attempt++;
		/* security access attempt num is overflow,*/
		if (dcmDspSec.attempt < DCM_SECURITY_ATTEMPT_NUM)
		{
			/* the sent key is invalid */
			Dcm_SendNcr(DCM_E_INVALID_KEY);
		}
		else
		{
			/* num of security access is exceeded */
			dcmDspSec.attempt--;

			/* start security timer */
			Dcm_StartSecurityTimer(DCM_SECURITY_TIME);

			/* send NRC */
			Dcm_SendNcr(DCM_E_EXCEEDED_NUMBER_OF_ATTEMPTS);
		}

    }
    else
    {

    }    
    return;
}
/******************************************************************************/
/**
 * @brief               <handle service 0x2E write data pending finish>
 * 
 * <handle service 0x2E write data pending finish> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <errorCode (IN),rxBuff (IN),>
 * @param[out]          <NONE>
 * @param[in/out]       <txBuff (IN/OUT)>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_Pending2E(const FL_ResultType errorCode,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    /* check if write data successful */
    if (FL_OK == errorCode)
    {
        txBuff->pduInfo.SduDataPtr[0] = 0x6Eu;
        txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
        txBuff->pduInfo.SduDataPtr[2] = rxBuff->pduInfo.SduDataPtr[2];
        txBuff->pduInfo.SduLength = 0x03u;
        Dcm_SendRsp();
    }
    else
    {
        /* program finger print failure */
        Dcm_SendNcr(DCM_E_GENERAL_PROGRAMMING_FAILURE);
    }
    
    return;
}

/******************************************************************************/
/**
 * @brief               <handle service 0x36 transfer data pending finish>
 * 
 * <handle service 0x36 transfer data pending finish> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <errorCode (IN),rxBuff (IN),>
 * @param[out]          <NONE>
 * @param[in/out]       <txBuff (IN/OUT)>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_Pending36(const FL_ResultType errorCode,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    /* check if program data successful */
    if (FL_OK == errorCode)
    {
        txBuff->pduInfo.SduDataPtr[0] = 0x76u;
        txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
        txBuff->pduInfo.SduLength = 0x02u;
        Dcm_SendRsp();
    }
    else
    {
        /* program data failure */
        Dcm_SendNcr(DCM_E_GENERAL_PROGRAMMING_FAILURE);
    }
    
    return;
}
/******************************************************************************/
/**
 * @brief               <handle service 0x37 exit transfer data pending finish>
 *
 * <handle service 0x36 transfer data pending finish> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <errorCode (IN),rxBuff (IN),>
 * @param[out]          <NONE>
 * @param[in/out]       <txBuff (IN/OUT)>
 * @return              <NONE>
 */
/******************************************************************************/
void Dcm_Pending37(const FL_ResultType errorCode,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    FL_ResultType programRet,crcret;
    uint8 crcType=rxBuff->pduInfo.SduDataPtr[1];
    SecM_CRCType crc=0;
    /* check if program data successful */
    if (FL_OK == errorCode)
    {
        // txBuff->pduInfo.SduDataPtr[0] = 0x77u;
        // txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];

        if(FL_SignVerifFlags() == FBL_TRUE)
        {
            /* execute tranfer data exit */
            programRet = FL_ExitTransferData();
            if(programRet == FL_OK)
            {
                switch(crcType)
                {
                    case 0x00: //CRC not required
                    {
                        FL_SetRequestStep();
                        txBuff->pduInfo.SduDataPtr[0] = 0x77u;
                        txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
                        // txBuff->pduInfo.SduDataPtr[1] = crcType;
                        txBuff->pduInfo.SduLength = 0x02u;
                        Dcm_SendRsp();
                        break;
                    }
                    case 0x01://CRC required
                    {
                        /*calculate CRC*/
                        // Dcm_SendNcr(DCM_E_PENDING);
                        // pendingReq = TRUE;
    
                        SetS37JobStatusBusy();
                        crcret=FBL_CheckSumFor37(&crc);
                        dcms37_crcResult.crcStatus=TRUE;
                        dcms37_crcResult.crcResult=crc;
                        GetS37JobStatusResult(crcret);
                        break;
                    }
                    default:
                    {
                        break;
                    }
                 }
            }
            else if(FL_ERR_SEQUENCE == programRet)
            {
                /* 0x34,0x36,0x37 service sequence is not correct */
                Dcm_SendNcr(DCM_E_REQUEST_SEQUENCE_ERROR);
            }
            else if(FBL_SECURE_MODULEID_UNMATCH == programRet)
            {
                /* The moduleID of header doesn't match the address. */
                Dcm_SendNcr(DCM_E_SECURE_MODULEID_UNMATCH);
            }
            else if(FBL_SECURE_CERT_VERIF_FAIL == programRet)
            {
                /* The certification of Sign is not valid */
                Dcm_SendNcr(DCM_E_SECURE_CERT_VERIF_FAIL);
            }
            else if(FBL_SECURE_SIGN_VERIF_FAIL == programRet)
            {
                /* The sign is not valid */
                Dcm_SendNcr(DCM_E_SECURE_SIGN_VERIF_FAIL);
            }
            else if(FBL_SECURE_VERSION_TOOLOW == programRet) 
            {
                /* The version of file to be programmed is lower than present */
                Dcm_SendNcr(DCM_E_SECURE_VERSION_TOOLOW);
            }
            else if(FBL_SECURE_OUT_OF_RANGE == programRet)
            {
                /* The address is out of range. */
                Dcm_SendNcr(DCM_E_SECURE_OUT_OF_RANGE);
            }
            
        }
        else
        {

        // if(rxBuff->pduInfo.SduDataPtr[1] == 0x01)
        {
            txBuff->pduInfo.SduDataPtr[0] = 0x77u;
            txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
            if(2==sizeof(dcms37_crcResult.crcResult))
            {
                if(dcms37_crcResult.crcStatus==TRUE)
                {
                    txBuff->pduInfo.SduDataPtr[2] =(uint8)((dcms37_crcResult.crcResult>>8)&0xFF);
                    txBuff->pduInfo.SduDataPtr[3] =(uint8)(dcms37_crcResult.crcResult&0xFF);
                }
                else
                {
                    txBuff->pduInfo.SduDataPtr[2] =(uint8)((dcms37_crcResult.crcDefault>>8)&0xFF);
                    txBuff->pduInfo.SduDataPtr[3] =(uint8)(dcms37_crcResult.crcDefault&0xFF);
                }
    
                txBuff->pduInfo.SduLength = 0x04u;
            }
            else
            {
                if(dcms37_crcResult.crcStatus==TRUE)
                {
                    txBuff->pduInfo.SduDataPtr[2] =(uint8)(dcms37_crcResult.crcResult>>24);
                    txBuff->pduInfo.SduDataPtr[3] =(uint8)((dcms37_crcResult.crcResult>>16)&0xFF);
                    txBuff->pduInfo.SduDataPtr[4] =(uint8)((dcms37_crcResult.crcResult>>8)&0xFF);
                    txBuff->pduInfo.SduDataPtr[5] =(uint8)(dcms37_crcResult.crcResult&0xFF);
                }
                else
                {
                    txBuff->pduInfo.SduDataPtr[2] =(uint8)(dcms37_crcResult.crcDefault>>24);
                    txBuff->pduInfo.SduDataPtr[3] =(uint8)((dcms37_crcResult.crcDefault>>16)&0xFF);
                    txBuff->pduInfo.SduDataPtr[4] =(uint8)((dcms37_crcResult.crcDefault>>8)&0xFF);
                    txBuff->pduInfo.SduDataPtr[5] =(uint8)(dcms37_crcResult.crcDefault&0xFF);
                }
    
                txBuff->pduInfo.SduLength = 0x06u;
            }
        }
            Dcm_SendRsp();
            dcms37_crcResult.crcStatus=FALSE;
        }

    }
    else
    {
        /* program data failure */
        Dcm_SendNcr(DCM_E_GENERAL_PROGRAMMING_FAILURE);
    }

    return;
}
/******************************************************************************/
/**
 * @brief               <handle service 0x31 routine control pending finish>
 * 
 * <handle service 0x31 routine control pending finish> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <errorCode (IN),rxBuff (IN),>
 * @param[out]          <NONE>
 * @param[in/out]       <txBuff (IN/OUT)>
 * @return              <NONE>    
 */
/******************************************************************************/
void Dcm_Pending31(const FL_ResultType errorCode,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    /* check if execute routine successful */
	uint16 did=0;
	FL_ResultType consistencyStatus;
        /* execute routine failure */

	did=(rxBuff->pduInfo.SduDataPtr[2]<<8)+rxBuff->pduInfo.SduDataPtr[3];
	switch(did)
	{

		case 0xDFFF:
		{
			txBuff->pduInfo.SduDataPtr[4]= Fl_crc;//FL_CheckProgramIntegrity();
            if(Fl_crc==0)
            {
                if(blsDIDF187WriteOnlyOnceFlag==1)
                {
                  blsDIDF187WriteOnlyOnceFlag=2;
                }
                else
                {

                }
            }
			break;
		}
		case 0xFF00:
		{
			txBuff->pduInfo.SduDataPtr[4]=0x00;
			break;
		}
		case 0xFF01:
		{
			 consistencyStatus = FL_CheckProgramDependencies(&txBuff->pduInfo.SduDataPtr[4]);
				if (FL_OK == consistencyStatus)
				{
					*(uint8 *)FL_APPL_UPDATE = FL_APPL_UPDATED;

                    if(blsDIDF187WriteOnlyOnceFlag==2)
                    {
                      blsDIDF187WriteOnlyOnceFlag=3;
                    }
                    else
                    {

                    }                   
				}
				break;
		}
		default:
		{
			txBuff->pduInfo.SduDataPtr[4]=0x00;
			break;
		}
	}

    txBuff->pduInfo.SduDataPtr[0] = 0x71u;
    txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
    txBuff->pduInfo.SduDataPtr[2] = rxBuff->pduInfo.SduDataPtr[2];
    txBuff->pduInfo.SduDataPtr[3] = rxBuff->pduInfo.SduDataPtr[3];
    txBuff->pduInfo.SduLength = 0x05u;

    Dcm_SendRsp();
    return;
}




void Dcm_Pending34(const FL_ResultType errorCode,const Dcm_BuffType * rxBuff,Dcm_BuffType * txBuff)
{

	uint32 address,length;
	if (FL_OK == errorCode)
	{
		/* get program address */
		address = Dcm_Get4Byte(&rxBuff->pduInfo.SduDataPtr[3]);
		if(address!=0)
		{
		  address=address|0xA0000000;
		}
		else
		{

		}
		/* get program length */
		length = Dcm_Get4Byte(&rxBuff->pduInfo.SduDataPtr[7]);

		FL_DownloadRequestValid(address, length);
		 /* set response message */
		txBuff->pduInfo.SduDataPtr[0] = 0x74u;
		txBuff->pduInfo.SduDataPtr[1] = 0x20u;
		txBuff->pduInfo.SduDataPtr[2] = (uint8)(DCM_RX_BUF_SIZE >> 0x08u);
		txBuff->pduInfo.SduDataPtr[3] = (uint8)DCM_RX_BUF_SIZE;
		txBuff->pduInfo.SduLength = 0x04u;
		Dcm_SendRsp();
	}
	else
	{
		 /* program data failure */
		Dcm_SendNcr(DCM_E_GENERAL_PROGRAMMING_FAILURE);
	}
}

/******************************************************************************/
/**
 * @brief               <get uint32 from data buffer>
 * 
 * <get uint32 from data buffer> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <data (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <uint32>    
 */
/******************************************************************************/
STATIC uint32 Dcm_Get4Byte(const uint8* data)
{
    uint32 retData;
    
    retData = ((uint32)data[0]) << 24;
    retData += ((uint32)data[1]) << 16;
    retData += ((uint32)data[2]) << 8;
    retData += (uint32)data[3];
    
    return retData;
}

/******************************************************************************/
/**
 * @brief               <set uint32 to data buffer>
 * 
 * <set uint32 to data buffer> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <data (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <uint32>    
 */
/******************************************************************************/
STATIC void Dcm_Set4Byte(uint8* destData,
    const uint32 sourceData)
{
    destData[0] = (uint8)(sourceData >> 24u);
    destData[1] = (uint8)(sourceData >> 16u);
    destData[2] = (uint8)(sourceData >> 8u);
    destData[3] = (uint8)sourceData;
    
    return;
}

/******************************************************************************/
/**
 * @brief               <check if subfunction is supported in current session>
 * 
 * <check if subfunction is supported in current session> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <sessionTable (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <boolean>    
 */
/******************************************************************************/
STATIC boolean Dcm_CheckSubFuncSession(const Dcm_SessionType sessionSupportMask)
{
    boolean ret;

    ret = Dcm_CheckSessionSupp(sessionSupportMask);
    
    if (FALSE == ret)
    {
        /* subfunction is not supportted in active session*/
        Dcm_SendNcr(DCM_E_SUBFUNC_NOT_SUPPORTED_INACTIVE_SESSION);
    }

    return ret;
}

/******************************************************************************/
/**
 * @brief               <check if RoutineID is supported in current session>
 * 
 * <check if RoutineID is supported in current session> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <sessionTable (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <boolean>    
 */
/******************************************************************************/
STATIC boolean Dcm_CheckRoutineIDSession(const Dcm_SessionType sessionSupportMask)
{
    boolean ret;

    ret = Dcm_CheckSessionSupp(sessionSupportMask);
    
    if (FALSE == ret)
    {
        /* RoutineID is not supportted in active session*/
        Dcm_SendNcr(DCM_E_CONDITION_NOT_CORRECT);
    }

    return ret;
}

/******************************************************************************/
/**
 * @brief               <check if receive message length is correct>
 * 
 * <check if receive message length is correct> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <minlength (IN),receivedLength (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <boolean>    
 */
/******************************************************************************/
STATIC boolean Dcm_CheckMsgLength(const uint16 minlength,
    const uint16 receivedLength,boolean isfixlength)
{
    boolean ret = TRUE;
    
    if(TRUE==isfixlength)
    {
    	if (receivedLength != minlength)
		{
			ret = FALSE;

			/* service length is not correct */
			Dcm_SendNcr(DCM_E_INCORRECT_MESSAGE_LENGTH_OR_INVALID_FORMAT);
		}
    }
    else
    {
    	if (receivedLength < minlength)
		{
			ret = FALSE;

			/* service length is not correct */
			Dcm_SendNcr(DCM_E_INCORRECT_MESSAGE_LENGTH_OR_INVALID_FORMAT);
		}
    }


    return ret;
}

/******************************************************************************/
/**
 * @brief               <check if service is supportted in current security level>
 * 
 * <check if service is supportted in current security level> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <secLevTable (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <boolean>    
 */
/******************************************************************************/
STATIC boolean Dcm_CheckSecurityCondition(const Dcm_SecurityType securitySupportMask)
{
    boolean ret;

    ret = Dcm_CheckSecuritySupp(securitySupportMask);
    
    if (FALSE == ret)
    {
        /* security condition is not correct */
        Dcm_SendNcr(DCM_E_SECURITY_ACCESS_DENIED);
    }

    return ret;
}

/******************************************************************************/
/**
 * @brief               <check if subfunction is finded>
 * 
 * <check if subfunction is finded> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <find (IN),rxBuff (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <boolean>    
 */
/******************************************************************************/
STATIC boolean Dcm_CheckSubFuncFind(const boolean find,
    const Dcm_BuffType * rxBuff)
{
    if (FALSE == find)
    {
        if (DCM_RX_PHY_PDU_ID == rxBuff->pduId)
        {
            /* subfunction is not supportted */
            Dcm_SendNcr(DCM_E_SUBFUNC_NOT_SUPPORTED);
        }
        else
        {
            /* reset service process */
            Dcm_ServiceFinish();
        }
    }

    return find;
}

/******************************************************************************/
/**
 * 锟斤拷锟斤拷锟斤拷锟藉：check if voltage is OK
 * 锟斤拷锟斤拷锟斤拷锟诫：
 * 锟斤拷锟斤拷锟斤拷锟斤拷锟�
 *
 */
/******************************************************************************/

STATIC boolean Dcm_CheckVoltageCondition(void)
{
	boolean voltage_ok=TRUE;
    #define VOLTAGE_HIGN 16000U
	#define VOLTAGE_LOW 9000U

	 if(u16_Kl30_voltage>VOLTAGE_HIGN)
	 {
		Dcm_SendNcr(DCM_E_VOLTAGE_TOO_HIGH);
		voltage_ok=FALSE;
	 }
	 else if(u16_Kl30_voltage<VOLTAGE_LOW)
	 {
		 Dcm_SendNcr(DCM_E_VOLTAGE_TOO_LOW);
		 voltage_ok=FALSE;
	 }
    return voltage_ok;
}




/******************************************************************************/
/**
 * @brief               <check if condition is correct in service>
 * 
 * <check if condition is correct in service.
 * Check if Precondition is correct when changing into prog. session.> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <condition (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <boolean>    
 */
/******************************************************************************/
STATIC boolean Dcm_SessionChangeConditionCheck(Dcm_SessionType sessionValue)
{
    Dcm_SessionType curSession;

    curSession = Dcm_GetSessionMode();
    if (DCM_SESSION_PROGRAMMING == sessionValue)
    {
        /* check if program condition is acceptted, other wise send NRC */
        if (FALSE == dcmDspProgram.condition)
        {
            /* condition is not correct */
            Dcm_SendNcr(DCM_E_CONDITION_NOT_CORRECT);
        }
    }
    if ( ( DCM_SESSION_EXTEND == sessionValue ) && ( DCM_SESSION_PROGRAMMING == curSession ) )
    {
        /* check if program condition is acceptted, other wise send NRC */
        Dcm_SendNcr(DCM_E_SUBFUNC_NOT_SUPPORTED_INACTIVE_SESSION);
        return FALSE;
    }
    return dcmDspProgram.condition;
}

/******************************************************************************/
/**
 * @brief               <check if condition is correct in service>
 * 
 * <check if condition is correct in service> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <condition (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <boolean>    
 */
/******************************************************************************/
STATIC boolean Dcm_ResetConditionCheck(void)
{
    boolean condition = TRUE;
    
    if (FALSE == condition)
    {
        Dcm_SendNcr(DCM_E_CONDITION_NOT_CORRECT);
    }
    
    return condition;
}

/******************************************************************************/
/**
 * @brief               <check if request is out of range>
 * 
 * <check if request is out of range> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <condition (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <boolean>    
 */
/******************************************************************************/
STATIC boolean Dcm_CheckReqOutOfRange(const boolean find,
    const Dcm_BuffType * rxBuff)
{
    if (FALSE == find)
    {
        /* subfunction is not supportted */
        Dcm_SendNcr(DCM_E_REQUEST_OUT_OF_RANGE);
    }
    return find;
}

/******************************************************************************/
/**
 * @brief               <execute generate seed>
 * 
 * <execute generate seed> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <secTablePtr (IN),rxBuff (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <txBuff (IN/OUT)>
 * @return              <NONE>    
 */
/******************************************************************************/
STATIC void Dcm_RequestSeed(const Dcm_SecurityRowType * secTablePtr,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    Dcm_SecurityType secLevel;
    uint8 counter=0;
    secLevel = Dcm_GetSecurityLevel();
    
    /* check if seed has requested */
    if (secTablePtr->secAccessLevel == secLevel)
    {
        /* if seed has requested, seed is 0x00 */
        dcmDspSec.seed = 0x00uL;
    }
    else
    {
        dcmDspSec.seedReq = TRUE;
        
        /* record received seed Id */
        dcmDspSec.seedIdReq = secTablePtr->reqSeedId;
        
        /* execute generate seed */
        (void)secTablePtr->generateSeed(&dcmDspSec.seed);

        /* seed must not be 0xFFFFFFFF */
        while(0xFFFFFFFF==dcmDspSec.seed)
        {
        	counter++;
        	(void)secTablePtr->generateSeed(&dcmDspSec.seed);
        	if(counter>=5) break;
        }
    }
    
    /* set response message */
    txBuff->pduInfo.SduDataPtr[0] = 0x67u;
    txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
    Dcm_Set4Byte(&txBuff->pduInfo.SduDataPtr[2], dcmDspSec.seed);
    txBuff->pduInfo.SduLength = 0x06u;
    
    Dcm_SendRsp();
    SecurityLog_trigger = 0x0;
    return;
}

/******************************************************************************/
/**
 * @brief               <execute compare key>
 * 
 * <execute compare key> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <secTablePtr (IN),rxBuff (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <txBuff (IN/OUT)>
 * @return              <NONE>    
 */
/******************************************************************************/
STATIC void Dcm_SendKey(const Dcm_SecurityRowType * secTablePtr,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff)
{
    SecM_KeyType receivedKey;
    SecM_StatusType compareStatus;
    
    /* check if seed has requested */
    if ((TRUE == dcmDspSec.seedReq) &&
        (dcmDspSec.seedIdReq == secTablePtr->reqSeedId))
    {
        dcmDspSec.seedReq = FALSE;
        
        /* check the key length*/
        if(6!=rxBuff->pduInfo.SduLength)
        {
        	Dcm_SendNcr(DCM_E_INVALID_KEY);
            FailReason = DCM_E_INVALID_KEY;
            SecurityLog_trigger = 0x1;

        }
        else
        {
        	 /* get received key */
			receivedKey = (SecM_KeyType)Dcm_Get4Byte(
				&rxBuff->pduInfo.SduDataPtr[2]);
			/* compare key */
			compareStatus = secTablePtr->compareKey(receivedKey, dcmDspSec.seed);
			 /* check if compare key successful */
			if (SECM_OK == compareStatus)
			{
                if(0x01 == FL_GetSecErrFlag())
                {
                    SecurityErrorFlag = 0x00;
                    UpdateSecurityErrorFlag();
                }
                else
                {

                }                  
                /* set to requested security level */
                Dcm_SetSecurityLevel(secTablePtr->secAccessLevel);

                /* clear security access attempt num */
                dcmDspSec.attempt = 0x00u;

                /* set response message */
                txBuff->pduInfo.SduDataPtr[0] = 0x67u;
                txBuff->pduInfo.SduDataPtr[1] = rxBuff->pduInfo.SduDataPtr[1];
                txBuff->pduInfo.SduLength = 0x02u;
                Dcm_SendRsp();
                SecurityLog_trigger = 0x0;

			}
			else
			{
				/* set to requested security level */
				Dcm_SetSecurityLevel(DCM_SECURITY_LOCKED);
                if(0x00 == FL_GetSecErrFlag())
                {
                    SecurityErrorFlag = 0x01;
                    UpdateSecurityErrorFlag();
                }
                else
                {
                    /* do nothing */
                }
                /* compare key fialed, increat security access attempt num */
                dcmDspSec.attempt++;
                /* security access attempt num is overflow,*/
                if (dcmDspSec.attempt < DCM_SECURITY_ATTEMPT_NUM)
                {
                    /* the sent key is invalid */
                    Dcm_SendNcr(DCM_E_INVALID_KEY);
                    FailReason = DCM_E_INVALID_KEY;
                    SecurityLog_trigger = 0x1;
                }
                else
                {
                    /* num of security access is exceeded */
                    dcmDspSec.attempt--;

                    /* start security timer */
                    Dcm_StartSecurityTimer(DCM_SECURITY_TIME);

                    /* send NRC */
                    Dcm_SendNcr(DCM_E_EXCEEDED_NUMBER_OF_ATTEMPTS);
                    FailReason = DCM_E_EXCEEDED_NUMBER_OF_ATTEMPTS;
                    SecurityLog_trigger = 0x1;

                }


			}
        }
    }
    else
    {
        /* seed is not sent */
        Dcm_SendNcr(DCM_E_REQUEST_SEQUENCE_ERROR);
    }

    return;
}

/*=======[E N D   O F   F I L E]==============================================*/
