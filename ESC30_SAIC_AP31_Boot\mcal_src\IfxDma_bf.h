/**
 * \file IfxDma_bf.h
 * \brief
 * \copyright Copyright (c) 2016 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC23XADAS_UM_V1.1.R1
 * Specification: tc23xadas_um_sfrs_MCSFR.xml (Revision: UM_V1.1)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Dma_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Dma
 * 
 */
#ifndef IFXDMA_BF_H
#define IFXDMA_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Dma_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN0 */
#define IFX_DMA_ACCEN00_EN0_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN0 */
#define IFX_DMA_ACCEN00_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN0 */
#define IFX_DMA_ACCEN00_EN0_OFF (0u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN10 */
#define IFX_DMA_ACCEN00_EN10_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN10 */
#define IFX_DMA_ACCEN00_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN10 */
#define IFX_DMA_ACCEN00_EN10_OFF (10u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN11 */
#define IFX_DMA_ACCEN00_EN11_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN11 */
#define IFX_DMA_ACCEN00_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN11 */
#define IFX_DMA_ACCEN00_EN11_OFF (11u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN12 */
#define IFX_DMA_ACCEN00_EN12_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN12 */
#define IFX_DMA_ACCEN00_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN12 */
#define IFX_DMA_ACCEN00_EN12_OFF (12u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN13 */
#define IFX_DMA_ACCEN00_EN13_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN13 */
#define IFX_DMA_ACCEN00_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN13 */
#define IFX_DMA_ACCEN00_EN13_OFF (13u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN14 */
#define IFX_DMA_ACCEN00_EN14_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN14 */
#define IFX_DMA_ACCEN00_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN14 */
#define IFX_DMA_ACCEN00_EN14_OFF (14u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN15 */
#define IFX_DMA_ACCEN00_EN15_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN15 */
#define IFX_DMA_ACCEN00_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN15 */
#define IFX_DMA_ACCEN00_EN15_OFF (15u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN16 */
#define IFX_DMA_ACCEN00_EN16_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN16 */
#define IFX_DMA_ACCEN00_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN16 */
#define IFX_DMA_ACCEN00_EN16_OFF (16u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN17 */
#define IFX_DMA_ACCEN00_EN17_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN17 */
#define IFX_DMA_ACCEN00_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN17 */
#define IFX_DMA_ACCEN00_EN17_OFF (17u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN18 */
#define IFX_DMA_ACCEN00_EN18_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN18 */
#define IFX_DMA_ACCEN00_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN18 */
#define IFX_DMA_ACCEN00_EN18_OFF (18u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN19 */
#define IFX_DMA_ACCEN00_EN19_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN19 */
#define IFX_DMA_ACCEN00_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN19 */
#define IFX_DMA_ACCEN00_EN19_OFF (19u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN1 */
#define IFX_DMA_ACCEN00_EN1_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN1 */
#define IFX_DMA_ACCEN00_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN1 */
#define IFX_DMA_ACCEN00_EN1_OFF (1u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN20 */
#define IFX_DMA_ACCEN00_EN20_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN20 */
#define IFX_DMA_ACCEN00_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN20 */
#define IFX_DMA_ACCEN00_EN20_OFF (20u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN21 */
#define IFX_DMA_ACCEN00_EN21_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN21 */
#define IFX_DMA_ACCEN00_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN21 */
#define IFX_DMA_ACCEN00_EN21_OFF (21u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN22 */
#define IFX_DMA_ACCEN00_EN22_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN22 */
#define IFX_DMA_ACCEN00_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN22 */
#define IFX_DMA_ACCEN00_EN22_OFF (22u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN23 */
#define IFX_DMA_ACCEN00_EN23_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN23 */
#define IFX_DMA_ACCEN00_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN23 */
#define IFX_DMA_ACCEN00_EN23_OFF (23u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN24 */
#define IFX_DMA_ACCEN00_EN24_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN24 */
#define IFX_DMA_ACCEN00_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN24 */
#define IFX_DMA_ACCEN00_EN24_OFF (24u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN25 */
#define IFX_DMA_ACCEN00_EN25_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN25 */
#define IFX_DMA_ACCEN00_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN25 */
#define IFX_DMA_ACCEN00_EN25_OFF (25u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN26 */
#define IFX_DMA_ACCEN00_EN26_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN26 */
#define IFX_DMA_ACCEN00_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN26 */
#define IFX_DMA_ACCEN00_EN26_OFF (26u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN27 */
#define IFX_DMA_ACCEN00_EN27_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN27 */
#define IFX_DMA_ACCEN00_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN27 */
#define IFX_DMA_ACCEN00_EN27_OFF (27u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN28 */
#define IFX_DMA_ACCEN00_EN28_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN28 */
#define IFX_DMA_ACCEN00_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN28 */
#define IFX_DMA_ACCEN00_EN28_OFF (28u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN29 */
#define IFX_DMA_ACCEN00_EN29_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN29 */
#define IFX_DMA_ACCEN00_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN29 */
#define IFX_DMA_ACCEN00_EN29_OFF (29u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN2 */
#define IFX_DMA_ACCEN00_EN2_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN2 */
#define IFX_DMA_ACCEN00_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN2 */
#define IFX_DMA_ACCEN00_EN2_OFF (2u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN30 */
#define IFX_DMA_ACCEN00_EN30_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN30 */
#define IFX_DMA_ACCEN00_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN30 */
#define IFX_DMA_ACCEN00_EN30_OFF (30u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN31 */
#define IFX_DMA_ACCEN00_EN31_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN31 */
#define IFX_DMA_ACCEN00_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN31 */
#define IFX_DMA_ACCEN00_EN31_OFF (31u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN3 */
#define IFX_DMA_ACCEN00_EN3_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN3 */
#define IFX_DMA_ACCEN00_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN3 */
#define IFX_DMA_ACCEN00_EN3_OFF (3u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN4 */
#define IFX_DMA_ACCEN00_EN4_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN4 */
#define IFX_DMA_ACCEN00_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN4 */
#define IFX_DMA_ACCEN00_EN4_OFF (4u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN5 */
#define IFX_DMA_ACCEN00_EN5_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN5 */
#define IFX_DMA_ACCEN00_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN5 */
#define IFX_DMA_ACCEN00_EN5_OFF (5u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN6 */
#define IFX_DMA_ACCEN00_EN6_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN6 */
#define IFX_DMA_ACCEN00_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN6 */
#define IFX_DMA_ACCEN00_EN6_OFF (6u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN7 */
#define IFX_DMA_ACCEN00_EN7_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN7 */
#define IFX_DMA_ACCEN00_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN7 */
#define IFX_DMA_ACCEN00_EN7_OFF (7u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN8 */
#define IFX_DMA_ACCEN00_EN8_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN8 */
#define IFX_DMA_ACCEN00_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN8 */
#define IFX_DMA_ACCEN00_EN8_OFF (8u)

/** \brief  Length for Ifx_DMA_ACCEN00_Bits.EN9 */
#define IFX_DMA_ACCEN00_EN9_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN00_Bits.EN9 */
#define IFX_DMA_ACCEN00_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN00_Bits.EN9 */
#define IFX_DMA_ACCEN00_EN9_OFF (9u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN0 */
#define IFX_DMA_ACCEN10_EN0_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN0 */
#define IFX_DMA_ACCEN10_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN0 */
#define IFX_DMA_ACCEN10_EN0_OFF (0u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN10 */
#define IFX_DMA_ACCEN10_EN10_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN10 */
#define IFX_DMA_ACCEN10_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN10 */
#define IFX_DMA_ACCEN10_EN10_OFF (10u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN11 */
#define IFX_DMA_ACCEN10_EN11_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN11 */
#define IFX_DMA_ACCEN10_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN11 */
#define IFX_DMA_ACCEN10_EN11_OFF (11u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN12 */
#define IFX_DMA_ACCEN10_EN12_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN12 */
#define IFX_DMA_ACCEN10_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN12 */
#define IFX_DMA_ACCEN10_EN12_OFF (12u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN13 */
#define IFX_DMA_ACCEN10_EN13_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN13 */
#define IFX_DMA_ACCEN10_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN13 */
#define IFX_DMA_ACCEN10_EN13_OFF (13u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN14 */
#define IFX_DMA_ACCEN10_EN14_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN14 */
#define IFX_DMA_ACCEN10_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN14 */
#define IFX_DMA_ACCEN10_EN14_OFF (14u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN15 */
#define IFX_DMA_ACCEN10_EN15_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN15 */
#define IFX_DMA_ACCEN10_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN15 */
#define IFX_DMA_ACCEN10_EN15_OFF (15u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN16 */
#define IFX_DMA_ACCEN10_EN16_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN16 */
#define IFX_DMA_ACCEN10_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN16 */
#define IFX_DMA_ACCEN10_EN16_OFF (16u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN17 */
#define IFX_DMA_ACCEN10_EN17_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN17 */
#define IFX_DMA_ACCEN10_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN17 */
#define IFX_DMA_ACCEN10_EN17_OFF (17u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN18 */
#define IFX_DMA_ACCEN10_EN18_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN18 */
#define IFX_DMA_ACCEN10_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN18 */
#define IFX_DMA_ACCEN10_EN18_OFF (18u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN19 */
#define IFX_DMA_ACCEN10_EN19_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN19 */
#define IFX_DMA_ACCEN10_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN19 */
#define IFX_DMA_ACCEN10_EN19_OFF (19u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN1 */
#define IFX_DMA_ACCEN10_EN1_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN1 */
#define IFX_DMA_ACCEN10_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN1 */
#define IFX_DMA_ACCEN10_EN1_OFF (1u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN20 */
#define IFX_DMA_ACCEN10_EN20_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN20 */
#define IFX_DMA_ACCEN10_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN20 */
#define IFX_DMA_ACCEN10_EN20_OFF (20u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN21 */
#define IFX_DMA_ACCEN10_EN21_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN21 */
#define IFX_DMA_ACCEN10_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN21 */
#define IFX_DMA_ACCEN10_EN21_OFF (21u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN22 */
#define IFX_DMA_ACCEN10_EN22_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN22 */
#define IFX_DMA_ACCEN10_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN22 */
#define IFX_DMA_ACCEN10_EN22_OFF (22u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN23 */
#define IFX_DMA_ACCEN10_EN23_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN23 */
#define IFX_DMA_ACCEN10_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN23 */
#define IFX_DMA_ACCEN10_EN23_OFF (23u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN24 */
#define IFX_DMA_ACCEN10_EN24_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN24 */
#define IFX_DMA_ACCEN10_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN24 */
#define IFX_DMA_ACCEN10_EN24_OFF (24u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN25 */
#define IFX_DMA_ACCEN10_EN25_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN25 */
#define IFX_DMA_ACCEN10_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN25 */
#define IFX_DMA_ACCEN10_EN25_OFF (25u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN26 */
#define IFX_DMA_ACCEN10_EN26_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN26 */
#define IFX_DMA_ACCEN10_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN26 */
#define IFX_DMA_ACCEN10_EN26_OFF (26u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN27 */
#define IFX_DMA_ACCEN10_EN27_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN27 */
#define IFX_DMA_ACCEN10_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN27 */
#define IFX_DMA_ACCEN10_EN27_OFF (27u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN28 */
#define IFX_DMA_ACCEN10_EN28_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN28 */
#define IFX_DMA_ACCEN10_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN28 */
#define IFX_DMA_ACCEN10_EN28_OFF (28u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN29 */
#define IFX_DMA_ACCEN10_EN29_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN29 */
#define IFX_DMA_ACCEN10_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN29 */
#define IFX_DMA_ACCEN10_EN29_OFF (29u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN2 */
#define IFX_DMA_ACCEN10_EN2_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN2 */
#define IFX_DMA_ACCEN10_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN2 */
#define IFX_DMA_ACCEN10_EN2_OFF (2u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN30 */
#define IFX_DMA_ACCEN10_EN30_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN30 */
#define IFX_DMA_ACCEN10_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN30 */
#define IFX_DMA_ACCEN10_EN30_OFF (30u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN31 */
#define IFX_DMA_ACCEN10_EN31_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN31 */
#define IFX_DMA_ACCEN10_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN31 */
#define IFX_DMA_ACCEN10_EN31_OFF (31u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN3 */
#define IFX_DMA_ACCEN10_EN3_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN3 */
#define IFX_DMA_ACCEN10_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN3 */
#define IFX_DMA_ACCEN10_EN3_OFF (3u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN4 */
#define IFX_DMA_ACCEN10_EN4_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN4 */
#define IFX_DMA_ACCEN10_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN4 */
#define IFX_DMA_ACCEN10_EN4_OFF (4u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN5 */
#define IFX_DMA_ACCEN10_EN5_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN5 */
#define IFX_DMA_ACCEN10_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN5 */
#define IFX_DMA_ACCEN10_EN5_OFF (5u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN6 */
#define IFX_DMA_ACCEN10_EN6_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN6 */
#define IFX_DMA_ACCEN10_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN6 */
#define IFX_DMA_ACCEN10_EN6_OFF (6u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN7 */
#define IFX_DMA_ACCEN10_EN7_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN7 */
#define IFX_DMA_ACCEN10_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN7 */
#define IFX_DMA_ACCEN10_EN7_OFF (7u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN8 */
#define IFX_DMA_ACCEN10_EN8_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN8 */
#define IFX_DMA_ACCEN10_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN8 */
#define IFX_DMA_ACCEN10_EN8_OFF (8u)

/** \brief  Length for Ifx_DMA_ACCEN10_Bits.EN9 */
#define IFX_DMA_ACCEN10_EN9_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN10_Bits.EN9 */
#define IFX_DMA_ACCEN10_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN10_Bits.EN9 */
#define IFX_DMA_ACCEN10_EN9_OFF (9u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN0 */
#define IFX_DMA_ACCEN20_EN0_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN0 */
#define IFX_DMA_ACCEN20_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN0 */
#define IFX_DMA_ACCEN20_EN0_OFF (0u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN10 */
#define IFX_DMA_ACCEN20_EN10_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN10 */
#define IFX_DMA_ACCEN20_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN10 */
#define IFX_DMA_ACCEN20_EN10_OFF (10u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN11 */
#define IFX_DMA_ACCEN20_EN11_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN11 */
#define IFX_DMA_ACCEN20_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN11 */
#define IFX_DMA_ACCEN20_EN11_OFF (11u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN12 */
#define IFX_DMA_ACCEN20_EN12_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN12 */
#define IFX_DMA_ACCEN20_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN12 */
#define IFX_DMA_ACCEN20_EN12_OFF (12u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN13 */
#define IFX_DMA_ACCEN20_EN13_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN13 */
#define IFX_DMA_ACCEN20_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN13 */
#define IFX_DMA_ACCEN20_EN13_OFF (13u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN14 */
#define IFX_DMA_ACCEN20_EN14_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN14 */
#define IFX_DMA_ACCEN20_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN14 */
#define IFX_DMA_ACCEN20_EN14_OFF (14u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN15 */
#define IFX_DMA_ACCEN20_EN15_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN15 */
#define IFX_DMA_ACCEN20_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN15 */
#define IFX_DMA_ACCEN20_EN15_OFF (15u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN16 */
#define IFX_DMA_ACCEN20_EN16_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN16 */
#define IFX_DMA_ACCEN20_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN16 */
#define IFX_DMA_ACCEN20_EN16_OFF (16u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN17 */
#define IFX_DMA_ACCEN20_EN17_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN17 */
#define IFX_DMA_ACCEN20_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN17 */
#define IFX_DMA_ACCEN20_EN17_OFF (17u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN18 */
#define IFX_DMA_ACCEN20_EN18_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN18 */
#define IFX_DMA_ACCEN20_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN18 */
#define IFX_DMA_ACCEN20_EN18_OFF (18u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN19 */
#define IFX_DMA_ACCEN20_EN19_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN19 */
#define IFX_DMA_ACCEN20_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN19 */
#define IFX_DMA_ACCEN20_EN19_OFF (19u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN1 */
#define IFX_DMA_ACCEN20_EN1_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN1 */
#define IFX_DMA_ACCEN20_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN1 */
#define IFX_DMA_ACCEN20_EN1_OFF (1u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN20 */
#define IFX_DMA_ACCEN20_EN20_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN20 */
#define IFX_DMA_ACCEN20_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN20 */
#define IFX_DMA_ACCEN20_EN20_OFF (20u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN21 */
#define IFX_DMA_ACCEN20_EN21_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN21 */
#define IFX_DMA_ACCEN20_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN21 */
#define IFX_DMA_ACCEN20_EN21_OFF (21u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN22 */
#define IFX_DMA_ACCEN20_EN22_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN22 */
#define IFX_DMA_ACCEN20_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN22 */
#define IFX_DMA_ACCEN20_EN22_OFF (22u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN23 */
#define IFX_DMA_ACCEN20_EN23_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN23 */
#define IFX_DMA_ACCEN20_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN23 */
#define IFX_DMA_ACCEN20_EN23_OFF (23u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN24 */
#define IFX_DMA_ACCEN20_EN24_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN24 */
#define IFX_DMA_ACCEN20_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN24 */
#define IFX_DMA_ACCEN20_EN24_OFF (24u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN25 */
#define IFX_DMA_ACCEN20_EN25_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN25 */
#define IFX_DMA_ACCEN20_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN25 */
#define IFX_DMA_ACCEN20_EN25_OFF (25u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN26 */
#define IFX_DMA_ACCEN20_EN26_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN26 */
#define IFX_DMA_ACCEN20_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN26 */
#define IFX_DMA_ACCEN20_EN26_OFF (26u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN27 */
#define IFX_DMA_ACCEN20_EN27_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN27 */
#define IFX_DMA_ACCEN20_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN27 */
#define IFX_DMA_ACCEN20_EN27_OFF (27u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN28 */
#define IFX_DMA_ACCEN20_EN28_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN28 */
#define IFX_DMA_ACCEN20_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN28 */
#define IFX_DMA_ACCEN20_EN28_OFF (28u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN29 */
#define IFX_DMA_ACCEN20_EN29_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN29 */
#define IFX_DMA_ACCEN20_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN29 */
#define IFX_DMA_ACCEN20_EN29_OFF (29u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN2 */
#define IFX_DMA_ACCEN20_EN2_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN2 */
#define IFX_DMA_ACCEN20_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN2 */
#define IFX_DMA_ACCEN20_EN2_OFF (2u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN30 */
#define IFX_DMA_ACCEN20_EN30_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN30 */
#define IFX_DMA_ACCEN20_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN30 */
#define IFX_DMA_ACCEN20_EN30_OFF (30u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN31 */
#define IFX_DMA_ACCEN20_EN31_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN31 */
#define IFX_DMA_ACCEN20_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN31 */
#define IFX_DMA_ACCEN20_EN31_OFF (31u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN3 */
#define IFX_DMA_ACCEN20_EN3_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN3 */
#define IFX_DMA_ACCEN20_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN3 */
#define IFX_DMA_ACCEN20_EN3_OFF (3u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN4 */
#define IFX_DMA_ACCEN20_EN4_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN4 */
#define IFX_DMA_ACCEN20_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN4 */
#define IFX_DMA_ACCEN20_EN4_OFF (4u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN5 */
#define IFX_DMA_ACCEN20_EN5_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN5 */
#define IFX_DMA_ACCEN20_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN5 */
#define IFX_DMA_ACCEN20_EN5_OFF (5u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN6 */
#define IFX_DMA_ACCEN20_EN6_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN6 */
#define IFX_DMA_ACCEN20_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN6 */
#define IFX_DMA_ACCEN20_EN6_OFF (6u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN7 */
#define IFX_DMA_ACCEN20_EN7_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN7 */
#define IFX_DMA_ACCEN20_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN7 */
#define IFX_DMA_ACCEN20_EN7_OFF (7u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN8 */
#define IFX_DMA_ACCEN20_EN8_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN8 */
#define IFX_DMA_ACCEN20_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN8 */
#define IFX_DMA_ACCEN20_EN8_OFF (8u)

/** \brief  Length for Ifx_DMA_ACCEN20_Bits.EN9 */
#define IFX_DMA_ACCEN20_EN9_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN20_Bits.EN9 */
#define IFX_DMA_ACCEN20_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN20_Bits.EN9 */
#define IFX_DMA_ACCEN20_EN9_OFF (9u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN0 */
#define IFX_DMA_ACCEN30_EN0_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN0 */
#define IFX_DMA_ACCEN30_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN0 */
#define IFX_DMA_ACCEN30_EN0_OFF (0u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN10 */
#define IFX_DMA_ACCEN30_EN10_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN10 */
#define IFX_DMA_ACCEN30_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN10 */
#define IFX_DMA_ACCEN30_EN10_OFF (10u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN11 */
#define IFX_DMA_ACCEN30_EN11_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN11 */
#define IFX_DMA_ACCEN30_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN11 */
#define IFX_DMA_ACCEN30_EN11_OFF (11u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN12 */
#define IFX_DMA_ACCEN30_EN12_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN12 */
#define IFX_DMA_ACCEN30_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN12 */
#define IFX_DMA_ACCEN30_EN12_OFF (12u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN13 */
#define IFX_DMA_ACCEN30_EN13_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN13 */
#define IFX_DMA_ACCEN30_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN13 */
#define IFX_DMA_ACCEN30_EN13_OFF (13u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN14 */
#define IFX_DMA_ACCEN30_EN14_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN14 */
#define IFX_DMA_ACCEN30_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN14 */
#define IFX_DMA_ACCEN30_EN14_OFF (14u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN15 */
#define IFX_DMA_ACCEN30_EN15_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN15 */
#define IFX_DMA_ACCEN30_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN15 */
#define IFX_DMA_ACCEN30_EN15_OFF (15u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN16 */
#define IFX_DMA_ACCEN30_EN16_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN16 */
#define IFX_DMA_ACCEN30_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN16 */
#define IFX_DMA_ACCEN30_EN16_OFF (16u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN17 */
#define IFX_DMA_ACCEN30_EN17_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN17 */
#define IFX_DMA_ACCEN30_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN17 */
#define IFX_DMA_ACCEN30_EN17_OFF (17u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN18 */
#define IFX_DMA_ACCEN30_EN18_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN18 */
#define IFX_DMA_ACCEN30_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN18 */
#define IFX_DMA_ACCEN30_EN18_OFF (18u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN19 */
#define IFX_DMA_ACCEN30_EN19_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN19 */
#define IFX_DMA_ACCEN30_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN19 */
#define IFX_DMA_ACCEN30_EN19_OFF (19u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN1 */
#define IFX_DMA_ACCEN30_EN1_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN1 */
#define IFX_DMA_ACCEN30_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN1 */
#define IFX_DMA_ACCEN30_EN1_OFF (1u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN20 */
#define IFX_DMA_ACCEN30_EN20_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN20 */
#define IFX_DMA_ACCEN30_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN20 */
#define IFX_DMA_ACCEN30_EN20_OFF (20u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN21 */
#define IFX_DMA_ACCEN30_EN21_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN21 */
#define IFX_DMA_ACCEN30_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN21 */
#define IFX_DMA_ACCEN30_EN21_OFF (21u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN22 */
#define IFX_DMA_ACCEN30_EN22_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN22 */
#define IFX_DMA_ACCEN30_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN22 */
#define IFX_DMA_ACCEN30_EN22_OFF (22u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN23 */
#define IFX_DMA_ACCEN30_EN23_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN23 */
#define IFX_DMA_ACCEN30_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN23 */
#define IFX_DMA_ACCEN30_EN23_OFF (23u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN24 */
#define IFX_DMA_ACCEN30_EN24_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN24 */
#define IFX_DMA_ACCEN30_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN24 */
#define IFX_DMA_ACCEN30_EN24_OFF (24u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN25 */
#define IFX_DMA_ACCEN30_EN25_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN25 */
#define IFX_DMA_ACCEN30_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN25 */
#define IFX_DMA_ACCEN30_EN25_OFF (25u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN26 */
#define IFX_DMA_ACCEN30_EN26_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN26 */
#define IFX_DMA_ACCEN30_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN26 */
#define IFX_DMA_ACCEN30_EN26_OFF (26u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN27 */
#define IFX_DMA_ACCEN30_EN27_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN27 */
#define IFX_DMA_ACCEN30_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN27 */
#define IFX_DMA_ACCEN30_EN27_OFF (27u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN28 */
#define IFX_DMA_ACCEN30_EN28_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN28 */
#define IFX_DMA_ACCEN30_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN28 */
#define IFX_DMA_ACCEN30_EN28_OFF (28u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN29 */
#define IFX_DMA_ACCEN30_EN29_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN29 */
#define IFX_DMA_ACCEN30_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN29 */
#define IFX_DMA_ACCEN30_EN29_OFF (29u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN2 */
#define IFX_DMA_ACCEN30_EN2_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN2 */
#define IFX_DMA_ACCEN30_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN2 */
#define IFX_DMA_ACCEN30_EN2_OFF (2u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN30 */
#define IFX_DMA_ACCEN30_EN30_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN30 */
#define IFX_DMA_ACCEN30_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN30 */
#define IFX_DMA_ACCEN30_EN30_OFF (30u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN31 */
#define IFX_DMA_ACCEN30_EN31_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN31 */
#define IFX_DMA_ACCEN30_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN31 */
#define IFX_DMA_ACCEN30_EN31_OFF (31u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN3 */
#define IFX_DMA_ACCEN30_EN3_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN3 */
#define IFX_DMA_ACCEN30_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN3 */
#define IFX_DMA_ACCEN30_EN3_OFF (3u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN4 */
#define IFX_DMA_ACCEN30_EN4_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN4 */
#define IFX_DMA_ACCEN30_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN4 */
#define IFX_DMA_ACCEN30_EN4_OFF (4u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN5 */
#define IFX_DMA_ACCEN30_EN5_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN5 */
#define IFX_DMA_ACCEN30_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN5 */
#define IFX_DMA_ACCEN30_EN5_OFF (5u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN6 */
#define IFX_DMA_ACCEN30_EN6_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN6 */
#define IFX_DMA_ACCEN30_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN6 */
#define IFX_DMA_ACCEN30_EN6_OFF (6u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN7 */
#define IFX_DMA_ACCEN30_EN7_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN7 */
#define IFX_DMA_ACCEN30_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN7 */
#define IFX_DMA_ACCEN30_EN7_OFF (7u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN8 */
#define IFX_DMA_ACCEN30_EN8_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN8 */
#define IFX_DMA_ACCEN30_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN8 */
#define IFX_DMA_ACCEN30_EN8_OFF (8u)

/** \brief  Length for Ifx_DMA_ACCEN30_Bits.EN9 */
#define IFX_DMA_ACCEN30_EN9_LEN (1u)

/** \brief  Mask for Ifx_DMA_ACCEN30_Bits.EN9 */
#define IFX_DMA_ACCEN30_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ACCEN30_Bits.EN9 */
#define IFX_DMA_ACCEN30_EN9_OFF (9u)

/** \brief  Length for Ifx_DMA_BLK_CLRE_Bits.CDER */
#define IFX_DMA_BLK_CLRE_CDER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_CLRE_Bits.CDER */
#define IFX_DMA_BLK_CLRE_CDER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_CLRE_Bits.CDER */
#define IFX_DMA_BLK_CLRE_CDER_OFF (17u)

/** \brief  Length for Ifx_DMA_BLK_CLRE_Bits.CDLLER */
#define IFX_DMA_BLK_CLRE_CDLLER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_CLRE_Bits.CDLLER */
#define IFX_DMA_BLK_CLRE_CDLLER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_CLRE_Bits.CDLLER */
#define IFX_DMA_BLK_CLRE_CDLLER_OFF (26u)

/** \brief  Length for Ifx_DMA_BLK_CLRE_Bits.CRAMER */
#define IFX_DMA_BLK_CLRE_CRAMER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_CLRE_Bits.CRAMER */
#define IFX_DMA_BLK_CLRE_CRAMER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_CLRE_Bits.CRAMER */
#define IFX_DMA_BLK_CLRE_CRAMER_OFF (24u)

/** \brief  Length for Ifx_DMA_BLK_CLRE_Bits.CSER */
#define IFX_DMA_BLK_CLRE_CSER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_CLRE_Bits.CSER */
#define IFX_DMA_BLK_CLRE_CSER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_CLRE_Bits.CSER */
#define IFX_DMA_BLK_CLRE_CSER_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_CLRE_Bits.CSLLER */
#define IFX_DMA_BLK_CLRE_CSLLER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_CLRE_Bits.CSLLER */
#define IFX_DMA_BLK_CLRE_CSLLER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_CLRE_Bits.CSLLER */
#define IFX_DMA_BLK_CLRE_CSLLER_OFF (25u)

/** \brief  Length for Ifx_DMA_BLK_CLRE_Bits.CSPBER */
#define IFX_DMA_BLK_CLRE_CSPBER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_CLRE_Bits.CSPBER */
#define IFX_DMA_BLK_CLRE_CSPBER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_CLRE_Bits.CSPBER */
#define IFX_DMA_BLK_CLRE_CSPBER_OFF (20u)

/** \brief  Length for Ifx_DMA_BLK_CLRE_Bits.CSRIER */
#define IFX_DMA_BLK_CLRE_CSRIER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_CLRE_Bits.CSRIER */
#define IFX_DMA_BLK_CLRE_CSRIER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_CLRE_Bits.CSRIER */
#define IFX_DMA_BLK_CLRE_CSRIER_OFF (21u)

/** \brief  Length for Ifx_DMA_BLK_EER_Bits.EDER */
#define IFX_DMA_BLK_EER_EDER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_EER_Bits.EDER */
#define IFX_DMA_BLK_EER_EDER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_EER_Bits.EDER */
#define IFX_DMA_BLK_EER_EDER_OFF (17u)

/** \brief  Length for Ifx_DMA_BLK_EER_Bits.ELER */
#define IFX_DMA_BLK_EER_ELER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_EER_Bits.ELER */
#define IFX_DMA_BLK_EER_ELER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_EER_Bits.ELER */
#define IFX_DMA_BLK_EER_ELER_OFF (26u)

/** \brief  Length for Ifx_DMA_BLK_EER_Bits.ERER */
#define IFX_DMA_BLK_EER_ERER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_EER_Bits.ERER */
#define IFX_DMA_BLK_EER_ERER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_EER_Bits.ERER */
#define IFX_DMA_BLK_EER_ERER_OFF (24u)

/** \brief  Length for Ifx_DMA_BLK_EER_Bits.ESER */
#define IFX_DMA_BLK_EER_ESER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_EER_Bits.ESER */
#define IFX_DMA_BLK_EER_ESER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_EER_Bits.ESER */
#define IFX_DMA_BLK_EER_ESER_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ERRSR_Bits.DER */
#define IFX_DMA_BLK_ERRSR_DER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ERRSR_Bits.DER */
#define IFX_DMA_BLK_ERRSR_DER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ERRSR_Bits.DER */
#define IFX_DMA_BLK_ERRSR_DER_OFF (17u)

/** \brief  Length for Ifx_DMA_BLK_ERRSR_Bits.DLLER */
#define IFX_DMA_BLK_ERRSR_DLLER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ERRSR_Bits.DLLER */
#define IFX_DMA_BLK_ERRSR_DLLER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ERRSR_Bits.DLLER */
#define IFX_DMA_BLK_ERRSR_DLLER_OFF (26u)

/** \brief  Length for Ifx_DMA_BLK_ERRSR_Bits.LEC */
#define IFX_DMA_BLK_ERRSR_LEC_LEN (7u)

/** \brief  Mask for Ifx_DMA_BLK_ERRSR_Bits.LEC */
#define IFX_DMA_BLK_ERRSR_LEC_MSK (0x7fu)

/** \brief  Offset for Ifx_DMA_BLK_ERRSR_Bits.LEC */
#define IFX_DMA_BLK_ERRSR_LEC_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ERRSR_Bits.RAMER */
#define IFX_DMA_BLK_ERRSR_RAMER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ERRSR_Bits.RAMER */
#define IFX_DMA_BLK_ERRSR_RAMER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ERRSR_Bits.RAMER */
#define IFX_DMA_BLK_ERRSR_RAMER_OFF (24u)

/** \brief  Length for Ifx_DMA_BLK_ERRSR_Bits.SER */
#define IFX_DMA_BLK_ERRSR_SER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ERRSR_Bits.SER */
#define IFX_DMA_BLK_ERRSR_SER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ERRSR_Bits.SER */
#define IFX_DMA_BLK_ERRSR_SER_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ERRSR_Bits.SLLER */
#define IFX_DMA_BLK_ERRSR_SLLER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ERRSR_Bits.SLLER */
#define IFX_DMA_BLK_ERRSR_SLLER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ERRSR_Bits.SLLER */
#define IFX_DMA_BLK_ERRSR_SLLER_OFF (25u)

/** \brief  Length for Ifx_DMA_BLK_ERRSR_Bits.SPBER */
#define IFX_DMA_BLK_ERRSR_SPBER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ERRSR_Bits.SPBER */
#define IFX_DMA_BLK_ERRSR_SPBER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ERRSR_Bits.SPBER */
#define IFX_DMA_BLK_ERRSR_SPBER_OFF (20u)

/** \brief  Length for Ifx_DMA_BLK_ERRSR_Bits.SRIER */
#define IFX_DMA_BLK_ERRSR_SRIER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ERRSR_Bits.SRIER */
#define IFX_DMA_BLK_ERRSR_SRIER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ERRSR_Bits.SRIER */
#define IFX_DMA_BLK_ERRSR_SRIER_OFF (21u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.CBLD */
#define IFX_DMA_BLK_ME_ADICR_CBLD_LEN (4u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.CBLD */
#define IFX_DMA_BLK_ME_ADICR_CBLD_MSK (0xfu)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.CBLD */
#define IFX_DMA_BLK_ME_ADICR_CBLD_OFF (12u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.CBLS */
#define IFX_DMA_BLK_ME_ADICR_CBLS_LEN (4u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.CBLS */
#define IFX_DMA_BLK_ME_ADICR_CBLS_MSK (0xfu)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.CBLS */
#define IFX_DMA_BLK_ME_ADICR_CBLS_OFF (8u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.DCBE */
#define IFX_DMA_BLK_ME_ADICR_DCBE_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.DCBE */
#define IFX_DMA_BLK_ME_ADICR_DCBE_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.DCBE */
#define IFX_DMA_BLK_ME_ADICR_DCBE_OFF (21u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.DMF */
#define IFX_DMA_BLK_ME_ADICR_DMF_LEN (3u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.DMF */
#define IFX_DMA_BLK_ME_ADICR_DMF_MSK (0x7u)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.DMF */
#define IFX_DMA_BLK_ME_ADICR_DMF_OFF (4u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.ETRL */
#define IFX_DMA_BLK_ME_ADICR_ETRL_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.ETRL */
#define IFX_DMA_BLK_ME_ADICR_ETRL_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.ETRL */
#define IFX_DMA_BLK_ME_ADICR_ETRL_OFF (23u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.INCD */
#define IFX_DMA_BLK_ME_ADICR_INCD_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.INCD */
#define IFX_DMA_BLK_ME_ADICR_INCD_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.INCD */
#define IFX_DMA_BLK_ME_ADICR_INCD_OFF (7u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.INCS */
#define IFX_DMA_BLK_ME_ADICR_INCS_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.INCS */
#define IFX_DMA_BLK_ME_ADICR_INCS_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.INCS */
#define IFX_DMA_BLK_ME_ADICR_INCS_OFF (3u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.INTCT */
#define IFX_DMA_BLK_ME_ADICR_INTCT_LEN (2u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.INTCT */
#define IFX_DMA_BLK_ME_ADICR_INTCT_MSK (0x3u)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.INTCT */
#define IFX_DMA_BLK_ME_ADICR_INTCT_OFF (26u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.IRDV */
#define IFX_DMA_BLK_ME_ADICR_IRDV_LEN (4u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.IRDV */
#define IFX_DMA_BLK_ME_ADICR_IRDV_MSK (0xfu)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.IRDV */
#define IFX_DMA_BLK_ME_ADICR_IRDV_OFF (28u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.SCBE */
#define IFX_DMA_BLK_ME_ADICR_SCBE_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.SCBE */
#define IFX_DMA_BLK_ME_ADICR_SCBE_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.SCBE */
#define IFX_DMA_BLK_ME_ADICR_SCBE_OFF (20u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.SHCT */
#define IFX_DMA_BLK_ME_ADICR_SHCT_LEN (4u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.SHCT */
#define IFX_DMA_BLK_ME_ADICR_SHCT_MSK (0xfu)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.SHCT */
#define IFX_DMA_BLK_ME_ADICR_SHCT_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.SMF */
#define IFX_DMA_BLK_ME_ADICR_SMF_LEN (3u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.SMF */
#define IFX_DMA_BLK_ME_ADICR_SMF_MSK (0x7u)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.SMF */
#define IFX_DMA_BLK_ME_ADICR_SMF_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.STAMP */
#define IFX_DMA_BLK_ME_ADICR_STAMP_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.STAMP */
#define IFX_DMA_BLK_ME_ADICR_STAMP_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.STAMP */
#define IFX_DMA_BLK_ME_ADICR_STAMP_OFF (22u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.WRPDE */
#define IFX_DMA_BLK_ME_ADICR_WRPDE_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.WRPDE */
#define IFX_DMA_BLK_ME_ADICR_WRPDE_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.WRPDE */
#define IFX_DMA_BLK_ME_ADICR_WRPDE_OFF (25u)

/** \brief  Length for Ifx_DMA_BLK_ME_ADICR_Bits.WRPSE */
#define IFX_DMA_BLK_ME_ADICR_WRPSE_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_ADICR_Bits.WRPSE */
#define IFX_DMA_BLK_ME_ADICR_WRPSE_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_ADICR_Bits.WRPSE */
#define IFX_DMA_BLK_ME_ADICR_WRPSE_OFF (24u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHCR_Bits.BLKM */
#define IFX_DMA_BLK_ME_CHCR_BLKM_LEN (3u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHCR_Bits.BLKM */
#define IFX_DMA_BLK_ME_CHCR_BLKM_MSK (0x7u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHCR_Bits.BLKM */
#define IFX_DMA_BLK_ME_CHCR_BLKM_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHCR_Bits.CHDW */
#define IFX_DMA_BLK_ME_CHCR_CHDW_LEN (3u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHCR_Bits.CHDW */
#define IFX_DMA_BLK_ME_CHCR_CHDW_MSK (0x7u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHCR_Bits.CHDW */
#define IFX_DMA_BLK_ME_CHCR_CHDW_OFF (21u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHCR_Bits.CHMODE */
#define IFX_DMA_BLK_ME_CHCR_CHMODE_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHCR_Bits.CHMODE */
#define IFX_DMA_BLK_ME_CHCR_CHMODE_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHCR_Bits.CHMODE */
#define IFX_DMA_BLK_ME_CHCR_CHMODE_OFF (20u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHCR_Bits.DMAPRIO */
#define IFX_DMA_BLK_ME_CHCR_DMAPRIO_LEN (2u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHCR_Bits.DMAPRIO */
#define IFX_DMA_BLK_ME_CHCR_DMAPRIO_MSK (0x3u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHCR_Bits.DMAPRIO */
#define IFX_DMA_BLK_ME_CHCR_DMAPRIO_OFF (30u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHCR_Bits.PATSEL */
#define IFX_DMA_BLK_ME_CHCR_PATSEL_LEN (3u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHCR_Bits.PATSEL */
#define IFX_DMA_BLK_ME_CHCR_PATSEL_MSK (0x7u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHCR_Bits.PATSEL */
#define IFX_DMA_BLK_ME_CHCR_PATSEL_OFF (24u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHCR_Bits.PRSEL */
#define IFX_DMA_BLK_ME_CHCR_PRSEL_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHCR_Bits.PRSEL */
#define IFX_DMA_BLK_ME_CHCR_PRSEL_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHCR_Bits.PRSEL */
#define IFX_DMA_BLK_ME_CHCR_PRSEL_OFF (28u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHCR_Bits.RROAT */
#define IFX_DMA_BLK_ME_CHCR_RROAT_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHCR_Bits.RROAT */
#define IFX_DMA_BLK_ME_CHCR_RROAT_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHCR_Bits.RROAT */
#define IFX_DMA_BLK_ME_CHCR_RROAT_OFF (19u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHCR_Bits.TREL */
#define IFX_DMA_BLK_ME_CHCR_TREL_LEN (14u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHCR_Bits.TREL */
#define IFX_DMA_BLK_ME_CHCR_TREL_MSK (0x3fffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHCR_Bits.TREL */
#define IFX_DMA_BLK_ME_CHCR_TREL_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHSR_Bits.BUFFER */
#define IFX_DMA_BLK_ME_CHSR_BUFFER_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHSR_Bits.BUFFER */
#define IFX_DMA_BLK_ME_CHSR_BUFFER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHSR_Bits.BUFFER */
#define IFX_DMA_BLK_ME_CHSR_BUFFER_OFF (22u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHSR_Bits.FROZEN */
#define IFX_DMA_BLK_ME_CHSR_FROZEN_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHSR_Bits.FROZEN */
#define IFX_DMA_BLK_ME_CHSR_FROZEN_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHSR_Bits.FROZEN */
#define IFX_DMA_BLK_ME_CHSR_FROZEN_OFF (23u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHSR_Bits.ICH */
#define IFX_DMA_BLK_ME_CHSR_ICH_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHSR_Bits.ICH */
#define IFX_DMA_BLK_ME_CHSR_ICH_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHSR_Bits.ICH */
#define IFX_DMA_BLK_ME_CHSR_ICH_OFF (18u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHSR_Bits.IPM */
#define IFX_DMA_BLK_ME_CHSR_IPM_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHSR_Bits.IPM */
#define IFX_DMA_BLK_ME_CHSR_IPM_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHSR_Bits.IPM */
#define IFX_DMA_BLK_ME_CHSR_IPM_OFF (19u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHSR_Bits.LXO */
#define IFX_DMA_BLK_ME_CHSR_LXO_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHSR_Bits.LXO */
#define IFX_DMA_BLK_ME_CHSR_LXO_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHSR_Bits.LXO */
#define IFX_DMA_BLK_ME_CHSR_LXO_OFF (15u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHSR_Bits.TCOUNT */
#define IFX_DMA_BLK_ME_CHSR_TCOUNT_LEN (14u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHSR_Bits.TCOUNT */
#define IFX_DMA_BLK_ME_CHSR_TCOUNT_MSK (0x3fffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHSR_Bits.TCOUNT */
#define IFX_DMA_BLK_ME_CHSR_TCOUNT_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHSR_Bits.WRPD */
#define IFX_DMA_BLK_ME_CHSR_WRPD_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHSR_Bits.WRPD */
#define IFX_DMA_BLK_ME_CHSR_WRPD_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHSR_Bits.WRPD */
#define IFX_DMA_BLK_ME_CHSR_WRPD_OFF (17u)

/** \brief  Length for Ifx_DMA_BLK_ME_CHSR_Bits.WRPS */
#define IFX_DMA_BLK_ME_CHSR_WRPS_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_CHSR_Bits.WRPS */
#define IFX_DMA_BLK_ME_CHSR_WRPS_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_CHSR_Bits.WRPS */
#define IFX_DMA_BLK_ME_CHSR_WRPS_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ME_DADR_Bits.DADR */
#define IFX_DMA_BLK_ME_DADR_DADR_LEN (32u)

/** \brief  Mask for Ifx_DMA_BLK_ME_DADR_Bits.DADR */
#define IFX_DMA_BLK_ME_DADR_DADR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_DADR_Bits.DADR */
#define IFX_DMA_BLK_ME_DADR_DADR_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_R0_Bits.RD00 */
#define IFX_DMA_BLK_ME_R0_RD00_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R0_Bits.RD00 */
#define IFX_DMA_BLK_ME_R0_RD00_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R0_Bits.RD00 */
#define IFX_DMA_BLK_ME_R0_RD00_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_R0_Bits.RD01 */
#define IFX_DMA_BLK_ME_R0_RD01_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R0_Bits.RD01 */
#define IFX_DMA_BLK_ME_R0_RD01_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R0_Bits.RD01 */
#define IFX_DMA_BLK_ME_R0_RD01_OFF (8u)

/** \brief  Length for Ifx_DMA_BLK_ME_R0_Bits.RD02 */
#define IFX_DMA_BLK_ME_R0_RD02_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R0_Bits.RD02 */
#define IFX_DMA_BLK_ME_R0_RD02_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R0_Bits.RD02 */
#define IFX_DMA_BLK_ME_R0_RD02_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ME_R0_Bits.RD03 */
#define IFX_DMA_BLK_ME_R0_RD03_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R0_Bits.RD03 */
#define IFX_DMA_BLK_ME_R0_RD03_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R0_Bits.RD03 */
#define IFX_DMA_BLK_ME_R0_RD03_OFF (24u)

/** \brief  Length for Ifx_DMA_BLK_ME_R1_Bits.RD10 */
#define IFX_DMA_BLK_ME_R1_RD10_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R1_Bits.RD10 */
#define IFX_DMA_BLK_ME_R1_RD10_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R1_Bits.RD10 */
#define IFX_DMA_BLK_ME_R1_RD10_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_R1_Bits.RD11 */
#define IFX_DMA_BLK_ME_R1_RD11_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R1_Bits.RD11 */
#define IFX_DMA_BLK_ME_R1_RD11_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R1_Bits.RD11 */
#define IFX_DMA_BLK_ME_R1_RD11_OFF (8u)

/** \brief  Length for Ifx_DMA_BLK_ME_R1_Bits.RD12 */
#define IFX_DMA_BLK_ME_R1_RD12_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R1_Bits.RD12 */
#define IFX_DMA_BLK_ME_R1_RD12_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R1_Bits.RD12 */
#define IFX_DMA_BLK_ME_R1_RD12_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ME_R1_Bits.RD13 */
#define IFX_DMA_BLK_ME_R1_RD13_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R1_Bits.RD13 */
#define IFX_DMA_BLK_ME_R1_RD13_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R1_Bits.RD13 */
#define IFX_DMA_BLK_ME_R1_RD13_OFF (24u)

/** \brief  Length for Ifx_DMA_BLK_ME_R2_Bits.RD20 */
#define IFX_DMA_BLK_ME_R2_RD20_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R2_Bits.RD20 */
#define IFX_DMA_BLK_ME_R2_RD20_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R2_Bits.RD20 */
#define IFX_DMA_BLK_ME_R2_RD20_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_R2_Bits.RD21 */
#define IFX_DMA_BLK_ME_R2_RD21_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R2_Bits.RD21 */
#define IFX_DMA_BLK_ME_R2_RD21_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R2_Bits.RD21 */
#define IFX_DMA_BLK_ME_R2_RD21_OFF (8u)

/** \brief  Length for Ifx_DMA_BLK_ME_R2_Bits.RD22 */
#define IFX_DMA_BLK_ME_R2_RD22_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R2_Bits.RD22 */
#define IFX_DMA_BLK_ME_R2_RD22_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R2_Bits.RD22 */
#define IFX_DMA_BLK_ME_R2_RD22_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ME_R2_Bits.RD23 */
#define IFX_DMA_BLK_ME_R2_RD23_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R2_Bits.RD23 */
#define IFX_DMA_BLK_ME_R2_RD23_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R2_Bits.RD23 */
#define IFX_DMA_BLK_ME_R2_RD23_OFF (24u)

/** \brief  Length for Ifx_DMA_BLK_ME_R3_Bits.RD30 */
#define IFX_DMA_BLK_ME_R3_RD30_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R3_Bits.RD30 */
#define IFX_DMA_BLK_ME_R3_RD30_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R3_Bits.RD30 */
#define IFX_DMA_BLK_ME_R3_RD30_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_R3_Bits.RD31 */
#define IFX_DMA_BLK_ME_R3_RD31_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R3_Bits.RD31 */
#define IFX_DMA_BLK_ME_R3_RD31_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R3_Bits.RD31 */
#define IFX_DMA_BLK_ME_R3_RD31_OFF (8u)

/** \brief  Length for Ifx_DMA_BLK_ME_R3_Bits.RD32 */
#define IFX_DMA_BLK_ME_R3_RD32_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R3_Bits.RD32 */
#define IFX_DMA_BLK_ME_R3_RD32_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R3_Bits.RD32 */
#define IFX_DMA_BLK_ME_R3_RD32_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ME_R3_Bits.RD33 */
#define IFX_DMA_BLK_ME_R3_RD33_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R3_Bits.RD33 */
#define IFX_DMA_BLK_ME_R3_RD33_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R3_Bits.RD33 */
#define IFX_DMA_BLK_ME_R3_RD33_OFF (24u)

/** \brief  Length for Ifx_DMA_BLK_ME_R4_Bits.RD40 */
#define IFX_DMA_BLK_ME_R4_RD40_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R4_Bits.RD40 */
#define IFX_DMA_BLK_ME_R4_RD40_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R4_Bits.RD40 */
#define IFX_DMA_BLK_ME_R4_RD40_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_R4_Bits.RD41 */
#define IFX_DMA_BLK_ME_R4_RD41_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R4_Bits.RD41 */
#define IFX_DMA_BLK_ME_R4_RD41_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R4_Bits.RD41 */
#define IFX_DMA_BLK_ME_R4_RD41_OFF (8u)

/** \brief  Length for Ifx_DMA_BLK_ME_R4_Bits.RD42 */
#define IFX_DMA_BLK_ME_R4_RD42_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R4_Bits.RD42 */
#define IFX_DMA_BLK_ME_R4_RD42_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R4_Bits.RD42 */
#define IFX_DMA_BLK_ME_R4_RD42_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ME_R4_Bits.RD43 */
#define IFX_DMA_BLK_ME_R4_RD43_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R4_Bits.RD43 */
#define IFX_DMA_BLK_ME_R4_RD43_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R4_Bits.RD43 */
#define IFX_DMA_BLK_ME_R4_RD43_OFF (24u)

/** \brief  Length for Ifx_DMA_BLK_ME_R5_Bits.RD50 */
#define IFX_DMA_BLK_ME_R5_RD50_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R5_Bits.RD50 */
#define IFX_DMA_BLK_ME_R5_RD50_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R5_Bits.RD50 */
#define IFX_DMA_BLK_ME_R5_RD50_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_R5_Bits.RD51 */
#define IFX_DMA_BLK_ME_R5_RD51_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R5_Bits.RD51 */
#define IFX_DMA_BLK_ME_R5_RD51_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R5_Bits.RD51 */
#define IFX_DMA_BLK_ME_R5_RD51_OFF (8u)

/** \brief  Length for Ifx_DMA_BLK_ME_R5_Bits.RD52 */
#define IFX_DMA_BLK_ME_R5_RD52_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R5_Bits.RD52 */
#define IFX_DMA_BLK_ME_R5_RD52_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R5_Bits.RD52 */
#define IFX_DMA_BLK_ME_R5_RD52_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ME_R5_Bits.RD53 */
#define IFX_DMA_BLK_ME_R5_RD53_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R5_Bits.RD53 */
#define IFX_DMA_BLK_ME_R5_RD53_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R5_Bits.RD53 */
#define IFX_DMA_BLK_ME_R5_RD53_OFF (24u)

/** \brief  Length for Ifx_DMA_BLK_ME_R6_Bits.RD60 */
#define IFX_DMA_BLK_ME_R6_RD60_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R6_Bits.RD60 */
#define IFX_DMA_BLK_ME_R6_RD60_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R6_Bits.RD60 */
#define IFX_DMA_BLK_ME_R6_RD60_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_R6_Bits.RD61 */
#define IFX_DMA_BLK_ME_R6_RD61_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R6_Bits.RD61 */
#define IFX_DMA_BLK_ME_R6_RD61_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R6_Bits.RD61 */
#define IFX_DMA_BLK_ME_R6_RD61_OFF (8u)

/** \brief  Length for Ifx_DMA_BLK_ME_R6_Bits.RD62 */
#define IFX_DMA_BLK_ME_R6_RD62_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R6_Bits.RD62 */
#define IFX_DMA_BLK_ME_R6_RD62_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R6_Bits.RD62 */
#define IFX_DMA_BLK_ME_R6_RD62_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ME_R6_Bits.RD63 */
#define IFX_DMA_BLK_ME_R6_RD63_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R6_Bits.RD63 */
#define IFX_DMA_BLK_ME_R6_RD63_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R6_Bits.RD63 */
#define IFX_DMA_BLK_ME_R6_RD63_OFF (24u)

/** \brief  Length for Ifx_DMA_BLK_ME_R7_Bits.RD70 */
#define IFX_DMA_BLK_ME_R7_RD70_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R7_Bits.RD70 */
#define IFX_DMA_BLK_ME_R7_RD70_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R7_Bits.RD70 */
#define IFX_DMA_BLK_ME_R7_RD70_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_R7_Bits.RD71 */
#define IFX_DMA_BLK_ME_R7_RD71_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R7_Bits.RD71 */
#define IFX_DMA_BLK_ME_R7_RD71_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R7_Bits.RD71 */
#define IFX_DMA_BLK_ME_R7_RD71_OFF (8u)

/** \brief  Length for Ifx_DMA_BLK_ME_R7_Bits.RD72 */
#define IFX_DMA_BLK_ME_R7_RD72_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R7_Bits.RD72 */
#define IFX_DMA_BLK_ME_R7_RD72_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R7_Bits.RD72 */
#define IFX_DMA_BLK_ME_R7_RD72_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ME_R7_Bits.RD73 */
#define IFX_DMA_BLK_ME_R7_RD73_LEN (8u)

/** \brief  Mask for Ifx_DMA_BLK_ME_R7_Bits.RD73 */
#define IFX_DMA_BLK_ME_R7_RD73_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_R7_Bits.RD73 */
#define IFX_DMA_BLK_ME_R7_RD73_OFF (24u)

/** \brief  Length for Ifx_DMA_BLK_ME_RDCRC_Bits.RDCRC */
#define IFX_DMA_BLK_ME_RDCRC_RDCRC_LEN (32u)

/** \brief  Mask for Ifx_DMA_BLK_ME_RDCRC_Bits.RDCRC */
#define IFX_DMA_BLK_ME_RDCRC_RDCRC_MSK (0xffffffffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_RDCRC_Bits.RDCRC */
#define IFX_DMA_BLK_ME_RDCRC_RDCRC_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_SADR_Bits.SADR */
#define IFX_DMA_BLK_ME_SADR_SADR_LEN (32u)

/** \brief  Mask for Ifx_DMA_BLK_ME_SADR_Bits.SADR */
#define IFX_DMA_BLK_ME_SADR_SADR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_SADR_Bits.SADR */
#define IFX_DMA_BLK_ME_SADR_SADR_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_SDCRC_Bits.SDCRC */
#define IFX_DMA_BLK_ME_SDCRC_SDCRC_LEN (32u)

/** \brief  Mask for Ifx_DMA_BLK_ME_SDCRC_Bits.SDCRC */
#define IFX_DMA_BLK_ME_SDCRC_SDCRC_MSK (0xffffffffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_SDCRC_Bits.SDCRC */
#define IFX_DMA_BLK_ME_SDCRC_SDCRC_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_SHADR_Bits.SHADR */
#define IFX_DMA_BLK_ME_SHADR_SHADR_LEN (32u)

/** \brief  Mask for Ifx_DMA_BLK_ME_SHADR_Bits.SHADR */
#define IFX_DMA_BLK_ME_SHADR_SHADR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_DMA_BLK_ME_SHADR_Bits.SHADR */
#define IFX_DMA_BLK_ME_SHADR_SHADR_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_SR_Bits.CH */
#define IFX_DMA_BLK_ME_SR_CH_LEN (7u)

/** \brief  Mask for Ifx_DMA_BLK_ME_SR_Bits.CH */
#define IFX_DMA_BLK_ME_SR_CH_MSK (0x7fu)

/** \brief  Offset for Ifx_DMA_BLK_ME_SR_Bits.CH */
#define IFX_DMA_BLK_ME_SR_CH_OFF (16u)

/** \brief  Length for Ifx_DMA_BLK_ME_SR_Bits.RS */
#define IFX_DMA_BLK_ME_SR_RS_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_SR_Bits.RS */
#define IFX_DMA_BLK_ME_SR_RS_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_SR_Bits.RS */
#define IFX_DMA_BLK_ME_SR_RS_OFF (0u)

/** \brief  Length for Ifx_DMA_BLK_ME_SR_Bits.WS */
#define IFX_DMA_BLK_ME_SR_WS_LEN (1u)

/** \brief  Mask for Ifx_DMA_BLK_ME_SR_Bits.WS */
#define IFX_DMA_BLK_ME_SR_WS_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_BLK_ME_SR_Bits.WS */
#define IFX_DMA_BLK_ME_SR_WS_OFF (4u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.CBLD */
#define IFX_DMA_CH_ADICR_CBLD_LEN (4u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.CBLD */
#define IFX_DMA_CH_ADICR_CBLD_MSK (0xfu)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.CBLD */
#define IFX_DMA_CH_ADICR_CBLD_OFF (12u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.CBLS */
#define IFX_DMA_CH_ADICR_CBLS_LEN (4u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.CBLS */
#define IFX_DMA_CH_ADICR_CBLS_MSK (0xfu)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.CBLS */
#define IFX_DMA_CH_ADICR_CBLS_OFF (8u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.DCBE */
#define IFX_DMA_CH_ADICR_DCBE_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.DCBE */
#define IFX_DMA_CH_ADICR_DCBE_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.DCBE */
#define IFX_DMA_CH_ADICR_DCBE_OFF (21u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.DMF */
#define IFX_DMA_CH_ADICR_DMF_LEN (3u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.DMF */
#define IFX_DMA_CH_ADICR_DMF_MSK (0x7u)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.DMF */
#define IFX_DMA_CH_ADICR_DMF_OFF (4u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.ETRL */
#define IFX_DMA_CH_ADICR_ETRL_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.ETRL */
#define IFX_DMA_CH_ADICR_ETRL_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.ETRL */
#define IFX_DMA_CH_ADICR_ETRL_OFF (23u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.INCD */
#define IFX_DMA_CH_ADICR_INCD_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.INCD */
#define IFX_DMA_CH_ADICR_INCD_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.INCD */
#define IFX_DMA_CH_ADICR_INCD_OFF (7u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.INCS */
#define IFX_DMA_CH_ADICR_INCS_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.INCS */
#define IFX_DMA_CH_ADICR_INCS_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.INCS */
#define IFX_DMA_CH_ADICR_INCS_OFF (3u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.INTCT */
#define IFX_DMA_CH_ADICR_INTCT_LEN (2u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.INTCT */
#define IFX_DMA_CH_ADICR_INTCT_MSK (0x3u)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.INTCT */
#define IFX_DMA_CH_ADICR_INTCT_OFF (26u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.IRDV */
#define IFX_DMA_CH_ADICR_IRDV_LEN (4u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.IRDV */
#define IFX_DMA_CH_ADICR_IRDV_MSK (0xfu)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.IRDV */
#define IFX_DMA_CH_ADICR_IRDV_OFF (28u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.SCBE */
#define IFX_DMA_CH_ADICR_SCBE_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.SCBE */
#define IFX_DMA_CH_ADICR_SCBE_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.SCBE */
#define IFX_DMA_CH_ADICR_SCBE_OFF (20u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.SHCT */
#define IFX_DMA_CH_ADICR_SHCT_LEN (4u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.SHCT */
#define IFX_DMA_CH_ADICR_SHCT_MSK (0xfu)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.SHCT */
#define IFX_DMA_CH_ADICR_SHCT_OFF (16u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.SMF */
#define IFX_DMA_CH_ADICR_SMF_LEN (3u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.SMF */
#define IFX_DMA_CH_ADICR_SMF_MSK (0x7u)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.SMF */
#define IFX_DMA_CH_ADICR_SMF_OFF (0u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.STAMP */
#define IFX_DMA_CH_ADICR_STAMP_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.STAMP */
#define IFX_DMA_CH_ADICR_STAMP_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.STAMP */
#define IFX_DMA_CH_ADICR_STAMP_OFF (22u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.WRPDE */
#define IFX_DMA_CH_ADICR_WRPDE_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.WRPDE */
#define IFX_DMA_CH_ADICR_WRPDE_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.WRPDE */
#define IFX_DMA_CH_ADICR_WRPDE_OFF (25u)

/** \brief  Length for Ifx_DMA_CH_ADICR_Bits.WRPSE */
#define IFX_DMA_CH_ADICR_WRPSE_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_ADICR_Bits.WRPSE */
#define IFX_DMA_CH_ADICR_WRPSE_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_ADICR_Bits.WRPSE */
#define IFX_DMA_CH_ADICR_WRPSE_OFF (24u)

/** \brief  Length for Ifx_DMA_CH_CHCFGR_Bits.BLKM */
#define IFX_DMA_CH_CHCFGR_BLKM_LEN (3u)

/** \brief  Mask for Ifx_DMA_CH_CHCFGR_Bits.BLKM */
#define IFX_DMA_CH_CHCFGR_BLKM_MSK (0x7u)

/** \brief  Offset for Ifx_DMA_CH_CHCFGR_Bits.BLKM */
#define IFX_DMA_CH_CHCFGR_BLKM_OFF (16u)

/** \brief  Length for Ifx_DMA_CH_CHCFGR_Bits.CHDW */
#define IFX_DMA_CH_CHCFGR_CHDW_LEN (3u)

/** \brief  Mask for Ifx_DMA_CH_CHCFGR_Bits.CHDW */
#define IFX_DMA_CH_CHCFGR_CHDW_MSK (0x7u)

/** \brief  Offset for Ifx_DMA_CH_CHCFGR_Bits.CHDW */
#define IFX_DMA_CH_CHCFGR_CHDW_OFF (21u)

/** \brief  Length for Ifx_DMA_CH_CHCFGR_Bits.CHMODE */
#define IFX_DMA_CH_CHCFGR_CHMODE_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCFGR_Bits.CHMODE */
#define IFX_DMA_CH_CHCFGR_CHMODE_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCFGR_Bits.CHMODE */
#define IFX_DMA_CH_CHCFGR_CHMODE_OFF (20u)

/** \brief  Length for Ifx_DMA_CH_CHCFGR_Bits.DMAPRIO */
#define IFX_DMA_CH_CHCFGR_DMAPRIO_LEN (2u)

/** \brief  Mask for Ifx_DMA_CH_CHCFGR_Bits.DMAPRIO */
#define IFX_DMA_CH_CHCFGR_DMAPRIO_MSK (0x3u)

/** \brief  Offset for Ifx_DMA_CH_CHCFGR_Bits.DMAPRIO */
#define IFX_DMA_CH_CHCFGR_DMAPRIO_OFF (30u)

/** \brief  Length for Ifx_DMA_CH_CHCFGR_Bits.PATSEL */
#define IFX_DMA_CH_CHCFGR_PATSEL_LEN (3u)

/** \brief  Mask for Ifx_DMA_CH_CHCFGR_Bits.PATSEL */
#define IFX_DMA_CH_CHCFGR_PATSEL_MSK (0x7u)

/** \brief  Offset for Ifx_DMA_CH_CHCFGR_Bits.PATSEL */
#define IFX_DMA_CH_CHCFGR_PATSEL_OFF (24u)

/** \brief  Length for Ifx_DMA_CH_CHCFGR_Bits.PRSEL */
#define IFX_DMA_CH_CHCFGR_PRSEL_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCFGR_Bits.PRSEL */
#define IFX_DMA_CH_CHCFGR_PRSEL_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCFGR_Bits.PRSEL */
#define IFX_DMA_CH_CHCFGR_PRSEL_OFF (28u)

/** \brief  Length for Ifx_DMA_CH_CHCFGR_Bits.RROAT */
#define IFX_DMA_CH_CHCFGR_RROAT_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCFGR_Bits.RROAT */
#define IFX_DMA_CH_CHCFGR_RROAT_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCFGR_Bits.RROAT */
#define IFX_DMA_CH_CHCFGR_RROAT_OFF (19u)

/** \brief  Length for Ifx_DMA_CH_CHCFGR_Bits.TREL */
#define IFX_DMA_CH_CHCFGR_TREL_LEN (14u)

/** \brief  Mask for Ifx_DMA_CH_CHCFGR_Bits.TREL */
#define IFX_DMA_CH_CHCFGR_TREL_MSK (0x3fffu)

/** \brief  Offset for Ifx_DMA_CH_CHCFGR_Bits.TREL */
#define IFX_DMA_CH_CHCFGR_TREL_OFF (0u)

/** \brief  Length for Ifx_DMA_CH_CHCSR_Bits.BUFFER */
#define IFX_DMA_CH_CHCSR_BUFFER_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCSR_Bits.BUFFER */
#define IFX_DMA_CH_CHCSR_BUFFER_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCSR_Bits.BUFFER */
#define IFX_DMA_CH_CHCSR_BUFFER_OFF (22u)

/** \brief  Length for Ifx_DMA_CH_CHCSR_Bits.CICH */
#define IFX_DMA_CH_CHCSR_CICH_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCSR_Bits.CICH */
#define IFX_DMA_CH_CHCSR_CICH_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCSR_Bits.CICH */
#define IFX_DMA_CH_CHCSR_CICH_OFF (26u)

/** \brief  Length for Ifx_DMA_CH_CHCSR_Bits.CWRP */
#define IFX_DMA_CH_CHCSR_CWRP_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCSR_Bits.CWRP */
#define IFX_DMA_CH_CHCSR_CWRP_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCSR_Bits.CWRP */
#define IFX_DMA_CH_CHCSR_CWRP_OFF (25u)

/** \brief  Length for Ifx_DMA_CH_CHCSR_Bits.FROZEN */
#define IFX_DMA_CH_CHCSR_FROZEN_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCSR_Bits.FROZEN */
#define IFX_DMA_CH_CHCSR_FROZEN_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCSR_Bits.FROZEN */
#define IFX_DMA_CH_CHCSR_FROZEN_OFF (23u)

/** \brief  Length for Ifx_DMA_CH_CHCSR_Bits.ICH */
#define IFX_DMA_CH_CHCSR_ICH_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCSR_Bits.ICH */
#define IFX_DMA_CH_CHCSR_ICH_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCSR_Bits.ICH */
#define IFX_DMA_CH_CHCSR_ICH_OFF (18u)

/** \brief  Length for Ifx_DMA_CH_CHCSR_Bits.IPM */
#define IFX_DMA_CH_CHCSR_IPM_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCSR_Bits.IPM */
#define IFX_DMA_CH_CHCSR_IPM_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCSR_Bits.IPM */
#define IFX_DMA_CH_CHCSR_IPM_OFF (19u)

/** \brief  Length for Ifx_DMA_CH_CHCSR_Bits.LXO */
#define IFX_DMA_CH_CHCSR_LXO_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCSR_Bits.LXO */
#define IFX_DMA_CH_CHCSR_LXO_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCSR_Bits.LXO */
#define IFX_DMA_CH_CHCSR_LXO_OFF (15u)

/** \brief  Length for Ifx_DMA_CH_CHCSR_Bits.SCH */
#define IFX_DMA_CH_CHCSR_SCH_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCSR_Bits.SCH */
#define IFX_DMA_CH_CHCSR_SCH_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCSR_Bits.SCH */
#define IFX_DMA_CH_CHCSR_SCH_OFF (31u)

/** \brief  Length for Ifx_DMA_CH_CHCSR_Bits.SIT */
#define IFX_DMA_CH_CHCSR_SIT_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCSR_Bits.SIT */
#define IFX_DMA_CH_CHCSR_SIT_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCSR_Bits.SIT */
#define IFX_DMA_CH_CHCSR_SIT_OFF (27u)

/** \brief  Length for Ifx_DMA_CH_CHCSR_Bits.SWB */
#define IFX_DMA_CH_CHCSR_SWB_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCSR_Bits.SWB */
#define IFX_DMA_CH_CHCSR_SWB_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCSR_Bits.SWB */
#define IFX_DMA_CH_CHCSR_SWB_OFF (24u)

/** \brief  Length for Ifx_DMA_CH_CHCSR_Bits.TCOUNT */
#define IFX_DMA_CH_CHCSR_TCOUNT_LEN (14u)

/** \brief  Mask for Ifx_DMA_CH_CHCSR_Bits.TCOUNT */
#define IFX_DMA_CH_CHCSR_TCOUNT_MSK (0x3fffu)

/** \brief  Offset for Ifx_DMA_CH_CHCSR_Bits.TCOUNT */
#define IFX_DMA_CH_CHCSR_TCOUNT_OFF (0u)

/** \brief  Length for Ifx_DMA_CH_CHCSR_Bits.WRPD */
#define IFX_DMA_CH_CHCSR_WRPD_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCSR_Bits.WRPD */
#define IFX_DMA_CH_CHCSR_WRPD_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCSR_Bits.WRPD */
#define IFX_DMA_CH_CHCSR_WRPD_OFF (17u)

/** \brief  Length for Ifx_DMA_CH_CHCSR_Bits.WRPS */
#define IFX_DMA_CH_CHCSR_WRPS_LEN (1u)

/** \brief  Mask for Ifx_DMA_CH_CHCSR_Bits.WRPS */
#define IFX_DMA_CH_CHCSR_WRPS_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CH_CHCSR_Bits.WRPS */
#define IFX_DMA_CH_CHCSR_WRPS_OFF (16u)

/** \brief  Length for Ifx_DMA_CH_DADR_Bits.DADR */
#define IFX_DMA_CH_DADR_DADR_LEN (32u)

/** \brief  Mask for Ifx_DMA_CH_DADR_Bits.DADR */
#define IFX_DMA_CH_DADR_DADR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_DMA_CH_DADR_Bits.DADR */
#define IFX_DMA_CH_DADR_DADR_OFF (0u)

/** \brief  Length for Ifx_DMA_CH_RDCRCR_Bits.RDCRC */
#define IFX_DMA_CH_RDCRCR_RDCRC_LEN (32u)

/** \brief  Mask for Ifx_DMA_CH_RDCRCR_Bits.RDCRC */
#define IFX_DMA_CH_RDCRCR_RDCRC_MSK (0xffffffffu)

/** \brief  Offset for Ifx_DMA_CH_RDCRCR_Bits.RDCRC */
#define IFX_DMA_CH_RDCRCR_RDCRC_OFF (0u)

/** \brief  Length for Ifx_DMA_CH_SADR_Bits.SADR */
#define IFX_DMA_CH_SADR_SADR_LEN (32u)

/** \brief  Mask for Ifx_DMA_CH_SADR_Bits.SADR */
#define IFX_DMA_CH_SADR_SADR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_DMA_CH_SADR_Bits.SADR */
#define IFX_DMA_CH_SADR_SADR_OFF (0u)

/** \brief  Length for Ifx_DMA_CH_SDCRCR_Bits.SDCRC */
#define IFX_DMA_CH_SDCRCR_SDCRC_LEN (32u)

/** \brief  Mask for Ifx_DMA_CH_SDCRCR_Bits.SDCRC */
#define IFX_DMA_CH_SDCRCR_SDCRC_MSK (0xffffffffu)

/** \brief  Offset for Ifx_DMA_CH_SDCRCR_Bits.SDCRC */
#define IFX_DMA_CH_SDCRCR_SDCRC_OFF (0u)

/** \brief  Length for Ifx_DMA_CH_SHADR_Bits.SHADR */
#define IFX_DMA_CH_SHADR_SHADR_LEN (32u)

/** \brief  Mask for Ifx_DMA_CH_SHADR_Bits.SHADR */
#define IFX_DMA_CH_SHADR_SHADR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_DMA_CH_SHADR_Bits.SHADR */
#define IFX_DMA_CH_SHADR_SHADR_OFF (0u)

/** \brief  Length for Ifx_DMA_CLC_Bits.DISR */
#define IFX_DMA_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_DMA_CLC_Bits.DISR */
#define IFX_DMA_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CLC_Bits.DISR */
#define IFX_DMA_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_DMA_CLC_Bits.DISS */
#define IFX_DMA_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_DMA_CLC_Bits.DISS */
#define IFX_DMA_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CLC_Bits.DISS */
#define IFX_DMA_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_DMA_CLC_Bits.EDIS */
#define IFX_DMA_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_DMA_CLC_Bits.EDIS */
#define IFX_DMA_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_CLC_Bits.EDIS */
#define IFX_DMA_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_DMA_ERRINTR_Bits.SIT */
#define IFX_DMA_ERRINTR_SIT_LEN (1u)

/** \brief  Mask for Ifx_DMA_ERRINTR_Bits.SIT */
#define IFX_DMA_ERRINTR_SIT_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_ERRINTR_Bits.SIT */
#define IFX_DMA_ERRINTR_SIT_OFF (0u)

/** \brief  Length for Ifx_DMA_HRR_Bits.HRP */
#define IFX_DMA_HRR_HRP_LEN (2u)

/** \brief  Mask for Ifx_DMA_HRR_Bits.HRP */
#define IFX_DMA_HRR_HRP_MSK (0x3u)

/** \brief  Offset for Ifx_DMA_HRR_Bits.HRP */
#define IFX_DMA_HRR_HRP_OFF (0u)

/** \brief  Length for Ifx_DMA_ID_Bits.MODNUMBER */
#define IFX_DMA_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_DMA_ID_Bits.MODNUMBER */
#define IFX_DMA_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_DMA_ID_Bits.MODNUMBER */
#define IFX_DMA_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_DMA_ID_Bits.MODREV */
#define IFX_DMA_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_DMA_ID_Bits.MODREV */
#define IFX_DMA_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_ID_Bits.MODREV */
#define IFX_DMA_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_DMA_ID_Bits.MODTYPE */
#define IFX_DMA_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_DMA_ID_Bits.MODTYPE */
#define IFX_DMA_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_ID_Bits.MODTYPE */
#define IFX_DMA_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_DMA_MEMCON_Bits.DATAERR */
#define IFX_DMA_MEMCON_DATAERR_LEN (1u)

/** \brief  Mask for Ifx_DMA_MEMCON_Bits.DATAERR */
#define IFX_DMA_MEMCON_DATAERR_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_MEMCON_Bits.DATAERR */
#define IFX_DMA_MEMCON_DATAERR_OFF (6u)

/** \brief  Length for Ifx_DMA_MEMCON_Bits.ERRDIS */
#define IFX_DMA_MEMCON_ERRDIS_LEN (1u)

/** \brief  Mask for Ifx_DMA_MEMCON_Bits.ERRDIS */
#define IFX_DMA_MEMCON_ERRDIS_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_MEMCON_Bits.ERRDIS */
#define IFX_DMA_MEMCON_ERRDIS_OFF (9u)

/** \brief  Length for Ifx_DMA_MEMCON_Bits.INTERR */
#define IFX_DMA_MEMCON_INTERR_LEN (1u)

/** \brief  Mask for Ifx_DMA_MEMCON_Bits.INTERR */
#define IFX_DMA_MEMCON_INTERR_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_MEMCON_Bits.INTERR */
#define IFX_DMA_MEMCON_INTERR_OFF (2u)

/** \brief  Length for Ifx_DMA_MEMCON_Bits.PMIC */
#define IFX_DMA_MEMCON_PMIC_LEN (1u)

/** \brief  Mask for Ifx_DMA_MEMCON_Bits.PMIC */
#define IFX_DMA_MEMCON_PMIC_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_MEMCON_Bits.PMIC */
#define IFX_DMA_MEMCON_PMIC_OFF (8u)

/** \brief  Length for Ifx_DMA_MEMCON_Bits.RMWERR */
#define IFX_DMA_MEMCON_RMWERR_LEN (1u)

/** \brief  Mask for Ifx_DMA_MEMCON_Bits.RMWERR */
#define IFX_DMA_MEMCON_RMWERR_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_MEMCON_Bits.RMWERR */
#define IFX_DMA_MEMCON_RMWERR_OFF (4u)

/** \brief  Length for Ifx_DMA_MODE_Bits.MODE */
#define IFX_DMA_MODE_MODE_LEN (1u)

/** \brief  Mask for Ifx_DMA_MODE_Bits.MODE */
#define IFX_DMA_MODE_MODE_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_MODE_Bits.MODE */
#define IFX_DMA_MODE_MODE_OFF (0u)

/** \brief  Length for Ifx_DMA_OTSS_Bits.BS */
#define IFX_DMA_OTSS_BS_LEN (1u)

/** \brief  Mask for Ifx_DMA_OTSS_Bits.BS */
#define IFX_DMA_OTSS_BS_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_OTSS_Bits.BS */
#define IFX_DMA_OTSS_BS_OFF (7u)

/** \brief  Length for Ifx_DMA_OTSS_Bits.TGS */
#define IFX_DMA_OTSS_TGS_LEN (4u)

/** \brief  Mask for Ifx_DMA_OTSS_Bits.TGS */
#define IFX_DMA_OTSS_TGS_MSK (0xfu)

/** \brief  Offset for Ifx_DMA_OTSS_Bits.TGS */
#define IFX_DMA_OTSS_TGS_OFF (0u)

/** \brief  Length for Ifx_DMA_PRR0_Bits.PAT00 */
#define IFX_DMA_PRR0_PAT00_LEN (8u)

/** \brief  Mask for Ifx_DMA_PRR0_Bits.PAT00 */
#define IFX_DMA_PRR0_PAT00_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_PRR0_Bits.PAT00 */
#define IFX_DMA_PRR0_PAT00_OFF (0u)

/** \brief  Length for Ifx_DMA_PRR0_Bits.PAT01 */
#define IFX_DMA_PRR0_PAT01_LEN (8u)

/** \brief  Mask for Ifx_DMA_PRR0_Bits.PAT01 */
#define IFX_DMA_PRR0_PAT01_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_PRR0_Bits.PAT01 */
#define IFX_DMA_PRR0_PAT01_OFF (8u)

/** \brief  Length for Ifx_DMA_PRR0_Bits.PAT02 */
#define IFX_DMA_PRR0_PAT02_LEN (8u)

/** \brief  Mask for Ifx_DMA_PRR0_Bits.PAT02 */
#define IFX_DMA_PRR0_PAT02_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_PRR0_Bits.PAT02 */
#define IFX_DMA_PRR0_PAT02_OFF (16u)

/** \brief  Length for Ifx_DMA_PRR0_Bits.PAT03 */
#define IFX_DMA_PRR0_PAT03_LEN (8u)

/** \brief  Mask for Ifx_DMA_PRR0_Bits.PAT03 */
#define IFX_DMA_PRR0_PAT03_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_PRR0_Bits.PAT03 */
#define IFX_DMA_PRR0_PAT03_OFF (24u)

/** \brief  Length for Ifx_DMA_PRR1_Bits.PAT10 */
#define IFX_DMA_PRR1_PAT10_LEN (8u)

/** \brief  Mask for Ifx_DMA_PRR1_Bits.PAT10 */
#define IFX_DMA_PRR1_PAT10_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_PRR1_Bits.PAT10 */
#define IFX_DMA_PRR1_PAT10_OFF (0u)

/** \brief  Length for Ifx_DMA_PRR1_Bits.PAT11 */
#define IFX_DMA_PRR1_PAT11_LEN (8u)

/** \brief  Mask for Ifx_DMA_PRR1_Bits.PAT11 */
#define IFX_DMA_PRR1_PAT11_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_PRR1_Bits.PAT11 */
#define IFX_DMA_PRR1_PAT11_OFF (8u)

/** \brief  Length for Ifx_DMA_PRR1_Bits.PAT12 */
#define IFX_DMA_PRR1_PAT12_LEN (8u)

/** \brief  Mask for Ifx_DMA_PRR1_Bits.PAT12 */
#define IFX_DMA_PRR1_PAT12_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_PRR1_Bits.PAT12 */
#define IFX_DMA_PRR1_PAT12_OFF (16u)

/** \brief  Length for Ifx_DMA_PRR1_Bits.PAT13 */
#define IFX_DMA_PRR1_PAT13_LEN (8u)

/** \brief  Mask for Ifx_DMA_PRR1_Bits.PAT13 */
#define IFX_DMA_PRR1_PAT13_MSK (0xffu)

/** \brief  Offset for Ifx_DMA_PRR1_Bits.PAT13 */
#define IFX_DMA_PRR1_PAT13_OFF (24u)

/** \brief  Length for Ifx_DMA_SUSACR_Bits.SUSAC */
#define IFX_DMA_SUSACR_SUSAC_LEN (1u)

/** \brief  Mask for Ifx_DMA_SUSACR_Bits.SUSAC */
#define IFX_DMA_SUSACR_SUSAC_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_SUSACR_Bits.SUSAC */
#define IFX_DMA_SUSACR_SUSAC_OFF (0u)

/** \brief  Length for Ifx_DMA_SUSENR_Bits.SUSEN */
#define IFX_DMA_SUSENR_SUSEN_LEN (1u)

/** \brief  Mask for Ifx_DMA_SUSENR_Bits.SUSEN */
#define IFX_DMA_SUSENR_SUSEN_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_SUSENR_Bits.SUSEN */
#define IFX_DMA_SUSENR_SUSEN_OFF (0u)

/** \brief  Length for Ifx_DMA_TIME_Bits.COUNT */
#define IFX_DMA_TIME_COUNT_LEN (32u)

/** \brief  Mask for Ifx_DMA_TIME_Bits.COUNT */
#define IFX_DMA_TIME_COUNT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_DMA_TIME_Bits.COUNT */
#define IFX_DMA_TIME_COUNT_OFF (0u)

/** \brief  Length for Ifx_DMA_TSR_Bits.CH */
#define IFX_DMA_TSR_CH_LEN (1u)

/** \brief  Mask for Ifx_DMA_TSR_Bits.CH */
#define IFX_DMA_TSR_CH_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_TSR_Bits.CH */
#define IFX_DMA_TSR_CH_OFF (3u)

/** \brief  Length for Ifx_DMA_TSR_Bits.CTL */
#define IFX_DMA_TSR_CTL_LEN (1u)

/** \brief  Mask for Ifx_DMA_TSR_Bits.CTL */
#define IFX_DMA_TSR_CTL_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_TSR_Bits.CTL */
#define IFX_DMA_TSR_CTL_OFF (18u)

/** \brief  Length for Ifx_DMA_TSR_Bits.DCH */
#define IFX_DMA_TSR_DCH_LEN (1u)

/** \brief  Mask for Ifx_DMA_TSR_Bits.DCH */
#define IFX_DMA_TSR_DCH_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_TSR_Bits.DCH */
#define IFX_DMA_TSR_DCH_OFF (17u)

/** \brief  Length for Ifx_DMA_TSR_Bits.ECH */
#define IFX_DMA_TSR_ECH_LEN (1u)

/** \brief  Mask for Ifx_DMA_TSR_Bits.ECH */
#define IFX_DMA_TSR_ECH_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_TSR_Bits.ECH */
#define IFX_DMA_TSR_ECH_OFF (16u)

/** \brief  Length for Ifx_DMA_TSR_Bits.HLTACK */
#define IFX_DMA_TSR_HLTACK_LEN (1u)

/** \brief  Mask for Ifx_DMA_TSR_Bits.HLTACK */
#define IFX_DMA_TSR_HLTACK_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_TSR_Bits.HLTACK */
#define IFX_DMA_TSR_HLTACK_OFF (9u)

/** \brief  Length for Ifx_DMA_TSR_Bits.HLTCLR */
#define IFX_DMA_TSR_HLTCLR_LEN (1u)

/** \brief  Mask for Ifx_DMA_TSR_Bits.HLTCLR */
#define IFX_DMA_TSR_HLTCLR_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_TSR_Bits.HLTCLR */
#define IFX_DMA_TSR_HLTCLR_OFF (24u)

/** \brief  Length for Ifx_DMA_TSR_Bits.HLTREQ */
#define IFX_DMA_TSR_HLTREQ_LEN (1u)

/** \brief  Mask for Ifx_DMA_TSR_Bits.HLTREQ */
#define IFX_DMA_TSR_HLTREQ_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_TSR_Bits.HLTREQ */
#define IFX_DMA_TSR_HLTREQ_OFF (8u)

/** \brief  Length for Ifx_DMA_TSR_Bits.HTRE */
#define IFX_DMA_TSR_HTRE_LEN (1u)

/** \brief  Mask for Ifx_DMA_TSR_Bits.HTRE */
#define IFX_DMA_TSR_HTRE_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_TSR_Bits.HTRE */
#define IFX_DMA_TSR_HTRE_OFF (1u)

/** \brief  Length for Ifx_DMA_TSR_Bits.RST */
#define IFX_DMA_TSR_RST_LEN (1u)

/** \brief  Mask for Ifx_DMA_TSR_Bits.RST */
#define IFX_DMA_TSR_RST_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_TSR_Bits.RST */
#define IFX_DMA_TSR_RST_OFF (0u)

/** \brief  Length for Ifx_DMA_TSR_Bits.TRL */
#define IFX_DMA_TSR_TRL_LEN (1u)

/** \brief  Mask for Ifx_DMA_TSR_Bits.TRL */
#define IFX_DMA_TSR_TRL_MSK (0x1u)

/** \brief  Offset for Ifx_DMA_TSR_Bits.TRL */
#define IFX_DMA_TSR_TRL_OFF (2u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXDMA_BF_H */
