#ifndef _VSS_KEYM_H_
#define _VSS_KEYM_H_

#include "vssconf.h"
#include "sm4.h"
#include "vsstype.h"

enum{
	SECOC_KEY = 0,
	SECOC_PIN = 4,
	COMM_KEY = 5,
	RESV_KEY = 9,
	SYMM_KEY_NUM = 14,

	SESS_KEY_CUR = 0,
	SESS_KEY_SAV,
	SESS_KEY_NUM,
	
	TOTAL_KEY_NUM = SYMM_KEY_NUM + SESS_KEY_NUM,
};
#define SESS_KEY_CUR_IDX	0x80
#define SESS_KEY_SAV_IDX	0x82

#define SYS_CERT_NUM 6
#define ROOT_CIPHER_LEN 160
#define USER_CIPHER_LEN 192
#define SYMM_KEY_LEN 24
#define ASYMM_KEY_LEN 96
#define ASYMM_KEY_MAC 8
#define SYMM_CIPHER_LEN 32
#define ASYMM_CIPHER_LEN 112

enum{
	FLASH_CFG_OFFSET = 0,
	FLASH_CFG_KEY = 28,
	FLASH_CFG_DATA = 96,
	FLASH_CFG_SIZE = 112,
	FLASH_ROOTCERT_OFFSET = 128,
	FLASH_ROOTCERT_SIZE = ROOT_CIPHER_LEN * SYS_CERT_NUM,
	FLASH_PFLASH_SIZE = FLASH_ROOTCERT_OFFSET + FLASH_ROOTCERT_SIZE,
	
	FLASH_KEYCODE_OFFSET = FLASH_PFLASH_SIZE,
	FLASH_KEYCODE_SIZE = 32,
	FLASH_KEYCFG_OFFSET = FLASH_KEYCODE_OFFSET + FLASH_KEYCODE_SIZE,
	FLASH_CFG_ENV = FLASH_KEYCFG_OFFSET,
	FLASH_CFG_ALG,
	FLASH_CFG_WROTE,
	FLASH_KEYCFG_SIZE = 32,
	FLASH_KEYDATA_OFFSET = FLASH_KEYCFG_OFFSET + FLASH_KEYCFG_SIZE,
	FLASH_KEYDATA_SIZE = SYMM_CIPHER_LEN * TOTAL_KEY_NUM,
	FLASH_ASYMMKEY_OFFSET = FLASH_KEYDATA_OFFSET + FLASH_KEYDATA_SIZE,
	FLASH_ASYMMKEY_SIZE = ASYMM_CIPHER_LEN,
	FLASH_USERCERT_OFFSET = FLASH_ASYMMKEY_OFFSET + FLASH_ASYMMKEY_SIZE,
	FLASH_USERCERT_SIZE = USER_CIPHER_LEN,
	FLASH_DATA_SIZE = FLASH_USERCERT_OFFSET + FLASH_USERCERT_SIZE,
	
	FLASH_EXTDATA_OFFSET = FLASH_DATA_SIZE,
};

void DumpVssKey();
vss_uint32 LoadCert(vss_uint32 certType, vss_uint32* certlen, vss_uint8* certData);
vss_uint32 LoadCertById(vss_uint32 certId, vss_uint32* certlen, vss_uint8* certData);
vss_uint32 SaveCert(vss_uint32 certType, vss_uint32 certlen, vss_uint8* certData);
vss_uint32 GenSymmKey(vss_uint8* code, vss_char8* keyList);
vss_uint32 GetSessKey(vss_uint32 keyId, vss_uint32* keyLen, vss_uint8* keyData);
vss_uint32 GetSymmKey(vss_uint32 keyId, vss_uint32* keyLen, vss_uint8* keyData);
vss_uint32 SetKeyActive(vss_uint32 keyId, vss_uint32 valid);
vss_uint32 GetKeyActive(vss_uint32 keyId, vss_uint8* valid);
vss_uint32 SetSessKey(vss_uint32 keyId, vss_uint32 keyLen, vss_uint8* keyData);
vss_uint32 SetSymmKey(vss_uint32 keyId, vss_uint32 flag, vss_uint32 keyLen, vss_uint8* keyData);
vss_uint32 GenAsymmKey(vss_uint8* x, vss_uint8* y);
vss_uint32 LoadAsymmKey(vss_uint8* x, vss_uint8* y, vss_uint8* sk);
vss_uint32 SaveAsymmKey(vss_uint8* x, vss_uint8* y, vss_uint8* sk);
vss_uint32 LoadCFGData(vss_uint8* cfgData);
vss_uint32 SaveCFGData(vss_uint8* cfgData);
vss_uint32 CryptoPaddingTo16 (vss_uint8 *in, vss_uint32 inLen, vss_uint32 force);
vss_uint32 CryptoUnPadding16(vss_uint8 *in, vss_uint32 inLen);
vss_uint32 SaveKeyCode(vss_uint32 len ,vss_uint8* code);
vss_uint32 LoadKeyCode(vss_uint32* len ,vss_uint8* code);
vss_uint32 SetAlg(vss_uint8 alg);
vss_uint32 GetAlg(void);
vss_uint32 SetEnvironment(vss_uint8 env);
vss_uint32 GetEnvironment(void);
vss_uint32 SetFlag(vss_uint8 flag);
vss_uint32 GetFlag(vss_uint8* flag);
vss_uint32 SetWroteFlag(vss_uint8 flag);
vss_uint32 GetWroteFLag(vss_uint8* flag);


#endif
