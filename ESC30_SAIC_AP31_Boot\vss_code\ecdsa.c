/**
ecdsa.c
Author: <PERSON>
*/
#include "ecdsa.h"
#include "ecc.h"
#include "calc.h"


#pragma section code "vss_api_code" 

#if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U)) || \
	(defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))


#define ECDSA_VERSION 0x00000001

#define ECDSA_NULL (vss_uint32*)0x0

#define RET_ECDSA_POINTMUL_SUCCESS RET_ECC_IMPLEMENT_SUCCESS
#define RET_ECDSA_POINTMUL_ERROR   RET_ECC_IMPLEMENT_FAILED
#define RET_ECDSA_POINT_INVALID    RET_ECC_IMPLEMENT_FAILED
#define RET_ECDSA_POINT_VALID      RET_ECC_IMPLEMENT_SUCCESS
#define RET_ECDSA_INFINITE_POINT   RET_ECC_INFINITEFAR_POINT
#define RET_ECDSA_FINITE_POINT     RET_ECC_IMPLEMENT_SUCCESS

#define ECDSA_INFINITE_POINT         (('E'<<24)|('I'<<16)|('F'<<8)|('P'))

 vss_uint32 pointmuls_hw_fp(ecc_point_a *result, ecc_point_a *a, vss_uint32 *k, vss_uint32 rand, ecc_fp *para);
vss_uint32 pointmul_hw_fp(ecc_point_a *result, ecc_point_a *a, vss_uint32 *k, ecc_fp *para);
vss_uint32 ecc_verifypoint(ecc_fp *para, ecc_point_a *op_a);
vss_uint32 ecc_fp_affine_pointadd(ecc_fp *para, ecc_point_a *result, ecc_point_a *op_a, ecc_point_a *op_b);
vss_uint32 ecc_fp_affine_pointdbl(ecc_fp *para, ecc_point_a *result, ecc_point_a *op_a);

vss_uint32 ecdsa_pointmul_fp_normal(ecdsa_point *Q, vss_uint32 *k, ecdsa_point *P, vss_uint8 wlen, ecdsa_DomainPara *para);
vss_uint32 ecdsa_pointmul_fp_secure(ecdsa_point *Q, vss_uint32 *k, ecdsa_point *P, vss_uint32 rand, vss_uint8 wlen, ecdsa_DomainPara *para);
vss_uint32 ecdsa_pointmul4G_fp_normal(ecdsa_point *Q, vss_uint32 *k, ecdsa_point *P, vss_uint8 wlen, ecdsa_DomainPara *para);
vss_uint32 ecdsa_verify_point(ecdsa_point *P, vss_uint8 wlen, ecdsa_DomainPara *para);
vss_uint32 ecdsa_pointdbl(ecdsa_point *R, ecdsa_point *P, vss_uint8 wlen, ecdsa_DomainPara *para);
vss_uint32 ecdsa_pointadd(ecdsa_point *R, ecdsa_point *P, ecdsa_point *Q, vss_uint8 wlen, ecdsa_DomainPara *para,vss_uint32 P_property,vss_uint32 Q_property);

/*
Prototype: void ecdsa_version(vss_uint8 *result)
Input:  None
Output: *result  the pointer point to 5 byte space
Return value: None
Description:  Call this function to get the library version. Byte 0 and byte 1 indicate the hardware IP version, byte 2 to byte 4 indicate the software version.
*/
vss_uint32 ecdsa_version(void)
{
	return ECDSA_VERSION;
}

/*
Prototype:  vss_uint32 ecdsa_genkey(ecdsa_point *PubKey, vss_uint32 *PriKey, vss_uint32 *rand, vss_uint8 wlen, ecdsa_DomainPara *para)
Input:  *rand  a word-aligned pointer to the random data, the number of words for the random data shall be matched with the parameter wlen

wlen:   the number of words for Domain parameters, the value of this parameter shell be satisfied "wlen >= 5"

*para:  a pointer of ecdsa_DomainPara type to domain parameters, the number of words for the member of domain parameters shall be matched with the parameter wlen
Output: *PriKey   a word-aligned pointer to private key, the number of words for the private key shall be matched with the parameter wlen

*PubKey:  a pointer of ecdsa_point type to public key, the number of words for the member of public key shall be matched with the parameter wlen
Return value: RET_ECDSA_RAND_ERROR  the random number exceeds [1, n-1]

              RET_ECDSA_GENKEY_ERROR  the key generation is error

              RET_ECDSA_GENKEY_SUCCESS  the key generation is success
Description:  Call this function to generate private key and public key. This function according to section A.4.3 in ANSI X9.62-2005.
*/
vss_uint32 ecdsa_genkey(ecdsa_point *PubKey, vss_uint32 *PriKey, vss_uint32 *ecdsa_rand, vss_uint8 wlen, ecdsa_DomainPara *para)
{
	vss_uint32 blen = wlen * 4;
	vss_uint32 rand_buf[8];
	ecdsa_DomainPara para_buf;
	vss_uint32 a_buf[8];  
	vss_uint32 b_buf[8]; 
	vss_uint32 p_buf[8];
	vss_uint32 n_buf[8];
	ecdsa_point G_buf;
	vss_uint32 G_bufx[8];
	vss_uint32 G_bufy[8];	

	calc_bigtolit((vss_uint8*)rand_buf, (vss_uint8*)ecdsa_rand, blen);
	
	para_buf.a = a_buf;
	para_buf.b = b_buf;
	para_buf.p = p_buf;
	para_buf.n = n_buf;
	para_buf.G = &G_buf;
	
	G_buf.x = G_bufx;
	G_buf.y = G_bufy;
	
	calc_bigtolit((vss_uint8*)para_buf.a, (vss_uint8 *)para->a, blen);
	calc_bigtolit((vss_uint8*)para_buf.b, (vss_uint8 *)para->b, blen);
	calc_bigtolit((vss_uint8*)para_buf.p, (vss_uint8 *)para->p, blen);
	calc_bigtolit((vss_uint8*)para_buf.n, (vss_uint8 *)para->n, blen);
	
	calc_bigtolit((vss_uint8*)para_buf.G->x, (vss_uint8 *)para->G->x, blen);
	calc_bigtolit((vss_uint8*)para_buf.G->y, (vss_uint8 *)para->G->y, blen);

  if((calc_mem_cmp2((vss_uint32 *)rand_buf, 0, wlen) != CALC_BIGGER) || (calc_mem_cmp((vss_uint32 *)rand_buf, (vss_uint32 *)para_buf.n, wlen) != CALC_SMALLER))
    return RET_ECDSA_RAND_ERROR;

  calc_mem_cpy(PriKey, ecdsa_rand, wlen);


  if(ecdsa_pointmul_fp_normal(PubKey, rand_buf, para_buf.G, wlen, &para_buf) != RET_ECDSA_POINTMUL_SUCCESS)
    return RET_ECDSA_GENKEY_ERROR; 
	
	calc_bigtolit((vss_uint8*)PubKey->x, (vss_uint8*)PubKey->x, blen);
	calc_bigtolit((vss_uint8*)PubKey->y, (vss_uint8*)PubKey->y, blen);

  return  RET_ECDSA_GENKEY_SUCCESS;
}

/*
Prototype:  vss_uint32 ecdsa_verifypubkey(ecdsa_point *PubKey, vss_uint8 wlen, ecdsa_DomainPara *para)
Input:  *PubKey  a pointer of ecdsa_point type to public key, the number of words for the public key shall be matched with the parameter wlen

wlen:  the number of words for Domain parameters, the value of this parameter shell be satisfied "wlen >= 5"

*para:  a pointer of ecdsa_DomainPara type to domain parameters, the number of words for the member of domain parameters shall be matched with the parameter wlen
Output: None
Return value: RET_ECDSA_VERIFYKEY_ERROR  the public key verification is error

              RET_ECDSA_VERIFYKEY_SUCCESS  the public key verification is success
Description:  This function verifies the public key to check whether the public key is valid. This function according to section A.4.2 in ANSI X9.62-2005.
*/
vss_uint32 ecdsa_verifypubkey(ecdsa_point *PubKey, vss_uint8 wlen, ecdsa_DomainPara *para)
{
	vss_uint32 blen = wlen * 4;
	ecdsa_DomainPara para_buf;
	vss_uint32 a_buf[8];  
	vss_uint32 b_buf[8]; 
	vss_uint32 p_buf[8];
	vss_uint32 n_buf[8];
	ecdsa_point PubKey_buf;
	vss_uint32 PubKey_bufx[8];
	vss_uint32 PubKey_bufy[8];	

	para_buf.a = a_buf;
	para_buf.b = b_buf;
	para_buf.p = p_buf;
	para_buf.n = n_buf;
	
	PubKey_buf.x = PubKey_bufx;
	PubKey_buf.y = PubKey_bufy;	
	
	calc_bigtolit((vss_uint8*)para_buf.a, (vss_uint8 *)para->a, blen);
	calc_bigtolit((vss_uint8*)para_buf.b, (vss_uint8 *)para->b, blen);
	calc_bigtolit((vss_uint8*)para_buf.p, (vss_uint8 *)para->p, blen);
	calc_bigtolit((vss_uint8*)para_buf.n, (vss_uint8 *)para->n, blen);
	
	calc_bigtolit((vss_uint8*)PubKey_buf.x, (vss_uint8*)PubKey->x, blen);
	calc_bigtolit((vss_uint8*)PubKey_buf.y, (vss_uint8*)PubKey->y, blen);	
	

  if((calc_mem_cmp((vss_uint32 *)PubKey_buf.x, (vss_uint32 *)para_buf.p, wlen) != CALC_SMALLER) || (calc_mem_cmp((vss_uint32 *)PubKey_buf.y, (vss_uint32 *)para_buf.p, wlen) != CALC_SMALLER))
  {
    return RET_ECDSA_VERIFYKEY_ERROR;
  }	

  if(ecdsa_verify_point(&PubKey_buf, wlen, &para_buf) != RET_ECDSA_POINT_VALID)
  {
    return RET_ECDSA_VERIFYKEY_ERROR;
  }	


  if(ecdsa_pointmul_fp_normal((ecdsa_point *)ECDSA_NULL, para_buf.n, &PubKey_buf, wlen, &para_buf) != RET_ECC_IMPLEMENT_FAILED)
   {
    return RET_ECDSA_VERIFYKEY_ERROR;
   }	   
  
  return  RET_ECDSA_VERIFYKEY_SUCCESS;
}

vss_uint32 ecdsa_gensig_error(ecdsa_sig *sig,vss_uint8 wlen)
{
	calc_mem_set(sig->r,0,wlen);
	calc_mem_set(sig->s,0,wlen);

	return RET_ECDSA_GENSIG_ERROR;	
}

/*
Prototype:  vss_uint32 ecdsa_gensig(ecdsa_sig *sig, vss_uint32 *HashValue, vss_uint32 *PriKey, vss_uint32 *rand, vss_uint8 wlen, ecdsa_DomainPara *para);
Input:  *HashValue   a word-aligned pointer to the Hash value e = Hv(M), Hv indicates a hash algorithm such as SHA-1, SHA-224 and SHA256. the number of words for the hash value shall be matched with the parameter wlen

*PriKey:  a word-aligned pointer to the private key of signature role. the number of words for the private key shall be matched with the parameter wlen

*rand:  a word-aligned pointer to the random data, the number of words for the random data shall be matched with the parameter wlen

wlen:  the number of words for Domain parameters, the value of this parameter shell be satisfied "wlen >= 5"

*para:  a pointer of ecdsa_DomainPara type to domain parameters, the number of words for the member of domain parameters shall be matched with the parameter wlen
Output: *sig  a pointer of ecdsa_sig type to the signature, the number of words for the member of signature shall be matched with the parameter wlen
Return value: RET_ECDSA_RAND_ERROR  the random number exceeds [1, n-1]

              RET_ECDSA_GENSIG_ERROR  the signature generation is error

              RET_ECDSA_GENSIG_SUCCESS  the signature generation is success
Description:  This function generates the signature of hash value in ECDSA. This function according to section 7.3 in ANSI X9.62-2005 basically except hash calculation, so user shall calculate the hash value before calling this function.
*/
vss_uint32 ecdsa_gensig(ecdsa_sig *sig, vss_uint32 *HashValue, vss_uint32 hash_wlen, vss_uint32 *PriKey, \
vss_uint32 *ecdsa_rand, vss_uint8 wlen, ecdsa_DomainPara *para)
{
  vss_uint32 buffer0[8];
  vss_uint32 buffer1[8];
  vss_uint32 hash_buffer[8];
  ecdsa_point R;
  vss_uint32 Rx[8];
  vss_uint32 Ry[8];

  volatile vss_uint32 rt = RET_ECDSA_GENSIG_ERROR;
  volatile vss_uint32 function_mark = 0x1234;
  vss_uint32 AccBuf[8];
  
	vss_uint32 blen = wlen * 4;
	vss_uint32 PriKey_buf[8];
	vss_uint32 rand_buf[8];
	ecdsa_DomainPara para_buf;
	vss_uint32 a_buf[8];  
	vss_uint32 b_buf[8]; 
	vss_uint32 p_buf[8];
	vss_uint32 n_buf[8];
	ecdsa_point G_buf;
	vss_uint32 G_bufx[8];
	vss_uint32 G_bufy[8];	

	calc_bigtolit((vss_uint8*)PriKey_buf, (vss_uint8*)PriKey, blen);
	calc_bigtolit((vss_uint8*)rand_buf, (vss_uint8*)ecdsa_rand, blen);
	
	para_buf.a = a_buf;
	para_buf.b = b_buf;
	para_buf.p = p_buf;
	para_buf.n = n_buf;
	para_buf.G = &G_buf;
	
	G_buf.x = G_bufx;
	G_buf.y = G_bufy;
	
	calc_bigtolit((vss_uint8*)para_buf.a, (vss_uint8 *)para->a, blen);
	calc_bigtolit((vss_uint8*)para_buf.b, (vss_uint8 *)para->b, blen);
	calc_bigtolit((vss_uint8*)para_buf.p, (vss_uint8 *)para->p, blen);
	calc_bigtolit((vss_uint8*)para_buf.n, (vss_uint8 *)para->n, blen);
	
	calc_bigtolit((vss_uint8*)para_buf.G->x, (vss_uint8 *)para->G->x, blen);
	calc_bigtolit((vss_uint8*)para_buf.G->y, (vss_uint8 *)para->G->y, blen);	
	

  if(hash_wlen>=wlen)
  {
  	calc_bigtolit((vss_uint8*)hash_buffer,(vss_uint8*)HashValue,blen);
  }
  else
  {
  	calc_mem_set(hash_buffer+hash_wlen,0,wlen-hash_wlen);
  	calc_bigtolit((vss_uint8*)hash_buffer,(vss_uint8*)HashValue,(hash_wlen*4));
  }

  R.x = Rx;
  R.y = Ry;

  if((calc_mem_cmp2((vss_uint32 *)rand_buf, 0, wlen) != CALC_BIGGER) || (calc_mem_cmp((vss_uint32 *)rand_buf, (vss_uint32 *)para_buf.n, wlen) != CALC_SMALLER))
    return RET_ECDSA_RAND_ERROR;
  

 

	rt = ecdsa_pointmul4G_fp_normal(&R, rand_buf, para_buf.G, wlen, &para_buf);

  
  if( rt != RET_ECDSA_POINTMUL_SUCCESS)
  {
  	return ecdsa_gensig_error(sig,wlen);
  }
  
  

 	calc_mod(sig->r, R.x, wlen, para_buf.n, wlen);
	
 	if(  calc_mem_cmp2(sig->r, 0, wlen) == CALC_EQUAL)
 	{
   	return ecdsa_gensig_error(sig,wlen);
  }   
   



  calc_modinv(buffer0, rand_buf, wlen, para_buf.n, wlen);


  calc_modmul(AccBuf, buffer0, PriKey_buf, para_buf.n, wlen);


  calc_modmul(buffer1, AccBuf, sig->r, para_buf.n, wlen);


  calc_modmul(buffer0, buffer0, hash_buffer, para_buf.n, wlen);


  calc_modadd(sig->s, buffer0, buffer1, para_buf.n, wlen);


  if(( calc_mem_cmp2(sig->s, 0, wlen) == CALC_EQUAL))
  {
  	return ecdsa_gensig_error(sig,wlen);
  } 

  calc_bigtolit((vss_uint8*)sig->r, (vss_uint8*)sig->r, blen);
	calc_bigtolit((vss_uint8*)sig->s, (vss_uint8*)sig->s, blen);
	
  return RET_ECDSA_GENSIG_SUCCESS;
}

/*
Prototype:  vss_uint32 ecdsa_verifysig(ecdsa_sig *sig, vss_uint32 *HashValue, ecdsa_point *PubKey, vss_uint8 wlen, ecdsa_DomainPara *para);
Input:  *sig  a pointer of ecdsa_sig type to the signature, the number of words for the member of signature shall be matched with the parameter wlen

*HashValue:   a word-aligned pointer to the Hash value e = Hv(M), Hv indicates a hash algorithm such as SHA-1, SHA-224 and SHA256. the number of words for the hash value shall be matched with the parameter wlen

*PubKey:  a pointer of ecdsa_point type to public key which is sent by signature role, the number of words for the public key shall be matched with the parameter wlen

wlen:  the number of words for Domain parameters, the value of this parameter shell be satisfied "wlen >= 5"

*para:  a pointer of ecdsa_DomainPara type to domain parameters, the number of words for the member of domain parameters shall be matched with the parameter wlen
Output: None
Return value: RET_ECDSA_VERIFYSIG_ERROR  the signature verification is error

              RET_ECDSA_VERIFYSIG_SUCCESS   the signature verification is success
Description:  This function verifies whether the signature of hash value is matched with hash value itself in ECDSA. This function according to section 7.4.1 in ANSI X9.62-2005 basically except hash calculation, so user shall calculate the hash value before calling this function.
*/
vss_uint32 ecdsa_verifysig(ecdsa_sig *sig, vss_uint32 *HashValue, vss_uint32 hash_wlen, \
ecdsa_point *PubKey, vss_uint8 wlen, ecdsa_DomainPara *para)
{
  ecdsa_point point_buffer0;
  ecdsa_point point_buffer1;
  vss_uint32 point_buffer0_x[8];
  vss_uint32 point_buffer0_y[8];
  vss_uint32 point_buffer1_x[8];
  vss_uint32 point_buffer1_y[8];
  vss_uint32 hash_buffer[8];
  
  vss_uint32 config1 = 0;
  vss_uint32 config2 = 0;
  vss_uint32 rt = RET_ECDSA_VERIFYSIG_ERROR;
  
	vss_uint32 blen = wlen * 4;
	ecdsa_sig sig_buf;
	vss_uint32 sig_bufr[8];
	vss_uint32 sig_bufs[8];	
	ecdsa_point PubKey_buf;
	vss_uint32 PubKey_bufx[8];
	vss_uint32 PubKey_bufy[8];	
	ecdsa_DomainPara para_buf;
	vss_uint32 a_buf[8];  
	vss_uint32 b_buf[8]; 
	vss_uint32 p_buf[8];
	vss_uint32 n_buf[8];
	ecdsa_point G_buf;
	vss_uint32 G_bufx[8];
	vss_uint32 G_bufy[8];	

	sig_buf.r = sig_bufr;
	sig_buf.s = sig_bufs;

	PubKey_buf.x = PubKey_bufx;
	PubKey_buf.y = PubKey_bufy;

	para_buf.a = a_buf;
	para_buf.b = b_buf;
	para_buf.p = p_buf;
	para_buf.n = n_buf;
	para_buf.G = &G_buf;
	
	G_buf.x = G_bufx;
	G_buf.y = G_bufy;

	calc_bigtolit((vss_uint8*)sig_buf.r, (vss_uint8*)sig->r, blen);
	calc_bigtolit((vss_uint8*)sig_buf.s, (vss_uint8*)sig->s, blen);

	calc_bigtolit((vss_uint8*)PubKey_buf.x, (vss_uint8*)PubKey->x, blen);
	calc_bigtolit((vss_uint8*)PubKey_buf.y, (vss_uint8*)PubKey->y, blen);

	calc_bigtolit((vss_uint8*)para_buf.a, (vss_uint8 *)para->a, blen);
	calc_bigtolit((vss_uint8*)para_buf.b, (vss_uint8 *)para->b, blen);
	calc_bigtolit((vss_uint8*)para_buf.p, (vss_uint8 *)para->p, blen);
	calc_bigtolit((vss_uint8*)para_buf.n, (vss_uint8 *)para->n, blen);
	
	calc_bigtolit((vss_uint8*)para_buf.G->x, (vss_uint8 *)para->G->x, blen);
	calc_bigtolit((vss_uint8*)para_buf.G->y, (vss_uint8 *)para->G->y, blen);	
	

  if(hash_wlen>=wlen)
  {
  	calc_bigtolit((vss_uint8*)hash_buffer,(vss_uint8*)HashValue,blen);
  }
  else
  {
  	calc_mem_set(hash_buffer+hash_wlen,0,wlen-hash_wlen);
  	calc_bigtolit((vss_uint8*)hash_buffer,(vss_uint8*)HashValue,(hash_wlen*4));
  }


  if((calc_mem_cmp2((vss_uint32 *)sig_buf.r, 0, wlen) != CALC_BIGGER) || (calc_mem_cmp((vss_uint32 *)sig_buf.r, (vss_uint32 *)para_buf.n, wlen) != CALC_SMALLER))
    return RET_ECDSA_VERIFYSIG_ERROR;
  if((calc_mem_cmp2((vss_uint32 *)sig_buf.s, 0, wlen) != CALC_BIGGER) || (calc_mem_cmp((vss_uint32 *)sig_buf.s, (vss_uint32 *)para_buf.n, wlen) != CALC_SMALLER))
    return RET_ECDSA_VERIFYSIG_ERROR;


  calc_modinv(point_buffer1_x, sig_buf.s, wlen, para_buf.n, wlen);


  calc_modmul(point_buffer0_x, hash_buffer, point_buffer1_x, para_buf.n, wlen);


  point_buffer0.x = point_buffer0_x;
  point_buffer0.y = point_buffer0_y;
  
  rt = ecdsa_pointmul_fp_normal(&point_buffer0, point_buffer0_x, para_buf.G, wlen, &para_buf);
  
  if(rt == RET_ECDSA_INFINITE_POINT)
    config1 = ECDSA_INFINITE_POINT;
  
  else if(rt != RET_ECDSA_POINTMUL_SUCCESS)
  {
    return RET_ECDSA_VERIFYSIG_ERROR;
  }


  calc_modmul(point_buffer1_x, sig_buf.r, point_buffer1_x, para_buf.n, wlen);


  point_buffer1.x = point_buffer1_x;
  point_buffer1.y = point_buffer1_y;
  
  rt = ecdsa_pointmul_fp_normal(&point_buffer1, point_buffer1_x, &PubKey_buf, wlen, &para_buf);
  
  if( rt == RET_ECDSA_INFINITE_POINT)
    config2 = ECDSA_INFINITE_POINT;
  
  else if(rt != RET_ECDSA_POINTMUL_SUCCESS)
   {	
    return RET_ECDSA_VERIFYSIG_ERROR;
   }


  if(ecdsa_pointadd(&point_buffer0, &point_buffer0, &point_buffer1, wlen, &para_buf, config1,config2) != RET_ECDSA_FINITE_POINT)
   {	
    return RET_ECDSA_VERIFYSIG_ERROR;
   }


  calc_mod(point_buffer0_y, point_buffer0.x, wlen, para_buf.n, wlen);


  if(calc_mem_cmp(point_buffer0_y, sig_buf.r, wlen) != CALC_EQUAL)
   {
    return RET_ECDSA_VERIFYSIG_ERROR;
   }

  return RET_ECDSA_VERIFYSIG_SUCCESS;
}



vss_uint32 ecdsa_pointmul_fp_normal(ecdsa_point *Q, vss_uint32 *k, ecdsa_point *P, vss_uint8 wlen, ecdsa_DomainPara *para)
{
	ecc_fp para1;

	para1.a = para->a;
	para1.b = para->b;
	para1.p = para->p;
	para1.n = para->n;
	para1.wlen = wlen;

	return ecc_pointmul((ecc_point_a *)Q, (ecc_point_a *)P, k, &para1);
}

vss_uint32 ecdsa_pointmul4G_fp_normal(ecdsa_point *Q, vss_uint32 *k, ecdsa_point *P, vss_uint8 wlen, ecdsa_DomainPara *para)
{
	ecc_fp para1;

	para1.a = para->a;
	para1.b = para->b;
	para1.p = para->p;
	para1.n = para->n;
	para1.wlen = wlen;

	return ecc_pointmul4G((ecc_point_a *)Q, (ecc_point_a *)P, k, &para1);
}

vss_uint32 ecdsa_verify_point(ecdsa_point *P, vss_uint8 wlen, ecdsa_DomainPara *para)
{
	ecc_fp para1;

	para1.a = para->a;
	para1.b = para->b;
	para1.p = para->p;
	para1.n = para->n;
	para1.wlen = wlen;

	return ecc_verifypoint(&para1, (ecc_point_a *)P);
}

vss_uint32 ecdsa_pointadd(ecdsa_point *R, ecdsa_point *P, ecdsa_point *Q, vss_uint8 wlen, ecdsa_DomainPara *para,vss_uint32 P_property,vss_uint32 Q_property)
{
	ecc_fp para1;

	para1.a = para->a;
	para1.b = para->b;
	para1.p = para->p;
	para1.n = para->n;
	para1.wlen = wlen;

  if( (P_property != ECDSA_INFINITE_POINT) && (Q_property == ECDSA_INFINITE_POINT) )
  {
  	  calc_mem_cpy(R->x,P->x,wlen);
  	  calc_mem_cpy(R->y,P->y,wlen);
  	  return RET_ECDSA_FINITE_POINT;
  }
  
  else if( (P_property == ECDSA_INFINITE_POINT) && (Q_property != ECDSA_INFINITE_POINT) )
  {
  	  calc_mem_cpy(R->x,Q->x,wlen);
  	  calc_mem_cpy(R->y,Q->y,wlen);
  	  return RET_ECDSA_FINITE_POINT;
  }
  
  else if( (P_property == ECDSA_INFINITE_POINT) && (Q_property == ECDSA_INFINITE_POINT) )
  {
  	 
  	  return RET_ECDSA_INFINITE_POINT;
  }
  
  else
  {
		return ecc_pointadd((ecc_point_a *)R, (ecc_point_a *)P, (ecc_point_a *)Q, &para1);
	}
}

vss_uint32 ecdsa_pointdbl(ecdsa_point *R, ecdsa_point *P, vss_uint8 wlen, ecdsa_DomainPara *para)
{
	ecc_fp para1;

	para1.a = para->a;
	para1.b = para->b;
	para1.p = para->p;
	para1.n = para->n;
	para1.wlen = wlen;

	return ecc_pointdbl((ecc_point_a *)R, (ecc_point_a *)P, &para1);
}

#endif

#pragma section code restore


