#ifndef _CALC_ECC_H_
#define _CALC_ECC_H_

#include "vsstype.h"
#include "vssconf.h"

#define MODMULP_OPTIMIZE_SM2 0xFFFFFFFE
#define MODMULP_OPTIMIZE_NISTP256 0xFFFFFFFF
#define MODMULP_OPTIMIZE_NONE 0x00000000


#define ECC_NULL        0

#define ECC_SCALAR_NOT_EVEN (('E'<<24)|('S'<<16)|('N'<<8)|('E'))
#define ECC_SCALAR_IS_EVEN (('E'<<24)|('S'<<16)|('I'<<8)|('E'))

#define mod8(x)       (x%8)

typedef struct
{
	vss_uint32 *x;
	vss_uint32 *y;
}ecc_point_a;

typedef struct
{
	vss_uint32 *x;
	vss_uint32 *y;
	vss_uint32 *z;
}ecc_point_j;

typedef struct
{
	vss_uint32 *a;
	vss_uint32 a_n3;
	vss_uint32 *b;
	vss_uint32 *p;
	vss_uint32 *n;
	vss_uint32 *h;
	vss_uint32 wlen;
}ecc_fp;


#define RET_ECC_INFINITEFAR_POINT 		(('R'<<24)|('E'<<16)|('I'<<8)|('P'))
#define RET_ECC_IMPLEMENT_SUCCESS  (('R'<<24)|('E'<<16)|('I'<<8)|('S'))
#define RET_ECC_IMPLEMENT_FAILED  (('R'<<24)|('E'<<16)|('I'<<8)|('F'))

vss_uint32 ecc_version(void);
vss_uint32 ecc_genpubkey(ecc_point_a *pubkey, ecc_point_a *base, vss_uint32 *prikey, ecc_fp *para);
vss_uint32 ecc_pointinv(ecc_point_a *result, ecc_point_a *op_a, ecc_fp *para);
vss_uint32 ecc_pointadd (ecc_point_a *result, ecc_point_a *op_a, ecc_point_a *op_b, ecc_fp *para);
vss_uint32 ecc_pointdbl (ecc_point_a *result, ecc_point_a *op_a, ecc_fp *para);
vss_uint32 ecc_pointmul(ecc_point_a *result, ecc_point_a *a, vss_uint32 *k, ecc_fp *para);

#endif
