///*============================================================================*/
///*  Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
// *
// *  All rights reserved. This software is iSOFT property. Duplication
// *  or disclosure without iSOFT written authorization is prohibited.
// *
// *
// *  @file       <Mcu.h>
// *  @brief      <This is Mcu header file>
// *
// *  <Compiler: TASKING3.5    MCU:TC1782>
// *
// *  <AUTHOR>
// *  @date       <2014-5-30>
// */
///*============================================================================*/
///******************************************************************************/
//#ifndef MCU_H
//#define MCU_H
//
///*=======[R E V I S I O N   H I S T O R Y]====================================*/
///*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
// *  V1.0.0       20140307  JJQ         Initial version
// *  V1.0.1       20140530  jianan.liu  fix it for bootloader
// */
///*============================================================================*/
//
//
///*=======[I N C L U D E S]====================================================*/
//#include "Std_Types.h"
//#include "Mcu_Cfg.h"
//
///*=======[M A C R O S]========================================================*/
//
//
///*=======[T Y P E   D E F I N I T I O N S]====================================*/
//typedef struct tagPllInitValue
//{
//	unsigned int uiOSCCON;
//	unsigned int uiPLLCON0;
//	unsigned int uiPLLCON1;
//	unsigned int uiCCUCON0;
//	unsigned int uiCCUCON1;
//	unsigned int uiCCUCON2;
//} TPllInitValue, *PPllInitValue;
///*=======[E X T E R N A L   D A T A]==========================================*/
//
//
///*=======[E X T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
///******************************************************************************/
///*
// * Brief               <Mcu_Init>
// * Sync/Async          <Synchronous>
// * Reentrancy          <Non-Reentrant>
// * Param-Name[in]      <ConfigPtr>
// * Param-Name[out]     <None>
// * Param-Name[in/out]  <None>
// * Return              <None>
// * PreCondition        <None>
// * CallByAPI           <APIName>
// */
///******************************************************************************/
//extern void Mcu_Init (void);
//
///******************************************************************************/
///*
// * Brief               <Mcu_Deinit>
// * Sync/Async          <Synchronous>
// * Reentrancy          <Non-Reentrant>
// * Param-Name[in]      <ConfigPtr>
// * Param-Name[out]     <None>
// * Param-Name[in/out]  <None>
// * Return              <None>
// * PreCondition        <None>
// * CallByAPI           <APIName>
// */
///******************************************************************************/
////extern void Mcu_Deinit(void);
//extern unsigned long SYSTEM_GetStmClock(void);
//
//#endif
