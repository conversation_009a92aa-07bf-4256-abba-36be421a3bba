"SecureSignHeader"
"datas",0xA1,0x1,0x0,0x0,0x0,0x1,0x80,0x8,0x0,0x0,0x0,0x18
"app_parameters"
"moduleId",0xA1,0x1
"NBID",0x0,0x0
"locInfo",0x0,0x1,0x80,0x8,0x0,0x0,0x0,0x18,0x0,0x0
"signerInfoNtl",0x20,0x0,0x0,0x0,0x82,0xFF,0xFF,0xFF,0xFF,0
"msgDigestNtl",0xCD,0x41,0x7,0x46,0x7D,0x3F,0xF8,0x4B,0x58
"signNtl",0x14,0x80,0xA2,0x66,0x49,0x4E,0x7,0xE2,0x50,0xD5
"signerInfoIntl",0x20,0x0,0x0,0x0,0x81,0xFF,0xFF,0xFF,0xFF
"msgDigestIntl",0x98,0x9D,0xC9,0x96,0x4A,0xD9,0x1A,0x71,0xB4
"signIntl",0x59,0x35,0x53,0xAA,0x13,0x3E,0x94,0x56,0x7E,0xC
"SecureSignHeader.app_parameters.signerInfoNtl[163]",0xA4
"SecureSignHeader.app_parameters.signerInfoNtl[98]",0xDB
"SecureSignHeader.app_parameters.signerInfoNtl[101]",0x92
"SecureSignHeader.app_parameters.signerInfoNtl[100]",0x9D
"SecureSignHeader.app_parameters.signerInfoNtl[99]",0x94
"RootPublickey",0x90,0x7A,0x89,0x39,0x64,0xEC,0x2E,0x88,0xFD,0x
"rootCertSigature",0x9D,0x92,0xDD,0x84,0x43,0xDB,0x99,0x0F,0x23
"rootdata"
0x20
0x0
0x0
0x0
0x82
0xFF
0xFF
0xFF
0xFF
0x30
0x30
0x30
0x30
0x30
0x30
0x30
0x30
0x30
0x30
0x30
0x30
0x30
0x30
0x30
0x30
0x42
0x11
0x5
0x0
0x0
0x0
0x19
0x4
0x11
0x7
0x41
0x8E
0xCB
0x34
0x6F
0x20
0xBD
0x8A
0x31
0x20
0x8B
0xFB
0x2
0x8C
0x11
0x67
0xEA
0xDD
0x50
0x4F
0xCA
0x9D
0xEF
0xFA
0x2C
0x8D
0x7A
0xAE
0xE7
0xE0
0x5B
0xEC
0xE4
0x1D
0x33
0x2E
0xB1
0x5D
0x4E
0x69
0xBF
0xDE
0x44
0x0E
0x54
0xC4
0x46
0xCC
0x6C
0xCF
0xE6
0x16
0x9E
0x18
0xEC
0xE9
0xD9
0xD7
0x47
0x7D
0xB7
0x10
0xAA
0xDB
0x94
