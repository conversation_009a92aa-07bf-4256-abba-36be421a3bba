<?xml version='1.0'?>
<datamodel version="3.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/08/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/08/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd" >
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Icu" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Icu" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/AURIX/Icu"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuildSelectable">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:lst name="IcuConfigSet" type="MAP">
                <d:ctr name="IcuConfigSet_0" type="IDENTIFIABLE">
                  <d:var name="IcuMaxChannel" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@CALC</a:v>
                      <a:v>@DEF</a:v>
                    </a:a>
                  </d:var>
                  <d:lst name="IcuChannel" type="MAP">
                    <d:ctr name="IcuChannel_0" type="IDENTIFIABLE">
                      <d:var name="IcuChannelId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                             value="ICU_FALLING_EDGE"/>
                      <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                             value="ICU_MODE_SIGNAL_EDGE_DETECT">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="IcuAssignedHwUnit" type="REFERENCE" 
                             value="ASPath:/Mcu/Mcu/McuModuleConfiguration_0/CcuConfiguration_0/Ccu61/T12/Cc62"/>
                      <d:var name="IcuSignalType" type="ENUMERATION" 
                             value="ICU_QM_SIGNAL">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:lst name="IcuSignalEdgeDetection" type="MAP">
                        <d:ctr name="IcuSignalEdgeDetection_0" 
                               type="IDENTIFIABLE">
                          <d:lst name="IcuSignalNotification">
                            <d:var type="FUNCTION-NAME" value="Ccu6_Edge_Isr"/>
                          </d:lst>
                        </d:ctr>
                      </d:lst>
                      <d:lst name="IcuSignalMeasurement" type="MAP"/>
                      <d:lst name="IcuTimestampMeasurement" type="MAP"/>
                      <d:lst name="IcuWakeup" type="MAP"/>
                    </d:ctr>
                  </d:lst>
                </d:ctr>
              </d:lst>
              <d:ctr name="IcuGeneral" type="IDENTIFIABLE">
                <d:var name="IcuDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuDebugSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuIndex" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuReportWakeupSource" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuPBFixedAddress" type="BOOLEAN" value="true"/>
                <d:var name="IcuResetSfrAtInit" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuUserModeRuntimeApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuUserModeInitApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuUserModeDeInitApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuRunningInUser0Mode" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="IcuOptionalApis" type="IDENTIFIABLE">
                <d:var name="IcuDeInitApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuDisableWakeupApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuEdgeCountApi" type="BOOLEAN" value="false"/>
                <d:var name="IcuEdgeDetectApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuEnableWakeupApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuGetDutyCycleValuesApi" type="BOOLEAN" 
                       value="false"/>
                <d:var name="IcuGetInputStateApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuGetTimeElapsedApi" type="BOOLEAN" value="false"/>
                <d:var name="IcuGetVersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuSetModeApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuSignalMeasurementApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuTimestampApi" type="BOOLEAN" value="false"/>
                <d:var name="IcuWakeupFunctionalityApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="IcuSafety" type="IDENTIFIABLE">
                <d:var name="IcuSafetyEnable" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuInitCheckApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuGetModeApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArMajorVersion" type="INTEGER" value="4"/>
                <d:var name="ArMinorVersion" type="INTEGER" value="2"/>
                <d:var name="ArPatchVersion" type="INTEGER" value="0"/>
                <d:var name="SwMajorVersion" type="INTEGER" value="4"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="0"/>
                <d:var name="SwPatchVersion" type="INTEGER" value="1"/>
                <d:var name="ModuleId" type="INTEGER" value="122">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="17">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="Release" type="STRING" value="_TRICORE_TC234">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="GtmCcu6">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
