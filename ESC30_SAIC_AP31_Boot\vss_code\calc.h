
#ifndef _VSS_CALC_H_
#define _VSS_CALC_H_

#include "bignum_wrapper.h"

#define CALC_NULL 0

#define CALC_BIGGER  (('C' << 24) | ('B' << 16) | ('I' << 8) | ('G'))
#define CALC_SMALLER (('C' << 24) | ('S' << 16) | ('M' << 8) | ('A'))
#define CALC_EQUAL   (('C' << 24) | ('E' << 16) | ('Q' << 8) | ('U'))
#define CALC_UNEQUAL (('C' << 24) | ('U' << 16) | ('N' << 8) | ('E')) 

void calc_bigtolit(vss_uint8* dst, vss_uint8 *src, vss_uint32 blen);
void calc_mem_set(vss_uint32 *data1,vss_uint32 data2,vss_uint32 wlen);
vss_uint32 calc_mem_cmp(vss_uint32 *data1,vss_uint32 *data2,vss_uint32 wlen);
vss_uint32 calc_mem_cmp2(vss_uint32 *data1,vss_uint32 data2,vss_uint32 wlen);
vss_uint32 calc_mem_cmp0(vss_uint32 *data1, vss_uint32 wlen);
void calc_mem_cpy(vss_uint32* dst, vss_uint32 *src, vss_uint32 wlen);
void calc_mem_invcpy(vss_uint32 *dst, vss_uint32 *src, vss_uint32 wlen);

#endif


