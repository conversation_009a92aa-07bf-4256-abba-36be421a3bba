# Tasking TriCore 工具链配置文件
# 基于ESC30_SAIC_AP31_Boot项目的Debug目录分析

# 设置工具链类型
set(CMAKE_SYSTEM_NAME Generic)
set(CMAKE_SYSTEM_PROCESSOR tricore)

# Tasking编译器路径配置
set(TASKING_PRODDIR "C:/Program Files (x86)/TASKING/TriCore v4.2r2/ctc")
set(TASKING_BIN_DIR "${TASKING_PRODDIR}/bin")

# 编译器可执行文件
set(CMAKE_C_COMPILER "${TASKING_BIN_DIR}/cctc.exe")
set(CMAKE_ASM_COMPILER "${TASKING_BIN_DIR}/cctc.exe")
set(CMAKE_LINKER "${TASKING_BIN_DIR}/cctc.exe")

# 禁用编译器检查（因为我们只是为了IDE支持）
set(CMAKE_C_COMPILER_FORCED TRUE)
set(CMAKE_ASM_COMPILER_FORCED TRUE)

# Tasking编译器特有的标志
set(TASKING_CPU_FLAGS "-Cuserdef16x -t")
set(TASKING_ASSEMBLER_FLAGS "-Wa-H\"sfr/reguserdef16x.def\" -Wa-gAHLs --emit-locals=-equ,-symbols -Wa-Ogs -Wa--error-limit=42")
set(TASKING_LANGUAGE_FLAGS "--iso=99 --language=-gcc,-volatile,+strings")
set(TASKING_OPTIMIZATION_FLAGS "-O2 --tradeoff=4 --compact-max-size=200")
set(TASKING_MEMORY_FLAGS "--default-near-size=0 --default-a0-size=0 --default-a1-size=0")
set(TASKING_ALIGNMENT_FLAGS "--switch=auto --align=4")
set(TASKING_DEBUG_FLAGS "-g --source")

# 组合所有编译器标志
set(CMAKE_C_FLAGS_INIT "${TASKING_CPU_FLAGS} ${TASKING_ASSEMBLER_FLAGS} ${TASKING_LANGUAGE_FLAGS} ${TASKING_OPTIMIZATION_FLAGS} ${TASKING_MEMORY_FLAGS} ${TASKING_ALIGNMENT_FLAGS} ${TASKING_DEBUG_FLAGS}")

# 预定义宏
set(CMAKE_C_FLAGS_INIT "${CMAKE_C_FLAGS_INIT} -D_TASKING_C_TRICORE_=1")

# 链接器标志
set(TASKING_LINKER_FLAGS "-Wl-o\"AP31_ESC_PBL.hex\":IHEX:4 --hex-format=s -Wl-D_TASKING_C_TRICORE_=1 -Wl-OtxycL -Wl--map-file=\"AP31_ESC_PBL.mapxml\":XML -Wl-mcrfiklSmNOduQ -Wl--error-limit=42 -Wl-Cmpe:tc0 -Wl--munch")
set(CMAKE_EXE_LINKER_FLAGS_INIT "${TASKING_LINKER_FLAGS}")

# 文件扩展名
set(CMAKE_C_OUTPUT_EXTENSION ".o")
set(CMAKE_ASM_OUTPUT_EXTENSION ".o")
set(CMAKE_EXECUTABLE_SUFFIX ".elf")

# 设置查找程序的模式
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)

# 禁用一些不适用的功能
set(CMAKE_C_COMPILER_WORKS 1)
set(CMAKE_ASM_COMPILER_WORKS 1)

# 输出配置信息
message(STATUS "Tasking TriCore toolchain configured")
message(STATUS "PRODDIR: ${TASKING_PRODDIR}")
message(STATUS "Compiler: ${CMAKE_C_COMPILER}")
