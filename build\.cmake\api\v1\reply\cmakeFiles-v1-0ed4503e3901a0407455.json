{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.2/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.2/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.2/CMakeASMCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Platform/Windows-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Platform/Windows-GNU.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.28.2/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Platform/Windows-GNU-C-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/CMakeASMInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Compiler/GNU-ASM.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Platform/Windows-GNU-ASM.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/Cmake3.28/share/cmake-3.28/Modules/Platform/Windows-GNU.cmake"}, {"path": "cmake/tasking-definitions.cmake"}, {"path": "ESC30_SAIC_AP31_Boot/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "D:/yeah/new requirement/AP31BOOT20250725/SBL/ESC30_SAIC_AP31_BootV1.03.01/build", "source": "D:/yeah/new requirement/AP31BOOT20250725/SBL/ESC30_SAIC_AP31_BootV1.03.01"}, "version": {"major": 1, "minor": 0}}