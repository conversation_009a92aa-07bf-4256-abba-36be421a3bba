<?xml version='1.0'?>
<datamodel version="3.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/08/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/08/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd" >
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Dio" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Dio" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/AURIX/Dio"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuildSelectable">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:lst name="DioConfig" type="MAP">
                <d:ctr name="DioConfig_13" type="IDENTIFIABLE">
                  <d:lst name="DioPort" type="MAP">
                    <d:ctr name="DioPort_34" type="IDENTIFIABLE">
                      <d:var name="DioPortId" type="INTEGER" value="34"/>
                      <d:lst name="DioChannel" type="MAP">
                        <d:ctr name="O_F_SCLB_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="3"/>
                        </d:ctr>
                      </d:lst>
                      <d:lst name="DioChannelGroup" type="MAP"/>
                    </d:ctr>
                    <d:ctr name="DioPort_33" type="IDENTIFIABLE">
                      <d:var name="DioPortId" type="INTEGER" value="33"/>
                      <d:lst name="DioChannel" type="MAP">
                        <d:ctr name="B_D_SDAB_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="O_F_SCLA_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="1">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="B_D_SDAA_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="2">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="O_S_POWERA_EN1_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="3">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="O_S_POWERA_EN2_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="4">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="O_S_POWERA_EN3_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="5">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="O_S_POWERA_NRST_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="6">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="O_S_POWERB_NRST_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="7">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                      </d:lst>
                      <d:lst name="DioChannelGroup" type="MAP"/>
                    </d:ctr>
                    <d:ctr name="DioPort_20" type="IDENTIFIABLE">
                      <d:var name="DioPortId" type="INTEGER" value="20"/>
                      <d:lst name="DioChannel" type="MAP">
                        <d:ctr name="O_S_WD_DIS_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="8"/>
                        </d:ctr>
                      </d:lst>
                      <d:lst name="DioChannelGroup" type="MAP"/>
                    </d:ctr>
                    <d:ctr name="DioPort_15" type="IDENTIFIABLE">
                      <d:var name="DioPortId" type="INTEGER" value="15"/>
                      <d:lst name="DioChannel" type="MAP">
                        <d:ctr name="O_S_EQ_POWER_B_EN1_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="O_S_EQ_POWER_B_EN2_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="1">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="O_S_EQ_POWER_B_EN3_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="2">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="O_S_1V8_SENSOR_EN_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="3">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="O_S_5V0_EN_M" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="5"/>
                        </d:ctr>
                      </d:lst>
                      <d:lst name="DioChannelGroup" type="MAP"/>
                    </d:ctr>
                    <d:ctr name="DioPort_10" type="IDENTIFIABLE">
                      <d:var name="DioPortId" type="INTEGER" value="10"/>
                      <d:lst name="DioChannel" type="MAP">
                        <d:ctr name="O_S_EQ_POR" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="1">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="O_S_EQ_RST" type="IDENTIFIABLE">
                          <d:var name="DioChannelId" type="INTEGER" value="2">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                        </d:ctr>
                      </d:lst>
                      <d:lst name="DioChannelGroup" type="MAP"/>
                    </d:ctr>
                  </d:lst>
                </d:ctr>
              </d:lst>
              <d:ctr name="DioGeneral" type="IDENTIFIABLE">
                <d:var name="DioDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioFlipChannelApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioPBFixedAddress" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioVersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioRunningInUser0Mode" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioUserModeInitApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioUserModeRuntimeApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="DioSafety" type="IDENTIFIABLE">
                <d:var name="DioSafetyEnable" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioInitCheckApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArMajorVersion" type="INTEGER" value="2"/>
                <d:var name="ArMinorVersion" type="INTEGER" value="5"/>
                <d:var name="ArPatchVersion" type="INTEGER" value="0"/>
                <d:var name="SwMajorVersion" type="INTEGER" value="3"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="3"/>
                <d:var name="SwPatchVersion" type="INTEGER" value="0"/>
                <d:var name="ModuleId" type="INTEGER" value="120">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="17">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="Release" type="STRING" value="_TRICORE_TC234">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
