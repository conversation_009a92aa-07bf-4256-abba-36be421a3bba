/*
 * CDD_Wdg.c
 *
 *  Created on: 2019-8-12
 *      Author: g<PERSON><PERSON><PERSON><PERSON>
 */
#include "Std_Types.h"
#include "Port.h"
#include "Dio.h"
#include "CDD_Wdg.h"

#include "Std_Types.h"
#include "IfxScu_reg.h"
#include "IfxCpu_reg.h"
/* Inclusion of EcuM.h */
#include "EcuM.h"
#include "Test_Print.h"
#include "Test_Time.h"

uint8 Wdg_Wdg1DemoStatusVar;

void CDD_Wdg_EN(void)
{
	Dio_WriteChannel(DIO_CHANNEL_20_8,1);     //使能看门狗
}

void CDD_Wdg_Dis(void)
{
	Dio_WriteChannel(DIO_CHANNEL_20_8,STD_LOW);     //关闭看门狗
}
