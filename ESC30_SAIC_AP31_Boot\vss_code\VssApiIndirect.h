/***************************************************
 * Introduction: VSS业务接口
 * author: wujialin
 * date created: 2020-2-1
 * date modified: 2021-4-21
 * version: V1.4
 * recently modified by: wu<PERSON><PERSON><PERSON>
 *
 * Copyright (C) 2017, 2021, Thinktech, Inc.
 ***************************************************/
#ifndef __VSS_API_H__
#define __VSS_API_H__

#include <stdio.h>
#include <stdint.h>


/*---functions---*/
#ifdef __cplusplus
extern "C" {
#endif

#include "vsstype.h"


enum ALG_TYPE {
	ALG_GJ = 1,	
	ALG_GM,
};

enum ENV_TYPE {
	ENV_QA = 1,
	ENV_PP,
	ENV_P,
};

enum{
	CERT_TYPE_ROOT = 0,
	CERT_TYPE_USR,
};

#define ROOT_CERT_SIZE  144
#define USER_CERT_SIZE  176
#define ECU_CERT_SIZE   164

enum CERT_TYPE {
  CERTTYPE_ROOT    = 0x10,
  CERTTYPE_ECU     = 0x20,
  CERTTYPE_USR     = 0x30,
  CERTTYPE_USRREQ  = 0x31,
};

#define INNER_KEY_MODE 0
#define OUTER_KEY_MODE 1
#define CALC_ENC 0
#define CALC_DEC 1
#define PAD_NO_FORCE 0
#define PAD_FORCE 1
#define HASH_NO_CALC 0
#define HASH_CALC 1

/* Function Table Base Address */
#define VSSAPI_FUNC_TABLE_BASE          0x80030000

/* Function Index In Func Table */
enum{
	VSSAPI_VssAESCMac = 0,
	VSSAPI_VssAESCMacIndex,
	VSSAPI_VssAESCalc,
	VSSAPI_VssAESCalcIndex,
	VSSAPI_VssAESMac,
	VSSAPI_VssAsymGenKey,
	VSSAPI_VssAsymGenKeyIndex,
	VSSAPI_VssAsymmCalc,
	VSSAPI_VssAsymmCalcIndex,
	VSSAPI_VssCMac,
	VSSAPI_VssCMacIndex,
	VSSAPI_VssCalcEcu,
	VSSAPI_VssCalcFinishHmac,
	VSSAPI_VssCertExport,
	VSSAPI_VssCertImport,
	VSSAPI_VssCertPkEnc,
	VSSAPI_VssChipRead,
	VSSAPI_VssChipWrite,
	VSSAPI_VssCryptoInit,
	VSSAPI_VssECCCalc,
	VSSAPI_VssECCCalcIndex,
	VSSAPI_VssECCGenKey,
	VSSAPI_VssECCGenKeyIndex,
	VSSAPI_VssECCSign,
	VSSAPI_VssECCSignIndex,
	VSSAPI_VssECCVerify,
	VSSAPI_VssExportAtKey,
	VSSAPI_VssExportAtPin,
	VSSAPI_VssExportKey,
	VSSAPI_VssExportSessKey,
	VSSAPI_VssGenCertReq,
	VSSAPI_VssGenRandom,
	VSSAPI_VssGenSessionKey,
	VSSAPI_VssGenerateKeyByCode,
	VSSAPI_VssGetAlgFlag,
	VSSAPI_VssGetCertInfo,
	VSSAPI_VssGetChipID,
	VSSAPI_VssGetWroteFlag,
	VSSAPI_VssHMAC,
	VSSAPI_VssHash,
	VSSAPI_VssHashFinal,
	VSSAPI_VssHashInit,
	VSSAPI_VssHashUpdate,
	VSSAPI_VssImportSessKey,
	VSSAPI_VssKeyCodeFeedback,
	VSSAPI_VssMac,
	VSSAPI_VssResetSessionKey,
	VSSAPI_VssSM2Calc,
	VSSAPI_VssSM2CalcIndex,
	VSSAPI_VssSM2GenKey,
	VSSAPI_VssSM2GenKeyIndex,
	VSSAPI_VssSM2Sign,
	VSSAPI_VssSM2SignIndex,
	VSSAPI_VssSM2Verify,
	VSSAPI_VssSM4CMac,
	VSSAPI_VssSM4CMacIndex,
	VSSAPI_VssSM4Calc,
	VSSAPI_VssSM4CalcIndex,
	VSSAPI_VssSM4Mac,
	VSSAPI_VssSetKeyActive,
	VSSAPI_VssSetWroteFlag,
	VSSAPI_VssSign,
	VSSAPI_VssSignData,
	VSSAPI_VssSignIndex,
	VSSAPI_VssSignVerify,
	VSSAPI_VssStSymmCalc,
	VSSAPI_VssSymmCalc,
	VSSAPI_VssSymmCalcIndex,
	VSSAPI_VssUpdateEnvironment,
	VSSAPI_VssUpdateMasterKey,
	VSSAPI_VssVerifyCert,
	VSSAPI_VssVerifyCertValid,
	VSSAPI_VssVerifyEcuSign,
	VSSAPI_VssVerifyToolCert,
	VSSAPI_VssZucCalc,
	VSSAPI_VssZucCalcData,
	VSSAPI_VssZucSetKey,
	VSSAPI_VssGetChipVersion,
	VSSAPI_VssGetChipState,
	VSSAPI_VssGetVersion,
	VSSAPI_VssVerifySignCertValid,
	VSSAPI_VssGetEnvironment,
	VSSAPI_VssGetKeyActive,

	VSSAPI_FUN_COUNT
};

/******************************************************************************
摘要: 算法库初始化，通过读取FLASH指定的算法来初始化系统算法标识，默认算法为国密算法
   需要通过该方法将FLASH读写的接口指针注册到VSS中，VSS可以通过该接口读写FLASH中指定的密钥存储区域。
   由于非对称算法的运算时间较长（例如完整软算法验签可能需要500ms），这就需要算法内部运算时分段复位WDT的时钟，否则会导致MCU复位。

参数: type[in]    --指定算法库的运算类型：0-软算法；1-M300芯片
	group[in]  --预留参数，椭圆曲率ID：0-NIST prime256v1; 1-brainpoolP256r1
           目前只支持0-NIST prime256v1曲率
     flashCb[in]  --读写FLASH密钥存储区域的回调函数指针，如不需使用，传入空指针即可
     wdtCb[in]   --复位看门狗计时器的回调函数指针，如不需使用，传入空指针即可

返回值：0       --成功
        0x04    --运算类型不是指定的数值
        0x1C    --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssCryptoInit(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb)
{
    vss_uint32 (*api_func_raw)(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCryptoInit));
    return api_func_raw(type, group, flashCb, wdtCb);
}

/******************************************************************************
摘要: 随机生成SM2非对称密钥对并导出接口

参数: szX[out]      --公钥X（定长32字节）
      szY[out]      --公钥Y（定长32字节）
      szSK[out]     --私钥SK（定长32字节）

返回值：0       --成功
        0x04    --传入空指针
        0x08    --计算失败
        0x18    --算法不支持
******************************************************************************/
static inline vss_uint32 VssSM2GenKey(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK)
{
    vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2GenKey));
    return api_func_raw(szX, szY, szSK);
}

/******************************************************************************
摘要: 随机生成SM2非对称密钥对并存储接口

参数: szX[out]      --公钥X（定长32字节）
      szY[out]      --公钥Y（定长32字节）

返回值：0       --成功
        0x04    --szX或szY传入空指针
        0x08    --计算失败
        0x1B    --未调用算法库初始化接口或未指定IO操作回调方法
        0x1D    --写缓存数据失败
******************************************************************************/
static inline vss_uint32 VssSM2GenKeyIndex(vss_uint8 *szX, vss_uint8 *szY)
{
    vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2GenKeyIndex));
    return api_func_raw(szX, szY);
}

/******************************************************************************
摘要: 以输入的车辆安全码作为种子，根据配置文件产生系列的对称密钥

参数: len[in]     --车辆安全码长度，固定32
     code[in]    --车辆安全码，固定32字节，由第32字节指定算法，当该数为偶数时指定为国密算法，为奇数时指定为国际算法（即alg=code[31]&1; 0-国密，1-国际）
     szKeyIdList[out] --生成成功的密钥ID清单，例如“1,2,5”
     AutoSetWroteFlag[in] -设1在功能完成后自动置wrote Flag为1；设0不做任何操作

返回值：0       --成功
        0x04    --传入空指针
        0x12    --安全码长度不为32
        0x1B    --未调用算法库初始化接口或未指定IO操作回调方法
        0x1D    --写缓存数据失败
        0x21    --码单已经被生成过
******************************************************************************/
static inline vss_uint32 VssGenerateKeyByCode(vss_uint32 len, vss_uint8* code, vss_char8* szKeyIdList, vss_uint8 AutoSetWroteFlag)
{
    vss_uint32 (*api_func_raw)(vss_uint32 len, vss_uint8* code, vss_char8* szKeyIdList, vss_uint8 AutoSetWroteFlag) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 len, vss_uint8* code, vss_char8* szKeyIdList, vss_uint8 AutoSetWroteFlag))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGenerateKeyByCode));
    return api_func_raw(len, code, szKeyIdList, AutoSetWroteFlag);
}

/******************************************************************************
摘要: 随机生成ECC非对称密钥对并导出接口

参数: szX[out]      --公钥X（定长32字节）
      szY[out]      --公钥Y（定长32字节）
      szSK[out]     --私钥SK（定长32字节）

返回值：0       --成功
        0x04    --传入空指针
        0x08    --计算失败
        0x18    --算法不支持
******************************************************************************/
static inline vss_uint32 VssECCGenKey(vss_uint8 *szX, vss_uint8 *szY, vss_uint8 *szSK)
{
    vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCGenKey));
    return api_func_raw(szX, szY, szSK);
}


/******************************************************************************
摘要: 随机生成ECC非对称密钥对并存储接口

参数: szX[out]      --公钥X（定长32字节）
      szY[out]      --公钥Y（定长32字节）

返回值：0       --成功
        0x04    --szX或szY传入空指针
        0x08    --计算失败
        0x1B    --未调用算法库初始化接口或未指定IO操作回调方法
        0x1D    --写缓存数据失败
******************************************************************************/
static inline vss_uint32 VssECCGenKeyIndex(vss_uint8 *szX, vss_uint8 *szY)
{
    vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCGenKeyIndex));
    return api_func_raw(szX, szY);
}

/********************************************************************************
摘要: 证书导入

参数:nCertType[in]    --证书类型: 0:根证书;1:身份证书
	nLen[in]         --证书长度
	szCert[in]        --证书数据

返回值：0         --成功
        0x04      --传入空指针或证书类型不为0-1
        0x12      --证书长度非法，根证书144，身份证书176
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1D      --写缓存数据失败
********************************************************************************/
static inline vss_uint32 VssCertImport(vss_uint32 nCertType, vss_uint32 nLen, vss_uint8* szCert)
{
    vss_uint32 (*api_func_raw)(vss_uint32 nCertType, vss_uint32 nLen, vss_uint8* szCert) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 nCertType, vss_uint32 nLen, vss_uint8* szCert))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCertImport));
    return api_func_raw(nCertType, nLen, szCert);
}

/********************************************************************************
摘要: 证书读取
参数: nCertType[in]    --证书类型: 0:根证书;1:身份证书
nCertLen[out]    --导出证书长度
szCert[out]      --导出证书数据
	info[out]         --输出16字节指定信息格式
                     证书类型  4B
                     有效日期  4B 格式为YYMMDD
                     证书序号  4B
                     证书属性  4B [算法，根索引，预留，预留]
                               算法：0-国密；1-国际
                               根索引：0x10-测试国密根
                                       0x11-测试国际根
                                       0x20-预生产国密根
                                       0x21-预生产国际根
                                       0x30-生产国密根
                                       0x31-生产国际根

返回值：0         --成功
        0x04      --传入空指针或证书类型不为0-1
        0x0E      --证书不存在
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
********************************************************************************/
static inline vss_uint32 VssCertExport(vss_uint32 nCertType, vss_uint32* nCertLen, vss_uint8* szCert, vss_uint8* info)
{
    vss_uint32 (*api_func_raw)(vss_uint32 nCertType, vss_uint32* nCertLen, vss_uint8* szCert, vss_uint8* info) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 nCertType, vss_uint32* nCertLen, vss_uint8* szCert, vss_uint8* info))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCertExport));
    return api_func_raw(nCertType, nCertLen, szCert, info);
}

/******************************************************************************
摘要: 导入明文会话密钥

参数:nKeylen[in]  --密钥的长度，定长16
    szKey[in]    --密钥值

返回值：0       --成功
        0x04    --密钥为空指针
        0x19    --密钥长度不为16
        0x1B    --未调用算法库初始化接口或未指定IO操作回调方法
        0x1D    --写缓存数据失败
******************************************************************************/
static inline vss_uint32 VssImportSessKey(vss_uint32 nKeyLen, vss_uint8* szKey)
{
    vss_uint32 (*api_func_raw)(vss_uint32 nKeyLen, vss_uint8* szKey) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 nKeyLen, vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssImportSessKey));
    return api_func_raw(nKeyLen, szKey);
}

/******************************************************************************
摘要: 读取会话密钥明文

参数: index[in]    --密钥存储的索引号，0-当前通信密钥，1-备份通信密钥
    nKeylen[out]  --密钥的长度
    szKey[out]    --密钥值

返回值：0       --成功
        0x04    --密钥长度或密钥值为空指针
        0x05    --指定密钥不存在
        0x0F    --密钥索引号非法，不为0-1
        0x1B    --未调用算法库初始化接口或未指定IO操作回调方法
        0x1C    --读缓存数据失败       
******************************************************************************/
static inline vss_uint32 VssExportSessKey(vss_uint32 index, vss_uint32* nKeyLen,vss_uint8* szKey)
{
    vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint32* nKeyLen,vss_uint8* szKey) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint32* nKeyLen,vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportSessKey));
    return api_func_raw(index, nKeyLen, szKey);
}

/******************************************************************************
摘要: 修改密钥的激活属性

参数: keyId[in]   --密钥ID（只针对5-8号通信密钥有效）
     valid[in]    --可用标识: 0-不可用；1-可用

返回值：0       --成功
        0x04    --可用标识不是指定值
        0x05    --密钥不存在
        0x0F    --密钥ID非法，不为5-8
        0x1B    --未调用算法库初始化接口或未指定IO操作回调方法
        0x1D    --写缓存数据失败
******************************************************************************/
static inline vss_uint32 VssSetKeyActive(vss_uint32 keyId, vss_uint32 valid)
{
    vss_uint32 (*api_func_raw)(vss_uint32 keyId, vss_uint32 valid) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 keyId, vss_uint32 valid))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSetKeyActive));
    return api_func_raw(keyId, valid);
}

/******************************************************************************
摘要: 读取防盗密钥明文，防盗密钥至多仅一条有效，如果存在防盗密钥返回对应密钥，如果不存在返回05--密钥不可用

参数:index[in]	  --密钥索引，0-3
	nKeyLen[out]  --密钥的长度
    szKey[out]    --密钥值

返回值：0        --成功
        0x04     --密钥或密钥长度传入空指针
        0x05     --指定密钥不存在
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssExportAtKey(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey)
{
    vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportAtKey));
    return api_func_raw(index, nKeyLen, szKey);
}

/******************************************************************************
摘要: 读取防盗PIN明文

参数: nPinLen[out]  --PIN的长度
    szPin[out]    --PIN值

返回值：0        --成功
        0x04     --传入空指针
        0x05     --指定密钥不存在
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssExportAtPin(vss_uint32* nPinLen, vss_uint8* szPin)
{
    vss_uint32 (*api_func_raw)(vss_uint32* nPinLen, vss_uint8* szPin) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32* nPinLen, vss_uint8* szPin))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportAtPin));
    return api_func_raw(nPinLen, szPin);
}

/******************************************************************************
摘要: 反馈车辆安全码输入情况

参数: szFeedback[out]    --车辆安全码反馈值，定长32字节
                        如果32字节车辆安全码没给安全算法包输入过，该接口吐出值为全FF
如果32字节车辆安全码已经输入安全算法包，内容如下：
国密算法：该接口吐出值为全00
国际算法：该接口吐出值为前31字节全00，最后一字节0x01

返回值：0        --成功
        0x04     --传入空指针
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssKeyCodeFeedback(vss_uint8* szFeedback)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szFeedback) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szFeedback))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssKeyCodeFeedback));
    return api_func_raw(szFeedback);
}

/******************************************************************************
摘要: 根据输入的码单的最后一位来判断国密/国际算法标识，本接口返回算法标识
      如果车辆安全码未输入，则返回”2-国密算法”

参数: nAlgFlag[out]    --算法标识: 1-国际算法；2-国密算法
                     
返回值：0        --成功
        0x04     --传入空指针
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssGetAlgFlag(vss_uint32* nAlgFlag)
{
    vss_uint32 (*api_func_raw)(vss_uint32* nAlgFlag) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32* nAlgFlag))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetAlgFlag));
    return api_func_raw(nAlgFlag);
}

/******************************************************************************
摘要: ECC非对称加解密接口

参数: szInData[in]      --输入数据
      nInLen[in]       --输入数据长度，加密最大64字节， 解密固定192字节
      calcFlag[in]      --运算标识: 0-加密；1-解密  
      szKey[in]        --运算密钥：如果是加密运算，该值为加密X||加密Y;
	      	  	  	  	  	 如果是解密运算，该值为解密SK
      nKeyLen[in]       --运算密钥长度，加密为64，解密为32
      szOutData[out]    --输出运算结果
      pOutLen[out]      --输出结果长度

返回值：0         --成功
        0x04      --传入空指针
        0x08      --计算错误
        0x12      --数据长度非法
        0x18      --算法不支持
        0x19      --密钥长度非法
        0x1B      --未调用算法库初始化接口
******************************************************************************/
static inline vss_uint32 VssECCCalc(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCCalc));
    return api_func_raw(szInData, nInLen, calcFlag, szKey, nKeyLen, szOutData, pOutLen);
}

/******************************************************************************
摘要: ECC非对称加解密接口

参数: szInData[in]      --输入数据
      nInLen[in]       --输入数据长度，加密最大64字节， 解密固定192字节
      calcFlag[in]      --运算标识: 0-加密；1-解密  
      szOutData[out]    --输出运算结果
      pOutLen[out]      --输出结果长度

返回值：0         --成功
        0x04      --传入空指针
        0x05      --密钥不存在
        0x08      --计算错误
        0x0B      --运算标识不为指定值
        0x12      --数据长度非法
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssECCCalcIndex(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCCalcIndex));
    return api_func_raw(szInData, nInLen, calcFlag, szOutData, pOutLen);
}

/******************************************************************************
摘要: ECC数据签名

参数: key[in]              --签名私钥，定长32字节
     data[in]             --运算数据
     dataLen[in]          --运算数据长度，不哈希直接签名时指定32，哈希签名时数据长度大于0
hashFlag[in]          --哈希标识: 0-不哈希直接签名; 1-先SHA256再签名
     sig[out]             --签名结果，定长64字节

返回值：0         --成功
        0x04      --传入空指针或哈希标识非法
        0x08      --计算错误
        0x12      --数据长度非法
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssECCSign(vss_uint8* key, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* key, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* key, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCSign));
    return api_func_raw(key, data, dataLen, hashFlag, sig);
}

/******************************************************************************
摘要: ECC数据签名

参数: data[in]             --运算数据
     dataLen[in]          --运算数据长度，不哈希直接签名时指定32，哈希签名时数据长度大于0
     hashFlag[in]         --哈希标识: 0-不哈希直接签名; 1-先SHA256再签名
     sig[out]             --签名结果，定长64字节

返回值：0         --成功
        0x04      --传入空指针或哈希标识非法
        0x05      --密钥不存在
        0x08      --计算错误
        0x18      --算法不支持
        0x12      --数据长度非法
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssECCSignIndex(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCSignIndex));
    return api_func_raw(data, dataLen, hashFlag, sig);
}

/******************************************************************************
摘要: ECC验证签名

参数: pk[in]              --验证签名公钥，定长64字节
     data[in]             --运算数据
     dataLen[in]          --运算数据长度，不哈希直接签名时指定32，哈希签名时数据长度大于0
     hashFlag[in]         --哈希标识: 0-不哈希直接签名; 1-先SHA256再签名
     sig[in]              --签名结果，定长64字节

返回值：0         --成功
        0x04      --传入空指针或哈希标识非法
        0x08      --验签失败
        0x12      --数据长度非法
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssECCVerify(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCVerify));
    return api_func_raw(pk, data, dataLen, hashFlag, sig);
}

/******************************************************************************
摘要: AES对称运算

参数: in[in]         --待运算数据
     inLen[in]      --待运算数据长度
     key[in]        --密钥
      keyLen[in]    --密钥长度，定长16
      calcFlag[in]   --加解密标识：0-加密；1-解密
      padding[in]   --补位标识：0-不强制补位；1-强制补位
      out[out]      --加密后数据
      pOutLen[out]  --加密后数据长度

返回值：0         --成功
        0x04      --传入空指针或加解密标识、补位标识非法
        0x19      --密钥长度非法
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssAESCalc(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAESCalc));
    return api_func_raw(in, inLen, key, keyLen, calcFlag, padding, out, pOutLen);
}

/******************************************************************************
摘要: AES对称运算

参数: index[in]     --密钥索引，指定0-13
in[in]         --待运算数据
      inLen[in]      --待运算数据长度
      calcFlag[in]   --加解密标识：0-加密；1-解密
      padding[in]   --补位标识：0-不强制补位；1-强制补位
      out[out]      --加密后数据
      pOutLen[out]  --加密后数据长度

返回值：0         --成功
        0x04      --传入空指针或加解密标识、补位标识非法
        0x05      --密钥不存在
        0x0F      --密钥索引非法，不为0-13
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssAESCalcIndex(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAESCalcIndex));
    return api_func_raw(index, in, inLen, calcFlag, padding, out, pOutLen);
}


/******************************************************************************
摘要: AES计算MAC

参数: in[in]         --待运算数据
      inLen[in]      --待运算数据长度
      key[in]      --密钥
      keyLen[in]   --密钥长度，定长16
      out[out]      --mac结果，定长16字节

返回值：0         --成功
        0x04      --传入空指针
        0x18      --算法不支持
        0x19      --密钥长度不为16
******************************************************************************/
static inline vss_uint32 VssAESMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAESMac));
    return api_func_raw(in, inLen, key, keyLen, out);
}

/******************************************************************************
摘要: AES计算CMAC

参数: in[in]         --待运算数据
      inLen[in]      --待运算数据长度
      key[in]      --密钥
      keyLen[in]   --密钥长度，定长16
      out[out]      --mac结果，定长16字节

返回值：0         --成功
        0x04      --传入空指针
        0x18      --算法不支持
        0x19      --密钥长度不为16
******************************************************************************/
static inline vss_uint32 VssAESCMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAESCMac));
    return api_func_raw(in, inLen, key, keyLen, out);
}

/******************************************************************************
摘要: AES计算CMAC，使用内部密钥

参数: in[in]         --待运算数据
     inLen[in]      --待运算数据长度
     index[in]      --密钥索引，指定0-13
      out[out]      --mac结果，定长16字节

返回值：0         --成功
        0x04      --传入空指针
        0x05      --指定密钥不存在
        0x0F      --密钥索引非法，不为0-13
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssAESCMacIndex(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAESCMacIndex));
    return api_func_raw(in, inLen, index, out);
}

/******************************************************************************
摘要: ZUC密钥初始化，适用于运算数据定长8字节

参数: key[in]	     --密钥，定长16字节
     zuc_key[out]	--ZUC运算时密钥

返回值：0         --成功
        0x04      --传入空指针
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssZucSetKey(vss_uint8* key, TZucKey* zuc_key)
{
    vss_uint32 (*api_func_raw)(vss_uint8* key, TZucKey* zuc_key) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* key, TZucKey* zuc_key))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssZucSetKey));
    return api_func_raw(key, zuc_key);
}

/******************************************************************************
摘要: ZUC运算，该接口适用于定长8字节数据

参数: zuc_key[in]	--转码后的密钥
in[in]	     --待运算数据，定长8字节     
      out[out]	     --结果数据，定长8字节

返回值：0         --成功
        0x04      --传入空指针
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssZucCalc(TZucKey* zuc_key, vss_uint8* in, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(TZucKey* zuc_key, vss_uint8* in, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(TZucKey* zuc_key, vss_uint8* in, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssZucCalc));
    return api_func_raw(zuc_key, in, out);
}

/******************************************************************************
摘要: SM4对称运算

参数: in[in]         --待运算数据
      inLen[in]      --待运算数据长度
      key[in]       --密钥
      keyLen[in]    --密钥长度，定长16
      calcFlag[in]   --加解密标识：0-加密；1-解密
      padding[in]   --补位标识：0-不强制补位；1-强制补位
      out[out]      --加密后数据
      pOutLen[out]  --加密后数据长度

返回值：0         --成功
        0x04      --传入空指针或加解密标识、补位标识非法
        0x19      --密钥长度非法
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssSM4Calc(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM4Calc));
    return api_func_raw(in, inLen, key, keyLen, calcFlag, padding, out, pOutLen);
}

/******************************************************************************
摘要: SM4对称运算

参数: index[in]     --密钥索引，指定0-13
in[in]         --待运算数据
      inLen[in]      --待运算数据长度
      calcFlag[in]   --加解密标识：0-加密；1-解密
      padding[in]   --补位标识：0-不强制补位；1-强制补位
      out[out]      --加密后数据
      pOutLen[out]  --加密后数据长度

返回值：0         --成功
        0x04      --传入空指针或加解密标识、补位标识非法
        0x05      --密钥不存在
        0x0F      --密钥索引非法，不为0-13
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssSM4CalcIndex(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM4CalcIndex));
    return api_func_raw(index, in, inLen, calcFlag, padding, out, pOutLen);
}

/******************************************************************************
摘要: ZUC对称运算

参数: in[in]         --待运算数据
      inLen[in]      --待运算数据长度，大于0且为8的整数倍
      key[in]      --密钥，定长16字节
      out[out]      --运算后数据，长度=输入数据长度

返回值：0         --成功
        0x04      --传入空指针
        0x12      --数据长度非法
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssZucCalcData(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssZucCalcData));
    return api_func_raw(in, inLen, key, out);
}

/******************************************************************************
摘要: SM4计算MAC

参数: in[in]         --待运算数据
      inLen[in]      --待运算数据长度
      key[in]      --密钥
      keyLen[in]   --密钥长度，定长16
      out[out]      --mac结果，定长16字节

返回值：0         --成功
        0x04      --传入空指针
        0x18      --算法不支持
        0x19      --密钥长度不为16
******************************************************************************/
static inline vss_uint32 VssSM4Mac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint32 keyLen, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint32 keyLen, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM4Mac));
    return api_func_raw(in, inLen, key, keyLen, out);
}

/******************************************************************************
摘要: SM4计算CMAC

参数: in[in]         --待运算数据
      inLen[in]      --待运算数据长度
      key[in]      --密钥
      keyLen[in]   --密钥长度，定长16
      out[out]      --mac结果，定长16字节

返回值：0         --成功
        0x04      --传入空指针
        0x18      --算法不支持
        0x19      --密钥长度不为16
******************************************************************************/
static inline vss_uint32 VssSM4CMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint32 keyLen, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint32 keyLen, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM4CMac));
    return api_func_raw(in, inLen, key, keyLen, out);
}

/******************************************************************************
摘要: SM4计算CMAC，使用内部密钥

参数: in[in]         --待运算数据
     inLen[in]      --待运算数据长度
     index[in]      --密钥索引，指定0-13
      out[out]      --mac结果，定长16字节

返回值：0         --成功
        0x04      --传入空指针
        0x05      --指定密钥不存在
        0x0F      --密钥索引非法，不为0-13
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssSM4CMacIndex(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM4CMacIndex));
    return api_func_raw(in, inLen, index, out);
}

/******************************************************************************
摘要: 产生随机数

参数: len[in]        --随机数长度
      out[out]      --随机数缓存

返回值：0         --成功
        0x04      --传入空指针
        0x12      --指定长度等于0
******************************************************************************/
static inline vss_uint32 VssGenRandom(vss_uint32 len, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint32 len, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 len, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGenRandom));
    return api_func_raw(len, out);
}

/******************************************************************************
摘要: HASH计算，通过算法库初始化接口自适应算法

参数: data[in]             --运算数据
      dataLen[in]          --运算数据长度
      out[out]             --HASH结果
      pOutLen[out]       --结果长度，定长32字节

返回值：0         --成功
        0x04      --传入空指针
        0x12      --指定长度等于0
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssHash(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssHash));
    return api_func_raw(data, dataLen, out, pOutLen);
}

/******************************************************************************
摘要: HASH计算初始化，通过算法库初始化接口自适应算法

参数: ctx[in]    --上下文环境

返回值：0         --成功
        0x04      --传入空指针
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssHashInit(THashCtx* ctx)
{
    vss_uint32 (*api_func_raw)(THashCtx* ctx) = 0;

    api_func_raw = (vss_uint32 (*)(THashCtx* ctx))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssHashInit));
    return api_func_raw(ctx);
}

/******************************************************************************
摘要: HASH计算数据更新，通过算法库初始化接口自适应算法

参数:  ctx[in]              --上下文环境
      data[in]             --运算数据
      dataLen[in]          --运算数据长度

返回值：0         --成功
        0x04      --传入空指针
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssHashUpdate(THashCtx* ctx, vss_uint8* data, vss_uint32 dataLen)
{
    vss_uint32 (*api_func_raw)(THashCtx* ctx, vss_uint8* data, vss_uint32 dataLen) = 0;

    api_func_raw = (vss_uint32 (*)(THashCtx* ctx, vss_uint8* data, vss_uint32 dataLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssHashUpdate));
    return api_func_raw(ctx, data, dataLen);
}

/******************************************************************************
摘要: HASH计算结束，通过算法库初始化接口自适应算法

参数: ctx[in]               --上下文环境
      out[out]             --HASH结果
      pOutLen[out]        --结果长度

返回值：0         --成功
        0x04      --传入空指针
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssHashFinal(THashCtx* ctx, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(THashCtx* ctx, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(THashCtx* ctx, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssHashFinal));
    return api_func_raw(ctx, out, pOutLen);
}

/******************************************************************************
摘要: HMAC计算，通过算法库初始化接口自适应算法

参数: key[in]             --密钥
     keyLen[in]          --密钥长度
     data[in]             --运算数据
     dataLen[in]          --运算数据长度
     out[out]             --HMAC结果
      pOutLen[out]       --结果长度

返回值：0         --成功
        0x04      --传入空指针
        0x12      --指定长度等于0
        0x18      --算法不支持
        0x19      --密钥长度为0
******************************************************************************/
static inline vss_uint32 VssHMAC(vss_uint8* key, vss_uint32 keyLen, vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* key, vss_uint32 keyLen, vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* key, vss_uint32 keyLen, vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssHMAC));
    return api_func_raw(key, keyLen, data, dataLen, out, pOutLen);
}

/******************************************************************************
摘要: SM2非对称加解密接口

参数: szInData[in]      --输入数据
      nInLen[in]        --输入数据长度，加密时1-64字节， 解密时97-160字节
      calcFlag[in]      --运算标识: 0-加密；1-解密  
      szKey[in]         --运算密钥：如果是加密运算，该值为加密X||加密Y;
	      	  	  	  	  	 如果是解密运算，该值为解密SK
      nKeyLen[in]       --运算密钥长度，加密为64，解密为32
      szOutData[out]    --输出运算结果
      pOutLen[out]      --输出结果长度

返回值：0         --成功
        0x04      --传入空指针
        0x08      --计算错误
        0x12      --数据长度非法
        0x18      --算法不支持
        0x19      --密钥长度非法
        0x1B      --未调用算法库初始化接口
******************************************************************************/
static inline vss_uint32 VssSM2Calc(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2Calc));
    return api_func_raw(szInData, nInLen, calcFlag, szKey, nKeyLen, szOutData, pOutLen);
}

/******************************************************************************
摘要: SM2非对称加解密接口

参数: szInData[in]      --输入数据
      nInLen[in]        --输入数据长度，加密时1-64字节， 解密时97-160字节
      calcFlag[in]       --运算标识: 0-加密；1-解密  
      szOutData[out]    --输出运算结果
      pOutLen[out]      --输出结果长度

返回值：0         --成功
        0x04      --传入空指针
        0x05      --密钥不存在
        0x08      --计算错误
        0x0B      --运算标识不为指定值
        0x12      --数据长度非法
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssSM2CalcIndex(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2CalcIndex));
    return api_func_raw(szInData, nInLen, calcFlag, szOutData, pOutLen);
}

/******************************************************************************
摘要: SM2数据签名

参数: sk[in]              --签名私钥，定长32字节
     data[in]             --运算数据
     dataLen[in]          --运算数据长度，不哈希直接签名时指定32，哈希签名时数据长度大于0
     hashFlag[in]         --哈希标识: 0-不哈希直接签名; 1-先SM3再签名
     sig[out]             --签名结果，定长64字节

返回值：0         --成功
        0x04      --传入空指针或哈希标识非法
        0x08      --计算错误
        0x12      --数据长度非法
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssSM2Sign(vss_uint8* sk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* sk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* sk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2Sign));
    return api_func_raw(sk, data, dataLen, hashFlag, sig);
}

/******************************************************************************
摘要: SM2数据签名

参数: data[in]             --运算数据
     dataLen[in]          --运算数据长度，不哈希直接签名时指定32，哈希签名时数据长度大于0
     hashFlag[in]         --哈希标识: 0-不哈希直接签名; 1-先SM3再签名
     sig[out]             --签名结果，定长64字节

返回值：0         --成功
        0x04      --传入空指针或哈希标识非法
        0x05      --密钥不存在
        0x08      --计算错误
        0x12      --数据长度非法
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssSM2SignIndex(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2SignIndex));
    return api_func_raw(data, dataLen, hashFlag, sig);
}

/******************************************************************************
摘要: SM2验证签名

参数: pk[in]              --验证签名公钥，定长64字节
     data[in]             --运算数据
     dataLen[in]          --运算数据长度，不哈希直接签名时指定32，哈希签名时数据长度大于0
     hashFlag[in]         --哈希标识: 0-不哈希直接签名; 1-先SM3再签名
     sig[in]              --签名结果，定长64字节

返回值：0         --成功
        0x04      --传入空指针或哈希标识非法
        0x08      --验签失败
        0x12      --数据长度非法
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssSM2Verify(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2Verify));
    return api_func_raw(pk, data, dataLen, hashFlag, sig);
}

/******************************************************************************
摘要: 读取证书信息，支持根证书和身份证书读取信息

参数: cert[in]          --证书数据
     certLen[in]       --证书长度
	info[out]         --输出16字节指定信息格式
                     证书类型  4B
                     有效日期  4B 格式为YYMMDD
                     证书序号  4B
                     证书属性  4B [算法，根索引，预留，预留]
                               算法：0-国密；1-国际
                               根索引：0x10-测试国密根
                                       0x11-测试国际根
                                       0x20-预生产国密根
                                       0x21-预生产国际根
                                       0x30-生产国密根
                                       0x31-生产国际根

返回值：0         --成功
        0x04      --传入空指针
        0x12      --证书长度非法
	 0x1A      --证书格式非法
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口
******************************************************************************/
static inline vss_uint32 VssGetCertInfo(vss_uint8* cert, vss_uint32 certLen, vss_uint8* info)
{
    vss_uint32 (*api_func_raw)(vss_uint8* cert, vss_uint32 certLen, vss_uint8* info) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* cert, vss_uint32 certLen, vss_uint8* info))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetCertInfo));
    return api_func_raw(cert, certLen, info);
}

/******************************************************************************
摘要: 证书公钥加密

参数: in[in]         --待加密数据
      inLen[in]      --待加密数据长度， 最大64字节
      cert[in]       --证书
      certLen[in]    --证书长度
      out[out]       --加密后数据
      pOutLen[out]  --加密后数据长度

返回值：0         --成功
        0x04      --传入空指针
        0x08      --计算错误
        0x0B      --不是正确的证书数据
	 0x12      --待加密数据长度超过64
	 0x1A      --证书格式非法
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口
******************************************************************************/
static inline vss_uint32 VssCertPkEnc(vss_uint8* in, vss_uint32 inLen, vss_uint8 *cert, vss_uint32 certLen, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *cert, vss_uint32 certLen, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *cert, vss_uint32 certLen, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCertPkEnc));
    return api_func_raw(in, inLen, cert, certLen, out, pOutLen);
}

/******************************************************************************
摘要: 证书合法性验证

参数: cert[in]              --待验证证书
      len[in]              --待验证证书长度

返回值：0         --成功
        0x04      --传入空指针
        0x08      --证书签名校验失败
	 0x1A      --证书格式非法
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssVerifyCert(vss_uint8* cert, vss_uint32 len)
{
    vss_uint32 (*api_func_raw)(vss_uint8* cert, vss_uint32 len) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* cert, vss_uint32 len))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssVerifyCert));
    return api_func_raw(cert, len);
}

/******************************************************************************
摘要: 验证证书有效期

参数: cert[in]              --待验证证书
      len[in]              --待验证证书长度
      szDataNow[in]       --当前日期”YYYYMMDD”格式的字符串

返回值：0         --证书校验通过，有效期在当前日期之前，已过期
        1         --证书校验通过，且未过期
        0x04      --传入空指针
        0x08      --证书签名校验失败
	 0x1A      --证书格式非法
        0x18      --算法不支持
        0x1A      --证书过期
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssVerifyCertValid(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow)
{
    vss_uint32 (*api_func_raw)(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssVerifyCertValid));
    return api_func_raw(cert, len, szDataNow);
}

/******************************************************************************
摘要: 验证诊断证书有效期

参数: cert[in]              --待验证证书
      len[in]              --待验证证书长度
      szDataNow[in]       --当前日期”YYYYMMDD”格式的字符串

返回值：0         --证书校验通过，有效期在当前日期之前，已过期
        1         --证书校验通过，且未过期
        0x04      --传入空指针
        0x08      --证书签名校验失败
	 0x1A      --证书格式非法
        0x18      --算法不支持
        0x1A      --证书过期
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssVerifyToolCert(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow)
{
    vss_uint32 (*api_func_raw)(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssVerifyToolCert));
    return api_func_raw(cert, len, szDataNow);
}

/******************************************************************************
摘要: 产生会话密钥并存储

参数: M1[in]               --共享密钥，定长32字节
     r1[in]                --客户端随机数，定长32字节
     r2[in]                --服务器随机数，定长32字节

返回值：0         --成功
        0x04      --传入空指针
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法  
        0x1D      --写缓存数据失败
******************************************************************************/
static inline vss_uint32 VssGenSessionKey(vss_uint8* M1, vss_uint8* r1, vss_uint8* r2)
{
    vss_uint32 (*api_func_raw)(vss_uint8* M1, vss_uint8* r1, vss_uint8* r2) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* M1, vss_uint8* r1, vss_uint8* r2))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGenSessionKey));
    return api_func_raw(M1, r1, r2);
}

/******************************************************************************
摘要: 重置会话密钥

参数: 无

返回值：0         --成功
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法  
        0x1D      --写缓存数据失败
******************************************************************************/
static inline vss_uint32 VssResetSessionKey(void)
{
    vss_uint32 (*api_func_raw)() = 0;

    api_func_raw = (vss_uint32 (*)())(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssResetSessionKey));
    return api_func_raw();
}

/******************************************************************************
摘要: 产生证书请求，算法由系统算法标识指定

参数: ecuType[in]            --ecu类型，1字节
VIN [in]                  --车辆识别号，17字节
     info[in]                   --证书识别信息，16字节
     pk[out]                  --密钥对，64字节（X+Y）
     sig[out]                 --申请证书用签名，64字节

返回值：0         --成功
        0x04      --传入空指针
        0x08      --计算失败
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
        0x1D      --写缓存数据失败
******************************************************************************/
static inline vss_uint32 VssGenCertReq(vss_uint8 ecuType, vss_char8* VIN, vss_char8* info, vss_uint8* pk, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8 ecuType, vss_char8* VIN, vss_char8* info, vss_uint8* pk, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 ecuType, vss_char8* VIN, vss_char8* info, vss_uint8* pk, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGenCertReq));
    return api_func_raw(ecuType, VIN, info, pk, sig);
}

/******************************************************************************
摘要: 安全通道-对称加解密运算，具体算法根据CryptoInit的alg参数指定

参数: in[in]         --待运算数据
      inLen[in]      --待运算数据长度
      calcFlag[in]   --加解密标识：0-加密；1-解密
      padding[in]   --补位标识：0-不强制补位；1-强制补位
      lastKey[in]    --是否使用备份的密钥：0-使用当前密钥；1-使用备份密钥
      out[out]      --加密后数据
      pOutLen[out]  --加密后数据长度

返回值：0         --成功
        0x04      --传入空指针或者加解密标识、补位标识、备份密钥标识值非法
        0x05      --指定密钥不存在
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssStSymmCalc(vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint32 lastKey, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint32 lastKey, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint32 lastKey, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssStSymmCalc));
    return api_func_raw(in, inLen, calcFlag, padding, lastKey, out, pOutLen);
}

/******************************************************************************
摘要: 数据签名

参数: data[in]             --运算数据
     dataLen[in]          --运算数据长度
     out[out]             --签名结果
     pOutLen[out]        --结果长度

返回值：0         --成功
        0x04      --传入空指针
        0x06      --未生成密钥对
        0x08      --签名计算错误
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssSignData(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSignData));
    return api_func_raw(data, dataLen, out, pOutLen);
}

/********************************************************************************
摘要: 计算安全握手校验值
参数: R1[in]            --R1数据，33字节
     R2[in]            --R2数据，33字节
     M1[in]           --M1数据，32字节
     S1[in]            --S1数据，64字节
     E1[in]            --E1数据，M1的非对称加密数据
     E1Len[in]        --E1数据长度
     KeyWord[in]      --计算F1用”SERVER”；计算F2用”CLIENT”
     siteCert[in]        --服务器证书数据，证书定长176字节
     siteLen[in]         --服务器证书长度，应为176
     vehicleCert[in]     --车辆证书数据，证书定长176字节
     vehicleLen[in]     --服务器证书长度，应为176
	hmac[out]        --输出F1或F2的数据，定长32字节

返回值：0         --成功
        0x04      --传入空指针
        0x0B      --keyWord未传入指定值
        0x12      --证书长度不是176
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口
********************************************************************************/
static inline vss_uint32 VssCalcFinishHmac(vss_uint8* R1, vss_uint8* R2, vss_uint8* M1, vss_uint8* S1,
vss_uint8* E1, vss_uint32 E1Len, vss_char8* KeyWord , vss_uint8* siteCert, vss_uint32 siteLen,
vss_uint8* vehicleCert, vss_uint32 vehicleLen, vss_uint8* hmac)
{
    vss_uint32 (*api_func_raw)(vss_uint8* R1, vss_uint8* R2, vss_uint8* M1, vss_uint8* S1,
													vss_uint8* E1, vss_uint32 E1Len, vss_char8* KeyWord , vss_uint8* siteCert, vss_uint32 siteLen,
													vss_uint8* vehicleCert, vss_uint32 vehicleLen, vss_uint8* hmac) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* R1, vss_uint8* R2, vss_uint8* M1, vss_uint8* S1,
															vss_uint8* E1, vss_uint32 E1Len, vss_char8* KeyWord , vss_uint8* siteCert, vss_uint32 siteLen,
															vss_uint8* vehicleCert, vss_uint32 vehicleLen, vss_uint8* hmac))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCalcFinishHmac));
    return api_func_raw(R1, R2, M1, S1, E1, E1Len, KeyWord , siteCert, siteLen, vehicleCert, vehicleLen, hmac);
}

/******************************************************************************
摘要: 固件文件签名验证

参数: szEcuFileName [in]   --固件文件路径名称

返回值：0         --成功
        0x04      --传入空指针
        0x08      --验签失败
        0x0B      --文件不存在或无法读取数据
	 0x1A      --证书格式非法
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssVerifyEcuSign(vss_char8* szEcuFileName)
{
    vss_uint32 (*api_func_raw)(vss_char8* szEcuFileName) = 0;

    api_func_raw = (vss_uint32 (*)(vss_char8* szEcuFileName))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssVerifyEcuSign));
    return api_func_raw(szEcuFileName);
}

/******************************************************************************
摘要: 固件文件加解密

参数: szSrcEcuFile [in]   --源固件原文件路径名称
     szDestEcuFile [in]  --目标固件文件路径名称
     calcFlag[in]       --运算标识: 0-加密；1-解密
     key[in]           --运算密钥明文，定长16字节
     srcOffset[in]	   --源固件原文件数据偏移量

返回值：0         --成功
        0x04      --传入空指针或运算标识非法
        0x0B      --文件不存在或无法读取数据或数据偏移量越界
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口
******************************************************************************/
static inline vss_uint32 VssCalcEcu(vss_char8* szSrcEcuFile, vss_char8* szDestEcuFile, vss_uint32 calcFlag, vss_uint8* key,  vss_uint32 srcOffset)
{
    vss_uint32 (*api_func_raw)(vss_char8* szSrcEcuFile, vss_char8* szDestEcuFile, vss_uint32 calcFlag, vss_uint8* key,  vss_uint32 srcOffset) = 0;

    api_func_raw = (vss_uint32 (*)(vss_char8* szSrcEcuFile, vss_char8* szDestEcuFile, vss_uint32 calcFlag, vss_uint8* key,  vss_uint32 srcOffset))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCalcEcu));
    return api_func_raw(szSrcEcuFile, szDestEcuFile, calcFlag, key, srcOffset);
}

/********************************************************************************
摘要: 获取芯片号
参数: szKeyX[out]      --芯片的公钥X，定长32字节
	szKeyY[out]      --芯片的公钥Y，定长32字节
	szChipCode[out]  --定长12字节

返回值：0         --成功
        0x06      --密钥不合法
        0x10      --读取OTP失败
        0xE1      --参数为空
        0xE4      --通信失败
        0xE5      --消息校验失败
        0xE6      --消息体长度错误
********************************************************************************/
static inline vss_uint32 VssGetChipID(vss_uint8 * szKeyX, vss_uint8 * szKeyY, vss_uint8* szChipCode)
{
    vss_uint32 (*api_func_raw)(vss_uint8 * szKeyX, vss_uint8 * szKeyY, vss_uint8* szChipCode) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 * szKeyX, vss_uint8 * szKeyY, vss_uint8* szChipCode))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetChipID));
    return api_func_raw(szKeyX, szKeyY, szChipCode);
}

/********************************************************************************
摘要: 更新芯片主控密钥KMC
参数:szMkey[in]   --新的KMC密钥密文，定长16字节
	szMac[in]    --密钥校验MAC，定长8字节
	szCV[in]     --新的KMC密钥校验值，定长3字节

返回值：0         --成功
        0x06      --密钥不合法
        0x08      --计算失败
        0x09      --FLASH校验失败
        0x0B      --数据不合法        
        0xE1      --参数为空
        0xE4      --通信失败
        0xE5      --消息校验失败
        0xE6      --消息体长度错误
********************************************************************************/
static inline vss_uint32 VssUpdateMasterKey(vss_uint8* szMkey, vss_uint8* szMac, vss_uint8* szCV)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szMkey, vss_uint8* szMac, vss_uint8* szCV) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szMkey, vss_uint8* szMac, vss_uint8* szCV))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssUpdateMasterKey));
    return api_func_raw(szMkey, szMac, szCV);
}

/********************************************************************************
摘要: 写入数据，芯片内有效存储空间为0-600，000，非芯片环境由各控制器的flash_io_cb实现中控制大小
参数: offset[in]       --地址偏移量
	dataLen[in]     --数据长度
	data[in]        --待写入数据

返回值：0         --成功
        0x04      --传入空指针
        0x09      --FLASH校验失败
        0x0B      --数据不合法，offset和dataLen不在有效地址范围内 
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0xE1      --参数为空
        0xE4      --通信失败
        0xE5      --消息校验失败
        0xE6      --消息体长度错误
********************************************************************************/
static inline vss_uint32 VssChipWrite(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data)
{
    vss_uint32 (*api_func_raw)(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssChipWrite));
    return api_func_raw(offset, dataLen, data);
}

/********************************************************************************
摘要: 读取数据，芯片内有效存储空间为0-600，000，非芯片环境由各控制器的flash_io_cb实现中控制大小

参数: offset[in]       --地址偏移量
	dataLen[in]     --数据长度
	data[out]        --输出数据
返回值：0         --成功
        0x04      --传入空指针
        0x09      --FLASH校验失败
        0x0B      --数据不合法，offset和dataLen不在有效地址范围内
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法        
        0xE1      --参数为空
        0xE4      --通信失败
        0xE5      --消息校验失败
        0xE6      --消息体长度错误
********************************************************************************/
static inline vss_uint32 VssChipRead(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data)
{
    vss_uint32 (*api_func_raw)(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssChipRead));
    return api_func_raw(offset, dataLen, data);
}

/******************************************************************************
摘要:非对称加解密接口

参数: szInData[in]      --输入数据
      nInLen[in]       --输入数据长度
      calcFlag[in]      --运算标识: 0-加密；1-解密  
      szKey[in]        --运算密钥：如果是加密运算，该值为加密X||加密Y;
	      	  	  	  	  	 如果是解密运算，该值为解密SK
      nKeyLen[in]       --运算密钥长度，加密为64，解密为32
      szOutData[out]    --输出运算结果
      pOutLen[out]      --输出结果长度

返回值：0         --成功
        0x04      --传入空指针或加解密标识非法
        0x08      --计算错误
        0x12      --数据长度非法
        0x18      --算法不支持
        0x19      --密钥长度非法
        0x1B      --未调用算法库初始化接口
******************************************************************************/
static inline vss_uint32 VssAsymmCalc(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAsymmCalc));
    return api_func_raw(szInData, nInLen, calcFlag, szKey, nKeyLen, szOutData, pOutLen);
}

/******************************************************************************
摘要:非对称加解密接口

参数: szInData[in]      --输入数据
      nInLen[in]       --输入数据长度
      calcFlag[in]      --运算标识: 0-加密；1-解密  
      szOutData[out]    --输出运算结果
      pOutLen[out]      --输出结果长度

返回值：0         --成功
        0x04      --传入空指针或加解密标识非法
        0x05      --密钥不存在
        0x08      --计算错误
        0x12      --数据长度非法
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssAsymmCalcIndex(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAsymmCalcIndex));
    return api_func_raw(szInData, nInLen, calcFlag, szOutData, pOutLen);
}

/******************************************************************************
摘要: 数据签名

参数:  key[in]              --私钥   
      keyLen[in]          --签名密钥长度，定长32字节
data[in]             --运算数据
     dataLen[in]          --运算数据长度，不哈希直接签名时指定32，哈希签名时数据长度大于0
hashFlag[in]          --哈希标识: 0-不哈希直接签名; 1-先哈希再签名
      sig[out]             --签名结果，定长64字节

返回值：0         --成功
        0x04      --传入空指针或哈希标识非法
        0x08      --计算错误
        0x12      --数据长度非法
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssSign(vss_uint8* key, vss_uint32 keyLen,vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* key, vss_uint32 keyLen,vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* key, vss_uint32 keyLen,vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSign));
    return api_func_raw(key, keyLen, data, dataLen, hashFlag, sig);
}

/******************************************************************************
摘要: 数据签名（内部密钥）

参数: data[in]             --运算数据
     dataLen[in]          --运算数据长度，不哈希直接签名时指定32，哈希签名时数据长度大于0
     hashFlag[in]         --哈希标识: 0-不哈希直接签名; 1-先哈希再签名
     sig[out]             --签名结果，定长64字节

返回值：0         --成功
        0x04      --传入空指针或哈希标识非法
        0x05      --密钥不存在
        0x08      --计算错误
        0x12      --数据长度非法
        0x18      --算法不支持
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssSignIndex(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSignIndex));
    return api_func_raw(data, dataLen, hashFlag, sig);
}

/******************************************************************************
摘要: 验证签名

参数: pk[in]              --验证签名公钥，定长64字节
     data[in]             --运算数据
     dataLen[in]          --运算数据长度，不哈希直接签名时指定32，哈希签名时数据长度大于0
     hashFlag[in]         --哈希标识: 0-不哈希直接签名; 1-先哈希再签名
     sig[in]              --签名结果，定长64字节

返回值：0         --成功
        0x04      --传入空指针或哈希标识非法
        0x08      --验签失败
        0x12      --数据长度非法
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssSignVerify(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSignVerify));
    return api_func_raw(pk, data, dataLen, hashFlag, sig);
}

/******************************************************************************
摘要: 对称算法数据运算

参数:  in[in]         --待运算数据
      inLen[in]      --待运算数据长度
      key[in]        --密钥
      keyLen[in]    --密钥长度，定长16
      calcFlag[in]   --加解密标识：0-加密；1-解密
      padding[in]   --补位标识：0-不强制补位；1-强制补位
      out[out]      --加密后数据
      pOutLen[out]  --加密后数据长度

返回值：0         --成功
        0x04      --传入空指针或加解密标识、补位标识非法
        0x18      --算法不支持
        0x19      --密钥长度非法
******************************************************************************/
static inline vss_uint32 VssSymmCalc(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSymmCalc));
    return api_func_raw(in, inLen, key, keyLen, calcFlag, padding, out, pOutLen);
}

/******************************************************************************
摘要: 对称算法数据运算

参数:  index[in]     --密钥索引，指定0-13
in[in]         --待运算数据
      inLen[in]      --待运算数据长度
      calcFlag[in]   --加解密标识：0-加密；1-解密
      padding[in]   --补位标识：0-不强制补位；1-强制补位
      out[out]      --加密后数据
      pOutLen[out]  --加密后数据长度

返回值：0         --成功
        0x04      --传入空指针或加解密标识、补位标识非法
        0x05      --密钥不存在
        0x0F      --密钥索引非法，不为0-13
        0x18      --算法不支持
        0x19      --密钥长度不为2，4，8，12，16字节
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法       
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssSymmCalcIndex(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSymmCalcIndex));
    return api_func_raw(index, in, inLen, calcFlag, padding, out, pOutLen);
}

/******************************************************************************
摘要: 计算MAC，初始化向量为全0，对原文数据强制补位0x8000...，执行对称CBC计算，取最后16字节为MAC结果值

参数: in[in]         --待运算数据
     inLen[in]      --待运算数据长度
key[in]      --密钥
      keyLen[in]   --密钥长度，定长16
      out[out]      --mac结果，定长16字节

返回值：0         --成功
        0x04      --传入空指针
        0x18      --算法不支持
        0x19      --密钥长度不为16
******************************************************************************/
static inline vss_uint32 VssMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssMac));
    return api_func_raw(in, inLen, key, keyLen, out);
}

/******************************************************************************
摘要: 计算CMAC

参数: in[in]         --待运算数据
      inLen[in]      --待运算数据长度
      key[in]      --密钥
      keyLen[in]   --密钥长度，定长16
      out[out]      --mac结果，定长16字节

返回值：0         --成功
        0x04      --传入空指针
        0x18      --算法不支持
        0x19      --密钥长度不为16
******************************************************************************/
static inline vss_uint32 VssCMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCMac));
    return api_func_raw(in, inLen, key, keyLen, out);
}

/******************************************************************************
摘要:计算CMAC 

参数: in[in]         --待运算数据
      inLen[in]      --待运算数据长度
      index[in]      --密钥索引，指定0-13
      out[out]      --mac结果，定长16字节

返回值：0         --成功
        0x04      --传入空指针
        0x05      --指定密钥不存在
        0x0F      --密钥索引非法，不为0-13
        0x18      --算法不支持
******************************************************************************/
static inline vss_uint32 VssCMacIndex(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCMacIndex));
    return api_func_raw(in, inLen, index, out);
}

/******************************************************************************
摘要: 写入码单写入确认标志位，自动置1

参数: 无

返回值：0       --成功
        0x1B    --未调用算法库初始化接口或未指定IO操作回调方法
        0x1D    --写缓存数据失败
******************************************************************************/
static inline vss_uint32 VssSetWroteFlag(void)
{
    vss_uint32 (*api_func_raw)() = 0;

    api_func_raw = (vss_uint32 (*)())(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSetWroteFlag));
    return api_func_raw();
}

/******************************************************************************
摘要: 读取码单写入确认标志位

参数: wroteFlag[out]  --返回wroteFlag值，0-码单未写入；1-码单已写入

返回值：0       --成功
        0x04    --传入空指针
        0x1B    --未调用算法库初始化接口或未指定IO操作回调方法
        0x1C    --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssGetWroteFlag(vss_uint8* wroteFlag)
{
    vss_uint32 (*api_func_raw)(vss_uint8* wroteFlag) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* wroteFlag))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetWroteFlag));
    return api_func_raw(wroteFlag);
}

/******************************************************************************
摘要: 随机生成非对称密钥对并导出接口

参数: szX[out]      --公钥X（定长32字节）
      szY[out]      --公钥Y（定长32字节）
      szSK[out]     --私钥SK（定长32字节）

返回值：0       --成功
        0x04    --传入空指针
        0x08    --计算失败
        0x18    --算法不支持
******************************************************************************/
static inline vss_uint32 VssAsymGenKey(vss_uint8 *szX, vss_uint8 *szY, vss_uint8 *szSK)
{
    vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8 *szY, vss_uint8 *szSK) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8 *szY, vss_uint8 *szSK))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAsymGenKey));
    return api_func_raw(szX, szY, szSK);
}

/******************************************************************************
摘要: 随机生成非对称密钥对并存储接口

参数: szX[out]      --公钥X（定长32字节）
      szY[out]      --公钥Y（定长32字节）

返回值：0       --成功
        0x04    --szX或szY传入空指针
        0x08    --计算失败
        0x1B    --未调用算法库初始化接口或未指定IO操作回调方法
        0x1D    --写缓存数据失败
******************************************************************************/
static inline vss_uint32 VssAsymGenKeyIndex(vss_uint8 *szX, vss_uint8 *szY)
{
    vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8 *szY) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8 *szY))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAsymGenKeyIndex));
    return api_func_raw(szX, szY);
}

/******************************************************************************
摘要: 读取9-13号备份密钥明文

参数: index[in]    --密钥索引，有效索引为9-13
    nKeyLen[out]  --密钥的长度
    szKey[out]    --密钥值

返回值：0        --成功
        0x04     --密钥或密钥长度传入空指针
        0x05     --指定密钥不存在
        0x0F     --索引值非法，不为9-13
        0x1B      --未调用算法库初始化接口或未指定IO操作回调方法
        0x1C      --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssExportKey(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey)
{
    vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportKey));
    return api_func_raw(index, nKeyLen, szKey);
}

/******************************************************************************
摘要: 根据环境非对称密钥对并导出接口

参数: env[in]      --环境ID：1-测试环境；2-预生产环境；3-生产环境

返回值：0       --成功
        0x04    --传入空指针
        0x1B    --未调用算法库初始化接口或未指定IO操作回调方法
        0x1D    --写缓存数据失败
******************************************************************************/
static inline vss_uint32 VssUpdateEnvironment(vss_uint32 env)
{
    vss_uint32 (*api_func_raw)(vss_uint32 env) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 env))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssUpdateEnvironment));
    return api_func_raw(env);
}

/********************************************************************************
摘要: 读取芯片版本号

参数: version[out]	--输出芯片版本号，最长16字节字符串
返回值：0		  --成功
		0x04	  --传入空指针
		0xE1	  --参数为空
		0xE4	  --通信失败
		0xE5	  --消息校验失败
		0xE6	  --消息体长度错误
********************************************************************************/
static inline vss_uint32 VssGetChipVersion(vss_char8* version)
{
    vss_uint32 (*api_func_raw)(vss_char8* version) = 0;

    api_func_raw = (vss_uint32 (*)(vss_char8* version))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetChipVersion));
    return api_func_raw(version);
}

/********************************************************************************
摘要: 读取芯片状态

参数: state[out]	   --返回当心芯片状态，0-空闲; 1-运算中; 2-故障; 3-未初始化
返回值：0		  --成功
		0x04	  --传入空指针
		0xE1	  --参数为空
		0xE4	  --通信失败
		0xE5	  --消息校验失败
		0xE6	  --消息体长度错误
********************************************************************************/
static inline vss_uint32 VssGetChipState(vss_uint32* state)
{
    vss_uint32 (*api_func_raw)(vss_uint32* state) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32* state))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetChipState));
    return api_func_raw(state);
}

/********************************************************************************
摘要: 读取软件版本号

参数: version[out]	--输出芯片版本号，最长16字节字符串
返回值：0		  --成功
		0x04	  --传入空指针
		0xE1	  --参数为空
		0xE4	  --通信失败
		0xE5	  --消息校验失败
		0xE6	  --消息体长度错误
********************************************************************************/
static inline vss_uint32 VssGetVersion(vss_char8* version)
{
    vss_uint32 (*api_func_raw)(vss_char8* version) = 0;

    api_func_raw = (vss_uint32 (*)(vss_char8* version))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetVersion));
    return api_func_raw(version);
}

/******************************************************************************
摘要: 验证签名证书有效期（安全刷新业务专用）

参数: cert[in]			  --待验证证书
	  len[in]			   --待验证证书长度
	  szDataNow[in] 	  --当前日期”YYYYMMDD”格式的字符串

返回值：0		  --证书校验通过，有效期在当前日期之前，已过期
		1		  --证书校验通过，且未过期
		0x04	  --传入空指针
		0x08	  --证书签名校验失败
	 0x1A	   --证书格式非法
		0x18	  --算法不支持
		0x1A	  --证书过期
		0x1B	  --未调用算法库初始化接口或未指定IO操作回调方法		
		0x1C	  --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssVerifySignCertValid(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow)
{
    vss_uint32 (*api_func_raw)(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssVerifySignCertValid));
    return api_func_raw(cert, len, szDataNow);
}

/******************************************************************************
摘要: 读取环境ID接口

参数: env[out]      --环境ID：1-测试环境；2-预生产环境；3-生产环境

返回值：0       --成功
        0x04    --传入空指针
        0x1B    --未调用算法库初始化接口或未指定IO操作回调方法
        0x1C    --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssGetEnvironment(vss_uint32* env)
{
    vss_uint32 (*api_func_raw)(vss_uint32* env) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32* env))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetEnvironment));
    return api_func_raw(env);
}

/******************************************************************************
摘要: 修改密钥的激活属性

参数: keyId[in]   --密钥ID（只针对5-8号通信密钥有效）
     valid[out]    --可用标识: 0-不可用；1-可用

返回值：0       --成功
        0x04    --可用标识不是指定值
        0x05    --密钥不存在
        0x0F    --密钥ID非法，不为5-8
        0x1B    --未调用算法库初始化接口或未指定IO操作回调方法
        0x1C    --读缓存数据失败
******************************************************************************/
static inline vss_uint32 VssGetKeyActive(vss_uint32 keyId, vss_uint8* valid)
{
    vss_uint32 (*api_func_raw)(vss_uint32 keyId, vss_uint8* valid) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 keyId, vss_uint8* valid))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetKeyActive));
    return api_func_raw(keyId, valid);
}

/**
 * @ Revision history
 * @ -------------------------------------------------------------------------
 * @ Version      Date              Author               Note
 * @ -------------------------------------------------------------------------
 * @ 1.0          2020-02-01  wujialin         创建版本
 * @ 1.1          2021-02-04  wujialin         	对应VSS接口说明书V1.1内容更新
 * @ 1.2          2021-03-10  wujialin         	对应VSS接口说明书V1.2内容更新
 												1. 追加SAIC TLS V1.1相关内容
 													1）修改FTLS_ctx_init接口，追加version参数区分应用的版本信息，拆分SOMEIP回调接口初始化到FTLS_init_someipcb
													2）SAIC TLS 版本枚举定义
												2. 追加安全刷新专用验签接口
 * @ 1.3          2021-04-14  wujialin         	对应VSS接口说明书V1.3内容更新
												1. 追加VssGetKeyActive接口
												2. 追加VssVerifySignCertValid接口
 * @
 */



#endif /* __VSS_API_H__ */
