/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: NvM
 *           Program: MSR_Vector_SLP4
 *          Customer: DIAS Automotive Electronic Systems Co. Ltd.
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix TC234L
 *    License Scope : The usage is restricted to CBD1900770_D01
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: NvM_Cfg.c
 *   Generation Time: 2021-10-28 10:37:22
 *           Project: PGM_BswCfg - Version 0.0.0
 *          Delivery: CBD1900770_D01
 *      Tool Version: DaVinci Configurator  5.20.35
 *
 *
 *********************************************************************************************************************/

	
/* PRQA S 5087 MemMap */ /* MD_MSR_MemMap */
    
/**********************************************************************************************************************
 *  MODULE SWITCH
 *********************************************************************************************************************/
/* this switch enables the header file(s) to hide some parts. */
#define NVM_CFG_SOURCE

/* multiple inclusion protection */
#define NVM_H_

/* Required for RTE ROM block definitions */
#define RTE_MICROSAR_PIM_EXPORT

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/
#include "Std_Types.h"

/* This tag will only be replaced, if one or more callbacks via Service Ports had been configured */


/**********************************************************************************************************************
 *  MODULE HEADER INCLUDES
 *********************************************************************************************************************/
/* only includes the public part of config */
#include "NvM_Cfg.h"
#include "NvM_PrivateCfg.h"
#include "Dem_Data.h"
#include "BswM_NvM.h"

/**********************************************************************************************************************
 *  VERSION CHECKS
 *********************************************************************************************************************/
#if ((NVM_CFG_MAJOR_VERSION != (5u)) \
        || (NVM_CFG_MINOR_VERSION != (13u)))
# error "Version numbers of NvM_Cfg.c and NvM_Cfg.h are inconsistent!"
#endif

/* include list of the callback definitions */
//#include "Dem_Cbk.h"
#include "MEM_NvmData.h" 
#include "Rte_Cbk.h" 


/* include configured file declaring or defining resource (un)locking service(s) */
#include "SchM_NvM.h"

/**********************************************************************************************************************
 *  PUBLIC CONSTANTS
 *********************************************************************************************************************/
#define NVM_START_SEC_CONST_16
//#include "MemMap.h"

/* maximum number of bytes to be processed in one crc calculation step */
CONST(uint16, NVM_CONFIG_CONST) NvM_NoOfCrcBytes_u16 = 64u;

/* constant holding Crc queue size value */
CONST(uint16, NVM_PRIVATE_CONST) NvM_CrcQueueSize_u16 = NVM_TOTAL_NUM_OF_NVRAM_BLOCKS;

#define NVM_STOP_SEC_CONST_16
//#include "MemMap.h"

/* 8Bit Data section containing the CRC buffers, as well as the internal buffer */
#define NVM_START_SEC_VAR_NOINIT_8
//#include "MemMap.h"

static VAR(uint8, NVM_PRIVATE_DATA) NvMConfigBlock_RamBlock_au8[4u];

#if ((NVM_CRC_INT_BUFFER == STD_ON) || (NVM_REPAIR_REDUNDANT_BLOCKS_API == STD_ON))
static VAR(uint8, NVM_PRIVATE_DATA) DemAdminDataBlock_Crc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemStatusDataBlock_Crc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock0_Crc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock1_Crc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock2_Crc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock3_Crc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock4_Crc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock5_Crc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock6_Crc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock7_Crc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemAgingDataBlock_Crc[2uL]; 


/* create the internal buffer of size NVM_INTERNAL_BUFFER_LENGTH */
VAR(uint8, NVM_PRIVATE_DATA) NvM_InternalBuffer_au8[NVM_INTERNAL_BUFFER_LENGTH]; /* PRQA S 1533 */ /* MD_NvM_Cfg_8.9_InternalBuffer */
#endif

 /*  CRC buffers for CRCCompareMechanism  */ 
static VAR(uint8, NVM_PRIVATE_DATA) DemAdminDataBlock_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemStatusDataBlock_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock0_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock1_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock2_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock3_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock4_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock5_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock6_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemPrimaryDataBlock7_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30OverVoltageLowerThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30OverVoltageHigherThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PGMrecoverytimes_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_SecurityAttemptCounter_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_Bat_UnderVoltage_Threshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_Bat_Normal_SOC_Threshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_BatDiscon_BatOpenDuration_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_DIDReserve18_C_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_DIDReserve19_C_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_DIDReserve20_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_DIDReserve21_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_DIDReserve22_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_DIDReserve23_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_DIDReserve24_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_DIDReserve25_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_DIDReserve26_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_DIDReserve27_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F110_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F111_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F112_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F113_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F114_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F115_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F116_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F117_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F118_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F119_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F11A_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F11B_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F11C_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F11D_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F11E_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_PIF_F11F_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_NIF_FactoryData_F120_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_NIF_CurrentData_F121_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_ECUPartNumber_F187_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_SystemSupplierIdentifier_F18A_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_ECUManufactureDate_F18B_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_ECUSerialNumber_F18C_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_VIN_F190_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_ECUHardwareNumber_F191_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_ConfigurationTraceabilityField_F198_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_ECUIndexInformation_F1A5_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_VehicleFeatureInformation_F1A8_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_LastPGMOpenInformation_B042_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_SwitchOpenReasonRecord_B044_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_Softwareintegritystatus_AFFD_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_ReseveforAPP_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_ReseveforFBL_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_ST_ResetBlockInfo_T_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_ST_ResetMngInfo_T_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) DemAgingDataBlock_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_OCV_Map_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_Usg_Md_CompCrc[2uL]; 
static VAR(uint8, NVM_PRIVATE_DATA) CtApNvM_NvSWC_SecurityLog_CompCrc[2uL]; 



#define NVM_STOP_SEC_VAR_NOINIT_8
//#include "MemMap.h"

#define NVM_START_SEC_CONST_DESCRIPTOR_TABLE
//#include "MemMap.h"

CONST(NvM_BlockIdType, NVM_PUBLIC_CONST) NvM_NoOfBlockIds_t = NVM_TOTAL_NUM_OF_NVRAM_BLOCKS;

CONST(NvM_CompiledConfigIdType, NVM_PUBLIC_CONST) NvM_CompiledConfigId_t = {(uint16)NVM_COMPILED_CONFIG_ID}; /* PRQA S 0759 */ /* MD_MSR_Union */
 
 
/* block descriptor table that holds the static configuration parameters of the RAM, ROM and NVBlocks.
* This table has to be adjusted according to the configuration of the NVManager.
*/

CONST(NvM_BlockDescriptorType, NVM_CONFIG_CONST) NvM_BlockDescriptorTable_at[NVM_TOTAL_NUM_OF_NVRAM_BLOCKS] =
    {
      { /*  MultiBlockRequest  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMReadRamBlockFromNvCallback  */ , 
        NULL_PTR /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        NULL_PTR /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0001u /*  NV block Base number (defined by FEE/EA)  */ , 
        0U /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        0U /*  NvMNvBlockNVRAMDataLength  */ , 
        0u /*  NvMNvBlockNum  */ , 
        255u /*  NvMBlockJobPriority  */ , 
        0u /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_USE_CRC_OFF /*  NvMBlockCrcType  */ , 
        0u /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  NvMConfigBlock  */ 
        (NvM_RamAddressType)NvMConfigBlock_RamBlock_au8 /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMReadRamBlockFromNvCallback  */ , 
        NULL_PTR /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        NULL_PTR /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0010u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        2u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_REDUNDANT /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_OFF |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  DemAdminDataBlock  */ 
        (NvM_RamAddressType)&Dem_Cfg_AdminData /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        Dem_NvM_InitAdminData /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Dem_NvM_JobFinished /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMReadRamBlockFromNvCallback  */ , 
        NULL_PTR /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        DemAdminDataBlock_Crc /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        DemAdminDataBlock_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0020u /*  NV block Base number (defined by FEE/EA)  */ , 
        10u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        10u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  DemStatusDataBlock  */ 
        (NvM_RamAddressType)&Dem_Cfg_StatusData /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        Dem_NvM_InitStatusData /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Dem_NvM_JobFinished /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMReadRamBlockFromNvCallback  */ , 
        NULL_PTR /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        DemStatusDataBlock_Crc /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        DemStatusDataBlock_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x00B0u /*  NV block Base number (defined by FEE/EA)  */ , 
        22u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        22u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  DemPrimaryDataBlock0  */ 
        (NvM_RamAddressType)&Dem_Cfg_PrimaryEntry_0 /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Dem_Cfg_MemoryEntryInit /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Dem_NvM_JobFinished /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMReadRamBlockFromNvCallback  */ , 
        NULL_PTR /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        DemPrimaryDataBlock0_Crc /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        DemPrimaryDataBlock0_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0030u /*  NV block Base number (defined by FEE/EA)  */ , 
        48u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        48u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  DemPrimaryDataBlock1  */ 
        (NvM_RamAddressType)&Dem_Cfg_PrimaryEntry_1 /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Dem_Cfg_MemoryEntryInit /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Dem_NvM_JobFinished /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMReadRamBlockFromNvCallback  */ , 
        NULL_PTR /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        DemPrimaryDataBlock1_Crc /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        DemPrimaryDataBlock1_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0040u /*  NV block Base number (defined by FEE/EA)  */ , 
        48u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        48u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  DemPrimaryDataBlock2  */ 
        (NvM_RamAddressType)&Dem_Cfg_PrimaryEntry_2 /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Dem_Cfg_MemoryEntryInit /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Dem_NvM_JobFinished /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMReadRamBlockFromNvCallback  */ , 
        NULL_PTR /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        DemPrimaryDataBlock2_Crc /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        DemPrimaryDataBlock2_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0050u /*  NV block Base number (defined by FEE/EA)  */ , 
        48u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        48u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  DemPrimaryDataBlock3  */ 
        (NvM_RamAddressType)&Dem_Cfg_PrimaryEntry_3 /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Dem_Cfg_MemoryEntryInit /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Dem_NvM_JobFinished /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMReadRamBlockFromNvCallback  */ , 
        NULL_PTR /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        DemPrimaryDataBlock3_Crc /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        DemPrimaryDataBlock3_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0060u /*  NV block Base number (defined by FEE/EA)  */ , 
        48u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        48u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  DemPrimaryDataBlock4  */ 
        (NvM_RamAddressType)&Dem_Cfg_PrimaryEntry_4 /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Dem_Cfg_MemoryEntryInit /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Dem_NvM_JobFinished /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMReadRamBlockFromNvCallback  */ , 
        NULL_PTR /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        DemPrimaryDataBlock4_Crc /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        DemPrimaryDataBlock4_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0070u /*  NV block Base number (defined by FEE/EA)  */ , 
        48u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        48u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  DemPrimaryDataBlock5  */ 
        (NvM_RamAddressType)&Dem_Cfg_PrimaryEntry_5 /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Dem_Cfg_MemoryEntryInit /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Dem_NvM_JobFinished /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMReadRamBlockFromNvCallback  */ , 
        NULL_PTR /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        DemPrimaryDataBlock5_Crc /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        DemPrimaryDataBlock5_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0080u /*  NV block Base number (defined by FEE/EA)  */ , 
        48u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        48u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  DemPrimaryDataBlock6  */ 
        (NvM_RamAddressType)&Dem_Cfg_PrimaryEntry_6 /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Dem_Cfg_MemoryEntryInit /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Dem_NvM_JobFinished /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMReadRamBlockFromNvCallback  */ , 
        NULL_PTR /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        DemPrimaryDataBlock6_Crc /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        DemPrimaryDataBlock6_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0090u /*  NV block Base number (defined by FEE/EA)  */ , 
        48u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        48u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  DemPrimaryDataBlock7  */ 
        (NvM_RamAddressType)&Dem_Cfg_PrimaryEntry_7 /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Dem_Cfg_MemoryEntryInit /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Dem_NvM_JobFinished /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMReadRamBlockFromNvCallback  */ , 
        NULL_PTR /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        DemPrimaryDataBlock7_Crc /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        DemPrimaryDataBlock7_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x00A0u /*  NV block Base number (defined by FEE/EA)  */ , 
        48u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        48u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30OverVoltageLowerThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30OverVoltageLowerThreshold_ROM_KL30OverVoltageLowerThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30OverVoltageLowerThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30OverVoltageLowerThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30OverVoltageLowerThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30OverVoltageLowerThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0600u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30OverVoltageHigherThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30OverVoltageHigherThreshold_ROM_KL30OverVoltageHigherThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30OverVoltageHigherThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30OverVoltageHigherThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30OverVoltageHigherThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30OverVoltageHigherThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x00F0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30OverVoltageHigherThresholdTimeout_ROM_KL30OverVoltageHigherThresholdTimeout /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0100u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30OverVoltageLowerThresholdTimeout_ROM_KL30OverVoltageLowerThresholdTimeout /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0110u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30UnderVoltageHigherThreshold_ROM_KL30UnderVoltageHigherThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0120u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30_RUnderVoltageHigherThreshold_ROM_KL30_RUnderVoltageHigherThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0130u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30_ROverVoltageLowerThresholdTimeout_ROM_KL30_ROverVoltageLowerThresholdTimeout /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0140u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30_ROverVoltageLowerThreshold_ROM_KL30_ROverVoltageLowerThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0150u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30_ROverVoltageHigherThresholdTimeout_ROM_KL30_ROverVoltageHigherThresholdTimeout /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0160u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30_ROverVoltageHigherThreshold_ROM_KL30_ROverVoltageHigherThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0170u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_PGMMosfetOverCurrentLowerThreshold_ROM_PGMMosfetOverCurrentLowerThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0180u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_PGMMosfetOverCurrentHigherThreshold_ROM_PGMMosfetOverCurrentHigherThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0190u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_PGMMosfetOverCurrentHigherThresholdTimeout_ROM_PGMMosfetOverCurrentHigherThresholdTimeout /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x01A0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_PGMMosfetOverCurrentLowerThresholdTimeout_ROM_PGMMosfetOverCurrentLowerThresholdTimeout /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x01B0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_PGMMosfetOverTemperatureThreshold_ROM_PGMMosfetOverTemperatureThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x01C0u /*  NV block Base number (defined by FEE/EA)  */ , 
        1u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        1u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_PGMrecoveryKL30OverVoltageThreshold_ROM_PGMrecoveryKL30OverVoltageThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x01D0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_PGMrecoveryKL30UnderVoltageThreshold_ROM_PGMrecoveryKL30UnderVoltageThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x01E0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_PGMrecoveryKL30_ROverVoltageThreshold_ROM_PGMrecoveryKL30_ROverVoltageThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x01F0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_PGMrecoveryKL30_RUnderVoltageThreshold_ROM_PGMrecoveryKL30_RUnderVoltageThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0200u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PGMrecoverytimes  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_PGMrecoverytimes_ROM_PGMrecoverytimes /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoverytimes /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PGMrecoverytimes /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PGMrecoverytimes /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PGMrecoverytimes_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0210u /*  NV block Base number (defined by FEE/EA)  */ , 
        1u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        1u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_SecurityAttemptCounter  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_SecurityAttemptCounter_ROM_SecurityAttemptCounter /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SecurityAttemptCounter /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_SecurityAttemptCounter /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_SecurityAttemptCounter /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_SecurityAttemptCounter_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0220u /*  NV block Base number (defined by FEE/EA)  */ , 
        1u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        1u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_PGMVoltageStabilizationFilterTimeAfterItOpen_ROM_PGMVoltageStabilizationFilterTimeAfterItOpen /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0230u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_AltDiscon_BatUnderVolDuration_ROM_AltDiscon_BatUnderVolDuration /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0250u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_PGM_Recovery_Normal_Voltage_Filter_Time_ROM_PGM_Recovery_Normal_Voltage_Filter_Time /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0260u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_Bat_UnderVoltage_Threshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_Bat_UnderVoltage_Threshold_ROM_Bat_UnderVoltage_Threshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Bat_UnderVoltage_Threshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_Bat_UnderVoltage_Threshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_Bat_UnderVoltage_Threshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_Bat_UnderVoltage_Threshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0270u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_Bat_Normal_SOC_Threshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_Bat_Normal_SOC_Threshold_ROM_Bat_Normal_SOC_Threshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Bat_Normal_SOC_Threshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_Bat_Normal_SOC_Threshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_Bat_Normal_SOC_Threshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_Bat_Normal_SOC_Threshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0280u /*  NV block Base number (defined by FEE/EA)  */ , 
        1u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        1u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_BatDiscon_BatOpenDuration  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_BatDiscon_BatOpenDuration_ROM_BatDiscon_BatOpenDuration /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_BatDiscon_BatOpenDuration /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_BatDiscon_BatOpenDuration /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_BatDiscon_BatOpenDuration /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_BatDiscon_BatOpenDuration_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0290u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_DIDReserve18_C  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_DIDReserve18_C_ROM_DIDReserve18_C /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve18_C /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_DIDReserve18_C /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_DIDReserve18_C /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_DIDReserve18_C_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x02A0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_DIDReserve19_C  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_DIDReserve19_C_ROM_DIDReserve19_C /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve19_C /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_DIDReserve19_C /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_DIDReserve19_C /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_DIDReserve19_C_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x02B0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_DIDReserve20  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_DIDReserve20_ROM_DIDReserve20 /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve20 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_DIDReserve20 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_DIDReserve20 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_DIDReserve20_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x02C0u /*  NV block Base number (defined by FEE/EA)  */ , 
        4u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        4u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_DIDReserve21  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_DIDReserve21_ROM_DIDReserve21 /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve21 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_DIDReserve21 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_DIDReserve21 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_DIDReserve21_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x02D0u /*  NV block Base number (defined by FEE/EA)  */ , 
        4u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        4u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_DIDReserve22  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_DIDReserve22_ROM_DIDReserve22 /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve22 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_DIDReserve22 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_DIDReserve22 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_DIDReserve22_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x02E0u /*  NV block Base number (defined by FEE/EA)  */ , 
        4u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        4u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_DIDReserve23  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve23 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_DIDReserve23 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_DIDReserve23 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_DIDReserve23_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x02F0u /*  NV block Base number (defined by FEE/EA)  */ , 
        4u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        4u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_DIDReserve24  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve24 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_DIDReserve24 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_DIDReserve24 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_DIDReserve24_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0300u /*  NV block Base number (defined by FEE/EA)  */ , 
        4u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        4u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_DIDReserve25  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve25 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_DIDReserve25 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_DIDReserve25 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_DIDReserve25_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0310u /*  NV block Base number (defined by FEE/EA)  */ , 
        4u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        4u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_DIDReserve26  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve26 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_DIDReserve26 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_DIDReserve26 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_DIDReserve26_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0320u /*  NV block Base number (defined by FEE/EA)  */ , 
        4u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        4u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_DIDReserve27  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve27 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_DIDReserve27 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_DIDReserve27 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_DIDReserve27_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0330u /*  NV block Base number (defined by FEE/EA)  */ , 
        4u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        4u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F110  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F110 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F110 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F110 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F110_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0340u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F111  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F111 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F111 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F111 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F111_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0350u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F112  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F112 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F112 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F112 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F112_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0360u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F113  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F113 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F113 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F113 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F113_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0370u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F114  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F114 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F114 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F114 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F114_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0380u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F115  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_PIF_F115_ROM_PIF_F115 /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F115 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F115 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F115 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F115_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0390u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F116  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F116 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F116 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F116 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F116_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x03A0u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F117  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F117 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F117 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F117 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F117_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x03B0u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F118  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F118 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F118 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F118 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F118_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x03C0u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F119  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F119 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F119 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F119 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F119_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x03D0u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F11A  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11A /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F11A /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F11A /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F11A_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x03E0u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F11B  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11B /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F11B /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F11B /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F11B_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x03F0u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F11C  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11C /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F11C /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F11C /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F11C_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0400u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F11D  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11D /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F11D /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F11D /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F11D_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0410u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F11E  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11E /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F11E /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F11E /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F11E_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0420u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_PIF_F11F  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11F /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_PIF_F11F /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_PIF_F11F /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_PIF_F11F_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0430u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_NIF_FactoryData_F120  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_NIF_FactoryData_F120 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_NIF_FactoryData_F120 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_NIF_FactoryData_F120_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0440u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_OFF
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_NIF_CurrentData_F121  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_NIF_CurrentData_F121 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_NIF_CurrentData_F121 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_NIF_CurrentData_F121_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0450u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_OFF
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_ECUPartNumber_F187  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUPartNumber_F187 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_ECUPartNumber_F187 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_ECUPartNumber_F187 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_ECUPartNumber_F187_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0460u /*  NV block Base number (defined by FEE/EA)  */ , 
        5u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        5u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_SystemSupplierIdentifier_F18A  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SystemSupplierIdentifier_F18A /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_SystemSupplierIdentifier_F18A /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_SystemSupplierIdentifier_F18A /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_SystemSupplierIdentifier_F18A_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0470u /*  NV block Base number (defined by FEE/EA)  */ , 
        5u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        5u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_ECUManufactureDate_F18B  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUManufactureDate_F18B /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_ECUManufactureDate_F18B /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_ECUManufactureDate_F18B /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_ECUManufactureDate_F18B_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0480u /*  NV block Base number (defined by FEE/EA)  */ , 
        3u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        3u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_ECUSerialNumber_F18C  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUSerialNumber_F18C /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_ECUSerialNumber_F18C /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_ECUSerialNumber_F18C /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_ECUSerialNumber_F18C_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0490u /*  NV block Base number (defined by FEE/EA)  */ , 
        16u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        16u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_VIN_F190  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_VIN_F190 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_VIN_F190 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_VIN_F190 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_VIN_F190_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x04A0u /*  NV block Base number (defined by FEE/EA)  */ , 
        17u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        17u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_ECUHardwareNumber_F191  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUHardwareNumber_F191 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_ECUHardwareNumber_F191 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_ECUHardwareNumber_F191 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_ECUHardwareNumber_F191_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x04B0u /*  NV block Base number (defined by FEE/EA)  */ , 
        5u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        5u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x04C0u /*  NV block Base number (defined by FEE/EA)  */ , 
        10u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        10u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_ConfigurationTraceabilityField_F198  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ConfigurationTraceabilityField_F198 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_ConfigurationTraceabilityField_F198 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_ConfigurationTraceabilityField_F198 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_ConfigurationTraceabilityField_F198_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x04D0u /*  NV block Base number (defined by FEE/EA)  */ , 
        11u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        11u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_ECUIndexInformation_F1A5  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUIndexInformation_F1A5 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_ECUIndexInformation_F1A5 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_ECUIndexInformation_F1A5 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_ECUIndexInformation_F1A5_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x04E0u /*  NV block Base number (defined by FEE/EA)  */ , 
        3u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        3u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_VehicleFeatureInformation_F1A8  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_VehicleFeatureInformation_F1A8 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_VehicleFeatureInformation_F1A8 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_VehicleFeatureInformation_F1A8 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_VehicleFeatureInformation_F1A8_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x04F0u /*  NV block Base number (defined by FEE/EA)  */ , 
        20u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        20u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_LastPGMOpenInformation_B042  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_LastPGMOpenInformation_B042 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_LastPGMOpenInformation_B042 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_LastPGMOpenInformation_B042 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_LastPGMOpenInformation_B042_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0500u /*  NV block Base number (defined by FEE/EA)  */ , 
        15u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        15u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0510u /*  NV block Base number (defined by FEE/EA)  */ , 
        15u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        15u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_SwitchOpenReasonRecord_B044  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SwitchOpenReasonRecord_B044 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_SwitchOpenReasonRecord_B044 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_SwitchOpenReasonRecord_B044 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_SwitchOpenReasonRecord_B044_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0520u /*  NV block Base number (defined by FEE/EA)  */ , 
        14u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        14u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_Softwareintegritystatus_AFFD  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Softwareintegritystatus_AFFD /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_Softwareintegritystatus_AFFD /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_Softwareintegritystatus_AFFD /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_Softwareintegritystatus_AFFD_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0530u /*  NV block Base number (defined by FEE/EA)  */ , 
        1u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        1u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0540u /*  NV block Base number (defined by FEE/EA)  */ , 
        1u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        1u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0550u /*  NV block Base number (defined by FEE/EA)  */ , 
        1u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        1u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_ReseveforAPP  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_ReseveforAPP /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_ReseveforAPP /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_ReseveforAPP_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0560u /*  NV block Base number (defined by FEE/EA)  */ , 
        64u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        64u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_OFF
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_ReseveforFBL  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ReseveforFBL /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_ReseveforFBL /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_ReseveforFBL /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_ReseveforFBL_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0570u /*  NV block Base number (defined by FEE/EA)  */ , 
        64u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        64u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_ST_ResetBlockInfo_T  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_ST_ResetBlockInfo_T /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_ST_ResetBlockInfo_T /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_ST_ResetBlockInfo_T_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0580u /*  NV block Base number (defined by FEE/EA)  */ , 
        8u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        8u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_OFF
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_ST_ResetMngInfo_T  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_ST_ResetMngInfo_T /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_ST_ResetMngInfo_T /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_ST_ResetMngInfo_T_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0590u /*  NV block Base number (defined by FEE/EA)  */ , 
        8u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        8u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_OFF
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30UnderVoltageLowerThreshold_ROM_KL30UnderVoltageLowerThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x05A0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30UnderVoltageHigherThresholdTimeout_ROM_KL30UnderVoltageHigherThresholdTimeout /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x05B0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30UnderVoltageLowerThresholdTimeout_ROM_KL30UnderVoltageLowerThresholdTimeout /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x05C0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30_RUnderVoltageLowerThresholdTimeout_ROM_KL30_RUnderVoltageLowerThresholdTimeout /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x05D0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30_RUnderVoltageLowerThreshold_ROM_KL30_RUnderVoltageLowerThreshold /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x05E0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_KL30_RUnderVoltageHigherThresholdTimeout_ROM_KL30_RUnderVoltageHigherThresholdTimeout /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x05F0u /*  NV block Base number (defined by FEE/EA)  */ , 
        2u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        2u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9 /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9 /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9 /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x00D0u /*  NV block Base number (defined by FEE/EA)  */ , 
        5u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        5u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x00C0u /*  NV block Base number (defined by FEE/EA)  */ , 
        5u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        5u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  DemAgingDataBlock  */ 
        (NvM_RamAddressType)&Dem_Cfg_AgingData /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        Dem_NvM_InitAgingData /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Dem_NvM_JobFinished /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        NULL_PTR /*  NvMReadRamBlockFromNvCallback  */ , 
        NULL_PTR /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        DemAgingDataBlock_Crc /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        DemAgingDataBlock_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x00E0u /*  NV block Base number (defined by FEE/EA)  */ , 
        12u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        12u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_ON |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_OCV_Map  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        (NvM_RomAddressType)&Rte_CtApNvM_OCV_Map_ROM_OCV_Map /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_OCV_Map /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_OCV_Map /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_OCV_Map /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_OCV_Map_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0610u /*  NV block Base number (defined by FEE/EA)  */ , 
        110u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        110u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_Usg_Md  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ ,
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Usg_Md /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_Usg_Md /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_Usg_Md /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_Usg_Md_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0240u /*  NV block Base number (defined by FEE/EA)  */ , 
        1u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        1u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }, 
      { /*  CtApNvM_NvSWC_SecurityLog  */ 
        NULL_PTR /*  NvMRamBlockDataAddress  */ , 
        NULL_PTR /*  NvMRomBlockDataAddress  */ , 
        NULL_PTR /*  NvMInitBlockCallback  */ , 
        NULL_PTR /*  NvMInitBlockCallback (extended)  */ , 
        Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SecurityLog /*  NvMSingleBlockCallback  */ , 
        NULL_PTR /*  NvMSingleBlockCallback (extended)  */ , 
        Rte_SetMirror_CtApNvM_NvSWC_SecurityLog /*  NvMReadRamBlockFromNvCallback  */ , 
        Rte_GetMirror_CtApNvM_NvSWC_SecurityLog /*  NvMWriteRamBlockToNvCallback  */ , 
        NULL_PTR /*  NvMBlockPreWriteTransformCallback  */ , 
        NULL_PTR /*  NvMBlockPostReadTransformCallback  */ , 
        NULL_PTR /*  RamBlockCRC data buffer (defined by NvM)  */ , 
        CtApNvM_NvSWC_SecurityLog_CompCrc /*  CRCCompMechanism CRC data (defined by NvM)  */ , 
        0x0620u /*  NV block Base number (defined by FEE/EA)  */ , 
        40u /*  NvMNvBlockLength  */ , 
        0u /*  CsmJobArrayIndex  */ , 
        40u /*  NvMNvBlockNVRAMDataLength  */ , 
        1u /*  NvMNvBlockNum  */ , 
        127u /*  NvMBlockJobPriority  */ , 
        MEMIF_Fee /*  Device Id (defined by MemIf)  */ , 
        NVM_BLOCK_NATIVE /*  NvMBlockManagementType  */ , 
        NVM_BLOCK_CRC_16_ON /*  NvMBlockCrcType  */ , 
        (
NVM_CALC_RAM_BLOCK_CRC_OFF |
NVM_BLOCK_WRITE_PROT_OFF |
NVM_BLOCK_WRITE_BLOCK_ONCE_OFF |
NVM_RESISTANT_TO_CHANGED_SW_ON |
NVM_SELECT_BLOCK_FOR_READALL_ON |
NVM_SELECT_BLOCK_FOR_WRITEALL_OFF |
NVM_CBK_DURING_READALL_ON
) /*  Flags  */ , 
        STD_OFF /*  NvMBswMBlockStatusInformation  */ 
      }
    };

/* Permanent RAM and ROM block length checks - compile time (only available for blocks with enabled length check */

/* PRQA S 3494, 3213, 1755 BlockLengthChecks */ /* MD_NvM_Cfg_14.3, MD_NvM_Cfg_2.4 */

/* How does it work:
 * data length = sizeof(ramBlock - CrcLength 
 *     - CRC internal buffer enabled: CRC length == 0, RAM blocks store only data, CRC is handles internally
 *     - CRC internal buffer disabled: CRC length is the number of CRC bytes, for blocks without CRC the length == 0
 *     - for ROM blocks the CRC does not matter
 * Data length has to be > or < or == to configured NvM block length, depending on configuration (see above). 
 * In case the lengths do not match a bitfield with length -1 will be created and shall cause a compiler error.
 * The compiler error shall mark the line with invalid bitfield (bitfield length == -1) - the line includes all required information:
 *     - Block_ + NvM block name
 *     - length error description
 *     - RAM block name, CRC length and configured NvM block length
 */

typedef unsigned int NvM_LengthCheck;
 
/* Block Length Check and Automatic Block Length enabled: error if sizeof RAM block is greater than the configured block length */
#define SizeOfRamBlockGreaterThanConfiguredLength(ramBlock, crcLength, blockLength) (((sizeof(ramBlock) - (crcLength)) > (blockLength)) ? -1 : 1) /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
/* Block Length Check and Strict Length Check enabled: error if the sizeof RAM block does not match the configured block length */
#define SizeOfRamBlockDoesNotMatchConfiguredLength(ramBlock, crcLength, blockLength) (((sizeof(ramBlock) - (crcLength)) != (blockLength)) ? -1 : 1) /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
/* Block Length Check enabled and Strict Length Check disabled: error if the sizeof RAM block is less than the configured block length */
#define SizeOfRamBlockLessThanConfiguredLength(ramBlock, crcLength, blockLength) (((sizeof(ramBlock) - (crcLength)) < (blockLength)) ? -1 : 1) /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */

/* RAM block length checks */



/* Block Length Check and Automatic Block Length enabled: error if sizeof ROM block is less than sizeof RAM block */
#define SizeOfRomBlockLessThanSizeOfRamBlock(romBlock, ramBlock) ((sizeof(romBlock) < sizeof(ramBlock)) ? -1 : 1) /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
/* Block Length Check and Strict Length Check enabled: error if the sizeof ROM block does not match the configured block length */
#define SizeOfRomBlockDoesNotMatchConfiguredLength(romBlock, blockLength) ((sizeof(romBlock) != (blockLength)) ? -1 : 1) /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */
/* Block Length Check enabled, Strict Length disabled: error if the sizeof ROM block is less than the configured block length */
#define SizeOfRomBlockLessThanConfiguredLength(romBlock, blockLength) ((sizeof(romBlock) < (blockLength)) ? -1 : 1) /* PRQA S 3453 */ /* MD_MSR_FctLikeMacro */

/* ROM block length checks */



/* PRQA L:BlockLengthChecks */

/* Permanent RAM and ROM block length checks - END */

#define NVM_STOP_SEC_CONST_DESCRIPTOR_TABLE
//#include "MemMap.h"

#define NVM_START_SEC_VAR_NOINIT_UNSPECIFIED
//#include "MemMap.h"

#if(NVM_API_CONFIG_CLASS != NVM_API_CONFIG_CLASS_1)
/* Job Queue used for normal and high prio jobs */
VAR(NvM_QueueEntryType, NVM_PRIVATE_DATA) NvM_JobQueue_at[NVM_SIZE_STANDARD_JOB_QUEUE + NVM_SIZE_IMMEDIATE_JOB_QUEUE];
#endif

#define NVM_STOP_SEC_VAR_NOINIT_UNSPECIFIED
//#include "MemMap.h"

#define NVM_START_SEC_VAR_POWER_ON_INIT_UNSPECIFIED
//#include "MemMap.h"

/* array of RAM Block Management areas, defined to be usable in block descriptor table */
VAR(NvM_RamMngmtAreaType, NVM_CONFIG_DATA) NvM_BlockMngmtArea_at[NVM_TOTAL_NUM_OF_NVRAM_BLOCKS];

/* management area for DCM blocks */
VAR(NvM_RamMngmtAreaType, NVM_CONFIG_DATA) NvM_DcmBlockMngmt_t;

#define NVM_STOP_SEC_VAR_POWER_ON_INIT_UNSPECIFIED
//#include "MemMap.h"

#define NVM_START_SEC_CODE
//#include "MemMap.h"

/**********************************************************************************************************************
*  NvM_EnterCriticalSection
**********************************************************************************************************************/
/*!
 * \internal
 *  - #10 enter SchM exclusive area for NvM 
 * \endinternal
 */
FUNC(void, NVM_PRIVATE_CODE) NvM_EnterCriticalSection(void)
{
  /* do what ever was defined to do for locking the resources */
  SchM_Enter_NvM_NVM_EXCLUSIVE_AREA_0();
}

/**********************************************************************************************************************
*  NvM_ExitCriticalSection
**********************************************************************************************************************/
/*!
 * \internal
 *  - #10 exit SchM exclusive area for NvM 
 * \endinternal
 */
FUNC(void, NVM_PRIVATE_CODE) NvM_ExitCriticalSection(void)
{
  /* do what ever was defined to do for unlocking the resources */
  SchM_Exit_NvM_NVM_EXCLUSIVE_AREA_0();
}

/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define NvM_invokeMultiBlockMode(serv, res) BswM_NvM_CurrentJobMode((serv),(res)) /*  if NvMBswMMultiBlockJobStatusInformation is TRUE  */
/* PRQA S 3453 1 */ /* MD_MSR_FctLikeMacro */
#define NvM_invokeMultiCbk(serv, res)   /*  if Multi Block Callback is configured  */

/**********************************************************************************************************************
*  NvM_MultiBlockCbk
**********************************************************************************************************************/
/*!
 * \internal
 *  - #10 invoke the BSWM notification if any is configured
 *  - #20 in case the given job result isn't set to pending, invoke the multi block job end notification
 * \endinternal
 */
/* PRQA S 3206 1 */ /* MD_NvM_Cfg_2.7 */
FUNC(void, NVM_PRIVATE_CODE) NvM_MultiBlockCbk(NvM_ServiceIdType ServiceId, NvM_RequestResultType JobResult)
{
  /* The complete function body is designed to be optimized away by the compiler, if it is not needed    *
   * If the used macro is empty, the compiler may decide to remove code because it would contain         *
   * empty execution blocks (it does not matter whether conditions were TRUE or FALSE                    */
  NvM_invokeMultiBlockMode(ServiceId, JobResult);

  if(JobResult != NVM_REQ_PENDING)
  {
    NvM_invokeMultiCbk(ServiceId, JobResult);
  }   
}

#define NVM_STOP_SEC_CODE
//#include "MemMap.h"

/* PRQA L:MemMap */

/* Justification for module specific MISRA deviations:

MD_NvM_Cfg_2.4
Reason: NvM provides compile time block length checks via structures with bitfields with positive or negative length -
        the negative length lead to compiler errors. It is possible to use == or even >= check, if only one is used,
        the other one will never be used. The macros are always available. The created structures will never be used by NvM.
Risk: No risk.
Prevention: No prevention.

MD_NvM_Cfg_2.7:
Reason: The function NvM_MultiBlockCbk gets all needed parameters to invoke the BSWM and multi block job end callback.
        If both are disabled, the function is empty and does nothing - the passed parameters remain unused.
Risk: No risk.
Prevention: No prevention.

MD_NvM_Cfg_8.9_InternalBuffer:
Reason: NvM uses an internal buffer for explicit synchronization, in internal CRC buffer use case and for repair redundant blocks.
        Depending on configuration all, one or even none of the uses is enabled - therefore sometimes the internal buffer is
        used only once.
Risk: No risk.
Prevention: No prevention.

MD_NvM_Cfg_8.11:
Reason: Array of unknown size is used in order to reduce dependencies.
Risk: In case the array size shall be determined it would be incorrect.
Prevention: No prevention.

MD_NvM_Cfg_14.3:
Reason: NvM provides compile time block length checks via bitfields with positive or negative length - the negative length
        lead to compiler errors. With valid configuration (all block length are configured correctly), all checks are false.
Risk: No risk.
Prevention: No prevention. If needed the compile time checks can be disabled via configuration.

 */

/**********************************************************************************************************************
 *  END OF FILE: NvM_Cfg.c
 *********************************************************************************************************************/


