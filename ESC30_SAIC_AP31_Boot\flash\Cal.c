/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <Cal.c>
 *  @brief      <Calculate the Crc >
 *    
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *
 *  <AUTHOR> Chen>
 *  @date       <2012-12-27>
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20121227    Gary       Initial version
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/

#include "Cal.h"

/*=======[E X T E R N A L   D A T A]==========================================*/
#if (CAL_CRC32 == CAL_METHOD)
const uint32 Cal_Crc32Tab[256] =
{
    0x00000000ul, 0x06233697ul, 0x05C45641ul, 0x03E760D6ul, 0x020A97EDul, 0x0429A17Aul, 0x07CEC1ACul, 0x01EDF73Bul, 
    0x04152FDAul, 0x0236194Dul, 0x01D1799Bul, 0x07F24F0Cul, 0x061FB837ul, 0x003C8EA0ul, 0x03DBEE76ul, 0x05F8D8E1ul, 
    0x01A864DBul, 0x078B524Cul, 0x046C329Aul, 0x024F040Dul, 0x03A2F336ul, 0x0581C5A1ul, 0x0666A577ul, 0x004593E0ul, 
    0x05BD4B01ul, 0x039E7D96ul, 0x00791D40ul, 0x065A2BD7ul, 0x07B7DCECul, 0x0194EA7Bul, 0x02738AADul, 0x0450BC3Aul, 
    0x0350C9B6ul, 0x0573FF21ul, 0x06949FF7ul, 0x00B7A960ul, 0x015A5E5Bul, 0x077968CCul, 0x049E081Aul, 0x02BD3E8Dul, 
    0x0745E66Cul, 0x0166D0FBul, 0x0281B02Dul, 0x04A286BAul, 0x054F7181ul, 0x036C4716ul, 0x008B27C0ul, 0x06A81157ul, 
    0x02F8AD6Dul, 0x04DB9BFAul, 0x073CFB2Cul, 0x011FCDBBul, 0x00F23A80ul, 0x06D10C17ul, 0x05366CC1ul, 0x03155A56ul, 
    0x06ED82B7ul, 0x00CEB420ul, 0x0329D4F6ul, 0x050AE261ul, 0x04E7155Aul, 0x02C423CDul, 0x0123431Bul, 0x0700758Cul, 
    0x06A1936Cul, 0x0082A5FBul, 0x0365C52Dul, 0x0546F3BAul, 0x04AB0481ul, 0x02883216ul, 0x016F52C0ul, 0x074C6457ul, 
    0x02B4BCB6ul, 0x04978A21ul, 0x0770EAF7ul, 0x0153DC60ul, 0x00BE2B5Bul, 0x069D1DCCul, 0x057A7D1Aul, 0x03594B8Dul, 
    0x0709F7B7ul, 0x012AC120ul, 0x02CDA1F6ul, 0x04EE9761ul, 0x0503605Aul, 0x032056CDul, 0x00C7361Bul, 0x06E4008Cul, 
    0x031CD86Dul, 0x053FEEFAul, 0x06D88E2Cul, 0x00FBB8BBul, 0x01164F80ul, 0x07357917ul, 0x04D219C1ul, 0x02F12F56ul, 
    0x05F15ADAul, 0x03D26C4Dul, 0x00350C9Bul, 0x06163A0Cul, 0x07FBCD37ul, 0x01D8FBA0ul, 0x023F9B76ul, 0x041CADE1ul, 
    0x01E47500ul, 0x07C74397ul, 0x04202341ul, 0x020315D6ul, 0x03EEE2EDul, 0x05CDD47Aul, 0x062AB4ACul, 0x0009823Bul, 
    0x04593E01ul, 0x027A0896ul, 0x019D6840ul, 0x07BE5ED7ul, 0x0653A9ECul, 0x00709F7Bul, 0x0397FFADul, 0x05B4C93Aul, 
    0x004C11DBul, 0x066F274Cul, 0x0588479Aul, 0x03AB710Dul, 0x02468636ul, 0x0465B0A1ul, 0x0782D077ul, 0x01A1E6E0ul, 
    0x04C11DB7ul, 0x02E22B20ul, 0x01054BF6ul, 0x07267D61ul, 0x06CB8A5Aul, 0x00E8BCCDul, 0x030FDC1Bul, 0x052CEA8Cul, 
    0x00D4326Dul, 0x06F704FAul, 0x0510642Cul, 0x033352BBul, 0x02DEA580ul, 0x04FD9317ul, 0x071AF3C1ul, 0x0139C556ul, 
    0x0569796Cul, 0x034A4FFBul, 0x00AD2F2Dul, 0x068E19BAul, 0x0763EE81ul, 0x0140D816ul, 0x02A7B8C0ul, 0x04848E57ul, 
    0x017C56B6ul, 0x075F6021ul, 0x04B800F7ul, 0x029B3660ul, 0x0376C15Bul, 0x0555F7CCul, 0x06B2971Aul, 0x0091A18Dul, 
    0x0791D401ul, 0x01B2E296ul, 0x02558240ul, 0x0476B4D7ul, 0x059B43ECul, 0x03B8757Bul, 0x005F15ADul, 0x067C233Aul, 
    0x0384FBDBul, 0x05A7CD4Cul, 0x0640AD9Aul, 0x00639B0Dul, 0x018E6C36ul, 0x07AD5AA1ul, 0x044A3A77ul, 0x02690CE0ul, 
    0x0639B0DAul, 0x001A864Dul, 0x03FDE69Bul, 0x05DED00Cul, 0x04332737ul, 0x021011A0ul, 0x01F77176ul, 0x07D447E1ul, 
    0x022C9F00ul, 0x040FA997ul, 0x07E8C941ul, 0x01CBFFD6ul, 0x002608EDul, 0x06053E7Aul, 0x05E25EACul, 0x03C1683Bul, 
    0x02608EDBul, 0x0443B84Cul, 0x07A4D89Aul, 0x0187EE0Dul, 0x006A1936ul, 0x06492FA1ul, 0x05AE4F77ul, 0x038D79E0ul, 
    0x0675A101ul, 0x00569796ul, 0x03B1F740ul, 0x0592C1D7ul, 0x047F36ECul, 0x025C007Bul, 0x01BB60ADul, 0x0798563Aul, 
    0x03C8EA00ul, 0x05EBDC97ul, 0x060CBC41ul, 0x002F8AD6ul, 0x01C27DEDul, 0x07E14B7Aul, 0x04062BACul, 0x02251D3Bul, 
    0x07DDC5DAul, 0x01FEF34Dul, 0x0219939Bul, 0x043AA50Cul, 0x05D75237ul, 0x03F464A0ul, 0x00130476ul, 0x063032E1ul, 
    0x0130476Dul, 0x071371FAul, 0x04F4112Cul, 0x02D727BBul, 0x033AD080ul, 0x0519E617ul, 0x06FE86C1ul, 0x00DDB056ul, 
    0x052568B7ul, 0x03065E20ul, 0x00E13EF6ul, 0x06C20861ul, 0x072FFF5Aul, 0x010CC9CDul, 0x02EBA91Bul, 0x04C89F8Cul, 
    0x009823B6ul, 0x06BB1521ul, 0x055C75F7ul, 0x037F4360ul, 0x0292B45Bul, 0x04B182CCul, 0x0756E21Aul, 0x0175D48Dul, 
    0x048D0C6Cul, 0x02AE3AFBul, 0x01495A2Dul, 0x076A6CBAul, 0x06879B81ul, 0x00A4AD16ul, 0x0343CDC0ul, 0x0560FB57ul

};

#else
const uint16 Cal_Crc16Tab[256] = 
{
    0x0000u, 0x1021u, 0x2042u, 0x3063u, 0x4084u, 0x50a5u, 0x60c6u, 0x70e7u,
    0x8108u, 0x9129u, 0xa14au, 0xb16bu, 0xc18cu, 0xd1adu, 0xe1ceu, 0xf1efu,
    0x1231u, 0x0210u, 0x3273u, 0x2252u, 0x52b5u, 0x4294u, 0x72f7u, 0x62d6u,
    0x9339u, 0x8318u, 0xb37bu, 0xa35au, 0xd3bdu, 0xc39cu, 0xf3ffu, 0xe3deu,
    0x2462u, 0x3443u, 0x0420u, 0x1401u, 0x64e6u, 0x74c7u, 0x44a4u, 0x5485u,
    0xa56au, 0xb54bu, 0x8528u, 0x9509u, 0xe5eeu, 0xf5cfu, 0xc5acu, 0xd58du,
    0x3653u, 0x2672u, 0x1611u, 0x0630u, 0x76d7u, 0x66f6u, 0x5695u, 0x46b4u,
    0xb75bu, 0xa77au, 0x9719u, 0x8738u, 0xf7dfu, 0xe7feu, 0xd79du, 0xc7bcu,
    0x48c4u, 0x58e5u, 0x6886u, 0x78a7u, 0x0840u, 0x1861u, 0x2802u, 0x3823u,
    0xc9ccu, 0xd9edu, 0xe98eu, 0xf9afu, 0x8948u, 0x9969u, 0xa90au, 0xb92bu,
    0x5af5u, 0x4ad4u, 0x7ab7u, 0x6a96u, 0x1a71u, 0x0a50u, 0x3a33u, 0x2a12u,
    0xdbfdu, 0xcbdcu, 0xfbbfu, 0xeb9eu, 0x9b79u, 0x8b58u, 0xbb3bu, 0xab1au,
    0x6ca6u, 0x7c87u, 0x4ce4u, 0x5cc5u, 0x2c22u, 0x3c03u, 0x0c60u, 0x1c41u,
    0xedaeu, 0xfd8fu, 0xcdecu, 0xddcdu, 0xad2au, 0xbd0bu, 0x8d68u, 0x9d49u,
    0x7e97u, 0x6eb6u, 0x5ed5u, 0x4ef4u, 0x3e13u, 0x2e32u, 0x1e51u, 0x0e70u,
    0xff9fu, 0xefbeu, 0xdfddu, 0xcffcu, 0xbf1bu, 0xaf3au, 0x9f59u, 0x8f78u,
    0x9188u, 0x81a9u, 0xb1cau, 0xa1ebu, 0xd10cu, 0xc12du, 0xf14eu, 0xe16fu,
    0x1080u, 0x00a1u, 0x30c2u, 0x20e3u, 0x5004u, 0x4025u, 0x7046u, 0x6067u,
    0x83b9u, 0x9398u, 0xa3fbu, 0xb3dau, 0xc33du, 0xd31cu, 0xe37fu, 0xf35eu,
    0x02b1u, 0x1290u, 0x22f3u, 0x32d2u, 0x4235u, 0x5214u, 0x6277u, 0x7256u,
    0xb5eau, 0xa5cbu, 0x95a8u, 0x8589u, 0xf56eu, 0xe54fu, 0xd52cu, 0xc50du,
    0x34e2u, 0x24c3u, 0x14a0u, 0x0481u, 0x7466u, 0x6447u, 0x5424u, 0x4405u,
    0xa7dbu, 0xb7fau, 0x8799u, 0x97b8u, 0xe75fu, 0xf77eu, 0xc71du, 0xd73cu,
    0x26d3u, 0x36f2u, 0x0691u, 0x16b0u, 0x6657u, 0x7676u, 0x4615u, 0x5634u,
    0xd94cu, 0xc96du, 0xf90eu, 0xe92fu, 0x99c8u, 0x89e9u, 0xb98au, 0xa9abu,
    0x5844u, 0x4865u, 0x7806u, 0x6827u, 0x18c0u, 0x08e1u, 0x3882u, 0x28a3u,
    0xcb7du, 0xdb5cu, 0xeb3fu, 0xfb1eu, 0x8bf9u, 0x9bd8u, 0xabbbu, 0xbb9au,
    0x4a75u, 0x5a54u, 0x6a37u, 0x7a16u, 0x0af1u, 0x1ad0u, 0x2ab3u, 0x3a92u,
    0xfd2eu, 0xed0fu, 0xdd6cu, 0xcd4du, 0xbdaau, 0xad8bu, 0x9de8u, 0x8dc9u,
    0x7c26u, 0x6c07u, 0x5c64u, 0x4c45u, 0x3ca2u, 0x2c83u, 0x1ce0u, 0x0cc1u,
    0xef1fu, 0xff3eu, 0xcf5du, 0xdf7cu, 0xaf9bu, 0xbfbau, 0x8fd9u, 0x9ff8u,
    0x6e17u, 0x7e36u, 0x4e55u, 0x5e74u, 0x2e93u, 0x3eb2u, 0x0ed1u, 0x1ef0u
};
#endif

/*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/

/******************************************************************************/
/**
 * @brief               <CRC32 initialize>
 * 
 * <This Funtion Initializes the CRC algorithm> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <curCrc (IN/OUT)>
 * @return              <NONE>    
 */
/******************************************************************************/
void Cal_CrcInit(SecM_CRCType *curCrc)
{
#if (CAL_CRC32 == CAL_METHOD)
    *curCrc = 0xFFFFFFFFuL;
#else
    /* CRC16 */
    *curCrc = 0xFFFFu;
#endif 
    return;
}

/******************************************************************************/
/**
 * @brief               <CRC32 compute>
 * 
 * <This Funtion computes the CRC value> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <buf (IN), size (IN)>
 * @param[out]          <NONE>
 * @param[in/out]       <curCrc (IN/OUT)>
 * @return              <NONE>    
 */
/******************************************************************************/
void Cal_CrcCal(SecM_CRCType *curCrc,
    const uint8 *buf,
    const uint32 size)
{
    uint32 i;
    
#if (CAL_CRC32 == CAL_METHOD)
    for (i = 0; i < size; i++)
    {

        *curCrc = Cal_Crc32Tab[(*curCrc ^ (uint32)buf[i]) & 0xffu]
            ^ (*curCrc >> 8);
    }

#else
    /* CRC16 */
    for (i = 0; i < size; i++)
    {
        *curCrc = Cal_Crc16Tab[(*curCrc >> 8 ^ (uint16)buf[i]) & 0xffu] 
            ^ (*curCrc << 8);
    }    
#endif
 
    return;
}


/******************************************************************************/
/**
 * @brief               <CRC32 finish>
 * 
 * <This Funtion finish the CRC compute.> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <curCrc (IN/OUT)>
 * @return              <NONE>    
 */
/******************************************************************************/
void Cal_CrcFinalize(SecM_CRCType *curCrc)
{
#if (CAL_CRC32 == CAL_METHOD)
    *curCrc ^= 0xFFFFFFFFuL;
#endif 
    
    return;
}

/*=======[E N D   O F   F I L E]==============================================*/

