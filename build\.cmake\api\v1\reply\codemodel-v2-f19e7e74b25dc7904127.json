{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 2]}, {"build": "ESC30_SAIC_AP31_Boot", "jsonFile": "directory-ESC30_SAIC_AP31_Boot-Debug-849dfa35dbaa7b444737.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "ESC30_SAIC_AP31_Boot", "targetIndexes": [1]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1], "name": "AP31_ESC_PBL", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "AP31_ESC_PBL_IDE::@6890427a1f51a3e7e1df", "jsonFile": "target-AP31_ESC_PBL_IDE-Debug-785086cd4e77a70f4861.json", "name": "AP31_ESC_PBL_IDE", "projectIndex": 0}, {"directoryIndex": 1, "id": "ESC30_SAIC_AP31_Boot_LIB::@236c4dc0fae44115f3a3", "jsonFile": "target-ESC30_SAIC_AP31_Boot_LIB-Debug-a8fbd870b1910deca28f.json", "name": "ESC30_SAIC_AP31_Boot_LIB", "projectIndex": 0}, {"directoryIndex": 0, "id": "tasking_info::@6890427a1f51a3e7e1df", "jsonFile": "target-tasking_info-Debug-c53c288f1965f441542f.json", "name": "tasking_info", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/yeah/new requirement/AP31BOOT20250725/SBL/ESC30_SAIC_AP31_BootV1.03.01/build", "source": "D:/yeah/new requirement/AP31BOOT20250725/SBL/ESC30_SAIC_AP31_BootV1.03.01"}, "version": {"major": 2, "minor": 6}}