<?xml version='1.0'?>
<datamodel version="3.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/08/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/08/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd" >
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Spi" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Spi" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/AURIX/Spi"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuildSelectable">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="SpiGeneral" type="IDENTIFIABLE">
                <d:var name="SpiDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiVersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiChannelBuffersAllowed" type="INTEGER" value="1"/>
                <d:var name="SpiHwStatusApi" type="BOOLEAN" value="true"/>
                <d:var name="SpiLevelDelivered" type="INTEGER" value="1"/>
                <d:var name="SpiSupportConcurrentSyncTransmit" type="BOOLEAN" 
                       value="true"/>
                <d:var name="SpiInterruptibleSeqAllowed" type="BOOLEAN" 
                       value="true"/>
                <d:var name="SpiCancelApi" type="BOOLEAN" value="true"/>
                <d:var name="SpiMaxJobTriggerQueueLength" type="INTEGER" 
                       value="32"/>
                <d:var name="SpiPBFixedAddress" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiDebugSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiAsyncParallelTransmit" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiBaudrateConfigAtRuntime" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiResetSfrAtInit" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiSyncTransmitTimeoutDuration" type="INTEGER" 
                       value="1048575">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiSlaveSelectRef" type="ENUMERATION" 
                       value="SPI_SLAVE_UNUSED">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiSlaveFlipTransferStart" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiSafetyMasterRef" type="ENUMERATION" 
                       value="QSPI0">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@DEF</a:v>
                    <a:v>@CALC</a:v>
                  </a:a>
                </d:var>
                <d:lst name="SpiMainFunctionPeriod"/>
                <d:var name="SpiRunningInUser0Mode" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiUserModeInitApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiUserModeDeInitApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiUserModeRuntimeApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="SpiSafety" type="IDENTIFIABLE">
                <d:var name="SpiSafetyEnable" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SpiInitCheckApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:lst name="SpiDriver" type="MAP">
                <d:ctr name="SpiDriver_0" type="IDENTIFIABLE">
                  <d:lst name="SpiMaxChannel"/>
                  <d:lst name="SpiMaxJob"/>
                  <d:lst name="SpiMaxSequence"/>
                  <d:ref name="SpiSystemClock" type="REFERENCE" 
                         value="ASPath:/Mcu/Mcu/McuModuleConfiguration_0/McuClockSettingConfig_0/McuClockReferencePoint"/>
                  <d:lst name="SpiChannel" type="MAP">
                    <d:ctr name="SpiChannel_EYEQ_Master" type="IDENTIFIABLE">
                      <d:var name="SpiChannelId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiChannelType" type="ENUMERATION" value="EB">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiDataWidth" type="INTEGER" value="32"/>
                      <d:var name="SpiDefaultData" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiEbMaxLength" type="INTEGER" value="128"/>
                      <d:var name="SpiIbNBuffers" type="INTEGER" value="10">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiTransferStart" type="ENUMERATION" 
                             value="LSB">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiSafetyBufferMarker" type="INTEGER" 
                             value="165">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="SpiChannel_Micron_Master" type="IDENTIFIABLE">
                      <d:var name="SpiChannelId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiChannelType" type="ENUMERATION" value="EB">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiDataWidth" type="INTEGER" value="8">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiDefaultData" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiEbMaxLength" type="INTEGER" value="511">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiIbNBuffers" type="INTEGER" value="10">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiTransferStart" type="ENUMERATION" 
                             value="MSB"/>
                      <d:var name="SpiSafetyBufferMarker" type="INTEGER" 
                             value="165">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:lst>
                  <d:lst name="SpiExternalDevice" type="MAP">
                    <d:ctr name="SpiExternalDevice_EYEQ4_Master" 
                           type="IDENTIFIABLE">
                      <d:var name="SpiBaudrate" type="FLOAT" value="1.0E7"/>
                      <d:var name="SpiCsIdentifier" type="STRING" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiCsPolarity" type="ENUMERATION" value="LOW">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiDataShiftEdge" type="ENUMERATION" 
                             value="LEADING">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiShiftClockIdleLevel" type="ENUMERATION" 
                             value="LOW">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiAutoCalcBaudParams" type="BOOLEAN" 
                             value="true"/>
                      <d:var name="SpiQSpiParamTq" type="INTEGER" value="2">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamQ" type="INTEGER" value="10">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamA" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamB" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamC" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiAutoCalcDelayParams" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiTimeClk2Cs" type="FLOAT" value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiTrailingTime" type="FLOAT" value="1.0E-7">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiIdleTime" type="FLOAT" value="1.0E-7">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamIPre" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamIdle" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamLPre" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamLead" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamTPre" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamTrail" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiEnableCs" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiCsSelection" type="ENUMERATION" 
                             value="CS_VIA_GPIO"/>
                      <d:var name="SpiParitySupport" type="ENUMERATION" 
                             value="Unused">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ctr name="SpiHwUnit" type="IDENTIFIABLE">
                        <d:var name="SpiAssignedHwModule" type="ENUMERATION" 
                               value="QSPI0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="SpiAssignedHwChannel" type="ENUMERATION" 
                               value="Channel6"/>
                      </d:ctr>
                      <d:ctr name="SpiCsGpio" type="IDENTIFIABLE">
                        <d:var name="SpiCsGpioPortSelection" type="INTEGER" 
                               value="20"/>
                        <d:var name="SpiCsGpioPinSelection" type="INTEGER" 
                               value="10"/>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="SpiExternalDevice_Micron_Master" 
                           type="IDENTIFIABLE">
                      <d:var name="SpiBaudrate" type="FLOAT" value="9600.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiCsIdentifier" type="STRING" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiCsPolarity" type="ENUMERATION" value="LOW">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiDataShiftEdge" type="ENUMERATION" 
                             value="TRAILING"/>
                      <d:var name="SpiShiftClockIdleLevel" type="ENUMERATION" 
                             value="LOW">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiAutoCalcBaudParams" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamTq" type="INTEGER" value="1"/>
                      <d:var name="SpiQSpiParamQ" type="INTEGER" value="0"/>
                      <d:var name="SpiQSpiParamA" type="INTEGER" value="2"/>
                      <d:var name="SpiQSpiParamB" type="INTEGER" value="1"/>
                      <d:var name="SpiQSpiParamC" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiAutoCalcDelayParams" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiTimeClk2Cs" type="FLOAT" value="0.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiTrailingTime" type="FLOAT" value="1.0E-7">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiIdleTime" type="FLOAT" value="1.0E-7">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamIPre" type="INTEGER" value="2"/>
                      <d:var name="SpiQSpiParamIdle" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamLPre" type="INTEGER" value="2"/>
                      <d:var name="SpiQSpiParamLead" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiQSpiParamTPre" type="INTEGER" value="2"/>
                      <d:var name="SpiQSpiParamTrail" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiEnableCs" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiCsSelection" type="ENUMERATION" 
                             value="CS_VIA_PERIPHERAL_ENGINE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiParitySupport" type="ENUMERATION" 
                             value="Unused">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ctr name="SpiHwUnit" type="IDENTIFIABLE">
                        <d:var name="SpiAssignedHwModule" type="ENUMERATION" 
                               value="QSPI1"/>
                        <d:var name="SpiAssignedHwChannel" type="ENUMERATION" 
                               value="Channel10"/>
                      </d:ctr>
                      <d:ctr name="SpiCsGpio" type="IDENTIFIABLE">
                        <d:var name="SpiCsGpioPortSelection" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="SpiCsGpioPinSelection" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:lst>
                  <d:lst name="SpiJob" type="MAP">
                    <d:ctr name="SpiJob_EYEQ_Master" type="IDENTIFIABLE">
                      <d:var name="SpiJobEndNotification" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiJobId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiJobPriority" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiHwUnitSynchronous" type="ENUMERATION" 
                             value="Asynchronous">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiChannelBasedCS" type="ENUMERATION" 
                             value="Enabled"/>
                      <d:ref name="SpiDeviceAssignment" type="REFERENCE" 
                             value="ASPath:/Spi/Spi/SpiDriver_0/SpiExternalDevice_EYEQ4_Master"/>
                      <d:lst name="SpiChannelList" type="MAP">
                        <d:ctr name="SpiChannelList_0" type="IDENTIFIABLE">
                          <d:var name="SpiChannelIndex" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:ref name="SpiChannelAssignment" type="REFERENCE" 
                                 value="ASPath:/Spi/Spi/SpiDriver_0/SpiChannel_EYEQ_Master"/>
                        </d:ctr>
                      </d:lst>
                    </d:ctr>
                    <d:ctr name="SpiJob_Micron_Master" type="IDENTIFIABLE">
                      <d:var name="SpiJobEndNotification" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiJobId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiJobPriority" type="INTEGER" value="0"/>
                      <d:var name="SpiHwUnitSynchronous" type="ENUMERATION" 
                             value="Asynchronous">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiChannelBasedCS" type="ENUMERATION" 
                             value="Disabled">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="SpiDeviceAssignment" type="REFERENCE" 
                             value="ASPath:/Spi/Spi/SpiDriver_0/SpiExternalDevice_Micron_Master"/>
                      <d:lst name="SpiChannelList" type="MAP">
                        <d:ctr name="SpiChannelList_0" type="IDENTIFIABLE">
                          <d:var name="SpiChannelIndex" type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:ref name="SpiChannelAssignment" type="REFERENCE" 
                                 value="ASPath:/Spi/Spi/SpiDriver_0/SpiChannel_Micron_Master"/>
                        </d:ctr>
                      </d:lst>
                    </d:ctr>
                  </d:lst>
                  <d:lst name="SpiSequence" type="MAP">
                    <d:ctr name="SpiSequence_EYEQ_Master" type="IDENTIFIABLE">
                      <d:var name="SpiInterruptibleSequence" type="BOOLEAN" 
                             value="false"/>
                      <d:var name="SpiSeqEndNotification" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiSequenceId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:lst name="SpiJobAssignment">
                        <d:ref type="REFERENCE" 
                               value="ASPath:/Spi/Spi/SpiDriver_0/SpiJob_EYEQ_Master"/>
                      </d:lst>
                    </d:ctr>
                    <d:ctr name="SpiSequence_Micron_Master" type="IDENTIFIABLE">
                      <d:var name="SpiInterruptibleSequence" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiSeqEndNotification" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="SpiSequenceId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:lst name="SpiJobAssignment">
                        <d:ref type="REFERENCE" 
                               value="ASPath:/Spi/Spi/SpiDriver_0/SpiJob_Micron_Master"/>
                      </d:lst>
                    </d:ctr>
                  </d:lst>
                  <d:lst name="SpiDemEventParameterRefs" type="MAP"/>
                </d:ctr>
              </d:lst>
              <d:ctr name="SpiHwConfiguration" type="IDENTIFIABLE">
                <d:ctr name="SpiHwConfigurationQspi0" type="IDENTIFIABLE">
                  <d:ctr name="SpiClockConfiguration" type="IDENTIFIABLE">
                    <d:var name="SpiSleepEnable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="SpiHwPinConfiguration" type="IDENTIFIABLE">
                    <d:var name="SpiHWPinMISO" type="ENUMERATION" 
                           value="MRST0A_PORT20_PIN12">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="SPISlaveHwPinMOSI" type="ENUMERATION" 
                           value="MTSR0A_PORT20_PIN14">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="SPISlaveHwPinSCLK" type="ENUMERATION" 
                           value="SCLK0A_PORT20_PIN11">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="SpiHwDmaConfiguration" type="IDENTIFIABLE">
                    <d:ref name="SpiHwDmaChannelReceptionRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration_0/DmaConfiguration_0/DmaChannel_2"/>
                    <d:ref name="SpiHwDmaChannelTransmissionRef" 
                           type="REFERENCE" value=""/>
                  </d:ctr>
                </d:ctr>
                <d:ctr name="SpiHwConfigurationQspi1" type="IDENTIFIABLE">
                  <d:ctr name="SpiClockConfiguration" type="IDENTIFIABLE">
                    <d:var name="SpiSleepEnable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="SpiHwPinConfiguration" type="IDENTIFIABLE">
                    <d:var name="SpiHWPinMISO" type="ENUMERATION" 
                           value="MRST1B_PORT11_PIN3"/>
                    <d:var name="SPISlaveHwPinMOSI" type="ENUMERATION" 
                           value="MTSR1A_PORT10_PIN3">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="SPISlaveHwPinSCLK" type="ENUMERATION" 
                           value="SCLK1A_PORT10_PIN2">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="SpiHwDmaConfiguration" type="IDENTIFIABLE">
                    <d:ref name="SpiHwDmaChannelReceptionRef" type="REFERENCE" 
                           value=""/>
                    <d:ref name="SpiHwDmaChannelTransmissionRef" 
                           type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration_0/DmaConfiguration_0/DmaChannel_4"/>
                  </d:ctr>
                </d:ctr>
                <d:ctr name="SpiHwConfigurationQspi2" type="IDENTIFIABLE">
                  <d:ctr name="SpiClockConfiguration" type="IDENTIFIABLE">
                    <d:var name="SpiSleepEnable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="SpiHwPinConfiguration" type="IDENTIFIABLE">
                    <d:var name="SpiHWPinMISO" type="ENUMERATION" 
                           value="MRST2E_PORT15_PIN2"/>
                    <d:var name="SPISlaveHwPinMOSI" type="ENUMERATION" 
                           value="MTSR2A_PORT15_PIN5">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="SPISlaveHwPinSCLK" type="ENUMERATION" 
                           value="SCLK2A_PORT15_PIN3">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="SpiHwDmaConfiguration" type="IDENTIFIABLE">
                    <d:ref name="SpiHwDmaChannelReceptionRef" type="REFERENCE" >
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ref name="SpiHwDmaChannelTransmissionRef" 
                           type="REFERENCE" >
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                  </d:ctr>
                </d:ctr>
                <d:ctr name="SpiHwConfigurationQspi3" type="IDENTIFIABLE">
                  <d:ctr name="SpiClockConfiguration" type="IDENTIFIABLE">
                    <d:var name="SpiSleepEnable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="SpiHwPinConfiguration" type="IDENTIFIABLE">
                    <d:var name="SpiHWPinMISO" type="ENUMERATION" 
                           value="MRST3A_PORT02_PIN5">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="SPISlaveHwPinMOSI" type="ENUMERATION" 
                           value="MTSR3A_PORT2_PIN6">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="SPISlaveHwPinSCLK" type="ENUMERATION" 
                           value="SCLK3A_PORT2_PIN7">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="SpiHwDmaConfiguration" type="IDENTIFIABLE">
                    <d:ref name="SpiHwDmaChannelReceptionRef" type="REFERENCE" >
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ref name="SpiHwDmaChannelTransmissionRef" 
                           type="REFERENCE" >
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                  </d:ctr>
                </d:ctr>
                <d:ctr name="SpiHwConfigurationQspi4" type="IDENTIFIABLE">
                  <d:ctr name="SpiClockConfiguration" type="IDENTIFIABLE">
                    <d:var name="SpiSleepEnable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="SpiHwPinConfiguration" type="IDENTIFIABLE">
                    <d:var name="SpiHWPinMISO" type="ENUMERATION" 
                           value="MRST4A_PORT33_PIN13">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="SPISlaveHwPinMOSI" type="ENUMERATION" 
                           value="MTSR4A_PORT33_PIN12">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="SPISlaveHwPinSCLK" type="ENUMERATION" 
                           value="SCLK4A_PORT33_PIN11">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="SpiHwDmaConfiguration" type="IDENTIFIABLE">
                    <d:ref name="SpiHwDmaChannelReceptionRef" type="REFERENCE" >
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ref name="SpiHwDmaChannelTransmissionRef" 
                           type="REFERENCE" >
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                  </d:ctr>
                </d:ctr>
                <d:ctr name="SpiHwConfigurationQspi5" type="IDENTIFIABLE">
                  <d:ctr name="SpiClockConfiguration" type="IDENTIFIABLE">
                    <d:var name="SpiSleepEnable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="SpiHwPinConfiguration" type="IDENTIFIABLE">
                    <d:var name="SpiHWPinMISO" type="ENUMERATION" 
                           value="MRST5A_PORT15_PIN10">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="SPISlaveHwPinMOSI" type="ENUMERATION" 
                           value="MTSR5A_PORT15_PIN14">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="SPISlaveHwPinSCLK" type="ENUMERATION" 
                           value="SCLK5A_PORT15_PIN15">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="SpiHwDmaConfiguration" type="IDENTIFIABLE">
                    <d:ref name="SpiHwDmaChannelReceptionRef" type="REFERENCE" >
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ref name="SpiHwDmaChannelTransmissionRef" 
                           type="REFERENCE" >
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                  </d:ctr>
                </d:ctr>
              </d:ctr>
              <d:ctr name="SpiPublishedInformation" type="IDENTIFIABLE">
                <d:var name="SpiMaxHwUnit" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArMajorVersion" type="INTEGER" value="3"/>
                <d:var name="ArMinorVersion" type="INTEGER" value="2"/>
                <d:var name="ArPatchVersion" type="INTEGER" value="0"/>
                <d:var name="SwMajorVersion" type="INTEGER" value="4"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="9"/>
                <d:var name="SwPatchVersion" type="INTEGER" value="2"/>
                <d:var name="ModuleId" type="INTEGER" value="83">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="17">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="Release" type="STRING" value="_TRICORE_TC233">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
