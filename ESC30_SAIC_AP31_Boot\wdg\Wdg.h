/*============================================================================*/
/*  Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *
 *  @file       <Wdg.h>
 *  @brief      <This is Wdg header file>
 *
 *  <Compiler: TASKING3.5    MCU:TC1782>
 *
 *  <AUTHOR>
 *  @date       <2014-5-30>
 */
/*============================================================================*/
/******************************************************************************/
#ifndef WDG_H
#define WDG_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
 *  V1.0.0       20140530  jianan.liu  Initial version
 */
/*============================================================================*/


/*=======[I N C L U D E S]====================================================*/
#include "Std_Types.h"
#include "Wdg_Cfg.h"
#define IFXSCUWDT_RESET_PASSWORD (0x3CU)

/*=======[E X T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
/******************************************************************************/
/*
 * Brief               <Initializes the WDG driver>
 * Sync/Async          <Synchronous>
 * Reentrancy          <Non-Reentrant>
 * Param-Name[in]      <None>
 * Param-Name[out]     <None>
 * Param-Name[in/out]  <None>
 * Return              <None>
 * PreCondition        <None>
 * CallByAPI           <APIName>
 */
/******************************************************************************/
extern void Wdg_Start(void);
extern void  Wdg_Stop(void);
/******************************************************************************/
/*
 * Brief               <This Funtion Triggers the Watchdog Hardware>
 * Sync/Async          <Synchronous>
 * Reentrancy          <Non-Reentrant>
 * Param-Name[in]      <None>
 * Param-Name[out]     <None>
 * Param-Name[in/out]  <None>
 * Return              <None>
 * PreCondition        <None>
 * CallByAPI           <APIName>
 */
/******************************************************************************/
extern void Wdg_Kick(void);
extern void IfxScuWdt_clearCpuEndinit(uint16 password);
extern void IfxScuWdt_setCpuEndinit(uint16 password);

/******************************************************************************/
/*
 * Brief               <This Funtion resets the Wdg module>
 * Sync/Async          <Synchronous>
 * Reentrancy          <Non-Reentrant>
 * Param-Name[in]      <None>
 * Param-Name[out]     <None>
 * Param-Name[in/out]  <None>
 * Return              <None>
 * PreCondition        <None>
 * CallByAPI           <APIName>
 */
/******************************************************************************/
extern void Wdg_SystemReset(void);

#endif
