# CMAKE generated file: DO NOT EDIT!
# Generated by "MinG<PERSON> Makefiles" Generator, CMake Version 3.28

# compile C with D:/mingw64/bin/gcc.exe
C_DEFINES = -DADC_ENABLED -DAPP_START_ADDRESS=0xA0080000 -DAUTOSAR_VERSION_403 -DBOOT_MODE_NORMAL=0xFF -DBOOT_MODE_PBL=0x6A -DBOOT_MODE_SBL2=0x3A -DBOOT_MODE_SBL=0x2A -DCAN_ENABLED -DCAN_TP_ENABLED -DCPU0_DMI_DSPR=0x70000100 -DCPU0_PMI_PSPR=0x70100100 -DCRC_CHECK_ENABLED -DDET_ENABLED -DDIAGNOSTIC_ENABLED -DDID_F186_ENABLED -DDIO_ENABLED -DDMA_ENABLED -DEXTERNAL_FLASH_ENABLED -DE_NOT_OK=1U -DE_OK=0U -D<PERSON><PERSON>H_DRIVER_ENABLED -DFL_BOOT_MODE_ADDRESS=0x70101FF0 -DGTM_ENABLED -DMCU_ENABLED -DOFF=0U -DON=1U -DPBL_SIZE=0x0000FFE0 -DPBL_START_ADDRESS=0x80000020 -DPBL_VERSION=103 -DPORT_ENABLED -DPRIMARY_BOOTLOADER -DSBL_CRC_ADDRESS=0x80014000 -DSBL_START_ADDRESS=0x80018200 -DSBL_VALID_FLAG_ADDRESS=0x80010000 -DSECURE_BOOT_ENABLED -DSPI_ENABLED -DSTATUSTYPEDEFINED -DSTD_ACTIVE=1U -DSTD_HIGH=1U -DSTD_IDLE=0U -DSTD_LOW=0U -DSTD_OFF=0U -DSTD_ON=1U -DUART_ENABLED -DUDS_ENABLED -DVERSION_INFO_API_ENABLED -DWDG_ENABLED -D_TASKING_C_TRICORE_=1 -D__A0_THRESHOLD__=0 -D__A1_THRESHOLD__=0 -D__ALIGNOF_INT__=4 -D__ALIGNOF_POINTER__=4 -D__BITFIELD_MSB_FIRST__=0 -D__BYTE_ORDER__=1234 -D__CORE_TC23X__ -D__CPU__=tc23x -D__DEBUG__ -D__LITTLE_ENDIAN__ -D__NEAR_THRESHOLD__=0 -D__OPTIMIZE__=2 -D__SIZEOF_DOUBLE__=8 -D__SIZEOF_FLOAT__=4 -D__SIZEOF_INT__=4 -D__SIZEOF_LONG__=4 -D__SIZEOF_POINTER__=4 -D__SOURCE__ -D__STDC_VERSION__=199901L -D__STDC__=1 -D__TASKING__ -D__TRADEOFF__=4 -D__TRICORE_HAVE_BIV__ -D__TRICORE_HAVE_CSFR__ -D__TRICORE_HAVE_DCACHE__ -D__TRICORE_HAVE_ENDINIT__ -D__TRICORE_HAVE_FPU__ -D__TRICORE_HAVE_ICR__ -D__TRICORE_HAVE_MPU__ -D__TRICORE_HAVE_PCACHE__ -D__TRICORE_NAME__=0x2300 -D__TRICORE__ -D__VERSION__=42020

C_INCLUDES = @CMakeFiles/ESC30_SAIC_AP31_Boot_LIB.dir/includes_C.rsp

C_FLAGS = -g -std=gnu99 -O2 -malign-functions=4 -std=c99 -Wall -Wextra -Wno-unused-parameter -Wno-unused-variable

