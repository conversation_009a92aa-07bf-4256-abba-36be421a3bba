/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <FL_Cfg.h>
 *  @brief      <Flash Loader Configuration file>
 *              Contained Macros of Mode,Timer and Address.
 *
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *
 *  <AUTHOR>
 *  @date       <2012-12-27>
 */
/*============================================================================*/
#ifndef FL_CFG_H
#define FL_CFG_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20121227    Gary       Initial version
 */
/*============================================================================*/

#include "Std_Types.h"
#define SIGNBLOCKINDEX     0U
#define APPBLOCKINDEX       1U

// #define APPBLOCKINDEX     0U
// #define CALDATABLOCKINDEX 1U
// #define EXTFLSBLOCKINDEX  2U

#define NO_INTEGRITY_ERR         0X00
#define APP_INTEGRITY_ERR        0X01
#define NETCFG_INTEGRITY_ERR     0X02
#define CAL_INTEGRITY_ERR        0X04
#define EXTFLS_INTEGRITY_ERR     0X10

#define NO_CPB_ERR      0X00
#define FBL_APP_CPB_ERR      0X01
#define CAL_APP_CPB_ERR      0X04
#define EYEQ_APP_CPB_ERR     0X10
/*=======[M A C R O S]========================================================*/
/** The physical memory location of boot request flag. LOCAL address*/
/* @type:uint32 range:0x00000000~0xFFFFFFFF note:NONE */
#define FL_BOOT_MODE  0x70101FF0 //0xc0001ffduL//0x00003FF0uL

/** The physical memory location of application software update flag. LOCAL address*/
/* @type:uint32 range:0x00000000~0xFFFFFFFF note:NONE */
#define FL_APPL_UPDATE 0xc0001ffeuL//0x00003FF1uL

/** The physical memory location of boot default session from prog. flag. LOCAL address*/
/* @type:uint32 range:0x00000000~0xFFFFFFFF note:NONE */
#define FL_BOOT_DEFAULT 0xc0001fffuL//0x00003FF2uL
#define FL_SECERR_FLAG 0xc0001ffCuL//0x00003FF2uL
/** Sleep time count:if configered 0,then no sleep function. */
/* @type:uint16 range:0~65535 note:unit ms */
#define FL_SLEEP_TIMER  0

/** Totol num of program blocks. */
/* @type:uint8 range:1~255 note:reference to num of FL_BlkInfo */
#define FL_NUM_LOGICAL_BLOCKS   (uint8)3u //add-lihaonan

/** Maxmum of segment in one block. */
/* @type:uint8 range:1~65535 note:NONE */
#define FL_MAX_SEGMENTS (uint16)100u

/** Value for fill gap,if configered 0xFF,then not excute. */
/* @type:uint8 range:0x00~0xFF note:NONE */
#define FL_GAP_FILL_VALUE   0xFFu

/* @type:define range:NONE note:auto generate */
#if (FL_GAP_FILL_VALUE == 0xFFu)
#define FL_USE_GAP_FILL STD_OFF
#else
#define  FL_USE_GAP_FILL STD_ON
#endif

/** Start address of flash driver in RAM. LOCAL address */
/* @type:uint32 range:0x00000000~0xFFFFFFFF note:NONE */
#define FL_DEV_BASE_ADDRESS  0x70101000//0x3B00u

/** Length of flash driver in RAM. */
/* @type:uint32 range:0x00000000~0xFFFFFFFF note:NONE */
#define FL_DEV_SIZE 0x22A//0x4F0uL

/*
 ** not standard config parameter
 */
/** Application RCHW address */
/* @type:uint32 range:0x0000C000~0xFFFFFFFF note:NONE */
#define FL_APP_RCHW_ADDR    0xA000C000uL

/** timer delay in bootloader when ECU is startup */
/* @type:uint16 range:0~65535 note:unit ms */
#define FL_MODE_STAY_TIME    20

/** Length of finger print infomation */
/* @type:uint16 range:1~65535 note:NONE */
#define FL_FINGER_PRINT_LENGTH  9u
#define FL_VEH_PRINT_LENGTH  20u
#define FL_PN_PRINT_LENGTH  6u

#define FL_VIN_PRINT_LENGTH  17u

/** bootloader infomation global address in NVM */
/* @type:uint32 range:NONE note:auto generate */
#define FL_NVM_INFO_ADDRESS  0xA0050000 //0xA001C000uL

/** code flash size that should program once time */
/* @type:uint32 range:NONE note:auto generate */
#define FL_FLASH_ALIGN_SIZE    (uint32)0x00000020uL

#endif/* endof FL_CFG_H */

/*=======[E N D   O F   F I L E]==============================================*/

