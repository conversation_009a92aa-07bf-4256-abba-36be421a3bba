<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.tasking.config.ctc.abs.debug.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.tasking.config.ctc.abs.debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="com.tasking.managedbuilder.TASKING_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.tasking.managedbuilder.TASKING_SRECORD" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.tasking.managedbuilder.TskRegexErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="CSUV_PGM_FBL" buildArtefactType="com.tasking.ctc.buildArtefactType.elf" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=com.tasking.ctc.buildArtefactType.elf" cleanCommand="&quot;${PRODDIR}/bin/rm&quot; -rf" description="" errorParsers="com.tasking.managedbuilder.TskRegexErrorParser" id="com.tasking.config.ctc.abs.debug.**********" name="Debug" parent="com.tasking.config.ctc.abs.debug" postannouncebuildStep="" postbuildStep="" preannouncebuildStep="" prebuildStep="">
					<folderInfo id="com.tasking.config.ctc.abs.debug.**********." name="/" resourcePath="">
						<toolChain errorParsers="" id="com.tasking.ctc.abs.debug.133150114" name="TASKING VX-toolset for TriCore" superClass="com.tasking.ctc.abs.debug">
							<option id="com.tasking.ctc.pluginVersion.10710879" name="Plugin version" superClass="com.tasking.ctc.pluginVersion" value="*********" valueType="string"/>
							<option id="com.tasking.ctc.prodDir.1088900576" name="Product directory:" superClass="com.tasking.ctc.prodDir" value="${eclipse_home}/.." valueType="string"/>
							<option id="com.tasking.ctc.cpu.899452390" name="Processor:" superClass="com.tasking.ctc.cpu" value="userdef16x" valueType="string"/>
							<targetPlatform archList="all" binaryParser="com.tasking.managedbuilder.TASKING_ELF;com.tasking.managedbuilder.TASKING_SRECORD" id="com.tasking.ctc.platform.abs.debug.167330955" name="Debug" osList="" superClass="com.tasking.ctc.platform.abs.debug"/>
							<builder buildPath="${workspace_loc:/CSUV_PGM_FBL/Debug}" errorParsers="" id="com.tasking.ctc.builder.abs.debug.1638981673" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="TASKING TriCore Makefile generator" parallelBuildOn="true" parallelizationNumber="optimal" superClass="com.tasking.ctc.builder.abs.debug"/>
							<tool command="cctc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="com.tasking.managedbuilder.TskRegexErrorParser" id="com.tasking.ctc.cc.abs.debug.869390838" name="C/C++ Compiler" superClass="com.tasking.ctc.cc.abs.debug">
								<option id="com.tasking.ctc.cc.pr36858.1106661604" name="workaround for PR36858" superClass="com.tasking.ctc.cc.pr36858" value="true" valueType="string"/>
								<option id="com.tasking.ctc.cc.includePaths.353799617" name="Include paths" superClass="com.tasking.ctc.cc.includePaths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/MCU}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/flash/flsloader}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/eeprom/Crc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/eeprom/Fee}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/eeprom/Fls}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/eeprom/MemIf}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/eeprom/NvM}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/eeprom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/flash}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/uds}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/wdg}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Secure}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Vss}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/vss_code}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_cfg}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/dma_infineon_tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/dma_infineon_tricore/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/dma_infineon_tricore/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/integration_general}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/integration_general/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/integration_general/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/spi_infineon_tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/spi_infineon_tricore/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/spi_infineon_tricore/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file/CtCdEyeQCom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file/CtCdEyeQCom/BDL_Stub}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file/CtCdEyeQCom/ContractHeader}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file/CtCdEyeQCom/EyeQDriver}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file/CtCdEyeQCom/ImplementationTemplate}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file/CtCdEyeQCom/RTE_header}&quot;"/>
								</option>
								<option id="com.tasking.ctc.cc.noTaskingSfr.987177786" name="Automatic inclusion of '.sfr' file" superClass="com.tasking.ctc.cc.noTaskingSfr" value="false" valueType="boolean"/>
								<option id="com.tasking.ctc.cc.definedSymbols.1420980428" name="Defined symbols" superClass="com.tasking.ctc.cc.definedSymbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="_TASKING_C_TRICORE_=1"/>
								</option>
								<option id="com.tasking.ctc.c.allocation.nearSize.1529010655" name="Threshold for putting data in __near:" superClass="com.tasking.ctc.c.allocation.nearSize" value="0" valueType="string"/>
								<option id="com.tasking.ctc.c.codeGeneration.alignment.193719231" name="Minimum alignment:" superClass="com.tasking.ctc.c.codeGeneration.alignment" value="4" valueType="string"/>
								<option id="com.tasking.ctc.cc.tradeoff.2045826499" name="Trade-off between speed and size:" superClass="com.tasking.ctc.cc.tradeoff" value="com.tasking.ctc.cc.tradeoff.4" valueType="enumerated"/>
								<inputType id="com.tasking.ctc.cppInputType.2120963237" name="C++" superClass="com.tasking.ctc.cppInputType"/>
								<inputType id="com.tasking.ctc.cpp.cInputType.655876252" name="C" superClass="com.tasking.ctc.cpp.cInputType"/>
								<inputType id="com.tasking.ctc.cc.msInputType.1614571309" name="MS" superClass="com.tasking.ctc.cc.msInputType"/>
							</tool>
							<tool command="cctc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="com.tasking.managedbuilder.TskRegexErrorParser" id="com.tasking.ctc.as.abs.debug.752192429" name="Assembler" superClass="com.tasking.ctc.as.abs.debug">
								<option id="com.tasking.ctc.as.includePaths.1269667525" name="Include paths" superClass="com.tasking.ctc.as.includePaths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/flash/flsloader}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/eeprom/Crc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/eeprom/Fee}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/eeprom/Fls}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/eeprom/MemIf}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/eeprom/NvM}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/eeprom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/flash}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/uds}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/wdg}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_cfg}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/dma_infineon_tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/dma_infineon_tricore/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/dma_infineon_tricore/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/integration_general}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/integration_general/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/integration_general/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/spi_infineon_tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/spi_infineon_tricore/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/mcal_src/spi_infineon_tricore/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file/CtCdEyeQCom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file/CtCdEyeQCom/BDL_Stub}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file/CtCdEyeQCom/ContractHeader}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file/CtCdEyeQCom/EyeQDriver}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file/CtCdEyeQCom/ImplementationTemplate}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/Src_file/CtCdEyeQCom/RTE_header}&quot;"/>
								</option>
								<inputType id="com.tasking.ctc.asmInputType.961032361" name="ASM" superClass="com.tasking.ctc.asmInputType"/>
							</tool>
							<tool command="cctc" commandLinePattern="${COMMAND} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} ${INPUTS}" errorParsers="com.tasking.managedbuilder.TskRegexErrorParser" id="com.tasking.ctc.lk.abs.debug.1909842363" name="Linker" superClass="com.tasking.ctc.lk.abs.debug">
								<option id="com.tasking.ctc.lk.lslFile.451667029" name="Linker script file:" superClass="com.tasking.ctc.lk.lslFile" value="&quot;..\_mcal_pjt.lsl&quot;" valueType="string"/>
								<option id="com.tasking.ctc.lk.definedSymbols.581393139" name="Defined symbols" superClass="com.tasking.ctc.lk.definedSymbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="_TASKING_C_TRICORE_=1"/>
								</option>
								<option id="com.tasking.ctc.lk.additionalOptions.1157544678" name="Additional options:" superClass="com.tasking.ctc.lk.additionalOptions" value="-Cmpe:tc0 --munch -mcrfiklSmNOduQ" valueType="string"/>
								<option id="com.tasking.ctc.lk.outputFormat.ihex.1645579489" name="Generate Intel Hex format file" superClass="com.tasking.ctc.lk.outputFormat.ihex" value="true" valueType="boolean"/>
								<option id="com.tasking.ctc.lk.mapFile.mapFile.1771294419" name="Generate map file (.map)" superClass="com.tasking.ctc.lk.mapFile.mapFile" value="true" valueType="boolean"/>
								<option id="com.tasking.ctc.lk.optimize.deleteUnreferencedSections.1195729834" name="Delete unreferenced sections" superClass="com.tasking.ctc.lk.optimize.deleteUnreferencedSections" value="true" valueType="boolean"/>
								<inputType id="com.tasking.ctc.lkObjInputType.1133588201" name="OBJ" superClass="com.tasking.ctc.lkObjInputType"/>
								<inputType id="com.tasking.ctc.lkLibInputType.315118576" name="LIB" superClass="com.tasking.ctc.lkLibInputType"/>
							</tool>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="MCAL|Src_file/CDD_Can.c|mcal_src/all_heard|mcal_src/general/Det.c|mcal_src/Irq|Src_file/CDD_CanChk.c|mcal_src/Irq/Eru_Irq.c|mcal_src/general/Sent_Callout.c|mcal_src/Irq/Fls_Irq.c|mcal_src/Irq/Sent_Irq.c|Src_file/CtCdEyeQCom|mcal_src/Irq/Eth_Irq.c|mcal_src/general/Dem.c|mcal_src/Irq/AscLin_Irq.c|mcal_src/Irq/Dma_Irq.c|mcal_src/general/Dma_Callout.c|mcal_src/Irq/Spi_Irq.c|mcal_src/Irq/Can_Irq.c|mcal_src/wdg|mcal_src/general/Test_Setup.c|Src_file/CDD_CanTp.c|Src_file/Apl_EolUDS.c|mcal_src/general/Test_Time.c|mcal_src/Irq/Ccu6_Irq.c|Src_file/CDD_AdcChk.c|mcal_src/Irq/Gtm_Irq.c|mcal_src/general/SafetyReport.c|mcal_src/general/OsStub.c|mcal_src/general/Fee_Fcpt.c|mcal_src/Irq/Hsm_Irq.c|mcal_src/general/EthIf_Cbk.c" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="com.tasking.toolInfo">
				<toolInfo>TASKING VX-toolset for TriCore: object linker v4.3r3 Build 248</toolInfo>
				<toolInfo>TASKING VX-toolset for TriCore: control program v4.3r3 Build 208.2.2</toolInfo>
				<toolInfo>TASKING program builder v4.3r3 Build 079</toolInfo>
				<toolInfo>TASKING VX-toolset for TriCore: assembler v4.3r3 Build 341</toolInfo>
				<toolInfo>TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1</toolInfo>
			</storageModule>
			<storageModule moduleId="com.tasking.flash.settings"/>
		</cconfiguration>
		<cconfiguration id="com.tasking.config.ctc.abs.release.543023948">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.tasking.config.ctc.abs.release.543023948" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="com.tasking.managedbuilder.TASKING_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.tasking.managedbuilder.TASKING_SRECORD" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.tasking.managedbuilder.TskRegexErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="CSUV_PGM_FBL" buildArtefactType="com.tasking.ctc.buildArtefactType.elf" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=com.tasking.ctc.buildArtefactType.elf" cleanCommand="&quot;${PRODDIR}/bin/rm&quot; -rf" description="" id="com.tasking.config.ctc.abs.release.543023948" name="Release" parent="com.tasking.config.ctc.abs.release">
					<folderInfo id="com.tasking.config.ctc.abs.release.543023948." name="/" resourcePath="">
						<toolChain id="com.tasking.ctc.abs.release.1348692911" name="TASKING VX-toolset for TriCore" superClass="com.tasking.ctc.abs.release">
							<option id="com.tasking.ctc.pluginVersion.714382789" name="Plugin version" superClass="com.tasking.ctc.pluginVersion" value="*********" valueType="string"/>
							<option id="com.tasking.ctc.prodDir.1167928889" name="Product directory:" superClass="com.tasking.ctc.prodDir" value="${eclipse_home}/.." valueType="string"/>
							<option id="com.tasking.ctc.cpu.76892035" name="Processor:" superClass="com.tasking.ctc.cpu" value="userdef16x" valueType="string"/>
							<targetPlatform archList="all" binaryParser="com.tasking.managedbuilder.TASKING_ELF;com.tasking.managedbuilder.TASKING_SRECORD" id="com.tasking.ctc.platform.abs.release.99931382" name="Release" osList="" superClass="com.tasking.ctc.platform.abs.release"/>
							<builder buildPath="${workspace_loc:/CSUV_PGM_FBL/Release}" id="com.tasking.ctc.builder.abs.debug.904378005" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="TASKING TriCore Makefile generator" parallelBuildOn="true" parallelizationNumber="optimal" superClass="com.tasking.ctc.builder.abs.debug"/>
							<tool id="com.tasking.ctc.cc.abs.release.612088871" name="C/C++ Compiler" superClass="com.tasking.ctc.cc.abs.release">
								<option id="com.tasking.ctc.cc.pr36858.1919775029" name="workaround for PR36858" superClass="com.tasking.ctc.cc.pr36858" value="true" valueType="string"/>
								<inputType id="com.tasking.ctc.cppInputType.1624552732" name="C++" superClass="com.tasking.ctc.cppInputType"/>
								<inputType id="com.tasking.ctc.cpp.cInputType.996407188" name="C" superClass="com.tasking.ctc.cpp.cInputType"/>
								<inputType id="com.tasking.ctc.cc.msInputType.1746270869" name="MS" superClass="com.tasking.ctc.cc.msInputType"/>
							</tool>
							<tool id="com.tasking.ctc.as.abs.release.75220016" name="Assembler" superClass="com.tasking.ctc.as.abs.release">
								<inputType id="com.tasking.ctc.asmInputType.655513439" name="ASM" superClass="com.tasking.ctc.asmInputType"/>
							</tool>
							<tool id="com.tasking.ctc.lk.abs.release.2037533369" name="Linker" superClass="com.tasking.ctc.lk.abs.release">
								<inputType id="com.tasking.ctc.lkObjInputType.1610730803" name="OBJ" superClass="com.tasking.ctc.lkObjInputType"/>
								<inputType id="com.tasking.ctc.lkLibInputType.1618589639" name="LIB" superClass="com.tasking.ctc.lkLibInputType"/>
							</tool>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="MCAL|Src_file/CDD_Can.c|mcal_src/all_heard|mcal_src/general/Det.c|mcal_src/Irq|Src_file/CDD_CanChk.c|mcal_src/Irq/Eru_Irq.c|mcal_src/general/Sent_Callout.c|mcal_src/Irq/Fls_Irq.c|mcal_src/Irq/Sent_Irq.c|Src_file/CtCdEyeQCom|mcal_src/Irq/Eth_Irq.c|mcal_src/general/Dem.c|mcal_src/Irq/AscLin_Irq.c|mcal_src/Irq/Dma_Irq.c|mcal_src/general/Dma_Callout.c|mcal_src/Irq/Spi_Irq.c|mcal_src/Irq/Can_Irq.c|mcal_src/wdg|mcal_src/general/Test_Setup.c|Src_file/CDD_CanTp.c|Src_file/Apl_EolUDS.c|mcal_src/general/Test_Time.c|mcal_src/Irq/Ccu6_Irq.c|Src_file/CDD_AdcChk.c|mcal_src/Irq/Gtm_Irq.c|mcal_src/general/SafetyReport.c|mcal_src/general/OsStub.c|mcal_src/general/Fee_Fcpt.c|mcal_src/Irq/Hsm_Irq.c|mcal_src/general/EthIf_Cbk.c" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="ADAS_2019_07.com.tasking.ctc.target.abs.1103263844" name="TASKING TriCore Application" projectType="com.tasking.ctc.target.abs"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="com.tasking.config.ctc.abs.release.543023948;com.tasking.config.ctc.abs.release.543023948.">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.tasking.ctc.TskTriCoreScannerInfo"/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.tasking.config.ctc.abs.debug.**********;com.tasking.config.ctc.abs.debug.**********.">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.tasking.ctc.TskTriCoreScannerInfo"/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="org.eclipse.cdt.core.language.mapping">
		<project-mappings>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cHeader" language="com.tasking.ctc.clanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cSource" language="com.tasking.ctc.clanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cxxHeader" language="com.tasking.ctc.cpplanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cxxSource" language="com.tasking.ctc.cpplanguage"/>
		</project-mappings>
	</storageModule>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Debug">
			<resource resourceType="PROJECT" workspacePath="/ADAS_2019_07"/>
		</configuration>
		<configuration configurationName="Release">
			<resource resourceType="PROJECT" workspacePath="/ADAS_2019_07"/>
		</configuration>
	</storageModule>
</cproject>
