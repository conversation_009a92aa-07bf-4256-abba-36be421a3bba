/*
* Configuration of module: Mcu (Mcu_Cfg.h)
*
* Created by:              
* Copyright:               
*
* Configured for (WDG):    TC1782
*
* Module vendor:           iSoft
* Generator version:       2.0.2
*
* Generated by iSoft Shanghai Software Lab 
*           on Mon Mar 24 11:05:11 CST 2014
*/



#ifndef WDG_CFG_H
#define WDG_CFG_H
/*==========================================================================*/
/*      G L O B A L   M A C R O   D E F I N I T I O N S                     */
/*==========================================================================*/
#define WDG_TIME_PERIOD               (uint32)0x1//EAE4 //(uint32)0xffc6 /* 10ms */

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/


/*==========================================================================*/
/*      G L O B A L   F U N C T I O N   P R O T O T Y P E S                 */
/*==========================================================================*/

#endif
/*==========================================================================*/
/*		E N D   O F   F I L E												*/
/*==========================================================================*/
/*=====================================================================*/

	
