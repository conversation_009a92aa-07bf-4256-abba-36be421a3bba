/*******************************************************************************
 * Copyright (C) 2023 Technomous. All rights reserved         *
 ******************************************************************************/
/**
 *  \file
 *      Vss.h
 *  \brief
 *      Vss basic function.
 *	\author
 *		Ou Hengyue
 */
#ifndef _VSS_H_
#define _VSS_H_

#include "Std_Types.h"

extern Std_ReturnType Vss_Init(void);
extern uint8 Vss_GetBypassFlag(void);

#endif