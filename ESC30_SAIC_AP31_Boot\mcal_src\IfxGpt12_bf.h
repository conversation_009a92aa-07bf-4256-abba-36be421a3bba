/**
 * \file IfxGpt12_bf.h
 * \brief
 * \copyright Copyright (c) 2016 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC23XADAS_UM_V1.1.R1
 * Specification: tc23xadas_um_sfrs_MCSFR.xml (Revision: UM_V1.1)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Gpt12_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Gpt12
 * 
 */
#ifndef IFXGPT12_BF_H
#define IFXGPT12_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Gpt12_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN0 */
#define IFX_GPT12_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN0 */
#define IFX_GPT12_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN0 */
#define IFX_GPT12_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN10 */
#define IFX_GPT12_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN10 */
#define IFX_GPT12_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN10 */
#define IFX_GPT12_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN11 */
#define IFX_GPT12_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN11 */
#define IFX_GPT12_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN11 */
#define IFX_GPT12_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN12 */
#define IFX_GPT12_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN12 */
#define IFX_GPT12_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN12 */
#define IFX_GPT12_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN13 */
#define IFX_GPT12_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN13 */
#define IFX_GPT12_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN13 */
#define IFX_GPT12_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN14 */
#define IFX_GPT12_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN14 */
#define IFX_GPT12_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN14 */
#define IFX_GPT12_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN15 */
#define IFX_GPT12_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN15 */
#define IFX_GPT12_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN15 */
#define IFX_GPT12_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN16 */
#define IFX_GPT12_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN16 */
#define IFX_GPT12_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN16 */
#define IFX_GPT12_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN17 */
#define IFX_GPT12_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN17 */
#define IFX_GPT12_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN17 */
#define IFX_GPT12_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN18 */
#define IFX_GPT12_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN18 */
#define IFX_GPT12_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN18 */
#define IFX_GPT12_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN19 */
#define IFX_GPT12_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN19 */
#define IFX_GPT12_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN19 */
#define IFX_GPT12_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN1 */
#define IFX_GPT12_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN1 */
#define IFX_GPT12_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN1 */
#define IFX_GPT12_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN20 */
#define IFX_GPT12_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN20 */
#define IFX_GPT12_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN20 */
#define IFX_GPT12_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN21 */
#define IFX_GPT12_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN21 */
#define IFX_GPT12_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN21 */
#define IFX_GPT12_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN22 */
#define IFX_GPT12_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN22 */
#define IFX_GPT12_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN22 */
#define IFX_GPT12_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN23 */
#define IFX_GPT12_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN23 */
#define IFX_GPT12_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN23 */
#define IFX_GPT12_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN24 */
#define IFX_GPT12_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN24 */
#define IFX_GPT12_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN24 */
#define IFX_GPT12_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN25 */
#define IFX_GPT12_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN25 */
#define IFX_GPT12_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN25 */
#define IFX_GPT12_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN26 */
#define IFX_GPT12_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN26 */
#define IFX_GPT12_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN26 */
#define IFX_GPT12_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN27 */
#define IFX_GPT12_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN27 */
#define IFX_GPT12_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN27 */
#define IFX_GPT12_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN28 */
#define IFX_GPT12_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN28 */
#define IFX_GPT12_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN28 */
#define IFX_GPT12_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN29 */
#define IFX_GPT12_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN29 */
#define IFX_GPT12_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN29 */
#define IFX_GPT12_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN2 */
#define IFX_GPT12_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN2 */
#define IFX_GPT12_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN2 */
#define IFX_GPT12_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN30 */
#define IFX_GPT12_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN30 */
#define IFX_GPT12_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN30 */
#define IFX_GPT12_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN31 */
#define IFX_GPT12_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN31 */
#define IFX_GPT12_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN31 */
#define IFX_GPT12_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN3 */
#define IFX_GPT12_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN3 */
#define IFX_GPT12_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN3 */
#define IFX_GPT12_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN4 */
#define IFX_GPT12_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN4 */
#define IFX_GPT12_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN4 */
#define IFX_GPT12_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN5 */
#define IFX_GPT12_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN5 */
#define IFX_GPT12_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN5 */
#define IFX_GPT12_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN6 */
#define IFX_GPT12_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN6 */
#define IFX_GPT12_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN6 */
#define IFX_GPT12_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN7 */
#define IFX_GPT12_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN7 */
#define IFX_GPT12_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN7 */
#define IFX_GPT12_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN8 */
#define IFX_GPT12_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN8 */
#define IFX_GPT12_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN8 */
#define IFX_GPT12_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_GPT12_ACCEN0_Bits.EN9 */
#define IFX_GPT12_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_GPT12_ACCEN0_Bits.EN9 */
#define IFX_GPT12_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_ACCEN0_Bits.EN9 */
#define IFX_GPT12_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_GPT12_CAPREL_Bits.CAPREL */
#define IFX_GPT12_CAPREL_CAPREL_LEN (16u)

/** \brief  Mask for Ifx_GPT12_CAPREL_Bits.CAPREL */
#define IFX_GPT12_CAPREL_CAPREL_MSK (0xffffu)

/** \brief  Offset for Ifx_GPT12_CAPREL_Bits.CAPREL */
#define IFX_GPT12_CAPREL_CAPREL_OFF (0u)

/** \brief  Length for Ifx_GPT12_CLC_Bits.DISR */
#define IFX_GPT12_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_GPT12_CLC_Bits.DISR */
#define IFX_GPT12_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_CLC_Bits.DISR */
#define IFX_GPT12_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_GPT12_CLC_Bits.DISS */
#define IFX_GPT12_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_GPT12_CLC_Bits.DISS */
#define IFX_GPT12_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_CLC_Bits.DISS */
#define IFX_GPT12_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_GPT12_CLC_Bits.EDIS */
#define IFX_GPT12_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_GPT12_CLC_Bits.EDIS */
#define IFX_GPT12_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_CLC_Bits.EDIS */
#define IFX_GPT12_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_GPT12_ID_Bits.MODNUMBER */
#define IFX_GPT12_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_GPT12_ID_Bits.MODNUMBER */
#define IFX_GPT12_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_GPT12_ID_Bits.MODNUMBER */
#define IFX_GPT12_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_GPT12_ID_Bits.MODREV */
#define IFX_GPT12_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_GPT12_ID_Bits.MODREV */
#define IFX_GPT12_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_GPT12_ID_Bits.MODREV */
#define IFX_GPT12_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_GPT12_ID_Bits.MODTYPE */
#define IFX_GPT12_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_GPT12_ID_Bits.MODTYPE */
#define IFX_GPT12_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_GPT12_ID_Bits.MODTYPE */
#define IFX_GPT12_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_GPT12_KRST0_Bits.RST */
#define IFX_GPT12_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_GPT12_KRST0_Bits.RST */
#define IFX_GPT12_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_KRST0_Bits.RST */
#define IFX_GPT12_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_GPT12_KRST0_Bits.RSTSTAT */
#define IFX_GPT12_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_GPT12_KRST0_Bits.RSTSTAT */
#define IFX_GPT12_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_KRST0_Bits.RSTSTAT */
#define IFX_GPT12_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_GPT12_KRST1_Bits.RST */
#define IFX_GPT12_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_GPT12_KRST1_Bits.RST */
#define IFX_GPT12_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_KRST1_Bits.RST */
#define IFX_GPT12_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_GPT12_KRSTCLR_Bits.CLR */
#define IFX_GPT12_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_GPT12_KRSTCLR_Bits.CLR */
#define IFX_GPT12_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_KRSTCLR_Bits.CLR */
#define IFX_GPT12_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_GPT12_OCS_Bits.SUS */
#define IFX_GPT12_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_GPT12_OCS_Bits.SUS */
#define IFX_GPT12_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_GPT12_OCS_Bits.SUS */
#define IFX_GPT12_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_GPT12_OCS_Bits.SUS_P */
#define IFX_GPT12_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_GPT12_OCS_Bits.SUS_P */
#define IFX_GPT12_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_OCS_Bits.SUS_P */
#define IFX_GPT12_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_GPT12_OCS_Bits.SUSSTA */
#define IFX_GPT12_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_GPT12_OCS_Bits.SUSSTA */
#define IFX_GPT12_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_OCS_Bits.SUSSTA */
#define IFX_GPT12_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_GPT12_PISEL_Bits.ISCAPIN */
#define IFX_GPT12_PISEL_ISCAPIN_LEN (2u)

/** \brief  Mask for Ifx_GPT12_PISEL_Bits.ISCAPIN */
#define IFX_GPT12_PISEL_ISCAPIN_MSK (0x3u)

/** \brief  Offset for Ifx_GPT12_PISEL_Bits.ISCAPIN */
#define IFX_GPT12_PISEL_ISCAPIN_OFF (14u)

/** \brief  Length for Ifx_GPT12_PISEL_Bits.IST2EUD */
#define IFX_GPT12_PISEL_IST2EUD_LEN (1u)

/** \brief  Mask for Ifx_GPT12_PISEL_Bits.IST2EUD */
#define IFX_GPT12_PISEL_IST2EUD_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_PISEL_Bits.IST2EUD */
#define IFX_GPT12_PISEL_IST2EUD_OFF (1u)

/** \brief  Length for Ifx_GPT12_PISEL_Bits.IST2IN */
#define IFX_GPT12_PISEL_IST2IN_LEN (1u)

/** \brief  Mask for Ifx_GPT12_PISEL_Bits.IST2IN */
#define IFX_GPT12_PISEL_IST2IN_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_PISEL_Bits.IST2IN */
#define IFX_GPT12_PISEL_IST2IN_OFF (0u)

/** \brief  Length for Ifx_GPT12_PISEL_Bits.IST3EUD */
#define IFX_GPT12_PISEL_IST3EUD_LEN (2u)

/** \brief  Mask for Ifx_GPT12_PISEL_Bits.IST3EUD */
#define IFX_GPT12_PISEL_IST3EUD_MSK (0x3u)

/** \brief  Offset for Ifx_GPT12_PISEL_Bits.IST3EUD */
#define IFX_GPT12_PISEL_IST3EUD_OFF (4u)

/** \brief  Length for Ifx_GPT12_PISEL_Bits.IST3IN */
#define IFX_GPT12_PISEL_IST3IN_LEN (2u)

/** \brief  Mask for Ifx_GPT12_PISEL_Bits.IST3IN */
#define IFX_GPT12_PISEL_IST3IN_MSK (0x3u)

/** \brief  Offset for Ifx_GPT12_PISEL_Bits.IST3IN */
#define IFX_GPT12_PISEL_IST3IN_OFF (2u)

/** \brief  Length for Ifx_GPT12_PISEL_Bits.IST4EUD */
#define IFX_GPT12_PISEL_IST4EUD_LEN (2u)

/** \brief  Mask for Ifx_GPT12_PISEL_Bits.IST4EUD */
#define IFX_GPT12_PISEL_IST4EUD_MSK (0x3u)

/** \brief  Offset for Ifx_GPT12_PISEL_Bits.IST4EUD */
#define IFX_GPT12_PISEL_IST4EUD_OFF (8u)

/** \brief  Length for Ifx_GPT12_PISEL_Bits.IST4IN */
#define IFX_GPT12_PISEL_IST4IN_LEN (2u)

/** \brief  Mask for Ifx_GPT12_PISEL_Bits.IST4IN */
#define IFX_GPT12_PISEL_IST4IN_MSK (0x3u)

/** \brief  Offset for Ifx_GPT12_PISEL_Bits.IST4IN */
#define IFX_GPT12_PISEL_IST4IN_OFF (6u)

/** \brief  Length for Ifx_GPT12_PISEL_Bits.IST5EUD */
#define IFX_GPT12_PISEL_IST5EUD_LEN (1u)

/** \brief  Mask for Ifx_GPT12_PISEL_Bits.IST5EUD */
#define IFX_GPT12_PISEL_IST5EUD_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_PISEL_Bits.IST5EUD */
#define IFX_GPT12_PISEL_IST5EUD_OFF (11u)

/** \brief  Length for Ifx_GPT12_PISEL_Bits.IST5IN */
#define IFX_GPT12_PISEL_IST5IN_LEN (1u)

/** \brief  Mask for Ifx_GPT12_PISEL_Bits.IST5IN */
#define IFX_GPT12_PISEL_IST5IN_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_PISEL_Bits.IST5IN */
#define IFX_GPT12_PISEL_IST5IN_OFF (10u)

/** \brief  Length for Ifx_GPT12_PISEL_Bits.IST6EUD */
#define IFX_GPT12_PISEL_IST6EUD_LEN (1u)

/** \brief  Mask for Ifx_GPT12_PISEL_Bits.IST6EUD */
#define IFX_GPT12_PISEL_IST6EUD_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_PISEL_Bits.IST6EUD */
#define IFX_GPT12_PISEL_IST6EUD_OFF (13u)

/** \brief  Length for Ifx_GPT12_PISEL_Bits.IST6IN */
#define IFX_GPT12_PISEL_IST6IN_LEN (1u)

/** \brief  Mask for Ifx_GPT12_PISEL_Bits.IST6IN */
#define IFX_GPT12_PISEL_IST6IN_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_PISEL_Bits.IST6IN */
#define IFX_GPT12_PISEL_IST6IN_OFF (12u)

/** \brief  Length for Ifx_GPT12_T2_Bits.T2 */
#define IFX_GPT12_T2_T2_LEN (16u)

/** \brief  Mask for Ifx_GPT12_T2_Bits.T2 */
#define IFX_GPT12_T2_T2_MSK (0xffffu)

/** \brief  Offset for Ifx_GPT12_T2_Bits.T2 */
#define IFX_GPT12_T2_T2_OFF (0u)

/** \brief  Length for Ifx_GPT12_T2CON_Bits.T2CHDIR */
#define IFX_GPT12_T2CON_T2CHDIR_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T2CON_Bits.T2CHDIR */
#define IFX_GPT12_T2CON_T2CHDIR_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T2CON_Bits.T2CHDIR */
#define IFX_GPT12_T2CON_T2CHDIR_OFF (14u)

/** \brief  Length for Ifx_GPT12_T2CON_Bits.T2EDGE */
#define IFX_GPT12_T2CON_T2EDGE_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T2CON_Bits.T2EDGE */
#define IFX_GPT12_T2CON_T2EDGE_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T2CON_Bits.T2EDGE */
#define IFX_GPT12_T2CON_T2EDGE_OFF (13u)

/** \brief  Length for Ifx_GPT12_T2CON_Bits.T2I */
#define IFX_GPT12_T2CON_T2I_LEN (3u)

/** \brief  Mask for Ifx_GPT12_T2CON_Bits.T2I */
#define IFX_GPT12_T2CON_T2I_MSK (0x7u)

/** \brief  Offset for Ifx_GPT12_T2CON_Bits.T2I */
#define IFX_GPT12_T2CON_T2I_OFF (0u)

/** \brief  Length for Ifx_GPT12_T2CON_Bits.T2IRDIS */
#define IFX_GPT12_T2CON_T2IRDIS_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T2CON_Bits.T2IRDIS */
#define IFX_GPT12_T2CON_T2IRDIS_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T2CON_Bits.T2IRDIS */
#define IFX_GPT12_T2CON_T2IRDIS_OFF (12u)

/** \brief  Length for Ifx_GPT12_T2CON_Bits.T2M */
#define IFX_GPT12_T2CON_T2M_LEN (3u)

/** \brief  Mask for Ifx_GPT12_T2CON_Bits.T2M */
#define IFX_GPT12_T2CON_T2M_MSK (0x7u)

/** \brief  Offset for Ifx_GPT12_T2CON_Bits.T2M */
#define IFX_GPT12_T2CON_T2M_OFF (3u)

/** \brief  Length for Ifx_GPT12_T2CON_Bits.T2R */
#define IFX_GPT12_T2CON_T2R_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T2CON_Bits.T2R */
#define IFX_GPT12_T2CON_T2R_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T2CON_Bits.T2R */
#define IFX_GPT12_T2CON_T2R_OFF (6u)

/** \brief  Length for Ifx_GPT12_T2CON_Bits.T2RC */
#define IFX_GPT12_T2CON_T2RC_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T2CON_Bits.T2RC */
#define IFX_GPT12_T2CON_T2RC_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T2CON_Bits.T2RC */
#define IFX_GPT12_T2CON_T2RC_OFF (9u)

/** \brief  Length for Ifx_GPT12_T2CON_Bits.T2RDIR */
#define IFX_GPT12_T2CON_T2RDIR_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T2CON_Bits.T2RDIR */
#define IFX_GPT12_T2CON_T2RDIR_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T2CON_Bits.T2RDIR */
#define IFX_GPT12_T2CON_T2RDIR_OFF (15u)

/** \brief  Length for Ifx_GPT12_T2CON_Bits.T2UD */
#define IFX_GPT12_T2CON_T2UD_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T2CON_Bits.T2UD */
#define IFX_GPT12_T2CON_T2UD_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T2CON_Bits.T2UD */
#define IFX_GPT12_T2CON_T2UD_OFF (7u)

/** \brief  Length for Ifx_GPT12_T2CON_Bits.T2UDE */
#define IFX_GPT12_T2CON_T2UDE_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T2CON_Bits.T2UDE */
#define IFX_GPT12_T2CON_T2UDE_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T2CON_Bits.T2UDE */
#define IFX_GPT12_T2CON_T2UDE_OFF (8u)

/** \brief  Length for Ifx_GPT12_T3_Bits.T3 */
#define IFX_GPT12_T3_T3_LEN (16u)

/** \brief  Mask for Ifx_GPT12_T3_Bits.T3 */
#define IFX_GPT12_T3_T3_MSK (0xffffu)

/** \brief  Offset for Ifx_GPT12_T3_Bits.T3 */
#define IFX_GPT12_T3_T3_OFF (0u)

/** \brief  Length for Ifx_GPT12_T3CON_Bits.BPS1 */
#define IFX_GPT12_T3CON_BPS1_LEN (2u)

/** \brief  Mask for Ifx_GPT12_T3CON_Bits.BPS1 */
#define IFX_GPT12_T3CON_BPS1_MSK (0x3u)

/** \brief  Offset for Ifx_GPT12_T3CON_Bits.BPS1 */
#define IFX_GPT12_T3CON_BPS1_OFF (11u)

/** \brief  Length for Ifx_GPT12_T3CON_Bits.T3CHDIR */
#define IFX_GPT12_T3CON_T3CHDIR_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T3CON_Bits.T3CHDIR */
#define IFX_GPT12_T3CON_T3CHDIR_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T3CON_Bits.T3CHDIR */
#define IFX_GPT12_T3CON_T3CHDIR_OFF (14u)

/** \brief  Length for Ifx_GPT12_T3CON_Bits.T3EDGE */
#define IFX_GPT12_T3CON_T3EDGE_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T3CON_Bits.T3EDGE */
#define IFX_GPT12_T3CON_T3EDGE_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T3CON_Bits.T3EDGE */
#define IFX_GPT12_T3CON_T3EDGE_OFF (13u)

/** \brief  Length for Ifx_GPT12_T3CON_Bits.T3I */
#define IFX_GPT12_T3CON_T3I_LEN (3u)

/** \brief  Mask for Ifx_GPT12_T3CON_Bits.T3I */
#define IFX_GPT12_T3CON_T3I_MSK (0x7u)

/** \brief  Offset for Ifx_GPT12_T3CON_Bits.T3I */
#define IFX_GPT12_T3CON_T3I_OFF (0u)

/** \brief  Length for Ifx_GPT12_T3CON_Bits.T3M */
#define IFX_GPT12_T3CON_T3M_LEN (3u)

/** \brief  Mask for Ifx_GPT12_T3CON_Bits.T3M */
#define IFX_GPT12_T3CON_T3M_MSK (0x7u)

/** \brief  Offset for Ifx_GPT12_T3CON_Bits.T3M */
#define IFX_GPT12_T3CON_T3M_OFF (3u)

/** \brief  Length for Ifx_GPT12_T3CON_Bits.T3OE */
#define IFX_GPT12_T3CON_T3OE_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T3CON_Bits.T3OE */
#define IFX_GPT12_T3CON_T3OE_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T3CON_Bits.T3OE */
#define IFX_GPT12_T3CON_T3OE_OFF (9u)

/** \brief  Length for Ifx_GPT12_T3CON_Bits.T3OTL */
#define IFX_GPT12_T3CON_T3OTL_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T3CON_Bits.T3OTL */
#define IFX_GPT12_T3CON_T3OTL_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T3CON_Bits.T3OTL */
#define IFX_GPT12_T3CON_T3OTL_OFF (10u)

/** \brief  Length for Ifx_GPT12_T3CON_Bits.T3R */
#define IFX_GPT12_T3CON_T3R_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T3CON_Bits.T3R */
#define IFX_GPT12_T3CON_T3R_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T3CON_Bits.T3R */
#define IFX_GPT12_T3CON_T3R_OFF (6u)

/** \brief  Length for Ifx_GPT12_T3CON_Bits.T3RDIR */
#define IFX_GPT12_T3CON_T3RDIR_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T3CON_Bits.T3RDIR */
#define IFX_GPT12_T3CON_T3RDIR_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T3CON_Bits.T3RDIR */
#define IFX_GPT12_T3CON_T3RDIR_OFF (15u)

/** \brief  Length for Ifx_GPT12_T3CON_Bits.T3UD */
#define IFX_GPT12_T3CON_T3UD_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T3CON_Bits.T3UD */
#define IFX_GPT12_T3CON_T3UD_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T3CON_Bits.T3UD */
#define IFX_GPT12_T3CON_T3UD_OFF (7u)

/** \brief  Length for Ifx_GPT12_T3CON_Bits.T3UDE */
#define IFX_GPT12_T3CON_T3UDE_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T3CON_Bits.T3UDE */
#define IFX_GPT12_T3CON_T3UDE_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T3CON_Bits.T3UDE */
#define IFX_GPT12_T3CON_T3UDE_OFF (8u)

/** \brief  Length for Ifx_GPT12_T4_Bits.T4 */
#define IFX_GPT12_T4_T4_LEN (16u)

/** \brief  Mask for Ifx_GPT12_T4_Bits.T4 */
#define IFX_GPT12_T4_T4_MSK (0xffffu)

/** \brief  Offset for Ifx_GPT12_T4_Bits.T4 */
#define IFX_GPT12_T4_T4_OFF (0u)

/** \brief  Length for Ifx_GPT12_T4CON_Bits.CLRT2EN */
#define IFX_GPT12_T4CON_CLRT2EN_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T4CON_Bits.CLRT2EN */
#define IFX_GPT12_T4CON_CLRT2EN_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T4CON_Bits.CLRT2EN */
#define IFX_GPT12_T4CON_CLRT2EN_OFF (10u)

/** \brief  Length for Ifx_GPT12_T4CON_Bits.CLRT3EN */
#define IFX_GPT12_T4CON_CLRT3EN_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T4CON_Bits.CLRT3EN */
#define IFX_GPT12_T4CON_CLRT3EN_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T4CON_Bits.CLRT3EN */
#define IFX_GPT12_T4CON_CLRT3EN_OFF (11u)

/** \brief  Length for Ifx_GPT12_T4CON_Bits.T4CHDIR */
#define IFX_GPT12_T4CON_T4CHDIR_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T4CON_Bits.T4CHDIR */
#define IFX_GPT12_T4CON_T4CHDIR_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T4CON_Bits.T4CHDIR */
#define IFX_GPT12_T4CON_T4CHDIR_OFF (14u)

/** \brief  Length for Ifx_GPT12_T4CON_Bits.T4EDGE */
#define IFX_GPT12_T4CON_T4EDGE_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T4CON_Bits.T4EDGE */
#define IFX_GPT12_T4CON_T4EDGE_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T4CON_Bits.T4EDGE */
#define IFX_GPT12_T4CON_T4EDGE_OFF (13u)

/** \brief  Length for Ifx_GPT12_T4CON_Bits.T4I */
#define IFX_GPT12_T4CON_T4I_LEN (3u)

/** \brief  Mask for Ifx_GPT12_T4CON_Bits.T4I */
#define IFX_GPT12_T4CON_T4I_MSK (0x7u)

/** \brief  Offset for Ifx_GPT12_T4CON_Bits.T4I */
#define IFX_GPT12_T4CON_T4I_OFF (0u)

/** \brief  Length for Ifx_GPT12_T4CON_Bits.T4IRDIS */
#define IFX_GPT12_T4CON_T4IRDIS_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T4CON_Bits.T4IRDIS */
#define IFX_GPT12_T4CON_T4IRDIS_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T4CON_Bits.T4IRDIS */
#define IFX_GPT12_T4CON_T4IRDIS_OFF (12u)

/** \brief  Length for Ifx_GPT12_T4CON_Bits.T4M */
#define IFX_GPT12_T4CON_T4M_LEN (3u)

/** \brief  Mask for Ifx_GPT12_T4CON_Bits.T4M */
#define IFX_GPT12_T4CON_T4M_MSK (0x7u)

/** \brief  Offset for Ifx_GPT12_T4CON_Bits.T4M */
#define IFX_GPT12_T4CON_T4M_OFF (3u)

/** \brief  Length for Ifx_GPT12_T4CON_Bits.T4R */
#define IFX_GPT12_T4CON_T4R_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T4CON_Bits.T4R */
#define IFX_GPT12_T4CON_T4R_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T4CON_Bits.T4R */
#define IFX_GPT12_T4CON_T4R_OFF (6u)

/** \brief  Length for Ifx_GPT12_T4CON_Bits.T4RC */
#define IFX_GPT12_T4CON_T4RC_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T4CON_Bits.T4RC */
#define IFX_GPT12_T4CON_T4RC_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T4CON_Bits.T4RC */
#define IFX_GPT12_T4CON_T4RC_OFF (9u)

/** \brief  Length for Ifx_GPT12_T4CON_Bits.T4RDIR */
#define IFX_GPT12_T4CON_T4RDIR_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T4CON_Bits.T4RDIR */
#define IFX_GPT12_T4CON_T4RDIR_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T4CON_Bits.T4RDIR */
#define IFX_GPT12_T4CON_T4RDIR_OFF (15u)

/** \brief  Length for Ifx_GPT12_T4CON_Bits.T4UD */
#define IFX_GPT12_T4CON_T4UD_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T4CON_Bits.T4UD */
#define IFX_GPT12_T4CON_T4UD_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T4CON_Bits.T4UD */
#define IFX_GPT12_T4CON_T4UD_OFF (7u)

/** \brief  Length for Ifx_GPT12_T4CON_Bits.T4UDE */
#define IFX_GPT12_T4CON_T4UDE_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T4CON_Bits.T4UDE */
#define IFX_GPT12_T4CON_T4UDE_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T4CON_Bits.T4UDE */
#define IFX_GPT12_T4CON_T4UDE_OFF (8u)

/** \brief  Length for Ifx_GPT12_T5_Bits.T5 */
#define IFX_GPT12_T5_T5_LEN (16u)

/** \brief  Mask for Ifx_GPT12_T5_Bits.T5 */
#define IFX_GPT12_T5_T5_MSK (0xffffu)

/** \brief  Offset for Ifx_GPT12_T5_Bits.T5 */
#define IFX_GPT12_T5_T5_OFF (0u)

/** \brief  Length for Ifx_GPT12_T5CON_Bits.CI */
#define IFX_GPT12_T5CON_CI_LEN (2u)

/** \brief  Mask for Ifx_GPT12_T5CON_Bits.CI */
#define IFX_GPT12_T5CON_CI_MSK (0x3u)

/** \brief  Offset for Ifx_GPT12_T5CON_Bits.CI */
#define IFX_GPT12_T5CON_CI_OFF (12u)

/** \brief  Length for Ifx_GPT12_T5CON_Bits.CT3 */
#define IFX_GPT12_T5CON_CT3_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T5CON_Bits.CT3 */
#define IFX_GPT12_T5CON_CT3_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T5CON_Bits.CT3 */
#define IFX_GPT12_T5CON_CT3_OFF (10u)

/** \brief  Length for Ifx_GPT12_T5CON_Bits.T5CLR */
#define IFX_GPT12_T5CON_T5CLR_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T5CON_Bits.T5CLR */
#define IFX_GPT12_T5CON_T5CLR_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T5CON_Bits.T5CLR */
#define IFX_GPT12_T5CON_T5CLR_OFF (14u)

/** \brief  Length for Ifx_GPT12_T5CON_Bits.T5I */
#define IFX_GPT12_T5CON_T5I_LEN (3u)

/** \brief  Mask for Ifx_GPT12_T5CON_Bits.T5I */
#define IFX_GPT12_T5CON_T5I_MSK (0x7u)

/** \brief  Offset for Ifx_GPT12_T5CON_Bits.T5I */
#define IFX_GPT12_T5CON_T5I_OFF (0u)

/** \brief  Length for Ifx_GPT12_T5CON_Bits.T5M */
#define IFX_GPT12_T5CON_T5M_LEN (3u)

/** \brief  Mask for Ifx_GPT12_T5CON_Bits.T5M */
#define IFX_GPT12_T5CON_T5M_MSK (0x7u)

/** \brief  Offset for Ifx_GPT12_T5CON_Bits.T5M */
#define IFX_GPT12_T5CON_T5M_OFF (3u)

/** \brief  Length for Ifx_GPT12_T5CON_Bits.T5R */
#define IFX_GPT12_T5CON_T5R_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T5CON_Bits.T5R */
#define IFX_GPT12_T5CON_T5R_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T5CON_Bits.T5R */
#define IFX_GPT12_T5CON_T5R_OFF (6u)

/** \brief  Length for Ifx_GPT12_T5CON_Bits.T5RC */
#define IFX_GPT12_T5CON_T5RC_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T5CON_Bits.T5RC */
#define IFX_GPT12_T5CON_T5RC_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T5CON_Bits.T5RC */
#define IFX_GPT12_T5CON_T5RC_OFF (9u)

/** \brief  Length for Ifx_GPT12_T5CON_Bits.T5SC */
#define IFX_GPT12_T5CON_T5SC_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T5CON_Bits.T5SC */
#define IFX_GPT12_T5CON_T5SC_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T5CON_Bits.T5SC */
#define IFX_GPT12_T5CON_T5SC_OFF (15u)

/** \brief  Length for Ifx_GPT12_T5CON_Bits.T5UD */
#define IFX_GPT12_T5CON_T5UD_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T5CON_Bits.T5UD */
#define IFX_GPT12_T5CON_T5UD_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T5CON_Bits.T5UD */
#define IFX_GPT12_T5CON_T5UD_OFF (7u)

/** \brief  Length for Ifx_GPT12_T5CON_Bits.T5UDE */
#define IFX_GPT12_T5CON_T5UDE_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T5CON_Bits.T5UDE */
#define IFX_GPT12_T5CON_T5UDE_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T5CON_Bits.T5UDE */
#define IFX_GPT12_T5CON_T5UDE_OFF (8u)

/** \brief  Length for Ifx_GPT12_T6_Bits.T6 */
#define IFX_GPT12_T6_T6_LEN (16u)

/** \brief  Mask for Ifx_GPT12_T6_Bits.T6 */
#define IFX_GPT12_T6_T6_MSK (0xffffu)

/** \brief  Offset for Ifx_GPT12_T6_Bits.T6 */
#define IFX_GPT12_T6_T6_OFF (0u)

/** \brief  Length for Ifx_GPT12_T6CON_Bits.BPS2 */
#define IFX_GPT12_T6CON_BPS2_LEN (2u)

/** \brief  Mask for Ifx_GPT12_T6CON_Bits.BPS2 */
#define IFX_GPT12_T6CON_BPS2_MSK (0x3u)

/** \brief  Offset for Ifx_GPT12_T6CON_Bits.BPS2 */
#define IFX_GPT12_T6CON_BPS2_OFF (11u)

/** \brief  Length for Ifx_GPT12_T6CON_Bits.T6CLR */
#define IFX_GPT12_T6CON_T6CLR_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T6CON_Bits.T6CLR */
#define IFX_GPT12_T6CON_T6CLR_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T6CON_Bits.T6CLR */
#define IFX_GPT12_T6CON_T6CLR_OFF (14u)

/** \brief  Length for Ifx_GPT12_T6CON_Bits.T6I */
#define IFX_GPT12_T6CON_T6I_LEN (3u)

/** \brief  Mask for Ifx_GPT12_T6CON_Bits.T6I */
#define IFX_GPT12_T6CON_T6I_MSK (0x7u)

/** \brief  Offset for Ifx_GPT12_T6CON_Bits.T6I */
#define IFX_GPT12_T6CON_T6I_OFF (0u)

/** \brief  Length for Ifx_GPT12_T6CON_Bits.T6M */
#define IFX_GPT12_T6CON_T6M_LEN (3u)

/** \brief  Mask for Ifx_GPT12_T6CON_Bits.T6M */
#define IFX_GPT12_T6CON_T6M_MSK (0x7u)

/** \brief  Offset for Ifx_GPT12_T6CON_Bits.T6M */
#define IFX_GPT12_T6CON_T6M_OFF (3u)

/** \brief  Length for Ifx_GPT12_T6CON_Bits.T6OE */
#define IFX_GPT12_T6CON_T6OE_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T6CON_Bits.T6OE */
#define IFX_GPT12_T6CON_T6OE_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T6CON_Bits.T6OE */
#define IFX_GPT12_T6CON_T6OE_OFF (9u)

/** \brief  Length for Ifx_GPT12_T6CON_Bits.T6OTL */
#define IFX_GPT12_T6CON_T6OTL_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T6CON_Bits.T6OTL */
#define IFX_GPT12_T6CON_T6OTL_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T6CON_Bits.T6OTL */
#define IFX_GPT12_T6CON_T6OTL_OFF (10u)

/** \brief  Length for Ifx_GPT12_T6CON_Bits.T6R */
#define IFX_GPT12_T6CON_T6R_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T6CON_Bits.T6R */
#define IFX_GPT12_T6CON_T6R_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T6CON_Bits.T6R */
#define IFX_GPT12_T6CON_T6R_OFF (6u)

/** \brief  Length for Ifx_GPT12_T6CON_Bits.T6SR */
#define IFX_GPT12_T6CON_T6SR_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T6CON_Bits.T6SR */
#define IFX_GPT12_T6CON_T6SR_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T6CON_Bits.T6SR */
#define IFX_GPT12_T6CON_T6SR_OFF (15u)

/** \brief  Length for Ifx_GPT12_T6CON_Bits.T6UD */
#define IFX_GPT12_T6CON_T6UD_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T6CON_Bits.T6UD */
#define IFX_GPT12_T6CON_T6UD_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T6CON_Bits.T6UD */
#define IFX_GPT12_T6CON_T6UD_OFF (7u)

/** \brief  Length for Ifx_GPT12_T6CON_Bits.T6UDE */
#define IFX_GPT12_T6CON_T6UDE_LEN (1u)

/** \brief  Mask for Ifx_GPT12_T6CON_Bits.T6UDE */
#define IFX_GPT12_T6CON_T6UDE_MSK (0x1u)

/** \brief  Offset for Ifx_GPT12_T6CON_Bits.T6UDE */
#define IFX_GPT12_T6CON_T6UDE_OFF (8u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXGPT12_BF_H */
