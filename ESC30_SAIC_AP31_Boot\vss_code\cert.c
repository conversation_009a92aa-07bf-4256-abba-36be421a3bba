#include "cert.h"
#include "sm2.h"
#include "vsstype.h"
#include "errid.h"
#include "vssapi.h"
#include "vsscommon.h"


#pragma section code "vss_api_code" 

#if (defined (_ENABLE_MIZAR_FOTA_)&&(_ENABLE_MIZAR_FOTA_ == 1U))
vss_uint32 _cert_get_cn_real_value(vss_uint8 data)
{
	vss_uint32 rt = ((data & 0xf0) >> 4) * 10 + (data & 0xf);
	return rt;
}

vss_uint32 _cert_decode(vss_uint8* cert, vss_uint32 certLen, CertInfo* ci)
{
	vss_uint32 offset = 0;

	if (cert == VSS_NULL || ci == VSS_NULL)
	{
		return ERR_PARAMETER;
	}
	

	switch (cert[0]) {
	case 0x10:
	if (certLen == ROOT_CERT_SIZE)
	{
		ci->cert = cert[offset];
		offset += 1;

		offset += 4;

		mem_cpy8(ci->expire, cert + offset, 3);
		offset += 3;

		mem_cpy8(ci->serial, cert + offset, 4);
		offset += 4;

		ci->alg = cert[offset];
		offset += 3;

		ci->root = cert[offset];
		offset += 1;

		mem_cpy8(ci->pk, cert + offset, 64);
		offset += 64;

		mem_cpy8(ci->sign, cert + offset, 64);
		offset += 64;
	}else{
		return ERR_LEN_INVALID;
	}
	break;
	case 0x20:
		if (certLen == ECU_CERT_SIZE)
		{
			ci->cert = cert[offset];
			offset += 1;

			offset += 8;

			offset += 16;

			mem_cpy8(ci->expire, cert + offset, 3);
			offset += 3;

			mem_cpy8(ci->serial, cert + offset, 4);
			offset += 4;

			ci->alg = cert[offset];
			offset += 3;

			ci->root = cert[offset];
			offset += 1;

			mem_cpy8(ci->pk, cert + offset, 64);
			offset += 64;

			mem_cpy8(ci->sign, cert + offset, 64);
			offset += 64;
		}else{
			return ERR_LEN_INVALID;
		}
	break;
	case 0x30:
		if (certLen == USER_CERT_SIZE)
		{
			ci->cert = cert[offset];
			offset += 1;

			mem_cpy8(&ci->type, cert + offset, 4);
			offset += 4;

			mem_cpy8(ci->name, cert + offset, 32);
			offset += 32;

			mem_cpy8(ci->expire, cert + offset, 3);
			offset += 3;

			mem_cpy8(ci->serial, cert + offset, 4);
			offset += 4;

			ci->alg = cert[offset];
			offset += 3;

			ci->root = cert[offset];
			offset += 1;

			mem_cpy8(ci->pk, cert + offset, 64);
			offset += 64;

			mem_cpy8(ci->sign, cert + offset, 64);
			offset += 64;
		}else{
			return ERR_LEN_INVALID;
		}
	break;
	default:
		return ERR_CERT_INVALID;
	}

	return 0;
}

extern unsigned char RootPublickey[64];
vss_uint32 _cert_verify(vss_uint8* cert, vss_uint32 certLen, vss_uint8* root, vss_uint32 rootLen)
{
	vss_uint32 ret;
	CertInfo rci, ci;
	
	if (cert == VSS_NULL || root == VSS_NULL)
	{
		return ERR_PARAMETER;
	}
	
	ret = _cert_decode(root, rootLen, &rci);
	if(ret)
	{
		return ERR_CERT_INVALID;
	}

	ret = _cert_decode(cert, certLen, &ci);
	if(ret)
		return ERR_CERT_INVALID;

	switch (ci.alg) {
	case CERTALG_ECC:
		return VssECCVerify(rci.pk ,cert, certLen - 64, 1, cert + certLen - 64);
		//return VssECCVerify(RootPublickey ,cert, certLen - 64, 1, cert + certLen - 64);
	case CERTALG_SM2:
		return VssSM2Verify(rci.pk ,cert, certLen - 64, 1, cert + certLen - 64);
		//return VssSM2Verify(RootPublickey ,cert, certLen - 64, 1, cert + certLen - 64);
	default:
		return ERR_DATA_INVALID;
	}
}

/******************************************************************************
 鎽樿: 鐢ㄨ瘉涔﹂獙璇佺鍚�
 
 鍙傛暟:	cert[in]		--寰呴獙璇佽瘉涔︽暟鎹�
		certLen[in]	--寰呴獙璇佽瘉涔﹂暱搴�
		data[in]		--绛惧悕鏁版嵁
		dataLen[in]	--绛惧悕鏁版嵁闀垮害
		hashFlag[in]	--鍝堝笇鏍囪瘑0-涓嶅搱甯岋紱1-鍝堝笇
 		sig[in]		--绛惧悕鏁版嵁

 杩斿洖鍊硷細0锛嶏紞鎴愬姛锛�<0锛嶏紞鍏朵粬閿欒鐮�
 ******************************************************************************/
vss_uint32 _cert_verify_sign(vss_uint8* cert, vss_uint32 certLen, vss_uint8* data, vss_uint32 dataLen, vss_uint32 hashFlag, vss_uint8* sig)
{
	CertInfo ci;
	vss_uint32 ret;
	
	ret = _cert_decode(cert, certLen, &ci);
	if(ret)
		return ERR_DATA_INVALID;

	switch (ci.alg) {
	case CERTALG_ECC:
 		return VssECCVerify(ci.pk, data, dataLen, hashFlag, sig);
	case CERTALG_SM2:
 		return VssSM2Verify(ci.pk, data, dataLen, hashFlag, sig);
	default:
		return ERR_DATA_INVALID;
	}
}

vss_uint32 _cert_encrypt(vss_uint8* cert, vss_uint32 certLen, vss_uint8* in, vss_uint32 inLen, vss_uint8* out, vss_uint32 *outlen)
{
	CertInfo ci;
	vss_uint32 ret;
	
	ret = _cert_decode(cert, certLen, &ci);
	if(ret)
		return ret;
		
	switch(ci.alg)
	{
	case CERTALG_ECC:
		ret = VssECCCalc(in, inLen, CALC_ENC, ci.pk, 64, out, outlen);
		if (ret)
			return ERR_CALC_FAIL;
		break;
	case CERTALG_SM2:
		ret = VssSM2Calc(in, inLen, CALC_ENC, ci.pk, 64, out, outlen);
		if (ret)
			return ERR_CALC_FAIL;
		break;
	default:
		return ERR_DATA_INVALID;
	}

	return 0;
}
#endif

#pragma section code restore


