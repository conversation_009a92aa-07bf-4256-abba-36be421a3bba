/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 * @file 	CanIf.c
 * @brief
 *
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *
 * <AUTHOR>
 * @date 	2013-5-22
 * 
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20130522    WBN       Initial version
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/
#include "Uds_CanIf.h"
#include "CanIf_Cfg.h"
#include "ComStack_Types.h"
#include "Std_Types.h"
#include "Can_17_MCanP.h"
#include "IfxPort_reg.h"
#include "Dcm.h"
/*=======[E X T E R N A L   D A T A]==========================================*/
extern const CanIf_TxChannelType CanIfTxCh[CANIF_TX_CHANNEL_NUM];
extern const CanIf_RxChannelType CanIfRxCh[CANIF_RX_CHANNEL_NUM];

/*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/
/******************************************************************************/
/*
 * Brief               Initialize CAN IF.
 * ServiceId           None
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      None      
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None  
 * PreCondition        None  
 * CallByAPI           CanIf_Init 
 */
/******************************************************************************/
void Uds_CanIf_Init(void)
{
//    Can_Init();
//#if 1
//    Can_InitController(2, &Can_ControllerConfigData[0]);
//    Can_SetControllerMode(2,CAN_T_START);
//#endif
//#if 0
//    Can_InitController(1, &Can_ControllerConfigData[1]);
//    Can_SetControllerMode(1,CAN_T_START);
//#endif

}
#include "Dio_Cfg.h"
#include "Dio.h"
#include "Port.h"


void g_v_IoAbstGpioInit(void)
{
//	g_v_IoPackSetAvhlampEnable();
    Dio_WriteChannel(DioConf_DioChannel_O_S_CAN1MSB_M, STD_HIGH);
	Dio_WriteChannel(DioConf_DioChannel_O_S_CAN1MEN_M, STD_HIGH);
    Dio_WriteChannel(DioConf_DioChannel_O_S_CAN2SB_M, STD_HIGH);
	Dio_WriteChannel(DioConf_DioChannel_O_S_CAN2EN_M, STD_HIGH);
}


void CanIf_Init(void)
{
	  Port_Init(&Port_ConfigRoot[0]);
	  Dio_Init(&Dio_ConfigRoot[0]);

	g_v_IoAbstGpioInit();


    P02_IOCR0.U = 0x00A800A8U; /*P02.0 TX P02.1 RX*/
//
//    P11_IOCR0.U = (P11_IOCR0.U & ~0xF8U) | 0x80U; /*P11.2 */
//    P11_OUT.B.P2 = 1;
//    P11_OUT.B.P3 = 1;
//
//
//    P02_IOCR0.U = (P02_IOCR0.U & ~0xF8U) | 0x80U; /*P02.2 */
//    P02_OUT.B.P2 = 1;
//    P02_OUT.B.P3 = 1;


    Can_17_MCanP_Init(&Can_17_MCanP_ConfigRoot[0]);
	while(CAN_OK != Can_17_MCanP_SetControllerMode(0, CAN_T_START))
	{
		__asm("nop");
	}
}





/*************************************************************************************************
** Syntax :   void g_v_CanIfControllerBusOff (uint8 ControllerId)
**                                                                            					**
** Service ID: none                                                           					**
**                                                                            					**
** Sync/Async:  none                                                          					**
**                                                                            					**
** Reentrancy:  none                                                          					**
**                                                                            					**
** Parameters (in): none                                                      					**
**                                                                            					**
** Parameters (out):  none                                                    					**
**                                                                           					**
** Return value:  none                                                        					**
**                                                                            					**
** Description : none
**                                                                            					**
*************************************************************************************************/
void g_v_CanIfControllerBusOff (uint8 ControllerId)
{
//	 Can_17_MCanP_Init(&Can_17_MCanP_ConfigRoot[0]);
	 (void)Can_17_MCanP_SetControllerMode(ControllerId,CAN_T_START);
}
/******************************************************************************/
/*
 * Brief               CAN If Transmit
 * ServiceId           None
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      Id, *PduInfoPtr      
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              Std_ReturnType  
 * PreCondition        None  
 * CallByAPI           CanIf_Transmit 
 */
/******************************************************************************/
Std_ReturnType Uds_CanIf_Transmit(PduIdType Id, const PduInfoType* PduInfoPtr)
{
    CanIf_TxChannelType* pCh = CanIfTxCh;
    Can_ReturnType canRes = CAN_NOT_OK;
    uint8 iloop;
    Can_PduType canPdu;
    Std_ReturnType res = E_OK;

    for(iloop=0;iloop<CANIF_TX_CHANNEL_NUM;iloop++) 
    { 
        if(pCh->TpPduId == Id)
        {
            canPdu.id = pCh->CanId;
            canPdu.length = PduInfoPtr->SduLength;
            canPdu.sdu = PduInfoPtr->SduDataPtr;
            canPdu.swPduHandle = pCh->PduId;   
            canRes = Can_Write(pCh->ObjectId, &canPdu);
            break;             
        }
        pCh++;
    }
    
    if(CAN_OK != canRes)
    {
       res = E_NOT_OK; 
    }
    return res;
}

/******************************************************************************/
/*
 * Brief               CAN If Tx Confirmation
 * ServiceId           None
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      CanTxPduId      
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None  
 * PreCondition        None  
 * CallByAPI           CanIf_TxConfirmation 
 */
/******************************************************************************/
void Uds_CanIf_TxConfirmation(PduIdType CanTxPduId)
{
    CanIf_TxChannelType* pCh = CanIfTxCh;
    uint8 iloop;

    for(iloop=0;iloop<CANIF_TX_CHANNEL_NUM;iloop++) 
    { 
        if(pCh->PduId == CanTxPduId)
        {
            pCh->TxConf(pCh->TpPduId);
            break;             
        }
        pCh++;
    }
}

/******************************************************************************/
/*
 * Brief               CAN If Rx Indication
 * ServiceId           None
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      Hrh, CanId, CanDlc, *CanSduPtr      
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None  
 * PreCondition        None  
 * CallByAPI           CanIf_RxIndication 
 */
/******************************************************************************/
void Uds_CanIf_RxIndication(uint8 Hrh,uint32 CanId,uint8 CanDlc,uint8 *CanSduPtr)
{
   CanIf_RxChannelType* pCh = CanIfRxCh;
   uint8 iloop;
   PduInfoType PduInfo;
   uint8 CANdataLenght[16] = {0,1,2,3,4,5,6,7,8,12,16,20,24,32,48,64};

//   if ((0x47df==CanId)&&(*CanSduPtr==0x02)&&(*(CanSduPtr+1)==0x3E)&&(*(CanSduPtr+2)==0x80))
//    {
//         Dcm_FunctionalTesterPresentIndi();
//        return;
//    }
    for(iloop=0;iloop<CANIF_RX_CHANNEL_NUM;iloop++) 
    {
        if((pCh->ObjectId == Hrh) && (pCh->CanId == (CanId & 0xFFFu))) //&&(CANTP_DIAGFRAME_LENTH==CanDlc))  add by cj
        {
            PduInfo.SduLength = CanDlc;
            PduInfo.SduDataPtr = CanSduPtr;
//            pCh->RxInd(pCh->TpPduId,&PduInfo);
            if((8 <= CanDlc) && (CanDlc<= 15))/*zhouchao fix to ignore FC with short DLC*/
            {
            	PduInfo.SduLength = CANdataLenght[CanDlc];
            	if(((0x7df==CanId)&&(CanSduPtr[0] == 0x02)) && (CanSduPtr[1] == 0x3E) && (CanSduPtr[2] == 0x80))
				{
            		if ((Dcm_GetSessionMode() == DCM_SESSION_DEFAULT))
            		{
            			Dcm_StopP3cTimer();
            		}
            		else
            		{
            			Dcm_StartP3cTimer();
            		}
				}
            	else
            	{
            		pCh->RxInd(pCh->TpPduId,&PduInfo);
            	}
            }
            break;         
        }
        pCh++;
    }
}


/******************************************************************************/
/*
 * Brief               CAN If Main Function
 * ServiceId           None
 * Sync/Async          Synchronous
 * Reentrancy          Non Reentrant
 * Param-Name[in]      None      
 * Param-Name[out]     None
 * Param-Name[in/out]  None
 * Return              None  
 * PreCondition        None  
 * CallByAPI           CanIf_MainFunction 
 */
/******************************************************************************/
void Uds_CanIf_MainFunction(void)
{
    #if (CAN_TX_PROCESSING == CAN_POLLING)    
	Can_17_MCanP_MainFunction_Write();
    #endif
    #if (CAN_RX_PROCESSING == CAN_POLLING) 
    Can_17_MCanP_MainFunction_Read();
    #endif
    #if (CAN_WAKEUP_PROCESSING == CAN_POLLING)
    Can_MainFunction_Wakeup();
    #endif
    #if (CAN_BUSOFF_PROCESSING == CAN_POLLING)
    Can_MainFunction_BusOff();
    #endif
}

/*=======[E N D   O F   F I L E]==============================================*/
