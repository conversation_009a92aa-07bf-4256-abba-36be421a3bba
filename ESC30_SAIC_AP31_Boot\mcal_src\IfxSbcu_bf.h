/**
 * \file IfxSbcu_bf.h
 * \brief
 * \copyright Copyright (c) 2016 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC23XADAS_UM_V1.1.R1
 * Specification: tc23xadas_um_sfrs_MCSFR.xml (Revision: UM_V1.1)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Sbcu_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Sbcu
 * 
 */
#ifndef IFXSBCU_BF_H
#define IFXSBCU_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Sbcu_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN0 */
#define IFX_SBCU_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN0 */
#define IFX_SBCU_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN0 */
#define IFX_SBCU_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN10 */
#define IFX_SBCU_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN10 */
#define IFX_SBCU_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN10 */
#define IFX_SBCU_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN11 */
#define IFX_SBCU_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN11 */
#define IFX_SBCU_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN11 */
#define IFX_SBCU_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN12 */
#define IFX_SBCU_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN12 */
#define IFX_SBCU_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN12 */
#define IFX_SBCU_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN13 */
#define IFX_SBCU_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN13 */
#define IFX_SBCU_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN13 */
#define IFX_SBCU_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN14 */
#define IFX_SBCU_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN14 */
#define IFX_SBCU_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN14 */
#define IFX_SBCU_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN15 */
#define IFX_SBCU_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN15 */
#define IFX_SBCU_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN15 */
#define IFX_SBCU_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN16 */
#define IFX_SBCU_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN16 */
#define IFX_SBCU_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN16 */
#define IFX_SBCU_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN17 */
#define IFX_SBCU_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN17 */
#define IFX_SBCU_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN17 */
#define IFX_SBCU_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN18 */
#define IFX_SBCU_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN18 */
#define IFX_SBCU_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN18 */
#define IFX_SBCU_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN19 */
#define IFX_SBCU_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN19 */
#define IFX_SBCU_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN19 */
#define IFX_SBCU_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN1 */
#define IFX_SBCU_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN1 */
#define IFX_SBCU_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN1 */
#define IFX_SBCU_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN20 */
#define IFX_SBCU_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN20 */
#define IFX_SBCU_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN20 */
#define IFX_SBCU_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN21 */
#define IFX_SBCU_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN21 */
#define IFX_SBCU_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN21 */
#define IFX_SBCU_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN22 */
#define IFX_SBCU_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN22 */
#define IFX_SBCU_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN22 */
#define IFX_SBCU_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN23 */
#define IFX_SBCU_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN23 */
#define IFX_SBCU_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN23 */
#define IFX_SBCU_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN24 */
#define IFX_SBCU_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN24 */
#define IFX_SBCU_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN24 */
#define IFX_SBCU_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN25 */
#define IFX_SBCU_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN25 */
#define IFX_SBCU_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN25 */
#define IFX_SBCU_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN26 */
#define IFX_SBCU_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN26 */
#define IFX_SBCU_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN26 */
#define IFX_SBCU_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN27 */
#define IFX_SBCU_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN27 */
#define IFX_SBCU_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN27 */
#define IFX_SBCU_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN28 */
#define IFX_SBCU_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN28 */
#define IFX_SBCU_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN28 */
#define IFX_SBCU_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN29 */
#define IFX_SBCU_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN29 */
#define IFX_SBCU_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN29 */
#define IFX_SBCU_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN2 */
#define IFX_SBCU_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN2 */
#define IFX_SBCU_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN2 */
#define IFX_SBCU_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN30 */
#define IFX_SBCU_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN30 */
#define IFX_SBCU_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN30 */
#define IFX_SBCU_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN31 */
#define IFX_SBCU_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN31 */
#define IFX_SBCU_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN31 */
#define IFX_SBCU_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN3 */
#define IFX_SBCU_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN3 */
#define IFX_SBCU_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN3 */
#define IFX_SBCU_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN4 */
#define IFX_SBCU_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN4 */
#define IFX_SBCU_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN4 */
#define IFX_SBCU_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN5 */
#define IFX_SBCU_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN5 */
#define IFX_SBCU_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN5 */
#define IFX_SBCU_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN6 */
#define IFX_SBCU_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN6 */
#define IFX_SBCU_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN6 */
#define IFX_SBCU_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN7 */
#define IFX_SBCU_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN7 */
#define IFX_SBCU_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN7 */
#define IFX_SBCU_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN8 */
#define IFX_SBCU_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN8 */
#define IFX_SBCU_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN8 */
#define IFX_SBCU_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_SBCU_ACCEN0_Bits.EN9 */
#define IFX_SBCU_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ACCEN0_Bits.EN9 */
#define IFX_SBCU_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ACCEN0_Bits.EN9 */
#define IFX_SBCU_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_SBCU_CON_Bits.DBG */
#define IFX_SBCU_CON_DBG_LEN (1u)

/** \brief  Mask for Ifx_SBCU_CON_Bits.DBG */
#define IFX_SBCU_CON_DBG_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_CON_Bits.DBG */
#define IFX_SBCU_CON_DBG_OFF (16u)

/** \brief  Length for Ifx_SBCU_CON_Bits.SPC */
#define IFX_SBCU_CON_SPC_LEN (8u)

/** \brief  Mask for Ifx_SBCU_CON_Bits.SPC */
#define IFX_SBCU_CON_SPC_MSK (0xffu)

/** \brief  Offset for Ifx_SBCU_CON_Bits.SPC */
#define IFX_SBCU_CON_SPC_OFF (24u)

/** \brief  Length for Ifx_SBCU_CON_Bits.TOUT */
#define IFX_SBCU_CON_TOUT_LEN (16u)

/** \brief  Mask for Ifx_SBCU_CON_Bits.TOUT */
#define IFX_SBCU_CON_TOUT_MSK (0xffffu)

/** \brief  Offset for Ifx_SBCU_CON_Bits.TOUT */
#define IFX_SBCU_CON_TOUT_OFF (0u)

/** \brief  Length for Ifx_SBCU_DBADR1_Bits.ADR1 */
#define IFX_SBCU_DBADR1_ADR1_LEN (32u)

/** \brief  Mask for Ifx_SBCU_DBADR1_Bits.ADR1 */
#define IFX_SBCU_DBADR1_ADR1_MSK (0xffffffffu)

/** \brief  Offset for Ifx_SBCU_DBADR1_Bits.ADR1 */
#define IFX_SBCU_DBADR1_ADR1_OFF (0u)

/** \brief  Length for Ifx_SBCU_DBADR2_Bits.ADR2 */
#define IFX_SBCU_DBADR2_ADR2_LEN (32u)

/** \brief  Mask for Ifx_SBCU_DBADR2_Bits.ADR2 */
#define IFX_SBCU_DBADR2_ADR2_MSK (0xffffffffu)

/** \brief  Offset for Ifx_SBCU_DBADR2_Bits.ADR2 */
#define IFX_SBCU_DBADR2_ADR2_OFF (0u)

/** \brief  Length for Ifx_SBCU_DBADRT_Bits.FPIADR */
#define IFX_SBCU_DBADRT_FPIADR_LEN (32u)

/** \brief  Mask for Ifx_SBCU_DBADRT_Bits.FPIADR */
#define IFX_SBCU_DBADRT_FPIADR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_SBCU_DBADRT_Bits.FPIADR */
#define IFX_SBCU_DBADRT_FPIADR_OFF (0u)

/** \brief  Length for Ifx_SBCU_DBBOS_Bits.OPC */
#define IFX_SBCU_DBBOS_OPC_LEN (4u)

/** \brief  Mask for Ifx_SBCU_DBBOS_Bits.OPC */
#define IFX_SBCU_DBBOS_OPC_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_DBBOS_Bits.OPC */
#define IFX_SBCU_DBBOS_OPC_OFF (0u)

/** \brief  Length for Ifx_SBCU_DBBOS_Bits.RD */
#define IFX_SBCU_DBBOS_RD_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBBOS_Bits.RD */
#define IFX_SBCU_DBBOS_RD_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBBOS_Bits.RD */
#define IFX_SBCU_DBBOS_RD_OFF (12u)

/** \brief  Length for Ifx_SBCU_DBBOS_Bits.SVM */
#define IFX_SBCU_DBBOS_SVM_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBBOS_Bits.SVM */
#define IFX_SBCU_DBBOS_SVM_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBBOS_Bits.SVM */
#define IFX_SBCU_DBBOS_SVM_OFF (4u)

/** \brief  Length for Ifx_SBCU_DBBOS_Bits.WR */
#define IFX_SBCU_DBBOS_WR_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBBOS_Bits.WR */
#define IFX_SBCU_DBBOS_WR_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBBOS_Bits.WR */
#define IFX_SBCU_DBBOS_WR_OFF (8u)

/** \brief  Length for Ifx_SBCU_DBBOST_Bits.ENDINIT */
#define IFX_SBCU_DBBOST_ENDINIT_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBBOST_Bits.ENDINIT */
#define IFX_SBCU_DBBOST_ENDINIT_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBBOST_Bits.ENDINIT */
#define IFX_SBCU_DBBOST_ENDINIT_OFF (15u)

/** \brief  Length for Ifx_SBCU_DBBOST_Bits.FPIABORT */
#define IFX_SBCU_DBBOST_FPIABORT_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBBOST_Bits.FPIABORT */
#define IFX_SBCU_DBBOST_FPIABORT_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBBOST_Bits.FPIABORT */
#define IFX_SBCU_DBBOST_FPIABORT_OFF (13u)

/** \brief  Length for Ifx_SBCU_DBBOST_Bits.FPIACK */
#define IFX_SBCU_DBBOST_FPIACK_LEN (2u)

/** \brief  Mask for Ifx_SBCU_DBBOST_Bits.FPIACK */
#define IFX_SBCU_DBBOST_FPIACK_MSK (0x3u)

/** \brief  Offset for Ifx_SBCU_DBBOST_Bits.FPIACK */
#define IFX_SBCU_DBBOST_FPIACK_OFF (5u)

/** \brief  Length for Ifx_SBCU_DBBOST_Bits.FPIOPC */
#define IFX_SBCU_DBBOST_FPIOPC_LEN (4u)

/** \brief  Mask for Ifx_SBCU_DBBOST_Bits.FPIOPC */
#define IFX_SBCU_DBBOST_FPIOPC_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_DBBOST_Bits.FPIOPC */
#define IFX_SBCU_DBBOST_FPIOPC_OFF (0u)

/** \brief  Length for Ifx_SBCU_DBBOST_Bits.FPIOPS */
#define IFX_SBCU_DBBOST_FPIOPS_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBBOST_Bits.FPIOPS */
#define IFX_SBCU_DBBOST_FPIOPS_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBBOST_Bits.FPIOPS */
#define IFX_SBCU_DBBOST_FPIOPS_OFF (11u)

/** \brief  Length for Ifx_SBCU_DBBOST_Bits.FPIRD */
#define IFX_SBCU_DBBOST_FPIRD_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBBOST_Bits.FPIRD */
#define IFX_SBCU_DBBOST_FPIRD_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBBOST_Bits.FPIRD */
#define IFX_SBCU_DBBOST_FPIRD_OFF (12u)

/** \brief  Length for Ifx_SBCU_DBBOST_Bits.FPIRDY */
#define IFX_SBCU_DBBOST_FPIRDY_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBBOST_Bits.FPIRDY */
#define IFX_SBCU_DBBOST_FPIRDY_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBBOST_Bits.FPIRDY */
#define IFX_SBCU_DBBOST_FPIRDY_OFF (7u)

/** \brief  Length for Ifx_SBCU_DBBOST_Bits.FPIRST */
#define IFX_SBCU_DBBOST_FPIRST_LEN (2u)

/** \brief  Mask for Ifx_SBCU_DBBOST_Bits.FPIRST */
#define IFX_SBCU_DBBOST_FPIRST_MSK (0x3u)

/** \brief  Offset for Ifx_SBCU_DBBOST_Bits.FPIRST */
#define IFX_SBCU_DBBOST_FPIRST_OFF (9u)

/** \brief  Length for Ifx_SBCU_DBBOST_Bits.FPISVM */
#define IFX_SBCU_DBBOST_FPISVM_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBBOST_Bits.FPISVM */
#define IFX_SBCU_DBBOST_FPISVM_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBBOST_Bits.FPISVM */
#define IFX_SBCU_DBBOST_FPISVM_OFF (4u)

/** \brief  Length for Ifx_SBCU_DBBOST_Bits.FPITAG */
#define IFX_SBCU_DBBOST_FPITAG_LEN (6u)

/** \brief  Mask for Ifx_SBCU_DBBOST_Bits.FPITAG */
#define IFX_SBCU_DBBOST_FPITAG_MSK (0x3fu)

/** \brief  Offset for Ifx_SBCU_DBBOST_Bits.FPITAG */
#define IFX_SBCU_DBBOST_FPITAG_OFF (16u)

/** \brief  Length for Ifx_SBCU_DBBOST_Bits.FPITOUT */
#define IFX_SBCU_DBBOST_FPITOUT_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBBOST_Bits.FPITOUT */
#define IFX_SBCU_DBBOST_FPITOUT_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBBOST_Bits.FPITOUT */
#define IFX_SBCU_DBBOST_FPITOUT_OFF (14u)

/** \brief  Length for Ifx_SBCU_DBBOST_Bits.FPIWR */
#define IFX_SBCU_DBBOST_FPIWR_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBBOST_Bits.FPIWR */
#define IFX_SBCU_DBBOST_FPIWR_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBBOST_Bits.FPIWR */
#define IFX_SBCU_DBBOST_FPIWR_OFF (8u)

/** \brief  Length for Ifx_SBCU_DBCNTL_Bits.CONCOM0 */
#define IFX_SBCU_DBCNTL_CONCOM0_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBCNTL_Bits.CONCOM0 */
#define IFX_SBCU_DBCNTL_CONCOM0_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBCNTL_Bits.CONCOM0 */
#define IFX_SBCU_DBCNTL_CONCOM0_OFF (12u)

/** \brief  Length for Ifx_SBCU_DBCNTL_Bits.CONCOM1 */
#define IFX_SBCU_DBCNTL_CONCOM1_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBCNTL_Bits.CONCOM1 */
#define IFX_SBCU_DBCNTL_CONCOM1_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBCNTL_Bits.CONCOM1 */
#define IFX_SBCU_DBCNTL_CONCOM1_OFF (13u)

/** \brief  Length for Ifx_SBCU_DBCNTL_Bits.CONCOM2 */
#define IFX_SBCU_DBCNTL_CONCOM2_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBCNTL_Bits.CONCOM2 */
#define IFX_SBCU_DBCNTL_CONCOM2_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBCNTL_Bits.CONCOM2 */
#define IFX_SBCU_DBCNTL_CONCOM2_OFF (14u)

/** \brief  Length for Ifx_SBCU_DBCNTL_Bits.EO */
#define IFX_SBCU_DBCNTL_EO_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBCNTL_Bits.EO */
#define IFX_SBCU_DBCNTL_EO_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBCNTL_Bits.EO */
#define IFX_SBCU_DBCNTL_EO_OFF (0u)

/** \brief  Length for Ifx_SBCU_DBCNTL_Bits.OA */
#define IFX_SBCU_DBCNTL_OA_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBCNTL_Bits.OA */
#define IFX_SBCU_DBCNTL_OA_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBCNTL_Bits.OA */
#define IFX_SBCU_DBCNTL_OA_OFF (1u)

/** \brief  Length for Ifx_SBCU_DBCNTL_Bits.ONA1 */
#define IFX_SBCU_DBCNTL_ONA1_LEN (2u)

/** \brief  Mask for Ifx_SBCU_DBCNTL_Bits.ONA1 */
#define IFX_SBCU_DBCNTL_ONA1_MSK (0x3u)

/** \brief  Offset for Ifx_SBCU_DBCNTL_Bits.ONA1 */
#define IFX_SBCU_DBCNTL_ONA1_OFF (20u)

/** \brief  Length for Ifx_SBCU_DBCNTL_Bits.ONA2 */
#define IFX_SBCU_DBCNTL_ONA2_LEN (2u)

/** \brief  Mask for Ifx_SBCU_DBCNTL_Bits.ONA2 */
#define IFX_SBCU_DBCNTL_ONA2_MSK (0x3u)

/** \brief  Offset for Ifx_SBCU_DBCNTL_Bits.ONA2 */
#define IFX_SBCU_DBCNTL_ONA2_OFF (24u)

/** \brief  Length for Ifx_SBCU_DBCNTL_Bits.ONBOS0 */
#define IFX_SBCU_DBCNTL_ONBOS0_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBCNTL_Bits.ONBOS0 */
#define IFX_SBCU_DBCNTL_ONBOS0_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBCNTL_Bits.ONBOS0 */
#define IFX_SBCU_DBCNTL_ONBOS0_OFF (28u)

/** \brief  Length for Ifx_SBCU_DBCNTL_Bits.ONBOS1 */
#define IFX_SBCU_DBCNTL_ONBOS1_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBCNTL_Bits.ONBOS1 */
#define IFX_SBCU_DBCNTL_ONBOS1_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBCNTL_Bits.ONBOS1 */
#define IFX_SBCU_DBCNTL_ONBOS1_OFF (29u)

/** \brief  Length for Ifx_SBCU_DBCNTL_Bits.ONBOS2 */
#define IFX_SBCU_DBCNTL_ONBOS2_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBCNTL_Bits.ONBOS2 */
#define IFX_SBCU_DBCNTL_ONBOS2_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBCNTL_Bits.ONBOS2 */
#define IFX_SBCU_DBCNTL_ONBOS2_OFF (30u)

/** \brief  Length for Ifx_SBCU_DBCNTL_Bits.ONBOS3 */
#define IFX_SBCU_DBCNTL_ONBOS3_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBCNTL_Bits.ONBOS3 */
#define IFX_SBCU_DBCNTL_ONBOS3_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBCNTL_Bits.ONBOS3 */
#define IFX_SBCU_DBCNTL_ONBOS3_OFF (31u)

/** \brief  Length for Ifx_SBCU_DBCNTL_Bits.ONG */
#define IFX_SBCU_DBCNTL_ONG_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBCNTL_Bits.ONG */
#define IFX_SBCU_DBCNTL_ONG_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBCNTL_Bits.ONG */
#define IFX_SBCU_DBCNTL_ONG_OFF (16u)

/** \brief  Length for Ifx_SBCU_DBCNTL_Bits.RA */
#define IFX_SBCU_DBCNTL_RA_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBCNTL_Bits.RA */
#define IFX_SBCU_DBCNTL_RA_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBCNTL_Bits.RA */
#define IFX_SBCU_DBCNTL_RA_OFF (4u)

/** \brief  Length for Ifx_SBCU_DBDAT_Bits.FPIDATA */
#define IFX_SBCU_DBDAT_FPIDATA_LEN (32u)

/** \brief  Mask for Ifx_SBCU_DBDAT_Bits.FPIDATA */
#define IFX_SBCU_DBDAT_FPIDATA_MSK (0xffffffffu)

/** \brief  Offset for Ifx_SBCU_DBDAT_Bits.FPIDATA */
#define IFX_SBCU_DBDAT_FPIDATA_OFF (0u)

/** \brief  Length for Ifx_SBCU_DBGNTT_Bits.CPU0 */
#define IFX_SBCU_DBGNTT_CPU0_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBGNTT_Bits.CPU0 */
#define IFX_SBCU_DBGNTT_CPU0_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBGNTT_Bits.CPU0 */
#define IFX_SBCU_DBGNTT_CPU0_OFF (7u)

/** \brief  Length for Ifx_SBCU_DBGNTT_Bits.DMACHNR */
#define IFX_SBCU_DBGNTT_DMACHNR_LEN (8u)

/** \brief  Mask for Ifx_SBCU_DBGNTT_Bits.DMACHNR */
#define IFX_SBCU_DBGNTT_DMACHNR_MSK (0xffu)

/** \brief  Offset for Ifx_SBCU_DBGNTT_Bits.DMACHNR */
#define IFX_SBCU_DBGNTT_DMACHNR_OFF (16u)

/** \brief  Length for Ifx_SBCU_DBGNTT_Bits.DMAH */
#define IFX_SBCU_DBGNTT_DMAH_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBGNTT_Bits.DMAH */
#define IFX_SBCU_DBGNTT_DMAH_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBGNTT_Bits.DMAH */
#define IFX_SBCU_DBGNTT_DMAH_OFF (0u)

/** \brief  Length for Ifx_SBCU_DBGNTT_Bits.DMAL */
#define IFX_SBCU_DBGNTT_DMAL_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBGNTT_Bits.DMAL */
#define IFX_SBCU_DBGNTT_DMAL_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBGNTT_Bits.DMAL */
#define IFX_SBCU_DBGNTT_DMAL_OFF (15u)

/** \brief  Length for Ifx_SBCU_DBGNTT_Bits.DMAM */
#define IFX_SBCU_DBGNTT_DMAM_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBGNTT_Bits.DMAM */
#define IFX_SBCU_DBGNTT_DMAM_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBGNTT_Bits.DMAM */
#define IFX_SBCU_DBGNTT_DMAM_OFF (5u)

/** \brief  Length for Ifx_SBCU_DBGNTT_Bits.HSMCMI */
#define IFX_SBCU_DBGNTT_HSMCMI_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBGNTT_Bits.HSMCMI */
#define IFX_SBCU_DBGNTT_HSMCMI_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBGNTT_Bits.HSMCMI */
#define IFX_SBCU_DBGNTT_HSMCMI_OFF (13u)

/** \brief  Length for Ifx_SBCU_DBGNTT_Bits.HSMRMI */
#define IFX_SBCU_DBGNTT_HSMRMI_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBGNTT_Bits.HSMRMI */
#define IFX_SBCU_DBGNTT_HSMRMI_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBGNTT_Bits.HSMRMI */
#define IFX_SBCU_DBGNTT_HSMRMI_OFF (12u)

/** \brief  Length for Ifx_SBCU_DBGRNT_Bits.CPU0 */
#define IFX_SBCU_DBGRNT_CPU0_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBGRNT_Bits.CPU0 */
#define IFX_SBCU_DBGRNT_CPU0_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBGRNT_Bits.CPU0 */
#define IFX_SBCU_DBGRNT_CPU0_OFF (7u)

/** \brief  Length for Ifx_SBCU_DBGRNT_Bits.DMAH */
#define IFX_SBCU_DBGRNT_DMAH_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBGRNT_Bits.DMAH */
#define IFX_SBCU_DBGRNT_DMAH_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBGRNT_Bits.DMAH */
#define IFX_SBCU_DBGRNT_DMAH_OFF (0u)

/** \brief  Length for Ifx_SBCU_DBGRNT_Bits.DMAL */
#define IFX_SBCU_DBGRNT_DMAL_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBGRNT_Bits.DMAL */
#define IFX_SBCU_DBGRNT_DMAL_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBGRNT_Bits.DMAL */
#define IFX_SBCU_DBGRNT_DMAL_OFF (15u)

/** \brief  Length for Ifx_SBCU_DBGRNT_Bits.DMAM */
#define IFX_SBCU_DBGRNT_DMAM_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBGRNT_Bits.DMAM */
#define IFX_SBCU_DBGRNT_DMAM_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBGRNT_Bits.DMAM */
#define IFX_SBCU_DBGRNT_DMAM_OFF (5u)

/** \brief  Length for Ifx_SBCU_DBGRNT_Bits.HSMCMI */
#define IFX_SBCU_DBGRNT_HSMCMI_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBGRNT_Bits.HSMCMI */
#define IFX_SBCU_DBGRNT_HSMCMI_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBGRNT_Bits.HSMCMI */
#define IFX_SBCU_DBGRNT_HSMCMI_OFF (13u)

/** \brief  Length for Ifx_SBCU_DBGRNT_Bits.HSMRMI */
#define IFX_SBCU_DBGRNT_HSMRMI_LEN (1u)

/** \brief  Mask for Ifx_SBCU_DBGRNT_Bits.HSMRMI */
#define IFX_SBCU_DBGRNT_HSMRMI_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_DBGRNT_Bits.HSMRMI */
#define IFX_SBCU_DBGRNT_HSMRMI_OFF (12u)

/** \brief  Length for Ifx_SBCU_EADD_Bits.FPIADR */
#define IFX_SBCU_EADD_FPIADR_LEN (32u)

/** \brief  Mask for Ifx_SBCU_EADD_Bits.FPIADR */
#define IFX_SBCU_EADD_FPIADR_MSK (0xffffffffu)

/** \brief  Offset for Ifx_SBCU_EADD_Bits.FPIADR */
#define IFX_SBCU_EADD_FPIADR_OFF (0u)

/** \brief  Length for Ifx_SBCU_ECON_Bits.ABT */
#define IFX_SBCU_ECON_ABT_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ECON_Bits.ABT */
#define IFX_SBCU_ECON_ABT_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ECON_Bits.ABT */
#define IFX_SBCU_ECON_ABT_OFF (16u)

/** \brief  Length for Ifx_SBCU_ECON_Bits.ACK */
#define IFX_SBCU_ECON_ACK_LEN (2u)

/** \brief  Mask for Ifx_SBCU_ECON_Bits.ACK */
#define IFX_SBCU_ECON_ACK_MSK (0x3u)

/** \brief  Offset for Ifx_SBCU_ECON_Bits.ACK */
#define IFX_SBCU_ECON_ACK_OFF (17u)

/** \brief  Length for Ifx_SBCU_ECON_Bits.ERRCNT */
#define IFX_SBCU_ECON_ERRCNT_LEN (14u)

/** \brief  Mask for Ifx_SBCU_ECON_Bits.ERRCNT */
#define IFX_SBCU_ECON_ERRCNT_MSK (0x3fffu)

/** \brief  Offset for Ifx_SBCU_ECON_Bits.ERRCNT */
#define IFX_SBCU_ECON_ERRCNT_OFF (0u)

/** \brief  Length for Ifx_SBCU_ECON_Bits.OPC */
#define IFX_SBCU_ECON_OPC_LEN (4u)

/** \brief  Mask for Ifx_SBCU_ECON_Bits.OPC */
#define IFX_SBCU_ECON_OPC_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_ECON_Bits.OPC */
#define IFX_SBCU_ECON_OPC_OFF (28u)

/** \brief  Length for Ifx_SBCU_ECON_Bits.RDN */
#define IFX_SBCU_ECON_RDN_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ECON_Bits.RDN */
#define IFX_SBCU_ECON_RDN_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ECON_Bits.RDN */
#define IFX_SBCU_ECON_RDN_OFF (21u)

/** \brief  Length for Ifx_SBCU_ECON_Bits.RDY */
#define IFX_SBCU_ECON_RDY_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ECON_Bits.RDY */
#define IFX_SBCU_ECON_RDY_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ECON_Bits.RDY */
#define IFX_SBCU_ECON_RDY_OFF (15u)

/** \brief  Length for Ifx_SBCU_ECON_Bits.SVM */
#define IFX_SBCU_ECON_SVM_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ECON_Bits.SVM */
#define IFX_SBCU_ECON_SVM_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ECON_Bits.SVM */
#define IFX_SBCU_ECON_SVM_OFF (19u)

/** \brief  Length for Ifx_SBCU_ECON_Bits.TAG */
#define IFX_SBCU_ECON_TAG_LEN (6u)

/** \brief  Mask for Ifx_SBCU_ECON_Bits.TAG */
#define IFX_SBCU_ECON_TAG_MSK (0x3fu)

/** \brief  Offset for Ifx_SBCU_ECON_Bits.TAG */
#define IFX_SBCU_ECON_TAG_OFF (22u)

/** \brief  Length for Ifx_SBCU_ECON_Bits.TOUT */
#define IFX_SBCU_ECON_TOUT_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ECON_Bits.TOUT */
#define IFX_SBCU_ECON_TOUT_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ECON_Bits.TOUT */
#define IFX_SBCU_ECON_TOUT_OFF (14u)

/** \brief  Length for Ifx_SBCU_ECON_Bits.WRN */
#define IFX_SBCU_ECON_WRN_LEN (1u)

/** \brief  Mask for Ifx_SBCU_ECON_Bits.WRN */
#define IFX_SBCU_ECON_WRN_MSK (0x1u)

/** \brief  Offset for Ifx_SBCU_ECON_Bits.WRN */
#define IFX_SBCU_ECON_WRN_OFF (20u)

/** \brief  Length for Ifx_SBCU_EDAT_Bits.FPIDAT */
#define IFX_SBCU_EDAT_FPIDAT_LEN (32u)

/** \brief  Mask for Ifx_SBCU_EDAT_Bits.FPIDAT */
#define IFX_SBCU_EDAT_FPIDAT_MSK (0xffffffffu)

/** \brief  Offset for Ifx_SBCU_EDAT_Bits.FPIDAT */
#define IFX_SBCU_EDAT_FPIDAT_OFF (0u)

/** \brief  Length for Ifx_SBCU_ID_Bits.MODNUMBER */
#define IFX_SBCU_ID_MODNUMBER_LEN (8u)

/** \brief  Mask for Ifx_SBCU_ID_Bits.MODNUMBER */
#define IFX_SBCU_ID_MODNUMBER_MSK (0xffu)

/** \brief  Offset for Ifx_SBCU_ID_Bits.MODNUMBER */
#define IFX_SBCU_ID_MODNUMBER_OFF (8u)

/** \brief  Length for Ifx_SBCU_ID_Bits.MODREV */
#define IFX_SBCU_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_SBCU_ID_Bits.MODREV */
#define IFX_SBCU_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_SBCU_ID_Bits.MODREV */
#define IFX_SBCU_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_SBCU_PRIOH_Bits.DMAL */
#define IFX_SBCU_PRIOH_DMAL_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOH_Bits.DMAL */
#define IFX_SBCU_PRIOH_DMAL_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOH_Bits.DMAL */
#define IFX_SBCU_PRIOH_DMAL_OFF (28u)

/** \brief  Length for Ifx_SBCU_PRIOH_Bits.HSMCMI */
#define IFX_SBCU_PRIOH_HSMCMI_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOH_Bits.HSMCMI */
#define IFX_SBCU_PRIOH_HSMCMI_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOH_Bits.HSMCMI */
#define IFX_SBCU_PRIOH_HSMCMI_OFF (20u)

/** \brief  Length for Ifx_SBCU_PRIOH_Bits.HSMRMI */
#define IFX_SBCU_PRIOH_HSMRMI_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOH_Bits.HSMRMI */
#define IFX_SBCU_PRIOH_HSMRMI_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOH_Bits.HSMRMI */
#define IFX_SBCU_PRIOH_HSMRMI_OFF (16u)

/** \brief  Length for Ifx_SBCU_PRIOH_Bits.RESERVED8 */
#define IFX_SBCU_PRIOH_RESERVED8_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOH_Bits.RESERVED8 */
#define IFX_SBCU_PRIOH_RESERVED8_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOH_Bits.RESERVED8 */
#define IFX_SBCU_PRIOH_RESERVED8_OFF (0u)

/** \brief  Length for Ifx_SBCU_PRIOH_Bits.RESERVED9 */
#define IFX_SBCU_PRIOH_RESERVED9_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOH_Bits.RESERVED9 */
#define IFX_SBCU_PRIOH_RESERVED9_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOH_Bits.RESERVED9 */
#define IFX_SBCU_PRIOH_RESERVED9_OFF (4u)

/** \brief  Length for Ifx_SBCU_PRIOH_Bits.RESERVEDA */
#define IFX_SBCU_PRIOH_RESERVEDA_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOH_Bits.RESERVEDA */
#define IFX_SBCU_PRIOH_RESERVEDA_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOH_Bits.RESERVEDA */
#define IFX_SBCU_PRIOH_RESERVEDA_OFF (8u)

/** \brief  Length for Ifx_SBCU_PRIOH_Bits.RESERVEDB */
#define IFX_SBCU_PRIOH_RESERVEDB_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOH_Bits.RESERVEDB */
#define IFX_SBCU_PRIOH_RESERVEDB_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOH_Bits.RESERVEDB */
#define IFX_SBCU_PRIOH_RESERVEDB_OFF (12u)

/** \brief  Length for Ifx_SBCU_PRIOH_Bits.RESERVEDE */
#define IFX_SBCU_PRIOH_RESERVEDE_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOH_Bits.RESERVEDE */
#define IFX_SBCU_PRIOH_RESERVEDE_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOH_Bits.RESERVEDE */
#define IFX_SBCU_PRIOH_RESERVEDE_OFF (24u)

/** \brief  Length for Ifx_SBCU_PRIOL_Bits.CPU0 */
#define IFX_SBCU_PRIOL_CPU0_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOL_Bits.CPU0 */
#define IFX_SBCU_PRIOL_CPU0_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOL_Bits.CPU0 */
#define IFX_SBCU_PRIOL_CPU0_OFF (28u)

/** \brief  Length for Ifx_SBCU_PRIOL_Bits.DMAH */
#define IFX_SBCU_PRIOL_DMAH_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOL_Bits.DMAH */
#define IFX_SBCU_PRIOL_DMAH_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOL_Bits.DMAH */
#define IFX_SBCU_PRIOL_DMAH_OFF (0u)

/** \brief  Length for Ifx_SBCU_PRIOL_Bits.DMAM */
#define IFX_SBCU_PRIOL_DMAM_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOL_Bits.DMAM */
#define IFX_SBCU_PRIOL_DMAM_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOL_Bits.DMAM */
#define IFX_SBCU_PRIOL_DMAM_OFF (20u)

/** \brief  Length for Ifx_SBCU_PRIOL_Bits.RESERVED1 */
#define IFX_SBCU_PRIOL_RESERVED1_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOL_Bits.RESERVED1 */
#define IFX_SBCU_PRIOL_RESERVED1_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOL_Bits.RESERVED1 */
#define IFX_SBCU_PRIOL_RESERVED1_OFF (4u)

/** \brief  Length for Ifx_SBCU_PRIOL_Bits.RESERVED2 */
#define IFX_SBCU_PRIOL_RESERVED2_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOL_Bits.RESERVED2 */
#define IFX_SBCU_PRIOL_RESERVED2_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOL_Bits.RESERVED2 */
#define IFX_SBCU_PRIOL_RESERVED2_OFF (8u)

/** \brief  Length for Ifx_SBCU_PRIOL_Bits.RESERVED3 */
#define IFX_SBCU_PRIOL_RESERVED3_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOL_Bits.RESERVED3 */
#define IFX_SBCU_PRIOL_RESERVED3_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOL_Bits.RESERVED3 */
#define IFX_SBCU_PRIOL_RESERVED3_OFF (12u)

/** \brief  Length for Ifx_SBCU_PRIOL_Bits.RESERVED4 */
#define IFX_SBCU_PRIOL_RESERVED4_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOL_Bits.RESERVED4 */
#define IFX_SBCU_PRIOL_RESERVED4_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOL_Bits.RESERVED4 */
#define IFX_SBCU_PRIOL_RESERVED4_OFF (16u)

/** \brief  Length for Ifx_SBCU_PRIOL_Bits.RESERVED6 */
#define IFX_SBCU_PRIOL_RESERVED6_LEN (4u)

/** \brief  Mask for Ifx_SBCU_PRIOL_Bits.RESERVED6 */
#define IFX_SBCU_PRIOL_RESERVED6_MSK (0xfu)

/** \brief  Offset for Ifx_SBCU_PRIOL_Bits.RESERVED6 */
#define IFX_SBCU_PRIOL_RESERVED6_OFF (24u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXSBCU_BF_H */
