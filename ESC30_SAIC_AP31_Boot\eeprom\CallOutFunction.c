/*
 * CallOutFunction.c
 *
 *  Created on: 2021-1-11
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#include "Mcal_TcLib.h"
#include "NvM.h"

/*This Function comes from Mcal.c*/
void Mcal_SafeErrorHandler(uint32 ErrorType)
{
  volatile uint32 TimeOut;

  TimeOut = 0U;
  /* User can add the code here */
  UNUSED_PARAMETER(ErrorType)
  /* While loop added for UTP ********** */
  while(TimeOut < 0xFFFFFFFFU)
  {
    TimeOut++;
  }
  /* Control should not reach here. WDG timeout happens before this. */
  __debug();

}

void BswM_NvM_CurrentJobMode(uint8 ServiceId, NvM_RequestResultType CurrentJobMode)
{

}

void FeeIllegalStateNotification(void)
{

}

