/*
 * eeprom_Cfg.c
 *
 *  Created on: 2020-11-5
 *      Author: fangh<PERSON>qing
 */
#include "Platform_Types.h"
#include "Did_Cfg.h"
#include "FL.h"
#include "eeprom.h"
#include "eeprom_Cfg.h"
uint8 NvM_DID_FFFF[16]={0};

uint8 NvM_DID_F100[6]={0x01,0x01,0x00,0x00,0x00,0x00};
uint8 NvM_DID_F110[16]={0};
uint8 NvM_DID_F111[16]={0};
uint8 NvM_DID_F112[16]={0};
uint8 NvM_DID_F113[16]={0};
uint8 NvM_DID_F114[16]={0};
uint8 NvM_DID_F115[16]={0};
uint8 NvM_DID_F116[16]={0};
uint8 NvM_DID_F117[16]={0};
uint8 NvM_DID_F118[16]={0};
uint8 NvM_DID_F119[16]={0};
uint8 NvM_DID_F11A[16]={0};
uint8 NvM_DID_F11B[16]={0};
uint8 NvM_DID_F11C[16]={0};
uint8 NvM_DID_F11D[16]={0};
uint8 NvM_DID_F11E[16]={0};
uint8 NvM_DID_F11F[16]={0};
uint8 NvM_DID_F183[10]={0};
uint8 NvM_DID_F187[5]={0,0,0,0,0};
uint8 NvM_DID_F18A[5]={0x05,0x45,0x29,0x84,0x24};
uint8 NvM_DID_F190[17]={0};
uint8 NvM_DID_F191[5]={0x11,0x03,0x42,0x61,0x01};
uint8 NvM_DID_F192[10]={0x50, 0x47, 0x4D, 0x20, 0x20, 0x21, 0x03, 0x24, 0x00, 0x02};
uint8 NvM_DID_F194[10]={0x41, 0x50, 0x50, 0x30, 0x2E, 0x30, 0x2E, 0x34, 0x2E, 0x30};
uint8 NvM_DID_F1A0[5]={0x11,0x03,0x42,0x62,0x01};
uint8 NvM_DID_F1A1[5]={0};
uint8 NvM_DID_F1A2[8]={0x11,0x10,0x38,0x91,0x02,0x00,0x00,0x00};
uint8 NvM_DID_F1A8[20]={0};
uint8 NvM_DID_F18B[3]={0};
uint8 NvM_DID_F18C[16]={0};

uint8 NvM_DID_AFFC[2]={0};
uint8 NvM_DID_AFFD[1]={0};
uint8 NvM_DID_AFFE[1]={0};
uint8 NvM_DID_AFFF[1]={0};

uint8 NvM_DID_F120[16]={0x11, 0x10, 0x38, 0x91, 0x02, 0x00, 0x00, 0x00, 0x00,0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
uint8 NvM_DID_F121[16]={0x11, 0x10, 0x38, 0x91, 0x02, 0x00, 0x00, 0x00, 0x00,0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
uint8 NvM_DID_F198[11]={0};
uint8 NvM_DID_F1A5[3]={0};
uint8 NvM_DID_F1A9[5]={0};
uint8 NvM_DID_F1AA[5]={0};

uint8 NvM_DID_F1A3[3]={0};
uint8 NvM_DID_F1E0[1]={2};
uint8 NvM_DID_F1E1[20]={0};
uint8 NvM_DID_F1E2[20]={0};
uint8 NvM_DID_F1EA[20]={0};
uint8 NvM_DID_F1EB[20]={0};

uint8 VSS_SecurityKey_PIM[576];

uint8 Ram_File_Digest[64];
uint8 Ram_Date_Information[3];

uint8 NvM_SECERR_DATA[64]={0};
uint8 NvM_SECLOG_DATA[40]={0};

#define F110_INDEX 2

volatile const ST_NVM_DID_TYPE Nvm_Did_Cfg[]=
{

	{0xF100,FUNC_TYPE,0 ,NvM_DID_F100,	6,NULL,&EEP_GetSWVerification_F100},
	{0xF111,EEPROM_TYPE,EEP_DIDF111_BLOCK_ID ,NvM_DID_F111,	16,NULL,NULL},
	{0xF110,EEPROM_TYPE,EEP_DIDF110_BLOCK_ID ,NvM_DID_F110,	16,&EEP_SetPIF_F110,&EEP_GetPIF_F110},
	{0xF112,EEPROM_TYPE,EEP_DIDF112_BLOCK_ID ,NvM_DID_F112,	16,NULL,NULL},
	{0xF113,EEPROM_TYPE,EEP_DIDF113_BLOCK_ID ,NvM_DID_F113,	16,NULL,NULL},
	{0xF114,EEPROM_TYPE,EEP_DIDF114_BLOCK_ID ,NvM_DID_F114,	16,NULL,NULL},
	{0xF115,EEPROM_TYPE,EEP_DIDF115_BLOCK_ID ,NvM_DID_F115,	16,NULL,NULL},
	{0xF116,EEPROM_TYPE,EEP_DIDF116_BLOCK_ID ,NvM_DID_F116,	16,NULL,NULL},
	{0xF117,EEPROM_TYPE,EEP_DIDF117_BLOCK_ID ,NvM_DID_F117,	16,NULL,NULL},
	{0xF118,EEPROM_TYPE,EEP_DIDF118_BLOCK_ID ,NvM_DID_F118,	16,NULL,NULL},
	{0xF119,EEPROM_TYPE,EEP_DIDF119_BLOCK_ID ,NvM_DID_F119,	16,NULL,NULL},
	{0xF11A,EEPROM_TYPE,EEP_DIDF11A_BLOCK_ID ,NvM_DID_F11A,	16,NULL,NULL},
	{0xF11B,EEPROM_TYPE,EEP_DIDF11B_BLOCK_ID ,NvM_DID_F11B,	16,NULL,NULL},
	{0xF11C,EEPROM_TYPE,EEP_DIDF11C_BLOCK_ID ,NvM_DID_F11C,	16,NULL,NULL},
	{0xF11D,EEPROM_TYPE,EEP_DIDF11D_BLOCK_ID ,NvM_DID_F11D,	16,NULL,NULL},
	{0xF11E,EEPROM_TYPE,EEP_DIDF11E_BLOCK_ID ,NvM_DID_F11E,	16,NULL,NULL},
	{0xF11F,EEPROM_TYPE,EEP_DIDF11F_BLOCK_ID ,NvM_DID_F11F,	16,NULL,NULL},

	{0xF120,EEPROM_TYPE,EEP_DIDF120_BLOCK_ID ,NvM_DID_F120,	16,NULL,NULL},
	{0xF121,EEPROM_TYPE,EEP_DIDF121_BLOCK_ID ,NvM_DID_F121,	16,NULL,NULL},
	{0xF183,FLASH_TYPE,(uint32)&Dcm_PflashDidInfo.fbl_ref_num[0] ,NvM_DID_F183,	10,NULL,NULL},
	{0xF187,EEPROM_TYPE, EEP_DIDF187_BLOCK_ID , 	NvM_DID_F187,	5,NULL,NULL},
	{0xF18A,DEFAULT_TYPE,0 ,NvM_DID_F18A,	5,NULL,NULL},
	{0xF18B,EEPROM_TYPE,EEP_DIDF18B_BLOCK_ID ,NvM_DID_F18B,	3,NULL,NULL},
	{0xF18C,EEPROM_TYPE,EEP_DIDF18C_BLOCK_ID ,NvM_DID_F18C,	16,NULL,NULL},
	{0xF190,EEPROM_TYPE,EEP_DIDF190_BLOCK_ID ,NvM_DID_F190,	17,NULL,NULL},
	{0xF191,EEPROM_TYPE, EEP_DIDF191_BLOCK_ID , 	NvM_DID_F191,	5,NULL,NULL},
	{0xF192,EEPROM_TYPE,EEP_DIDF192_BLOCK_ID ,NvM_DID_F192,	10,NULL,NULL},
	{0xF194,FLASH_TYPE,0xA0038050,NvM_DID_F194,	10,NULL,NULL},
	{0xF198,EEPROM_TYPE,EEP_DIDF198_BLOCK_ID,NvM_DID_F198,	11,NULL,NULL},
	{0xF1A0,FLASH_TYPE,0xA0038030 ,NvM_DID_F1A0,	5,NULL,NULL},
	{0xF1A1,FLASH_TYPE,0xA0028030 ,NvM_DID_F1A1,	5,NULL,NULL},
	{0xF1A2,FLASH_TYPE,0xA0028010 ,NvM_DID_F1A2,	8,NULL,NULL},
	{0xF1A5,FLASH_TYPE,0xA0038060 ,NvM_DID_F1A5,	3,NULL,NULL},
	{0xF1A8,EEPROM_TYPE,EEP_DIDF1A8_BLOCK_ID ,NvM_DID_F1A8,	20,NULL,NULL},
	{0xF1A9,EEPROM_TYPE,EEP_DIDF1A9_BLOCK_ID ,NvM_DID_F1A9,	5,NULL,NULL},
	{0xF1AA,EEPROM_TYPE,EEP_DIDF1AA_BLOCK_ID ,NvM_DID_F1AA,	5,NULL,NULL},
	{0xAFFC,FUNC_TYPE,0 ,NvM_DID_AFFC,	2,NULL,&EEP_GetProgrammingCounter_AFFC},
	{0xAFFD,FUNC_TYPE,0 ,NvM_DID_AFFD,	1,NULL,&EEP_GetSoftwareIntegrityStatus_AFFD},
	{0xAFFE,FUNC_TYPE,0 ,NvM_DID_AFFE,	1,NULL,&EEP_GetSoftwareCompatibilityStatus_AFFE},
	{0xAFFF,FUNC_TYPE,0 ,NvM_DID_AFFF,	1,NULL,&EEP_GetSoftwareValidFlag_AFFF},



};


volatile const uint8 Nvm_Did_Cfg_Size=sizeof(Nvm_Did_Cfg)/sizeof(ST_NVM_DID_TYPE);


