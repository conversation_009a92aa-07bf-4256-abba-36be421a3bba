/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte.c
 *           Config:  PGM_BswCfg.dpa
 *      ECU-Project:  PGM_BswCfg
 *
 *        Generator:  MICROSAR RTE Generator Version 4.21.0
 *                    RTE Core Version 1.21.0
 *          License:  CBD1900770
 *
 *      Description:  RTE implementation file
 *********************************************************************************************************************/

/**********************************************************************************************************************
 *  INCLUDES
 *********************************************************************************************************************/

/* PRQA S 0777, 0779, 0857 EOF */ /* MD_MSR_Rule5.1, MD_MSR_Rule5.2, MD_MSR_Dir1.1 */

#define RTE_CORE
#include "Os.h" /* PRQA S 0828, 0883 */ /* MD_MSR_Dir1.1, MD_Rte_Os */
#include "Rte_Type.h"

#include "eeprom_Cfg.h"
#include "SchM_Adc.h"

#include "SchM_Fls_17_Pmu.h"

#include "SchM_NvM.h"


#include "Com.h"
#if defined(IL_ASRCOM_VERSION)
# define RTE_USE_COM_TXSIGNAL_RDACCESS
#endif

#include "Rte_Cbk.h"

#include "NvM.h" /* PRQA S 3451 */ /* MD_Rte_3451_NvM */

/* AUTOSAR 3.x compatibility */
#if !defined (RTE_LOCAL)
# define RTE_LOCAL static
#endif


/**********************************************************************************************************************
 * API for enable / disable interrupts global
 *********************************************************************************************************************/

#if defined(osDisableGlobalKM) && !defined(RTE_DISABLE_ENHANCED_INTERRUPT_LOCK_API)
# define Rte_DisableAllInterrupts() osDisableGlobalKM()   /* MICROSAR OS */
#else
# define Rte_DisableAllInterrupts() DisableAllInterrupts()   /* AUTOSAR OS */
#endif

#if defined(osEnableGlobalKM) && !defined(RTE_DISABLE_ENHANCED_INTERRUPT_LOCK_API)
# define Rte_EnableAllInterrupts() osEnableGlobalKM()   /* MICROSAR OS */
#else
# define Rte_EnableAllInterrupts() EnableAllInterrupts()   /* AUTOSAR OS */
#endif

/**********************************************************************************************************************
 * API for enable / disable interrupts up to the systemLevel
 *********************************************************************************************************************/

#if defined(osDisableLevelKM) && !defined(RTE_DISABLE_ENHANCED_INTERRUPT_LOCK_API)
# define Rte_DisableOSInterrupts() osDisableLevelKM()   /* MICROSAR OS */
#else
# define Rte_DisableOSInterrupts() SuspendOSInterrupts()   /* AUTOSAR OS */
#endif

#if defined(osEnableLevelKM) && !defined(RTE_DISABLE_ENHANCED_INTERRUPT_LOCK_API)
# define Rte_EnableOSInterrupts() osEnableLevelKM()   /* MICROSAR OS */
#else
# define Rte_EnableOSInterrupts() ResumeOSInterrupts()   /* AUTOSAR OS */
#endif

/**********************************************************************************************************************
 * Rte Init State Variable
 *********************************************************************************************************************/

#define RTE_START_SEC_VAR_ZERO_INIT_8BIT
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

volatile VAR(uint8, RTE_VAR_ZERO_INIT) Rte_InitState = RTE_STATE_UNINIT; /* PRQA S 3408 */ /* MD_Rte_3408 */
volatile VAR(uint8, RTE_VAR_ZERO_INIT) Rte_StartTiming_InitState = RTE_STATE_UNINIT; /* PRQA S 0850, 3408, 1514 */ /* MD_MSR_MacroArgumentEmpty, MD_Rte_3408, MD_Rte_1514 */

#define RTE_STOP_SEC_VAR_ZERO_INIT_8BIT
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 * Buffers for unqueued S/R
 *********************************************************************************************************************/

#define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_AltDiscon_BatUnderVolDuration;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_BatDiscon_BatOpenDuration;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_Bat_Normal_SOC_Threshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_Bat_UnderVoltage_Threshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize15, RTE_VAR_NOINIT) Rte_CtApNvM_BeforeLastPGMOpenInformation_B043;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize11, RTE_VAR_NOINIT) Rte_CtApNvM_ConfigurationTraceabilityField_F198;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve18_C;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve19_C;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve20;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve21;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve22;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve23;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve24;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve25;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve26;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve27;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize5, RTE_VAR_NOINIT) Rte_CtApNvM_ECUConfigurationFileNumber_F1A9;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize5, RTE_VAR_NOINIT) Rte_CtApNvM_ECUHardwareNumber_F191;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize3, RTE_VAR_NOINIT) Rte_CtApNvM_ECUIndexInformation_F1A5;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize3, RTE_VAR_NOINIT) Rte_CtApNvM_ECUManufactureDate_F18B;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize5, RTE_VAR_NOINIT) Rte_CtApNvM_ECUPartNumber_F187;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize5, RTE_VAR_NOINIT) Rte_CtApNvM_ECUProgrammingProcessFileNumber_F1AA;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_ECUSerialNumber_F18C;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30OverVoltageHigherThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30OverVoltageHigherThresholdTimeout;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30OverVoltageLowerThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30OverVoltageLowerThresholdTimeout;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30UnderVoltageHigherThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30UnderVoltageHigherThresholdTimeout;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30UnderVoltageLowerThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30UnderVoltageLowerThresholdTimeout;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_ROverVoltageHigherThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_ROverVoltageHigherThresholdTimeout;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_ROverVoltageLowerThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_ROverVoltageLowerThresholdTimeout;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_RUnderVoltageHigherThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_RUnderVoltageHigherThresholdTimeout;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_RUnderVoltageLowerThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_RUnderVoltageLowerThresholdTimeout;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize15, RTE_VAR_NOINIT) Rte_CtApNvM_LastPGMOpenInformation_B042;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_NIF_CurrentData_F121;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_NIF_FactoryData_F120;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize110, RTE_VAR_NOINIT) Rte_CtApNvM_OCV_Map;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMMosfetOverCurrentHigherThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMMosfetOverCurrentHigherThresholdTimeout;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMMosfetOverCurrentLowerThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMMosfetOverCurrentLowerThresholdTimeout;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_PGMMosfetOverTemperatureThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMVoltageStabilizationFilterTimeAfterItOpen;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGM_Recovery_Normal_Voltage_Filter_Time;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMrecoveryKL30OverVoltageThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMrecoveryKL30UnderVoltageThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMrecoveryKL30_ROverVoltageThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMrecoveryKL30_RUnderVoltageThreshold;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_PGMrecoverytimes;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F110;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F111;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F112;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F113;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F114;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F115;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F116;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F117;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F118;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F119;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F11A;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F11B;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F11C;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F11D;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F11E;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F11F;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize64, RTE_VAR_NOINIT) Rte_CtApNvM_ReseveforAPP;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize64, RTE_VAR_NOINIT) Rte_CtApNvM_ReseveforFBL;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize8, RTE_VAR_NOINIT) Rte_CtApNvM_ST_ResetBlockInfo_T;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize8, RTE_VAR_NOINIT) Rte_CtApNvM_ST_ResetMngInfo_T;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_SecurityAttemptCounter;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize40, RTE_VAR_NOINIT) Rte_CtApNvM_SecurityLog;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_Softwarecompatibilitystatus_AFFE;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_Softwarecompatibilitystatus_AFFF;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_Softwareintegritystatus_AFFD;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize10, RTE_VAR_NOINIT) Rte_CtApNvM_SupplierECUHardwareReferenceNumbe_F192;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize14, RTE_VAR_NOINIT) Rte_CtApNvM_SwitchOpenReasonRecord_B044;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize5, RTE_VAR_NOINIT) Rte_CtApNvM_SystemSupplierIdentifier_F18A;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_Usg_Md;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize17, RTE_VAR_NOINIT) Rte_CtApNvM_VIN_F190;
/* PRQA L:L1 */
/* PRQA S 3408, 1504, 1514 L1 */ /* MD_Rte_3408, MD_MSR_Rule8.7, MD_Rte_1514 */
VAR(IdtNvM_u8AryNvBlkSize20, RTE_VAR_NOINIT) Rte_CtApNvM_VehicleFeatureInformation_F1A8;
/* PRQA L:L1 */

#define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 * Constants
 *********************************************************************************************************************/

#define RTE_START_SEC_CONST_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize1_0 = {
  0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize1_2 = {
  48U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize10, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize10_0 = {
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize11, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize11_0 = {
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize110, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize110_0 = {
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize14, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize14_0 = {
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize15, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize15_0 = {
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize16, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize16_0 = {
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize17, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize17_0 = {
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize2_0 = {
  2U, 58U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize2_1 = {
  0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize2_6 = {
  5U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize20, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize20_0 = {
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize3, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize3_0 = {
  0U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize4_0 = {
  0U, 0U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize4_1 = {
  11U, 184U, 19U, 136U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize4_2 = {
  58U, 152U, 7U, 208U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize4_3 = {
  234U, 96U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize5, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize5_0 = {
  0U, 0U, 0U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize64, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize64_0 = {
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 
  0U, 0U, 0U, 0U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 1514, 1533 L1 */ /* MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize8, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize8_0 = {
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
};
/* PRQA L:L1 */

#define RTE_STOP_SEC_CONST_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 * Calibration Parameters (SW-C local and calibration component calibration parameters)
 *********************************************************************************************************************/

#define RTE_START_SEC_CONST_DEFAULT_RTE_CDATA_GROUP_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


uint8 Dem_Cfg_AgingData[11]={0};
Std_ReturnType Dem_NvM_InitAgingData()
{
	Std_ReturnType ret = E_OK;
	return ret;
}



/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_AltDiscon_BatUnderVolDuration_ROM_AltDiscon_BatUnderVolDuration = {
  2U, 88U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_BatDiscon_BatOpenDuration_ROM_BatDiscon_BatOpenDuration = {
  0U, 200U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_Bat_Normal_SOC_Threshold_ROM_Bat_Normal_SOC_Threshold = {
  65U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_Bat_UnderVoltage_Threshold_ROM_Bat_UnderVoltage_Threshold = {
  29U, 176U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve18_C_ROM_DIDReserve18_C = {
  5U, 220U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve19_C_ROM_DIDReserve19_C = {
  5U, 0U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve20_ROM_DIDReserve20 = {
  11U, 184U, 19U, 136U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve21_ROM_DIDReserve21 = {
  58U, 152U, 7U, 208U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve22_ROM_DIDReserve22 = {
  234U, 96U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30OverVoltageHigherThreshold_ROM_KL30OverVoltageHigherThreshold = {
  101U, 144U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30OverVoltageHigherThresholdTimeout_ROM_KL30OverVoltageHigherThresholdTimeout = {
  1U, 44U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30OverVoltageLowerThreshold_ROM_KL30OverVoltageLowerThreshold = {
  66U, 104U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30OverVoltageLowerThresholdTimeout_ROM_KL30OverVoltageLowerThresholdTimeout = {
  3U, 232U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30UnderVoltageHigherThreshold_ROM_KL30UnderVoltageHigherThreshold = {
  35U, 40U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30UnderVoltageHigherThresholdTimeout_ROM_KL30UnderVoltageHigherThresholdTimeout = {
  1U, 44U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30UnderVoltageLowerThreshold_ROM_KL30UnderVoltageLowerThreshold = {
  31U, 64U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30UnderVoltageLowerThresholdTimeout_ROM_KL30UnderVoltageLowerThresholdTimeout = {
  0U, 250U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_ROverVoltageHigherThreshold_ROM_KL30_ROverVoltageHigherThreshold = {
  101U, 144U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_ROverVoltageHigherThresholdTimeout_ROM_KL30_ROverVoltageHigherThresholdTimeout = {
  1U, 44U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_ROverVoltageLowerThreshold_ROM_KL30_ROverVoltageLowerThreshold = {
  66U, 104U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_ROverVoltageLowerThresholdTimeout_ROM_KL30_ROverVoltageLowerThresholdTimeout = {
  3U, 232U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_RUnderVoltageHigherThreshold_ROM_KL30_RUnderVoltageHigherThreshold = {
  35U, 40U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_RUnderVoltageHigherThresholdTimeout_ROM_KL30_RUnderVoltageHigherThresholdTimeout = {
  1U, 44U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_RUnderVoltageLowerThreshold_ROM_KL30_RUnderVoltageLowerThreshold = {
  31U, 64U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_RUnderVoltageLowerThresholdTimeout_ROM_KL30_RUnderVoltageLowerThresholdTimeout = {
  0U, 250U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize110, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_OCV_Map_ROM_OCV_Map = {
  43U, 92U, 43U, 192U, 43U, 217U, 44U, 49U, 44U, 90U, 44U, 236U, 45U, 0U, 45U, 11U, 45U, 63U, 45U, 98U, 45U, 177U, 45U, 
  187U, 45U, 195U, 45U, 239U, 46U, 17U, 46U, 151U, 46U, 171U, 46U, 101U, 46U, 142U, 46U, 175U, 46U, 229U, 46U, 233U, 
  46U, 255U, 47U, 38U, 47U, 71U, 47U, 106U, 47U, 107U, 47U, 148U, 47U, 186U, 47U, 218U, 47U, 217U, 47U, 254U, 48U, 37U, 
  48U, 74U, 48U, 106U, 48U, 105U, 48U, 140U, 48U, 179U, 48U, 214U, 48U, 246U, 48U, 245U, 49U, 23U, 49U, 60U, 49U, 95U, 
  49U, 126U, 49U, 126U, 49U, 159U, 49U, 195U, 49U, 229U, 50U, 2U, 50U, 3U, 50U, 35U, 50U, 68U, 50U, 101U, 50U, 128U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverCurrentHigherThreshold_ROM_PGMMosfetOverCurrentHigherThreshold = {
  1U, 194U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverCurrentHigherThresholdTimeout_ROM_PGMMosfetOverCurrentHigherThresholdTimeout = {
  0U, 250U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverCurrentLowerThreshold_ROM_PGMMosfetOverCurrentLowerThreshold = {
  1U, 44U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverCurrentLowerThresholdTimeout_ROM_PGMMosfetOverCurrentLowerThresholdTimeout = {
  1U, 44U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverTemperatureThreshold_ROM_PGMMosfetOverTemperatureThreshold = {
  160U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMVoltageStabilizationFilterTimeAfterItOpen_ROM_PGMVoltageStabilizationFilterTimeAfterItOpen = {
  7U, 208U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGM_Recovery_Normal_Voltage_Filter_Time_ROM_PGM_Recovery_Normal_Voltage_Filter_Time = {
  39U, 16U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoveryKL30OverVoltageThreshold_ROM_PGMrecoveryKL30OverVoltageThreshold = {
  58U, 152U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoveryKL30UnderVoltageThreshold_ROM_PGMrecoveryKL30UnderVoltageThreshold = {
  42U, 248U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoveryKL30_ROverVoltageThreshold_ROM_PGMrecoveryKL30_ROverVoltageThreshold = {
  58U, 152U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoveryKL30_RUnderVoltageThreshold_ROM_PGMrecoveryKL30_RUnderVoltageThreshold = {
  42U, 248U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoverytimes_ROM_PGMrecoverytimes = {
  3U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize16, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PIF_F115_ROM_PIF_F115 = {
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
};
/* PRQA L:L1 */
/* PRQA S 3408, 1514, 1533 L1 */ /* MD_Rte_3408, MD_Rte_1514, MD_Rte_1533 */
CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_SecurityAttemptCounter_ROM_SecurityAttemptCounter = {
  0U
};
/* PRQA L:L1 */

#define RTE_STOP_SEC_CONST_DEFAULT_RTE_CDATA_GROUP_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 * TxAck/ModeSwitchAck Flags
 *********************************************************************************************************************/


#define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

VAR(Rte_AckFlagsType, RTE_VAR_NOINIT) Rte_AckFlags;

#define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define Rte_AckFlagsInit() (Rte_MemClr(&Rte_AckFlags, sizeof(Rte_AckFlagsType)))


/**********************************************************************************************************************
 * Update Flags for each Receiver with enableUpdate != 0
 *********************************************************************************************************************/


#define RTE_START_SEC_VAR_ZERO_INIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */



#define RTE_STOP_SEC_VAR_ZERO_INIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define Rte_RxUpdateFlagsInit() (Rte_MemClr(&Rte_RxUpdateFlags, sizeof(Rte_RxUpdateFlagsType)))


/**********************************************************************************************************************
 * Dirty Flags for NVBlockDescriptors
 *********************************************************************************************************************/

#define RTE_START_SEC_VAR_ZERO_INIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

VAR(Rte_DirtyFlagsType, RTE_VAR_ZERO_INIT) Rte_DirtyFlags = {
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U
};

#define RTE_STOP_SEC_VAR_ZERO_INIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define Rte_DirtyFlagsInit() (Rte_MemClr(&Rte_DirtyFlags, sizeof(Rte_DirtyFlagsType)))


/**********************************************************************************************************************
 * Pending Flags for NVBlockDescriptors
 *********************************************************************************************************************/

#define RTE_START_SEC_VAR_ZERO_INIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

VAR(Rte_NvBlockPendingFlagsType, RTE_VAR_ZERO_INIT) Rte_NvBlockPendingFlags = {
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U,
  0U
};

#define RTE_STOP_SEC_VAR_ZERO_INIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define Rte_NvBlockPendingFlagInit() (Rte_MemClr(&Rte_NvBlockPendingFlags, sizeof(Rte_NvBlockPendingFlagsType)))


/**********************************************************************************************************************
 * Prototypes for Runnable Entities of Nv Block Components
 *********************************************************************************************************************/

#define RTE_START_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

RTE_LOCAL FUNC(void, RTE_CODE) RunnableCtApNvM_200ms(void);

#define RTE_STOP_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#define RTE_START_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

FUNC(void, RTE_CODE) Rte_MemClr(P2VAR(void, AUTOMATIC, RTE_VAR_NOINIT) ptr, uint32_least num);
FUNC(void, RTE_CODE) Rte_MemCpy(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) destination, P2CONST(void, AUTOMATIC, RTE_APPL_DATA) source, uint32_least num); /* PRQA S 1505, 3408 */ /* MD_MSR_Rule8.7, MD_Rte_3408 */
FUNC(void, RTE_CODE) Rte_MemCpy32(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) destination, P2CONST(void, AUTOMATIC, RTE_APPL_DATA) source, uint32_least num); /* PRQA S 1505, 3408 */ /* MD_MSR_Rule8.7, MD_Rte_3408 */

#define RTE_STOP_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define RTE_START_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * Helper functions for mode management
 *********************************************************************************************************************/

#define RTE_STOP_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * Data structures for mode management
 *********************************************************************************************************************/



#define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */



/**********************************************************************************************************************
 * Timer handling
 *********************************************************************************************************************/

#if defined OS_US2TICKS_SystemTimer_Core0
# define RTE_USEC_SystemTimer_Core0 OS_US2TICKS_SystemTimer_Core0
#else
# define RTE_USEC_SystemTimer_Core0(val) ((TickType)RTE_CONST_USEC_SystemTimer_Core0_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
#endif

#if defined OS_MS2TICKS_SystemTimer_Core0
# define RTE_MSEC_SystemTimer_Core0 OS_MS2TICKS_SystemTimer_Core0
#else
# define RTE_MSEC_SystemTimer_Core0(val) ((TickType)RTE_CONST_MSEC_SystemTimer_Core0_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
#endif

#if defined OS_SEC2TICKS_SystemTimer_Core0
# define RTE_SEC_SystemTimer_Core0 OS_SEC2TICKS_SystemTimer_Core0
#else
# define RTE_SEC_SystemTimer_Core0(val)  ((TickType)RTE_CONST_SEC_SystemTimer_Core0_##val) /* PRQA S 0342 */ /* MD_MSR_Rule20.10_0342 */
#endif

#define RTE_CONST_MSEC_SystemTimer_Core0_0 (0UL)
#define RTE_CONST_MSEC_SystemTimer_Core0_1 (100000UL)
#define RTE_CONST_MSEC_SystemTimer_Core0_10 (1000000UL)
#define RTE_CONST_MSEC_SystemTimer_Core0_100 (10000000UL)
#define RTE_CONST_MSEC_SystemTimer_Core0_20 (2000000UL)
#define RTE_CONST_MSEC_SystemTimer_Core0_200 (20000000UL)
#define RTE_CONST_MSEC_SystemTimer_Core0_5 (500000UL)
#define RTE_CONST_MSEC_SystemTimer_Core0_50 (5000000UL)
#define RTE_CONST_MSEC_SystemTimer_Core0_500 (50000000UL)

#define RTE_CONST_SEC_SystemTimer_Core0_0 (0UL)
#define RTE_CONST_SEC_SystemTimer_Core0_1 (100000000UL)


/**********************************************************************************************************************
 * Internal definitions
 *********************************************************************************************************************/

#define RTE_TASK_TIMEOUT_EVENT_MASK   ((EventMaskType)0x01)
#define RTE_TASK_WAITPOINT_EVENT_MASK ((EventMaskType)0x02)

/**********************************************************************************************************************
 * RTE life cycle API
 *********************************************************************************************************************/

#define RTE_START_SEC_CODE
#include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

FUNC(void, RTE_CODE) Rte_MemCpy(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) destination, P2CONST(void, AUTOMATIC, RTE_APPL_DATA) source, uint32_least num) /* PRQA S 3408, 1505 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
{
  P2CONST(uint8, AUTOMATIC, RTE_APPL_DATA) src = (P2CONST(uint8, AUTOMATIC, RTE_APPL_DATA)) source; /* PRQA S 0316 */ /* MD_Rte_0316 */
  P2VAR(uint8, AUTOMATIC, RTE_APPL_VAR) dst = (P2VAR(uint8, AUTOMATIC, RTE_APPL_VAR)) destination; /* PRQA S 0316 */ /* MD_Rte_0316 */
  uint32_least i;
  for (i = 0; i < num; i++)
  {
    dst[i] = src[i];
  }
}

#define RTE_MEMCPY32ALIGN (sizeof(uint32) - 1U)

FUNC(void, RTE_CODE) Rte_MemCpy32(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) destination, P2CONST(void, AUTOMATIC, RTE_APPL_DATA) source, uint32_least num)
{
  P2CONST(uint32, AUTOMATIC, RTE_APPL_DATA) asrc = (P2CONST(uint32, AUTOMATIC, RTE_APPL_DATA)) source; /* PRQA S 0316 */ /* MD_Rte_0316 */
  P2VAR(uint32, AUTOMATIC, RTE_APPL_VAR) adst = (P2VAR(uint32, AUTOMATIC, RTE_APPL_VAR)) destination; /* PRQA S 0316 */ /* MD_Rte_0316 */
  P2CONST(uint8, AUTOMATIC, RTE_APPL_DATA) src = (P2CONST(uint8, AUTOMATIC, RTE_APPL_DATA)) source; /* PRQA S 0316 */ /* MD_Rte_0316 */
  P2VAR(uint8, AUTOMATIC, RTE_APPL_VAR) dst = (P2VAR(uint8, AUTOMATIC, RTE_APPL_VAR)) destination; /* PRQA S 0316 */ /* MD_Rte_0316 */
  uint32_least i = 0;

  if (num >= 16U)
  {
    if (((((uint32)src) & RTE_MEMCPY32ALIGN) == 0U) && ((((uint32)dst) & RTE_MEMCPY32ALIGN) == 0U)) /* PRQA S 0306 */ /* MD_Rte_0306 */
    {
      uint32_least asize = num / sizeof(uint32);
      uint32_least rem = num & RTE_MEMCPY32ALIGN;
      for (i = 0; i < (asize - 3U); i += 4U)
      {
        adst[i] = asrc[i];
        adst[i+1U] = asrc[i+1U];
        adst[i+2U] = asrc[i+2U];
        adst[i+3U] = asrc[i+3U];
      }

      while (i < asize)
      {
        adst[i] = asrc[i];
        ++i;
      }
      i = num - rem;
    }
    else
    {
      for (i = 0; (i + 15U) < num; i += 16U)
      {
        dst[i] = src[i];
        dst[i+1U] = src[i+1U];
        dst[i+2U] = src[i+2U];
        dst[i+3U] = src[i+3U];
        dst[i+4U] = src[i+4U];
        dst[i+5U] = src[i+5U];
        dst[i+6U] = src[i+6U];
        dst[i+7U] = src[i+7U];
        dst[i+8U] = src[i+8U];
        dst[i+9U] = src[i+9U];
        dst[i+10U] = src[i+10U];
        dst[i+11U] = src[i+11U];
        dst[i+12U] = src[i+12U];
        dst[i+13U] = src[i+13U];
        dst[i+14U] = src[i+14U];
        dst[i+15U] = src[i+15U];
      }
    }

  }
  while (i < num)
  {
    dst[i] = src[i];
    ++i;
  }
}

FUNC(void, RTE_CODE) Rte_MemClr(P2VAR(void, AUTOMATIC, RTE_VAR_NOINIT) ptr, uint32_least num)
{
  P2VAR(uint8, AUTOMATIC, RTE_VAR_NOINIT) dst = (P2VAR(uint8, AUTOMATIC, RTE_VAR_NOINIT))ptr; /* PRQA S 0316 */ /* MD_Rte_0316 */
  uint32_least i;
  for (i = 0; i < num; i++)
  {
    dst[i] = 0;
  }
}



/**********************************************************************************************************************
 * Internal/External Tx connections
 *********************************************************************************************************************/


/**********************************************************************************************************************
 * NvM-Callback for synchronous copying of the mirror buffer from the NvM
 *********************************************************************************************************************/

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_BatDiscon_BatOpenDuration(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_Bat_Normal_SOC_Threshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_Bat_UnderVoltage_Threshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize15);

  if (size <= 15U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ConfigurationTraceabilityField_F198(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize11);

  if (size <= 11U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
	  		{
	  			NvM_DID_F198[i]=((uint8*)NVMBuffer)[i];
	  		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve18_C(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve19_C(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve20(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve21(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve22(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve23(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve24(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve25(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve26(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve27(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize5);

  if (size <= 5U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
		{
			NvM_DID_F1A9[i]=((uint8*)NVMBuffer)[i];
		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUHardwareNumber_F191(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize5);

  if (size <= 5U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
		{
			NvM_DID_F191[i]=((uint8*)NVMBuffer)[i];
		}

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUIndexInformation_F1A5(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize3);

  if (size <= 3U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
		{
			NvM_DID_F1A5[i]=((uint8*)NVMBuffer)[i];
		}

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUManufactureDate_F18B(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize3);

  if (size <= 3U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	for(i=0;i<size;i++)
	{
		NvM_DID_F18B[i]=((uint8*)NVMBuffer)[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUPartNumber_F187(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize5);

  if (size <= 5U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	for(i=0;i<size;i++)
	{
		NvM_DID_F187[i]=((uint8*)NVMBuffer)[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize5);

  if (size <= 5U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
		for(i=0;i<size;i++)
		{
			NvM_DID_F1AA[i]=((uint8*)NVMBuffer)[i];
		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUSerialNumber_F18C(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
		{
			NvM_DID_F18C[i]=((uint8*)NVMBuffer)[i];
		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30OverVoltageHigherThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30OverVoltageLowerThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_LastPGMOpenInformation_B042(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize15);

  if (size <= 15U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_NIF_CurrentData_F121(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
	  		{
	  			NvM_DID_F121[i]=((uint8*)NVMBuffer)[i];
	  		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_NIF_FactoryData_F120(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
		{
			NvM_DID_F120[i]=((uint8*)NVMBuffer)[i];
		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_OCV_Map(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize110);

  if (size <= 110U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMrecoverytimes(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F110(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
	  		{
	  			NvM_DID_F110[i]=((uint8*)NVMBuffer)[i];
	  		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F111(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
		{
			NvM_DID_F111[i]=((uint8*)NVMBuffer)[i];
		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F112(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
		{
			NvM_DID_F112[i]=((uint8*)NVMBuffer)[i];
		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F113(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
		{
			NvM_DID_F113[i]=((uint8*)NVMBuffer)[i];
		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F114(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
	{
		NvM_DID_F114[i]=((uint8*)NVMBuffer)[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F115(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
	{
		NvM_DID_F115[i]=((uint8*)NVMBuffer)[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F116(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
	{
		NvM_DID_F116[i]=((uint8*)NVMBuffer)[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F117(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	  for(i=0;i<size;i++)
	  	{
	  		NvM_DID_F117[i]=((uint8*)NVMBuffer)[i];
	  	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F118(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	  for(i=0;i<size;i++)
	  	{
	  		NvM_DID_F118[i]=((uint8*)NVMBuffer)[i];
	  	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F119(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	  for(i=0;i<size;i++)
	  	{
	  		NvM_DID_F119[i]=((uint8*)NVMBuffer)[i];
	  	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F11A(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	 for(i=0;i<size;i++)
	  	{
	  		NvM_DID_F11A[i]=((uint8*)NVMBuffer)[i];
	  	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F11B(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
	{
		NvM_DID_F11B[i]=((uint8*)NVMBuffer)[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F11C(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	  for(i=0;i<size;i++)
	  	{
	  		NvM_DID_F11C[i]=((uint8*)NVMBuffer)[i];
	  	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F11D(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

	  int i;
	  for(i=0;i<size;i++)
	{
		NvM_DID_F11D[i]=((uint8*)NVMBuffer)[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F11E(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	  for(i=0;i<size;i++)
	  	{
	  		NvM_DID_F11E[i]=((uint8*)NVMBuffer)[i];
	  	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F11F(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	  for(i=0;i<size;i++)
	  	{
	  		NvM_DID_F11F[i]=((uint8*)NVMBuffer)[i];
	  	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ReseveforAPP(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize64);

  if (size <= 64U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ReseveforFBL(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize64);

  if (size <= 64U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
		{
			NvM_SECERR_DATA[i]=((uint8*)NVMBuffer)[i];
		}

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ST_ResetBlockInfo_T(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize8);

  if (size <= 8U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ST_ResetMngInfo_T(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize8);

  if (size <= 8U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_SecurityAttemptCounter(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_Softwareintegritystatus_AFFD(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize10);

  if (size <= 10U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	  for(i=0;i<size;i++)
	  	{
	  		NvM_DID_F192[i]=((uint8*)NVMBuffer)[i];
	  	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_SwitchOpenReasonRecord_B044(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize14);

  if (size <= 14U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_SystemSupplierIdentifier_F18A(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize5);

  if (size <= 5U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	 for(i=0;i<size;i++)
	{
		NvM_DID_F18A[i]=((uint8*)NVMBuffer)[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_VIN_F190(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize17);

  if (size <= 17U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	  for(i=0;i<size;i++)
	  	{
	  		NvM_DID_F190[i]=((uint8*)NVMBuffer)[i];
	  	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_VehicleFeatureInformation_F1A8(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize20);

  if (size <= 20U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	  for(i=0;i<size;i++)
	  	{
	  		NvM_DID_F1A8[i]=((uint8*)NVMBuffer)[i];
	  	}
    ret = E_OK;
  }
  return ret;
}


/**********************************************************************************************************************
 * NvM-Callback for synchronous copying of the mirror buffer to the NvM
 *********************************************************************************************************************/

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}


FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_BatDiscon_BatOpenDuration(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_Bat_Normal_SOC_Threshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_Bat_UnderVoltage_Threshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize15);

  if (size <= 15U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ConfigurationTraceabilityField_F198(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize11);

  if (size <= 11U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	 for(i=0;i<size;i++)
	  	{
	  		NvM_DID_F198[i]=((uint8*)NVMBuffer)[i];
	  	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve18_C(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve19_C(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve20(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve21(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve22(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve23(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve24(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve25(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve26(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve27(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize4);

  if (size <= 4U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize5);

  if (size <= 5U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	 for(i=0;i<size;i++)
	{
		NvM_DID_F1A9[i]=((uint8*)NVMBuffer)[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUHardwareNumber_F191(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize5);

  if (size <= 5U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	  	 for(i=0;i<size;i++)
	  	  	{
	  	  		NvM_DID_F191[i]=((uint8*)NVMBuffer)[i];
	  	  	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUIndexInformation_F1A5(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize3);

  if (size <= 3U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	 for(i=0;i<size;i++)
	{
		NvM_DID_F1A5[i]=((uint8*)NVMBuffer)[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUManufactureDate_F18B(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize3);

  if (size <= 3U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
		 for(i=0;i<size;i++)
		{
			NvM_DID_F18B[i]=((uint8*)NVMBuffer)[i];
		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUPartNumber_F187(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize5);

  if (size <= 5U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
		 for(i=0;i<size;i++)
		{
			((uint8*)NVMBuffer)[i]=NvM_DID_F187[i];
		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize5);

  if (size <= 5U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
		 for(i=0;i<size;i++)
		{
			((uint8*)NVMBuffer)[i]=NvM_DID_F1AA[i];
		}
    ret = E_OK;
  }

  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUSerialNumber_F18C(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	 for(i=0;i<size;i++)
	{
		NvM_DID_F18C[i]=((uint8*)NVMBuffer)[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30OverVoltageHigherThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30OverVoltageLowerThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_LastPGMOpenInformation_B042(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize15);

  if (size <= 15U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_NIF_CurrentData_F121(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	 for(i=0;i<size;i++)
	{
		((uint8*)NVMBuffer)[i]=NvM_DID_F121[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_NIF_FactoryData_F120(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
		 for(i=0;i<size;i++)
		{
			NvM_DID_F120[i]=((uint8*)NVMBuffer)[i];
		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_OCV_Map(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize110);

  if (size <= 110U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize2);

  if (size <= 2U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMrecoverytimes(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F110(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
		 for(i=0;i<size;i++)
		{
			((uint8*)NVMBuffer)[i]=NvM_DID_F110[i];
		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F111(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	  	 for(i=0;i<size;i++)
	  	  	{
	  	  		((uint8*)NVMBuffer)[i]=NvM_DID_F111[i];
	  	  	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F112(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
		 for(i=0;i<size;i++)
		{
			 ((uint8*)NVMBuffer)[i]=NvM_DID_F112[i];
		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F113(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
		 for(i=0;i<size;i++)
		{
			 ((uint8*)NVMBuffer)[i]=NvM_DID_F113[i];
		}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F114(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	 for(i=0;i<size;i++)
	{
		 ((uint8*)NVMBuffer)[i]=NvM_DID_F114[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F115(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

	  int i;
	 for(i=0;i<size;i++)
	{
		 ((uint8*)NVMBuffer)[i]=NvM_DID_F115[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F116(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	 for(i=0;i<size;i++)
	{
		 ((uint8*)NVMBuffer)[i]=NvM_DID_F116[i];
	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F117(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	 	 for(i=0;i<size;i++)
	 	{
	 		((uint8*)NVMBuffer)[i]=NvM_DID_F117[i];
	 	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F118(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	 	 for(i=0;i<size;i++)
	  	 	{
	  	 		((uint8*)NVMBuffer)[i]=NvM_DID_F118[i];
	  	 	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F119(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	 	 for(i=0;i<size;i++)
	  	 	{
	  	 		((uint8*)NVMBuffer)[i]=NvM_DID_F119[i];
	  	 	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F11A(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	 	 for(i=0;i<size;i++)
	  	 	{
	  	 		((uint8*)NVMBuffer)[i]=NvM_DID_F11A[i];
	  	 	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F11B(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	 	 for(i=0;i<size;i++)
	  	 	{
	  	 		((uint8*)NVMBuffer)[i]=NvM_DID_F11B[i];
	  	 	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F11C(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	 	 for(i=0;i<size;i++)
	  	 	{
	  	 		((uint8*)NVMBuffer)[i]=NvM_DID_F11C[i];
	  	 	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F11D(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	 	 for(i=0;i<size;i++)
	  	 	{
	  	 		((uint8*)NVMBuffer)[i]=NvM_DID_F11D[i];
	  	 	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F11E(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	 	 for(i=0;i<size;i++)
	  	 	{
	  	 		((uint8*)NVMBuffer)[i]=NvM_DID_F11E[i];
	  	 	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F11F(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize16);

  if (size <= 16U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  	 	 for(i=0;i<size;i++)
	  	 	{
	  	 		((uint8*)NVMBuffer)[i]=NvM_DID_F11F[i];
	  	 	}
    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ReseveforAPP(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize64);

  if (size <= 64U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ReseveforFBL(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize64);

  if (size <= 64U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
		{
			((uint8*)NVMBuffer)[i] = NvM_SECERR_DATA[i];
		}

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ST_ResetBlockInfo_T(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize8);

  if (size <= 8U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ST_ResetMngInfo_T(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize8);

  if (size <= 8U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_SecurityAttemptCounter(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_Softwareintegritystatus_AFFD(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize10);

  if (size <= 10U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_SwitchOpenReasonRecord_B044(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize14);

  if (size <= 14U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_SystemSupplierIdentifier_F18A(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize5);

  if (size <= 5U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_VIN_F190(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize17);

  if (size <= 17U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_VehicleFeatureInformation_F1A8(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize20);

  if (size <= 20U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}


/**********************************************************************************************************************
 * NvM-Callbacks for forwarding Job Finished notifications from the NvM to the SW-Cs
 *********************************************************************************************************************/

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}


FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_BatDiscon_BatOpenDuration(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Bat_Normal_SOC_Threshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Bat_UnderVoltage_Threshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ConfigurationTraceabilityField_F198(uint8 ServiceId, NvM_RequestResultType JobResult)
{

	switch(ServiceId)
	{
		case 6:/*nvm read block*/
		{
			if(0==JobResult)
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
			}
			else
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
			}
			break;
		}
		case 7:/*nvm write block*/
		{
			break;
		}
	}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve18_C(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve19_C(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve20(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve21(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve22(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve23(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve24(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve25(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve26(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve27(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
	{
		case 6:/*nvm read block*/
		{
			if(0==JobResult)
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
			}
			else
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
			}
			break;
		}
		case 7:/*nvm write block*/
		{
			break;
		}
	}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUHardwareNumber_F191(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
	{
		case 6:/*nvm read block*/
		{
			if(0==JobResult)
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
			}
			else
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
			}
			break;
		}
		case 7:/*nvm write block*/
		{
			break;
		}
	}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUIndexInformation_F1A5(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
	{
		case 6:/*nvm read block*/
		{
			if(0==JobResult)
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
			}
			else
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
			}
			break;
		}
		case 7:/*nvm write block*/
		{
			NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;

			break;
		}
	}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUManufactureDate_F18B(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
	{
		case 6:/*nvm read block*/
		{
			if(0==JobResult)
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
			}
			else
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
			}
			break;
		}
		case 7:/*nvm write block*/
		{

			break;
		}
	}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUPartNumber_F187(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
		{
			case 6:/*nvm read block*/
			{
				if(0==JobResult)
				{
					NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
				}
				else
				{
					NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
				}
				break;
			}
			case 7:/*nvm write block*/
			{
				NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_F187;
				if(0==JobResult)
				{

				}
				else
				{
					NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_F187_FAIL;
				}
				break;
			}
		}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
	{
		case 6:/*nvm read block*/
		{
			if(0==JobResult)
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
			}
			else
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
			}
			break;
		}
		case 7:/*nvm write block*/
		{
			NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_F1AA;
			if(0==JobResult)
			{

			}
			else
			{
				NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_F1AA_FAIL;
			}
			break;
		}
	}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUSerialNumber_F18C(uint8 ServiceId, NvM_RequestResultType JobResult)
{

	switch(ServiceId)
	{
		case 6:/*nvm read block*/
		{
			if(0==JobResult)
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
			}
			else
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
			}
			break;
		}
		case 7:/*nvm write block*/
		{
			break;
		}
	}
  return E_OK;
}


FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30OverVoltageHigherThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}


FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30OverVoltageLowerThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_LastPGMOpenInformation_B042(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_OCV_Map(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}


FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoverytimes(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}


FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F110(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
	{
		case 6:/*nvm read block*/
		{
			if(0==JobResult)
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
			}
			else
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
			}
			break;
		}
		case 7:/*nvm write block*/
		{
			NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_F110;
			if(0==JobResult)
			{

			}
			else
			{
				NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_F110_FAIL;
			}
			break;
		}
	}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F111(uint8 ServiceId, NvM_RequestResultType JobResult)
{

	switch(ServiceId)
		{
			case 6:/*nvm read block*/
			{
				if(0==JobResult)
				{
					NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
				}
				else
				{
					NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
				}
				break;
			}
			case 7:/*nvm write block*/
			{
				NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
				if(0==JobResult)
				{

				}
				else
				{
					NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
				}
				break;
			}
		}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F112(uint8 ServiceId, NvM_RequestResultType JobResult)
{

	switch(ServiceId)
	{
		case 6:/*nvm read block*/
		{
			if(0==JobResult)
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
			}
			else
			{
				NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
			}
			break;
		}
		case 7:/*nvm write block*/
		{
			NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
			if(0==JobResult)
			{

			}
			else
			{
				NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
			}
			break;
		}
	}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F113(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F114(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F115(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F116(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F117(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F118(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F119(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11A(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11B(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11C(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11D(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11E(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11F(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_PIF;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ReseveforFBL(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_SECERR;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_SECERR_FAIL;
					}
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SecurityAttemptCounter(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Softwareintegritystatus_AFFD(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SwitchOpenReasonRecord_B044(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SystemSupplierIdentifier_F18A(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{

					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_VIN_F190(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					break;
				}
			}
  return E_OK;
}

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_VehicleFeatureInformation_F1A8(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{

					break;
				}
			}
  return E_OK;
}
FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_SecurityLog(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize40);

  if (size <= 40U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
		{
			NvM_SECLOG_DATA[i]=((uint8*)NVMBuffer)[i];
		}

    ret = E_OK;
  }
  return ret;
}
FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_Usg_Md(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_SecurityLog(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize40);

  if (size <= 40U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {
	  int i;
	  for(i=0;i<size;i++)
		{
			((uint8*)NVMBuffer)[i] = NvM_SECLOG_DATA[i];
		}

    ret = E_OK;
  }
  return ret;
}
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_Usg_Md(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer) /* PRQA S 3112 */ /* MD_Rte_3112 */
{
  Std_ReturnType ret = E_NOT_OK; /* PRQA S 2981 */ /* MD_MSR_RetVal */
  CONST(uint16_least, RTE_CONST) size = sizeof(IdtNvM_u8AryNvBlkSize1);

  if (size <= 1U) /* PRQA S 2991, 2995 */ /* MD_Rte_2991, MD_Rte_2995 */ /* COV_RTE_NVMBUFFER_SIZE */
  {

    ret = E_OK;
  }
  return ret;
}
FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SecurityLog(uint8 ServiceId, NvM_RequestResultType JobResult)
{
	switch(ServiceId)
			{
				case 6:/*nvm read block*/
				{
					if(0==JobResult)
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
					}
					else
					{
						NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_FAIL;
					}
					break;
				}
				case 7:/*nvm write block*/
				{
					NvM_JobFinished_Flag&=~NVM_JOB_STATUS_WRTIE_SECLOG;
					if(0==JobResult)
					{

					}
					else
					{
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_SECLOG_FAIL;
					}
					break;
				}
			}
  return E_OK;
}
FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Usg_Md(uint8 ServiceId, NvM_RequestResultType JobResult)
{

  return E_OK;
}
