#include "vsstype.h"
#include "vssconf.h"
#include "EccInternal.h"


#pragma section all "vss_api_code"

const vss_uint8 vss_ZERO[32] = { 0 };

#if (defined (_MPU_LINUX_)&&(_MPU_LINUX_ == 1U))
#if (defined (_TTM_CHIP_)&&(_TTM_CHIP_ == 1U))
const vss_char8 VSS_VERSION[16] = "VSS_CHIP_V1.3";
#else
const vss_char8 VSS_VERSION[16] = "VSS_MPU_V1.3";
#endif
#else
const vss_char8 VSS_VERSION[16] = "VSS_MCU_V1.3";
#endif

#if (defined (_ENABLE_MIZAR_AES_)&&(_ENABLE_MIZAR_AES_ == 1U))
const vss_uint8 aes_sbox[256] = {
	0x63, 0x7c, 0x77, 0x7b, 0xf2, 0x6b, 0x6f, 0xc5,
	0x30, 0x01, 0x67, 0x2b, 0xfe, 0xd7, 0xab, 0x76,
	0xca, 0x82, 0xc9, 0x7d, 0xfa, 0x59, 0x47, 0xf0,
	0xad, 0xd4, 0xa2, 0xaf, 0x9c, 0xa4, 0x72, 0xc0,
	0xb7, 0xfd, 0x93, 0x26, 0x36, 0x3f, 0xf7, 0xcc,
	0x34, 0xa5, 0xe5, 0xf1, 0x71, 0xd8, 0x31, 0x15,
	0x04, 0xc7, 0x23, 0xc3, 0x18, 0x96, 0x05, 0x9a,
	0x07, 0x12, 0x80, 0xe2, 0xeb, 0x27, 0xb2, 0x75,
	0x09, 0x83, 0x2c, 0x1a, 0x1b, 0x6e, 0x5a, 0xa0,
	0x52, 0x3b, 0xd6, 0xb3, 0x29, 0xe3, 0x2f, 0x84,
	0x53, 0xd1, 0x00, 0xed, 0x20, 0xfc, 0xb1, 0x5b,
	0x6a, 0xcb, 0xbe, 0x39, 0x4a, 0x4c, 0x58, 0xcf,
	0xd0, 0xef, 0xaa, 0xfb, 0x43, 0x4d, 0x33, 0x85,
	0x45, 0xf9, 0x02, 0x7f, 0x50, 0x3c, 0x9f, 0xa8,
	0x51, 0xa3, 0x40, 0x8f, 0x92, 0x9d, 0x38, 0xf5,
	0xbc, 0xb6, 0xda, 0x21, 0x10, 0xff, 0xf3, 0xd2,
	0xcd, 0x0c, 0x13, 0xec, 0x5f, 0x97, 0x44, 0x17,
	0xc4, 0xa7, 0x7e, 0x3d, 0x64, 0x5d, 0x19, 0x73,
	0x60, 0x81, 0x4f, 0xdc, 0x22, 0x2a, 0x90, 0x88,
	0x46, 0xee, 0xb8, 0x14, 0xde, 0x5e, 0x0b, 0xdb,
	0xe0, 0x32, 0x3a, 0x0a, 0x49, 0x06, 0x24, 0x5c,
	0xc2, 0xd3, 0xac, 0x62, 0x91, 0x95, 0xe4, 0x79,
	0xe7, 0xc8, 0x37, 0x6d, 0x8d, 0xd5, 0x4e, 0xa9,
	0x6c, 0x56, 0xf4, 0xea, 0x65, 0x7a, 0xae, 0x08,
	0xba, 0x78, 0x25, 0x2e, 0x1c, 0xa6, 0xb4, 0xc6,
	0xe8, 0xdd, 0x74, 0x1f, 0x4b, 0xbd, 0x8b, 0x8a,
	0x70, 0x3e, 0xb5, 0x66, 0x48, 0x03, 0xf6, 0x0e,
	0x61, 0x35, 0x57, 0xb9, 0x86, 0xc1, 0x1d, 0x9e,
	0xe1, 0xf8, 0x98, 0x11, 0x69, 0xd9, 0x8e, 0x94,
	0x9b, 0x1e, 0x87, 0xe9, 0xce, 0x55, 0x28, 0xdf,
	0x8c, 0xa1, 0x89, 0x0d, 0xbf, 0xe6, 0x42, 0x68,
	0x41, 0x99, 0x2d, 0x0f, 0xb0, 0x54, 0xbb, 0x16,
};   

const vss_uint8 aes_contrary_sbox[256] = {                            
	0x52, 0x09, 0x6a, 0xd5, 0x30, 0x36, 0xa5, 0x38,
	0xbf, 0x40, 0xa3, 0x9e, 0x81, 0xf3, 0xd7, 0xfb,
	0x7c, 0xe3, 0x39, 0x82, 0x9b, 0x2f, 0xff, 0x87,
	0x34, 0x8e, 0x43, 0x44, 0xc4, 0xde, 0xe9, 0xcb,
	0x54, 0x7b, 0x94, 0x32, 0xa6, 0xc2, 0x23, 0x3d,
	0xee, 0x4c, 0x95, 0x0b, 0x42, 0xfa, 0xc3, 0x4e,
	0x08, 0x2e, 0xa1, 0x66, 0x28, 0xd9, 0x24, 0xb2,
	0x76, 0x5b, 0xa2, 0x49, 0x6d, 0x8b, 0xd1, 0x25,
	0x72, 0xf8, 0xf6, 0x64, 0x86, 0x68, 0x98, 0x16,
	0xd4, 0xa4, 0x5c, 0xcc, 0x5d, 0x65, 0xb6, 0x92,
	0x6c, 0x70, 0x48, 0x50, 0xfd, 0xed, 0xb9, 0xda,
	0x5e, 0x15, 0x46, 0x57, 0xa7, 0x8d, 0x9d, 0x84,
	0x90, 0xd8, 0xab, 0x00, 0x8c, 0xbc, 0xd3, 0x0a,
	0xf7, 0xe4, 0x58, 0x05, 0xb8, 0xb3, 0x45, 0x06,
	0xd0, 0x2c, 0x1e, 0x8f, 0xca, 0x3f, 0x0f, 0x02,
	0xc1, 0xaf, 0xbd, 0x03, 0x01, 0x13, 0x8a, 0x6b,
	0x3a, 0x91, 0x11, 0x41, 0x4f, 0x67, 0xdc, 0xea,
	0x97, 0xf2, 0xcf, 0xce, 0xf0, 0xb4, 0xe6, 0x73,
	0x96, 0xac, 0x74, 0x22, 0xe7, 0xad, 0x35, 0x85,
	0xe2, 0xf9, 0x37, 0xe8, 0x1c, 0x75, 0xdf, 0x6e,
	0x47, 0xf1, 0x1a, 0x71, 0x1d, 0x29, 0xc5, 0x89,
	0x6f, 0xb7, 0x62, 0x0e, 0xaa, 0x18, 0xbe, 0x1b,
	0xfc, 0x56, 0x3e, 0x4b, 0xc6, 0xd2, 0x79, 0x20,
	0x9a, 0xdb, 0xc0, 0xfe, 0x78, 0xcd, 0x5a, 0xf4,
	0x1f, 0xdd, 0xa8, 0x33, 0x88, 0x07, 0xc7, 0x31,
	0xb1, 0x12, 0x10, 0x59, 0x27, 0x80, 0xec, 0x5f,
	0x60, 0x51, 0x7f, 0xa9, 0x19, 0xb5, 0x4a, 0x0d,
	0x2d, 0xe5, 0x7a, 0x9f, 0x93, 0xc9, 0x9c, 0xef,
	0xa0, 0xe0, 0x3b, 0x4d, 0xae, 0x2a, 0xf5, 0xb0,
	0xc8, 0xeb, 0xbb, 0x3c, 0x83, 0x53, 0x99, 0x61,
	0x17, 0x2b, 0x04, 0x7e, 0xba, 0x77, 0xd6, 0x26,
	0xe1, 0x69, 0x14, 0x63, 0x55, 0x21, 0x0c, 0x7d,
};

/*轮常量表 The key schedule rcon table*/    
const vss_uint8 aes_Rcon[10] = {       
	0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40, 0x80, 0x1b, 0x36
};    
#endif

const vss_uint8 cmac_Rb[16] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x87
};	

#if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U)) || \
	(defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))
const vss_uint32 ecc_precompute_data_table_NIST256[] = {  
	0xd898c296, 0xf4a13945, 0x2deb33a0, 0x77037d81, 0x63a440f2, 0xf8bce6e5, 0xe12c4247, 0x6b17d1f2, 
	0x37bf51f5, 0xcbb64068, 0x6b315ece, 0x2bce3357, 0x7c0f9e16, 0x8ee7eb4a, 0xfe1a7f9b, 0x4fe342e2, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x04bac870, 0xf7d24bb7, 0x3a23c6ab, 0x593a09a0, 0xf94c9d1d, 0xdfcc2358, 0x297bed02, 0x3cfa0f87, 
	0x40f26940, 0xce98a30b, 0x0248a8af, 0x62121c0d, 0x8309af9b, 0xa758aa80, 0x70be12c6, 0xe4e37694, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x86ef7d7d, 0xdd37e3ff, 0x088b86db, 0xf6d77c27, 0x254c5491, 0x28fe9a4f, 0x6df0fd5e, 0xd6690337, 
	0xaddad596, 0x9ff04992, 0x9e4373f9, 0xf3d1a7af, 0xdf074167, 0xa13e9578, 0xe6d13d22, 0x20e2a53c, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x525d6abf, 0xaebfd735, 0x96bea25a, 0xc302f8f4, 0x544920a4, 0xdb82b3ea, 0x02eadb2e, 0x621c75d1, 
	0x9ef485f0, 0x8939dc4c, 0x57c46d63, 0x225d03d8, 0x522d7f70, 0x4fdac96f, 0xb4fa649d, 0xd7c4a4fe, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0xc0b9372a, 0x8bc659aa, 0xedd9583f, 0xf7659958, 0x8c267d88, 0x9f05f94a, 0xc99a739d, 0x00dc46e7, 
	0xdf55d0f2, 0x4af50a00, 0x8156bf6a, 0xb5eb202d, 0x5228c111, 0x40d1e3ab, 0x45793424, 0x0312a557, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x7eb8cfee, 0x8d9692f7, 0x0d8c013d, 0x05e3f223, 0x84e32e59, 0x76347a52, 0x15b0a1e5, 0x3c53e290, 
	0xfae798d4, 0x538b7da5, 0x00d23591, 0x1b9f1bd1, 0x9a08693f, 0x11a9f072, 0x140efeb3, 0xd30e7cda, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0xf8e8f683, 0x6dfcf787, 0x3f7fbe90, 0x13d72b7a, 0x2df232cf, 0xfd426d94, 0x5fe39aad, 0xed84bb42, 
	0x732995fc, 0x023e67a1, 0x355430e3, 0x67dd0a8e, 0x97a1d703, 0x0cf83b61, 0x583c33f2, 0xa3233455, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x5f165d99, 0xcebbbc7b, 0x8a4eee61, 0x50cc51c1, 0x1b4d0d1f, 0xb31d2353, 0x66382ada, 0x95e18452, 
	0x0a839b5b, 0xacad4f81, 0x4142ff0f, 0xa0a2a96e, 0x1f4fa12f, 0x3eaa8289, 0x6b0fb8f3, 0x68d68c8f, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x51bbb3f1, 0x9311a269, 0x8d0f4f65, 0xe80f26bd, 0x6beccbb9, 0x9d3dc334, 0x101e5de4, 0x54e244d5, 
	0xf1b19e28, 0xb3ad4c6e, 0x58c2e3b7, 0x4334fbc0, 0x35df9c25, 0x19bd4107, 0xec106eb6, 0xd6bbec0e, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x3fefcfc8, 0xe8881a83, 0xb9b5290b, 0xaea3c9e0, 0x771e4688, 0x10b37ecd, 0xd4d021b6, 0xee0816a3, 
	0xb3a8caa1, 0x8e9929bf, 0xc105f2d1, 0x48915dcf, 0xdb49019f, 0x3a5fdf82, 0xad9006e1, 0xc4a438e3, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0xe83ad2c9, 0x5d6dc503, 0xaed035be, 0xca9f7a1d, 0xcbd21e33, 0x552788ac, 0xe09cb9f0, 0x8699dd31, 
	0x329bf961, 0x38584196, 0xb82a5af9, 0x4cb20e96, 0xc72c78c1, 0x24199908, 0xe92859b7, 0x16e65484, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0xdb3038dd, 0xa20a2c70, 0xe99d5c7c, 0x5f0b46d5, 0x4b600b83, 0xc9b97d37, 0x3df3245e, 0x186c7f79, 
	0x4f1ce57f, 0x2af72460, 0x91e2d8ed, 0x9249897f, 0x8d2ea797, 0x8139b36a, 0x9ab58913, 0x9c428db8, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x4be6458d, 0x1f1e4f3f, 0x595e6547, 0x5f72cc22, 0x271a93f1, 0x5bc5341e, 0x58a5f263, 0xc62e155c, 
	0x58ba7ff4, 0x5f6f845a, 0x7e36a6ad, 0x67e1f7dc, 0xeeaa4d04, 0xd33a7657, 0x18267e4e, 0xff9f2322, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0xc7644c1d, 0xe33f0255, 0xbb9002d8, 0x4030ecc3, 0xf4646f9f, 0xa4486916, 0x959c44fa, 0x5e677d0c, 
	0xd88b9144, 0xe2e7d7d0, 0x6248f91f, 0x5d93a86f, 0x02993aea, 0xe33d0bd5, 0x3100d31e, 0x449f0ce6, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0xfdaab256, 0x52df1588, 0x3127354c, 0x68c0cd44, 0xa591f853, 0x2a849471, 0x93d0cb92, 0xe4da88e9, 
	0x1639c624, 0x6d1ea35d, 0x263707ba, 0x60fe2a36, 0xd0f3bc51, 0x97fc50de, 0x10062e80, 0xf7fa4d15, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x5b696527, 0x2e75a266, 0x5a00169c, 0x1a2530b0, 0x4286fb42, 0x76c4c180, 0x8e831d5b, 0x825f0194, 
	0xef703739, 0xdbf0a11f, 0xce5b106a, 0x106f9bc4, 0x24111150, 0x61794c4f, 0xbc723a17, 0x435872fe, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
};

const vss_uint32 ecc_precompute_data_table_SM2[] = { 
	0x334c74c7, 0x715a4589, 0xf2660be1, 0x8fe30bbf, 0x6a39c994, 0x5f990446, 0x1f198119, 0x32c4ae2c, 
	0x2139f0a0, 0x02df32e5, 0xc62a4740, 0xd0a9877c, 0x6b692153, 0x59bdcee3, 0xf4f6779c, 0xbc3736a2, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x95a242dd, 0x1fe83bea, 0x9a47ecee, 0x8c771acc, 0x6a2b0d81, 0x625165be, 0xa82a0c56, 0xd73c307c, 
	0xfdf565da, 0x38688d92, 0x85244e7e, 0x12da1dde, 0x5f9d4c55, 0xb6a191c7, 0x4cb1c759, 0x0de7bf2b, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x45001029, 0xae59f354, 0x483fbb51, 0xb6bfe296, 0x19529edc, 0x41951b8e, 0x1eb1de7f, 0xdff1e824, 
	0x27010535, 0x8571c688, 0xde9e9ca3, 0x9d4312e6, 0x7c5d9873, 0x054289c7, 0x13fdfda2, 0x3dfb27fc, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x8fa73272, 0xaf8587cf, 0x063ade40, 0xa8ecc926, 0xb74de5f6, 0x96d966d1, 0x4dfe1a2c, 0xc5348b34, 
	0x686ce116, 0xb0a30ddb, 0x6b769315, 0x2bf84f32, 0xb5b68380, 0xe8f6c722, 0x2efdb4cc, 0xaa9fd3f6, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x02c80626, 0xa92a2098, 0xb97556eb, 0xa621dc4b, 0x374309ae, 0x0c137441, 0xec77691c, 0x4302c5ec, 
	0x4276db9d, 0x215a604a, 0x360addbb, 0xb2b83605, 0x41f097cb, 0xb7f4d209, 0xf618afa2, 0x2e41a3d9, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x25f11789, 0x26fab63e, 0x38859dc2, 0x9de0e723, 0xf97bcacf, 0x5f4dacd2, 0x9756efb2, 0x0a9aa461, 
	0x29f1066e, 0xc8cde669, 0x074b172e, 0x54abfd30, 0xec439508, 0x81cf4701, 0x2c4a8a9a, 0x2fc9a4f0, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x6af571dc, 0x1c2304b1, 0x209ddfe9, 0x6f3dac1a, 0xf7c55941, 0x07a2a5ef, 0xfaf2ce4b, 0x55a7146e, 
	0x7d3ee627, 0x7109ba5f, 0x12e88095, 0xc8dbb605, 0x7ef38cf2, 0x63b2680a, 0x9e530f7e, 0x51112815, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x342ee4cf, 0xb7424eaa, 0x7e644acf, 0xb342d998, 0x5d67f117, 0x5cc731d4, 0xbf809c5f, 0xd5c50898, 
	0x314a3c16, 0xf1cb19d1, 0x64b04dc8, 0x49e346de, 0x783d30bc, 0x43a0d3d6, 0x8c0028b5, 0x9a585251, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x0937e4d7, 0x26158104, 0x5eae3dc6, 0xa5c306b8, 0xda1ac9f2, 0x9100e703, 0x52ae3c25, 0xeeb0be34, 
	0xcd037cd2, 0xaf2827cb, 0xb212ab8e, 0x43adcc00, 0xe8806282, 0x857d88fb, 0x5dbcffcf, 0xbcb1c87c, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0xe9dccbd3, 0xbf9e0c8b, 0x4e634746, 0x5e736c25, 0xac7772ea, 0x4c038c0a, 0xb44bef6e, 0xfefc09d6, 
	0x34be10c8, 0x51057fc3, 0x89156970, 0x74facf3d, 0x352f0b3d, 0xfde74e3a, 0x0147a676, 0x43892742, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x17fb0540, 0xb34cea39, 0x31e0fb38, 0xf711587e, 0x157862ec, 0x1b2246b0, 0x0cc945a5, 0x133ff494, 
	0x52405897, 0xa7b5a196, 0x6ff5b910, 0x71d55dff, 0xf09611e7, 0xf4188a38, 0x639ed762, 0xa9eb1002, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x5aedc515, 0x9b6165c3, 0xad9c3a9a, 0x74d1595e, 0xd90492b1, 0xc0f55824, 0xd1aeeee0, 0xb7a8083d, 
	0x1a5e1679, 0x72a54727, 0x3cfd71a6, 0xc5ed7a8b, 0x2b49a6b0, 0xdeb630c5, 0x7d99192f, 0x392fdaf3, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x08a0f54f, 0x7df01b02, 0x71d310e1, 0x0d515bc8, 0x3ba945e3, 0x51932c50, 0xb56cef1a, 0x626f0095, 
	0x303eaa14, 0x995f0bf0, 0xf99ba498, 0x20ac8559, 0x7e6e71c0, 0x6731d794, 0x1447bee5, 0x7dcf6813, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x185df5e9, 0x5d9845ea, 0xcc2800d9, 0x5dfbcc4a, 0xc83566a9, 0x090d5ae4, 0x22711eef, 0x39308636, 
	0x6bf92062, 0xfd52f34b, 0x89a88bdb, 0x3ee11960, 0x0eff0caa, 0x408378c9, 0xf84f070d, 0x19bcfb93, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0x577f5324, 0x013b7283, 0xbb3d53d9, 0x8610b5f0, 0x68a20568, 0x26e0fb28, 0x63743abe, 0x818b1062, 
	0x641453a6, 0x4744944f, 0xdab51706, 0x014fd7d3, 0xe6504674, 0xc57b2107, 0x6d37e0f2, 0xf17f4685, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 
	0xdf1cf2d5, 0x406f5b44, 0x82e646c8, 0x8e4b0c42, 0xef7b16ec, 0x893221e5, 0xae76e93f, 0x282389be, 
	0x1d0ca406, 0xcc23fdbc, 0x0adfb4a8, 0x1cc7e935, 0x3c09eac9, 0xe14f2481, 0x710e14b7, 0xc9032eea, 
	0x00000001, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,
};

#define SIZE_NUM 8
const ecc_point_j ecc_precompute_point_table_SM2[] = 
{
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[0*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[1*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[2*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[3*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[4*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[5*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[6*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[7*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[8*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[9*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[10*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[11*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[12*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[13*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[14*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[15*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[16*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[17*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[18*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[19*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[20*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[21*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[22*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[23*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[24*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[25*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[26*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[27*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[28*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[29*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[30*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[31*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[32*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[33*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[34*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[35*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[36*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[37*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[38*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[39*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[40*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[41*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[42*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[43*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[44*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_SM2[45*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[46*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_SM2[47*SIZE_NUM]},
};

const ecc_point_j ecc_precompute_point_table_NIST256[] = 
{
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[0*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[1*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[2*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[3*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[4*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[5*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[6*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[7*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[8*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[9*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[10*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[11*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[12*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[13*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[14*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[15*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[16*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[17*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[18*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[19*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[20*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[21*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[22*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[23*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[24*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[25*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[26*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[27*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[28*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[29*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[30*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[31*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[32*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[33*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[34*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[35*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[36*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[37*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[38*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[39*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[40*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[41*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[42*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[43*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[44*SIZE_NUM]},
	{(vss_uint32 *)&ecc_precompute_data_table_NIST256[45*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[46*SIZE_NUM], (vss_uint32 *)&ecc_precompute_data_table_NIST256[47*SIZE_NUM]},
};
#endif 

#if (defined (_ENABLE_MIZAR_ZUC_)&&(_ENABLE_MIZAR_ZUC_ == 1U))
const vss_uint8 KZUC_S0[256] = {
	0x3e, 0x72, 0x5b, 0x47, 0xca, 0xe0, 0x00, 0x33, 0x04, 0xd1, 0x54, 0x98, 0x09, 0xb9, 0x6d, 0xcb,
	0x7b, 0x1b, 0xf9, 0x32, 0xaf, 0x9d, 0x6a, 0xa5, 0xb8, 0x2d, 0xfc, 0x1d, 0x08, 0x53, 0x03, 0x90,
	0x4d, 0x4e, 0x84, 0x99, 0xe4, 0xce, 0xd9, 0x91, 0xdd, 0xb6, 0x85, 0x48, 0x8b, 0x29, 0x6e, 0xac,
	0xcd, 0xc1, 0xf8, 0x1e, 0x73, 0x43, 0x69, 0xc6, 0xb5, 0xbd, 0xfd, 0x39, 0x63, 0x20, 0xd4, 0x38,
	0x76, 0x7d, 0xb2, 0xa7, 0xcf, 0xed, 0x57, 0xc5, 0xf3, 0x2c, 0xbb, 0x14, 0x21, 0x06, 0x55, 0x9b,
	0xe3, 0xef, 0x5e, 0x31, 0x4f, 0x7f, 0x5a, 0xa4, 0x0d, 0x82, 0x51, 0x49, 0x5f, 0xba, 0x58, 0x1c,
	0x4a, 0x16, 0xd5, 0x17, 0xa8, 0x92, 0x24, 0x1f, 0x8c, 0xff, 0xd8, 0xae, 0x2e, 0x01, 0xd3, 0xad,
	0x3b, 0x4b, 0xda, 0x46, 0xeb, 0xc9, 0xde, 0x9a, 0x8f, 0x87, 0xd7, 0x3a, 0x80, 0x6f, 0x2f, 0xc8,
	0xb1, 0xb4, 0x37, 0xf7, 0x0a, 0x22, 0x13, 0x28, 0x7c, 0xcc, 0x3c, 0x89, 0xc7, 0xc3, 0x96, 0x56,
	0x07, 0xbf, 0x7e, 0xf0, 0x0b, 0x2b, 0x97, 0x52, 0x35, 0x41, 0x79, 0x61, 0xa6, 0x4c, 0x10, 0xfe,
	0xbc, 0x26, 0x95, 0x88, 0x8a, 0xb0, 0xa3, 0xfb, 0xc0, 0x18, 0x94, 0xf2, 0xe1, 0xe5, 0xe9, 0x5d,
	0xd0, 0xdc, 0x11, 0x66, 0x64, 0x5c, 0xec, 0x59, 0x42, 0x75, 0x12, 0xf5, 0x74, 0x9c, 0xaa, 0x23,
	0x0e, 0x86, 0xab, 0xbe, 0x2a, 0x02, 0xe7, 0x67, 0xe6, 0x44, 0xa2, 0x6c, 0xc2, 0x93, 0x9f, 0xf1,
	0xf6, 0xfa, 0x36, 0xd2, 0x50, 0x68, 0x9e, 0x62, 0x71, 0x15, 0x3d, 0xd6, 0x40, 0xc4, 0xe2, 0x0f,
	0x8e, 0x83, 0x77, 0x6b, 0x25, 0x05, 0x3f, 0x0c, 0x30, 0xea, 0x70, 0xb7, 0xa1, 0xe8, 0xa9, 0x65,
	0x8d, 0x27, 0x1a, 0xdb, 0x81, 0xb3, 0xa0, 0xf4, 0x45, 0x7a, 0x19, 0xdf, 0xee, 0x78, 0x34, 0x60
};

 const vss_uint8 KZUC_S1[256] ={
 	0x55, 0xc2, 0x63, 0x71, 0x3b, 0xc8, 0x47, 0x86, 0x9f, 0x3c, 0xda, 0x5b, 0x29, 0xaa, 0xfd, 0x77,
	0x8c, 0xc5, 0x94, 0x0c, 0xa6, 0x1a, 0x13, 0x00, 0xe3, 0xa8, 0x16, 0x72, 0x40, 0xf9, 0xf8, 0x42,
	0x44, 0x26, 0x68, 0x96, 0x81, 0xd9, 0x45, 0x3e, 0x10, 0x76, 0xc6, 0xa7, 0x8b, 0x39, 0x43, 0xe1,
	0x3a, 0xb5, 0x56, 0x2a, 0xc0, 0x6d, 0xb3, 0x05, 0x22, 0x66, 0xbf, 0xdc, 0x0b, 0xfa, 0x62, 0x48,
	0xdd, 0x20, 0x11, 0x06, 0x36, 0xc9, 0xc1, 0xcf, 0xf6, 0x27, 0x52, 0xbb, 0x69, 0xf5, 0xd4, 0x87,
	0x7f, 0x84, 0x4c, 0xd2, 0x9c, 0x57, 0xa4, 0xbc, 0x4f, 0x9a, 0xdf, 0xfe, 0xd6, 0x8d, 0x7a, 0xeb,
	0x2b, 0x53, 0xd8, 0x5c, 0xa1, 0x14, 0x17, 0xfb, 0x23, 0xd5, 0x7d, 0x30, 0x67, 0x73, 0x08, 0x09,
	0xee, 0xb7, 0x70, 0x3f, 0x61, 0xb2, 0x19, 0x8e, 0x4e, 0xe5, 0x4b, 0x93, 0x8f, 0x5d, 0xdb, 0xa9,
	0xad, 0xf1, 0xae, 0x2e, 0xcb, 0x0d, 0xfc, 0xf4, 0x2d, 0x46, 0x6e, 0x1d, 0x97, 0xe8, 0xd1, 0xe9,
	0x4d, 0x37, 0xa5, 0x75, 0x5e, 0x83, 0x9e, 0xab, 0x82, 0x9d, 0xb9, 0x1c, 0xe0, 0xcd, 0x49, 0x89,
	0x01, 0xb6, 0xbd, 0x58, 0x24, 0xa2, 0x5f, 0x38, 0x78, 0x99, 0x15, 0x90, 0x50, 0xb8, 0x95, 0xe4,
	0xd0, 0x91, 0xc7, 0xce, 0xed, 0x0f, 0xb4, 0x6f, 0xa0, 0xcc, 0xf0, 0x02, 0x4a, 0x79, 0xc3, 0xde,
	0xa3, 0xef, 0xea, 0x51, 0xe6, 0x6b, 0x18, 0xec, 0x1b, 0x2c, 0x80, 0xf7, 0x74, 0xe7, 0xff, 0x21,
	0x5a, 0x6a, 0x54, 0x1e, 0x41, 0x31, 0x92, 0x35, 0xc4, 0x33, 0x07, 0x0a, 0xba, 0x7e, 0x0e, 0x34,
	0x88, 0xb1, 0x98, 0x7c, 0xf3, 0x3d, 0x60, 0x6c, 0x7b, 0xca, 0xd3, 0x1f, 0x32, 0x65, 0x04, 0x28,
	0x64, 0xbe, 0x85, 0x9b, 0x2f, 0x59, 0x8a, 0xd7, 0xb0, 0x25, 0xac, 0xaf, 0x12, 0x03, 0xe2, 0xf2
};

const vss_uint32 KZUC_d[16] = {
	0x44D7, 0x26BC, 0x626B, 0x135E, 0x5789, 0x35E2, 0x7135, 0x09AF,
	0x4D78, 0x2F13, 0x6BC4, 0x1AF1, 0x5E26, 0x3C4D, 0x789A, 0x47AC
};
#endif

#if (defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))
#if (defined (_BIG_ENDIAN_)&&(_BIG_ENDIAN_ == 1U))
const vss_uint32 mizar_ecc_g_ecdsa_para_a[8] = {
	0xFFFFFFFF, 0x00000001, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFC
};
const vss_uint32 mizar_ecc_g_ecdsa_para_b[8] = {
	0x5AC635D8, 0xAA3A93E7, 0xB3EBBD55, 0x769886BC, 0x651D06B0, 0xCC53B0F6, 0x3BCE3C3E, 0x27D2604B
};
const vss_uint32 mizar_ecc_g_ecdsa_para_p[8] = {
	0xFFFFFFFF, 0x00000001, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF
};
const vss_uint32 mizar_ecc_g_ecdsa_para_n[8] = {
	0xFFFFFFFF, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xBCE6FAAD, 0xA7179E84, 0xF3B9CAC2, 0xFC632551
};
const vss_uint32 mizar_ecc_g_ecdsa_para_G[16] = {
	0x6B17D1F2, 0xE12C4247, 0xF8BCE6E5, 0x63A440F2, 0x77037D81, 0x2DEB33A0, 0xF4A13945, 0xD898C296,
	0x4FE342E2, 0xFE1A7F9B, 0x8EE7EB4A, 0x7C0F9E16, 0x2BCE3357, 0x6B315ECE, 0xCBB64068, 0x37BF51F5
};
#else
const vss_uint32 mizar_ecc_g_ecdsa_para_a[8] = {
	0xFFFFFFFF, 0x01000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFCFFFFFF
};
const vss_uint32 mizar_ecc_g_ecdsa_para_b[8] = {
	0xD835C65A, 0xE7933AAA, 0x55BDEBB3, 0xBC869876, 0xB0061D65, 0xF6B053CC, 0x3E3CCE3B, 0x4B60D227
};
const vss_uint32 mizar_ecc_g_ecdsa_para_p[8] = {
	0xFFFFFFFF, 0x01000000, 0x00000000, 0x00000000, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF
};
const vss_uint32 mizar_ecc_g_ecdsa_para_n[8] = {
	0xFFFFFFFF, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF, 0xADFAE6BC, 0x849E17A7, 0xC2CAB9F3, 0x512563FC
};
const vss_uint32 mizar_ecc_g_ecdsa_para_G[16] = {
	0xF2D1176B, 0x47422CE1, 0xE5E6BCF8, 0xF240A463, 0x817D0377, 0xA033EB2D, 0x4539A1F4, 0x96C298D8,
	0xE242E34F, 0x9B7F1AFE, 0x4AEBE78E, 0x169E0F7C, 0x5733CE2B, 0xCE5E316B, 0x6840B6CB, 0xF551BF37
};
#endif
const vss_uint8 mizar_ecc_vss_ecc_rand[32] = {
	0x06, 0xC1, 0xEB, 0x2A, 0xCF, 0x22, 0x12, 0x2A, 0x54, 0xB4, 0x7D, 0x6A, 0x06, 0xFF, 0x62, 0xCC, 
	0x8A, 0x07, 0x7D, 0xB2, 0x1B, 0xA5, 0x3C, 0x67, 0xC0, 0x7A, 0x4A, 0x44, 0x6E, 0x7B, 0xF5, 0x00
};
const vss_uint8 mizar_ecc_rand1[32] = {
	0x6C, 0xE3, 0xF2, 0xBB, 0x2E, 0xC5, 0x18, 0x6B, 0x66, 0x3B, 0x03, 0x69, 0x31, 0xB9, 0x50, 0xDE,
	0xA8, 0x2B, 0x41, 0x10, 0x56, 0xB4, 0x9F, 0xA1, 0x08, 0x30, 0x6F, 0x7E, 0x43, 0x79, 0x41, 0x19
};
const vss_uint8 mizar_ecc_k[32] = {
	0x8A, 0x07, 0x7D, 0xB6, 0x1B, 0xA5, 0x3C, 0x67, 0xC0, 0x7A, 0x4A, 0x44, 0x6E, 0x7B, 0x95, 0x07,
	0x71, 0xC1, 0xEB, 0x4A, 0xCF, 0x22, 0x12, 0x2A, 0x54, 0xB4, 0x7D, 0x6A, 0x06, 0xFF, 0x62, 0xCC
};
const vss_uint32 mizar_ecc_g_ecdsa_para_h = 1;
#endif 

#if (defined (_ENABLE_MIZAR_SHA256_)&&(_ENABLE_MIZAR_SHA256_ == 1U))
const WORD sha256_k[64] = {
	0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
	0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
	0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
	0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
	0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
	0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
	0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
	0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
};
#endif

#if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U)) 
#if (defined (_BIG_ENDIAN_)&&(_BIG_ENDIAN_ == 1U))
const vss_uint32 sm2_G_SM2_Gx[8] = {
	0x32C4AE2C, 0x1F198119, 0x5F990446, 0x6A39C994, 0x8FE30BBF, 0xF2660BE1, 0x715A4589, 0x334C74C7
};
const vss_uint32 sm2_G_SM2_Gy[8] = {
	0xBC3736A2, 0xF4F6779C, 0x59BDCEE3, 0x6B692153, 0xD0A9877C, 0xC62A4740, 0x02DF32E5, 0x2139F0A0
};
const vss_uint32 sm2_G_SM2_a[8] = {
	0xFFFFFFFE, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFC
};
const vss_uint32 sm2_G_SM2_b[8] = {
	0x28E9FA9E, 0x9D9F5E34, 0x4D5A9E4B, 0xCF6509A7, 0xF39789F5, 0x15AB8F92, 0xDDBCBD41, 0x4D940E93
};
const vss_uint32 sm2_G_SM2_p[8] = {
	0xFFFFFFFE, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF
};
const vss_uint32 sm2_G_SM2_n[8] = {
	0xFFFFFFFE, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x7203DF6B, 0x21C6052B, 0x53BBF409, 0x39D54123
};
#else
const vss_uint32 sm2_G_SM2_Gx[8] = {
	0x2CAEC432, 0x1981191F, 0x4604995F, 0x94C9396A, 0xBF0BE38F, 0xE10B66F2, 0x89455A71, 0xC7744C33
};
const vss_uint32 sm2_G_SM2_Gy[8] = {
	0xA23637BC, 0x9C77F6F4, 0xE3CEBD59, 0x5321696B, 0x7C87A9D0, 0x40472AC6, 0xE532DF02, 0xA0F03921
};
const vss_uint32 sm2_G_SM2_a[8] = {
	0xFEFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0xFFFFFFFF, 0xFCFFFFFF
};
const vss_uint32 sm2_G_SM2_b[8] = {
	0x9EFAE928, 0x345E9F9D, 0x4B9E5A4D, 0xA70965CF, 0xF58997F3, 0x928FAB15, 0x41BDBCDD, 0x930E944D
};
const vss_uint32 sm2_G_SM2_p[8] = {
	0xFEFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x00000000, 0xFFFFFFFF, 0xFFFFFFFF
};
const vss_uint32 sm2_G_SM2_n[8] = {
	0xFEFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0x6BDF0372, 0x2B05C621, 0x09F4BB53, 0x2341D539
};
#endif
const vss_uint8 sm2_pub_enc_rand[32] = {
	0x8A, 0x07, 0x7D, 0xB6, 0x1B, 0xA5, 0x3C, 0x67, 0xC0, 0x7A, 0x4A, 0x44, 0x6E, 0x7B, 0x95, 0x07,
	0x71, 0xC1, 0xEB, 0x4A, 0xCF, 0x22, 0x12, 0x2A, 0x54, 0xB4, 0x7D, 0x6A, 0x06, 0xFF, 0x62, 0xCC
};

const vss_uint32 sm2_pri_dec_rand[8] = {
	0x2AEBC171, 0x2A1222CF, 0x6A7DB454, 0xCC62FF06, 0xB27D078A, 0x673CA51B, 0x444A7AC0, 0x00F57B6E
};
#endif

#if (defined (_ENABLE_MIZAR_SM3_)&&(_ENABLE_MIZAR_SM3_ == 1U)) || \
	(defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U))
const SM3_WORD_T SM3_IV[8] = {
	0x7380166f, 0x4914b2b9, 0x172442d7, 0xda8a0600, 0xa96f30bc, 0x163138aa, 0xe38dee4d, 0xb0fb0e4e
};
#endif

const vss_uint8 sm4_SboxTable[16][16] = {
	{0xd6, 0x90, 0xe9, 0xfe, 0xcc, 0xe1, 0x3d, 0xb7, 0x16, 0xb6, 0x14, 0xc2, 0x28, 0xfb, 0x2c, 0x05},
	{0x2b, 0x67, 0x9a, 0x76, 0x2a, 0xbe, 0x04, 0xc3, 0xaa, 0x44, 0x13, 0x26, 0x49, 0x86, 0x06, 0x99},
	{0x9c, 0x42, 0x50, 0xf4, 0x91, 0xef, 0x98, 0x7a, 0x33, 0x54, 0x0b, 0x43, 0xed, 0xcf, 0xac, 0x62},
	{0xe4, 0xb3, 0x1c, 0xa9, 0xc9, 0x08, 0xe8, 0x95, 0x80, 0xdf, 0x94, 0xfa, 0x75, 0x8f, 0x3f, 0xa6},
	{0x47, 0x07, 0xa7, 0xfc, 0xf3, 0x73, 0x17, 0xba, 0x83, 0x59, 0x3c, 0x19, 0xe6, 0x85, 0x4f, 0xa8},
	{0x68, 0x6b, 0x81, 0xb2, 0x71, 0x64, 0xda, 0x8b, 0xf8, 0xeb, 0x0f, 0x4b, 0x70, 0x56, 0x9d, 0x35},
	{0x1e, 0x24, 0x0e, 0x5e, 0x63, 0x58, 0xd1, 0xa2, 0x25, 0x22, 0x7c, 0x3b, 0x01, 0x21, 0x78, 0x87},
	{0xd4, 0x00, 0x46, 0x57, 0x9f, 0xd3, 0x27, 0x52, 0x4c, 0x36, 0x02, 0xe7, 0xa0, 0xc4, 0xc8, 0x9e},
	{0xea, 0xbf, 0x8a, 0xd2, 0x40, 0xc7, 0x38, 0xb5, 0xa3, 0xf7, 0xf2, 0xce, 0xf9, 0x61, 0x15, 0xa1},
	{0xe0, 0xae, 0x5d, 0xa4, 0x9b, 0x34, 0x1a, 0x55, 0xad, 0x93, 0x32, 0x30, 0xf5, 0x8c, 0xb1, 0xe3},
	{0x1d, 0xf6, 0xe2, 0x2e, 0x82, 0x66, 0xca, 0x60, 0xc0, 0x29, 0x23, 0xab, 0x0d, 0x53, 0x4e, 0x6f},
	{0xd5, 0xdb, 0x37, 0x45, 0xde, 0xfd, 0x8e, 0x2f, 0x03, 0xff, 0x6a, 0x72, 0x6d, 0x6c, 0x5b, 0x51},
	{0x8d, 0x1b, 0xaf, 0x92, 0xbb, 0xdd, 0xbc, 0x7f, 0x11, 0xd9, 0x5c, 0x41, 0x1f, 0x10, 0x5a, 0xd8},
	{0x0a, 0xc1, 0x31, 0x88, 0xa5, 0xcd, 0x7b, 0xbd, 0x2d, 0x74, 0xd0, 0x12, 0xb8, 0xe5, 0xb4, 0xb0},
	{0x89, 0x69, 0x97, 0x4a, 0x0c, 0x96, 0x77, 0x7e, 0x65, 0xb9, 0xf1, 0x09, 0xc5, 0x6e, 0xc6, 0x84},
	{0x18, 0xf0, 0x7d, 0xec, 0x3a, 0xdc, 0x4d, 0x20, 0x79, 0xee, 0x5f, 0x3e, 0xd7, 0xcb, 0x39, 0x48}
};

const vss_ulong sm4_FK[4] = {
	0xa3b1bac6UL, 0x56aa3350UL, 0x677d9197UL, 0xb27022dcUL
};

const vss_ulong sm4_CK[32] = {
	0x00070e15UL, 0x1c232a31UL, 0x383f464dUL, 0x545b6269UL,
	0x70777e85UL, 0x8c939aa1UL, 0xa8afb6bdUL, 0xc4cbd2d9UL,
	0xe0e7eef5UL, 0xfc030a11UL, 0x181f262dUL, 0x343b4249UL,
	0x50575e65UL, 0x6c737a81UL, 0x888f969dUL, 0xa4abb2b9UL,
	0xc0c7ced5UL, 0xdce3eaf1UL, 0xf8ff060dUL, 0x141b2229UL,
	0x30373e45UL, 0x4c535a61UL, 0x686f767dUL, 0x848b9299UL,
	0xa0a7aeb5UL, 0xbcc3cad1UL, 0xd8dfe6edUL, 0xf4fb0209UL,
	0x10171e25UL, 0x2c333a41UL, 0x484f565dUL, 0x646b7279UL
};

const vss_uint8 vssapi_sm2_rand[32] = {
	0x06, 0xC1, 0xEB, 0x2A, 0xCF, 0x22, 0x12, 0x2A, 0x54, 0xB4, 0x7D, 0x6A, 0x06, 0xFF, 0x62, 0xCC, 
	0x8A, 0x07, 0x7D, 0xB2, 0x1B, 0xA5, 0x3C, 0x67, 0xC0, 0x7A, 0x4A, 0x44, 0x6E, 0x7B, 0xF5, 0x00
};

const vss_uint8 vssapi_sm2_rand1[32] = {
	0x6C, 0xE3, 0xF2, 0xBB, 0x2E, 0xC5, 0x18, 0x6B, 0x66, 0x3B, 0x03, 0x69, 0x31, 0xB9, 0x50, 0xDE, 
	0xA8, 0x2B, 0x41, 0x10, 0x56, 0xB4, 0x9F, 0xA1, 0x08, 0x30, 0x6F, 0x7E, 0x43, 0x79, 0x41, 0x19
};
	
const vss_uint8 vssapi_SESS_KEY[16] = {
	0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f
};

const vss_char8 vssapi_KEY[4] = "KEY";
const vss_char8 vssapi_SERVER[7] = "SERVER";
const vss_char8 vssapi_CLIENT[7] = "CLIENT";

const vss_uint8 vsskeym_KEK[16]={
	0x6a, 0x43, 0x54, 0x43, 0x41, 0x4e, 0x44, 0x4b, 0x4f, 0x41, 0x4c, 0x49, 0x4e, 0x46, 0x49, 0x2f
};

const vss_uint8 vsskeym_SECOC[16] = {
	0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10
};

#pragma section all restore


