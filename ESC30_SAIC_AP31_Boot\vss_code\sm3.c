
#include "sm3.h"
#include "vsscommon.h"



#pragma section code "vss_api_code" 

/***************************************************************************************
 *
 *	��1����: 
 *	��ݡ�SM3�����Ӵ��㷨��ʵ�ָ��½ڶ�����㷨�߼�
 *
 *	!!!! ע�⣬��ݹ淶����������еġ��֡�Ϊ32λ��4�ֽڣ�������ͨ��x86�ܹ������е�16λ��2�ֽڣ�
 *
 ***************************************************************************************/
#if (defined (_BIG_ENDIAN_)&&(_BIG_ENDIAN_ == 1U)) 

#else
	#ifndef __LITTLE_ENDIAN
		#define __LITTLE_ENDIAN
	#endif
#endif

#if (defined (_ENABLE_MIZAR_SM3_)&&(_ENABLE_MIZAR_SM3_ == 1U)) || \
	(defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U))

/*	4.1 ��ʼֵ			*/
extern const SM3_WORD_T SM3_IV[8];

/*	4.2 ����			*/
SM3_WORD_T T(vss_uint32 j)
{
	if(j <= 15)
	{ 
		return 0x79cc4519; 
	}
	else{ 
		if((j >= 16) && (j <= 63)) 
		{ 
			return 0x7a879d8a; 
		}else {
			return 0;
		}
	}
}

/*	4.3 �������Ӻ���	*/
#define FG(x,y,z)		(x ^ y ^ z)
#define F1(x,y,z)		((x & y) | (y & z) | (x & z))
#define G1(x,y,z)		((x & y) | (~x & z))

SM3_WORD_T FF(SM3_WORD_T j, SM3_WORD_T x, SM3_WORD_T y, SM3_WORD_T z)
{
	if(j <= 15){
		return FG(x,y,z);
	}else{
		if(j >= 16 && j <= 63){	
			return F1(x,y,z);
		}else{
			return 0;
		}
	}
}

SM3_WORD_T GG(SM3_WORD_T j, SM3_WORD_T x, SM3_WORD_T y, SM3_WORD_T z)
{
	if( j <= 15){
		return FG(x,y,z);
	}else{
		if(j >= 16 && j <= 63){	
			 return  G1(x,y,z);
		}else{
			return 0;
		}
	}
}

/*	4.4 �û�����P0		*/
#define P0(x)			(x ^ (sm3_lshift(x,9))  ^ (sm3_lshift(x,17)))
#define P1(x)			(x ^ (sm3_lshift(x,15)) ^ (sm3_lshift(x,23)))




/*	��Ե��ֵ�ѭ������	*/
static SM3_WORD_T sm3_lshift(SM3_WORD_T num, vss_uint32 bits)
{
	SM3_WORD_T rt;
	while(bits > 32)	bits -= 32;
	rt = (num >> (32 - bits) | (num << bits));
	return rt;
}

/*	��Ե��ֵĴ�С��ת��	*/
#ifdef __LITTLE_ENDIAN
static SM3_WORD_T sm3_rot(SM3_WORD_T num)
{
#ifdef __LITTLE_ENDIAN
	vss_uint32 i = 0;
	SM3_WORD_T num_b = 0;

	vss_uint8 *ln = (vss_uint8 *)(&num);
	vss_uint8 *bn = (vss_uint8 *)(&num_b);

	for (i = 0; i < 4; i++)
	{
		bn[i] = ln[3-i];
	}

	return num_b;
#else
	return num;
#endif /* __LITTLE_ENDIAN */
}
#endif /* __LITTLE_ENDIAN */

/*	���������Ĵ�С��ת��	*/
static void sm3_rot_r(const SM3_WORD_T* in, vss_uint32 count, SM3_WORD_T* out)
{
#ifdef __LITTLE_ENDIAN
	vss_uint32 i = 0;
	for (i = 0; i < count; i++) {
		out[i] = sm3_rot(in[i]);
	}
#else
	mem_cpy8(out, in, count * 4);
#endif /* __LITTLE_ENDIAN */
}

/**
 *	5.2 ���
 *	
 *	������Ϣm�ĳ���Ϊmbits���ء����Ƚ�����1��ӵ���Ϣ��ĩβ�������k��0��
 *	k������mbits + 1 + k = 448mod512 ����С�ķǸ�����
 *	Ȼ�������һ��64λ���ش����ñ��ش��ǳ���mbits�Ķ����Ʊ�ʾ��
 *	�������Ϣm'�ı��س���Ϊ512�ı���
 *
 *	��1������Ϣ01100001 01100010 01100011���䳤��mbits=24��k=448-1-24=443�������õ����ش���
 *	                             {423����0}  {64����}
 *	01100001 01100010 01100011 1 00......00  00 ... 000011000
 *	                                         {24�Ķ����Ʊ�ʾ}
 *
 *	��2������Ϣ01100001 01100010 01100011���䳤��mbits=440��k=448-1-440=7�������õ����ش���
 *	                             {7����0}    {64����}
 *	01100001 01100010 01100011 1 00......00  00 ... 110111000
 *	                                         {440�Ķ����Ʊ�ʾ}
 *
 *	��2������Ϣ00000000 ........ 00000000���䳤��mbits=504��k=512+448-1-504=455�������õ����ش���
 *	                             {455����0}  {64����}
 *	00000000 ........ 00000000 1 00......00  00 ... 111111000
 *	                                         {504�Ķ����Ʊ�ʾ}
 */
 
static vss_uint32 sm3_padding(vss_uint32 m_bytes, vss_uint8* out)
{
	vss_uint32 k = 0;
	vss_uint32 m_bits = m_bytes * 8;
	vss_uint32 mod_bits = m_bits % 512;
	vss_uint8 *p = VSS_NULL;
	
	/*	�������k���ȣ�k = 448mod512 - 1 - mod_bits������Ϊm_bitsΪ8���������k����Ϊ0	*/
	if (mod_bits <= 447) {
		k = 447 - mod_bits;
	}
	else  {
		k = 512 + 447 - mod_bits;
	}
	
	/*	���δָ���������ֻ���㳤�ȣ��ֽڣ�������	*/
	if (VSS_NULL == out) {
		return (m_bits + 1 + k + 64)/8;
	}

	p = out;

	/*	��Ϊ���Ǵ����m_bits����8�ı�����������ֱ����0x80�������1�������	*/
	*p = 0x80;
	p++;

	/*	�ٲ���(k/8)�ֽ�0		*/
	if ( (k/8) > 0 ) {
		mem_set8(p, 0, k/8);
		p += k/8;
	}

	/*	�ٲ���8�ֽ�(64����)���ȣ���m_bytesΪ32λ����£�ǰ4�ֽڹ̶�Ϊ0	*/
	mem_set8(p, 0, 4);
	p += 4;

	*p++ = (vss_uint8)((m_bits & 0xFF000000) >> 24);
	*p++ = (vss_uint8)((m_bits & 0x00FF0000) >> 16);
	*p++ = (vss_uint8)((m_bits & 0x0000FF00) >> 8);
	*p++ = (vss_uint8)((m_bits & 0x000000FF));

	/*	�����������Ϣ���ȣ��ֽڣ�����ֵӦ����64�ֽ�(512����)������	*/
	return p - out;
}

/**
 *	5.3.2 ��Ϣ��չ
 *
 *	����Ϣ����B(i)�����·�����չ���132����W0, W1, ...W67, W��0, W��1, ...W��63
 *	a)	����Ϣ����B(i)����Ϊ16����W0, W1, ...W15
 *	b)	FOR j=16 TO 67
 *			Wj = P1(Wj-16 ? Wj-9 ? (Wj-3 <<< 15)) ? (Wj-13 <<< 7) ? Wj-6
 *		ENDFOR
 *	c)	FOR j=0 TO 63
 *			W��j = Wj ? Wj+4
 *		ENDFOR
 *	
 *	ע1��?  ��ʾ32�����������
 *	     <<< ��ʾѭ������k��������
 */
static void sm3_extend(const vss_uint8 *b, SM3_WORD_T *w)
{
	vss_uint32 i = 0;
	vss_uint32 j = 0;

	/*	b�ĳ���Ӧ�ù̶�Ϊ16���֣�Ҳ��64�ֽ�	*/
	sm3_rot_r((const SM3_WORD_T *)b, 16, w);
	
	for (i = 16; i < 68; i++) {
		w[i] = P1((w[i - 16]) ^ (w[i - 9]) ^ (sm3_lshift(w[i - 3],15))) ^ (sm3_lshift(w[i - 13],7))  ^ w[i - 6];
	}

 	for (j = 0; j < 64; j++)
 	{
 		w[68 + j] = w[j] ^ w[j + 4];
 	}
}

/**
 *	5.3.2 ѹ������
 *	��A,B,C,D,E,F,G,HΪ�ּĴ���, SS1,SS2,TT1,TT2Ϊ�м����,
 *	ѹ������V(i+1) = CF(V(i),B(i)), 0 <= i <= n-1��
 *
 *	�������������£�
 *	ABCDEFGH = V(i)
 *	FOR j=0 TO 63
 *		SS1 = ((A <<< 12) + E + (Tj <<< j)) <<< 7
 *		SS2 = SS1 ? (A <<< 12)
 *		TT1 = FFj(A,B,C) + D + SS2 +W��j
 *		TT2 = GGj(E,F,G) + H + SS1 +Wj
 *		D = C
 *		C = B <<< 9
 *		B = A
 *		A = TT1
 *		H = G
 *		G = F <<< 19
 *		F = E
 *		E = P0(TT2)
 *	ENDFOR
 *	
 *	V(i+1) = ABCDEFGH ? V(i)
 *
 *	ע1��?  ��ʾ32�����������
 *	     <<< ��ʾѭ������k��������
 *	ע2���ֵĴ洢Ϊ���(big-endian)��ʽ��
 */
static void sm3_compress(SM3_WORD_T *v, SM3_WORD_T *w)
{
	/*	v��vi���̶�Ϊ8���֣�Ҳ��32�ֽ�	*/
	SM3_WORD_T vi[8];
	SM3_WORD_T *A = vi;
	SM3_WORD_T *B = vi+1;
	SM3_WORD_T *C = vi+2;
	SM3_WORD_T *D = vi+3;
	SM3_WORD_T *E = vi+4;
	SM3_WORD_T *F = vi+5;
	SM3_WORD_T *G = vi+6;
	SM3_WORD_T *H = vi+7;
	
	SM3_WORD_T SS1 = 0;
	SM3_WORD_T SS2 = 0;
	SM3_WORD_T TT1 = 0;
	SM3_WORD_T TT2 = 0;

	vss_uint32 j = 0;
	
	/*	ABCDEFGH = V(i)	*/
	mem_cpy8(vi, v, 32);
	
	for (j = 0; j <= 63; j++) {
		/*		SS1 = ((A <<< 12) + E + (Tj <<< j)) <<< 7	*/
		SS1 = sm3_lshift(sm3_lshift(*A, 12) + (*E) + sm3_lshift(T(j), j), 7);
		
		/*		SS2 = SS1 ? (A <<< 12)						*/
		SS2 = SS1 ^ sm3_lshift(*A, 12);
		
		/*		TT1 = FFj(A,B,C) + D + SS2 +W��j			*/
		TT1 = FF(j, *A, *B, *C) + (*D) + SS2 + w[68+j];

		/*		TT2 = GGj(E,F,G) + H + SS1 +Wj				*/
		TT2 = GG(j, *E, *F, *G) + (*H) + SS1 + w[j];
		
		/*		D = C										*/
		*D = *C;
		
		/*		C = B <<< 9									*/
		*C = sm3_lshift(*B, 9);

		/*		B = A										*/
		*B = *A;
		
		/*		A = TT1										*/
		*A = TT1;
		
		/*		H = G										*/
		*H = *G;
		
		/*		G = F <<< 19								*/
		*G = sm3_lshift(*F, 19);
		
		/*		F = E										*/
		*F = *E;

		/*		E = P0(TT2)									*/
		*E = P0(TT2);
	}

	/*	V(i+1) = ABCDEFGH ? V(i)	*/
	for (j = 0; j < 8; j++) {
		v[j] = v[j] ^ vi[j];
	}
}


/**
 *	5.3.1 �����
 *	���������Ϣm�䰴64�ֽڣ�512���أ����з��飺m�� = B(0)B(1)...B(n-1)
 *	����n=m_size/512��
 *	��m�䰴���з�ʽ���
 *
 *	FOR i=0 TO n-1
 *		V(i+1) = CF(V(i), B(i))
 *	ENDFOR
 *
 *	����CF��ѹ������V(0)Ϊ256���س�ʼֵIV��B(i)Ϊ�������Ϣ���飬���ѹ���Ľ��ΪV(n)�� 
 */
static vss_uint32 sm3_loop(const vss_uint8 *m, vss_uint32 m_bytes, SM3_WORD_T *iv)
{
	vss_uint32 left_bytes = m_bytes;
	const vss_uint8 *b = m;
	vss_uint32 i = 0;
	
	/*	�������Ϣ����(132����)	*/
	SM3_WORD_T w[132] = {0};

	while (left_bytes > 0) {

		if (left_bytes < 64) {
			/*	���������Ӧ�ó���	*/
			return 0;
		}

		sm3_extend(b, w);

		sm3_compress(iv, w);
	
		left_bytes -= 64;
		b += 64;
		
		i++;
	}	

	/*	���ص�����	*/
	return i;
}



/***************************************************************************************
 *
 *	��2����: 
 *	���㷨ԭ�Ӳ�����װΪ3��ʽ(init/update/final)�ĵ��ú���
 *
 ***************************************************************************************/

 vss_uint32 sm3_init(SM3_CTX_T *ctx)
{
	mem_set8(ctx, 0, 168);

	mem_cpy8(ctx->iv, SM3_IV, 32);

	return 1;
}

 vss_uint32 sm3_update(SM3_CTX_T *ctx, vss_uint8 *m, vss_uint32 m_bytes)
{
	vss_uint32 pm_len = 0;
	const vss_uint8 *pm = VSS_NULL;

	/*	��¼��ݳ��ȣ������������padding	*/
	ctx->m_size += m_bytes;

	if ( ctx->r_len && (ctx->r_len + m_bytes) >= 64 ) {

		/*	������ʣ����ݣ��ҿ��Ժ���������һ���µĿ飬�����ƴ�Ӳ�����	*/
		mem_cpy8(ctx->remain + ctx->r_len, m, 64 - ctx->r_len);
		sm3_loop(ctx->remain, 64, ctx->iv);

		/*	�ƶ�m��ָ�룬���ݼ�m_bytes		*/
		m += (64 - ctx->r_len);
		m_bytes -= (64 - ctx->r_len);

		/*	ʣ�������0						*/
		mem_set8(ctx->remain, 0, 128);
		ctx->r_len = 0;
	}
	
	if (ctx->r_len) {

		/*	ʣ����ݺ��������Ȼ���������һ���µĿ飬ֻ�ܽ�����ݼ���浽remain��	*/
		mem_cpy8(ctx->remain + ctx->r_len, m, m_bytes);
		ctx->r_len += m_bytes;
	}
	else {

		/*	ֻ������뵽�鳤�ȵ���ݣ�����ı�����ctx->remain�����������	*/
		pm = m;
		pm_len = m_bytes - (m_bytes % 64);
			
		if (pm_len) {
			sm3_loop(pm, pm_len, ctx->iv);
		}

		/*	����ʣ����ݵ�remain��	*/
		if (m_bytes > pm_len) {
			mem_cpy8(ctx->remain, pm + pm_len, (m_bytes - pm_len));
			ctx->r_len = (m_bytes - pm_len);
		}
	}

	return 1;
}

vss_uint32 sm3_final(vss_uint8 *dgst, SM3_CTX_T *ctx)
{
	vss_uint32 pm_len = 0;
	
	pm_len = sm3_padding(ctx->m_size, ctx->remain + ctx->r_len);
	pm_len += ctx->r_len;

	sm3_loop(ctx->remain, pm_len, ctx->iv);

	sm3_rot_r(ctx->iv, 8, (SM3_WORD_T*)dgst);

	
	return 32;
}

void mizar_sm3(vss_uint8 *msg, vss_size msglen, vss_uint8 *dgst)
{
	SM3_CTX_T ctx;
	sm3_init(&ctx);
	sm3_update(&ctx, msg, msglen);
	sm3_final(dgst, &ctx);
}


/*	SM3ժҪ�Ľ���Ϊ32�ֽڣ�256���أ�	*/
#define SM3_DIGEST_LENGTH	(32)

/*	SM3������Ϣ�Ŀ鳤��Ϊ64�ֽڣ�512���أ������ֵ�ڼ���HMACʱ���õ�	*/
#define SM3_CBLOCK			(64)

#endif

#pragma section code restore


