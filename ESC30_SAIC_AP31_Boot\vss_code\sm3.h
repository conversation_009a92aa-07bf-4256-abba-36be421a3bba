#ifndef __VSS_SM3_H__
#define __VSS_SM3_H__

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#include "vssconf.h"
#include "vsstype.h"



vss_uint32 sm3_init(SM3_CTX_T *ctx);
vss_uint32 sm3_update(SM3_CTX_T *ctx, vss_uint8 *m, vss_uint32 m_bytes);
vss_uint32 sm3_final(vss_uint8 *dgst, SM3_CTX_T *ctx);
void mizar_sm3(vss_uint8 *msg, vss_size msglen, vss_uint8* dgst);

#ifdef __cplusplus
}
#endif
#endif

