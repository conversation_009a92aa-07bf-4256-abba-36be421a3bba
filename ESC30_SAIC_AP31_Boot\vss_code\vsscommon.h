#ifndef _VSS_COMMON_H_
#define _VSS_COMMON_H_

#include "vsstype.h"

//module sfr
//module sfr bit
//struct
//macro define
#define EQUAL 0
#define BIGGER 1
#define SMALLER 2
//function
vss_sint32 VssA2I(const char*  content);

void mem_set(vss_uint32 *result, const vss_uint32 content, vss_uint32 len);
void mem_set8(vss_uint8 *result, const vss_uint8 content, vss_uint32 len);
void mem_cpy(vss_uint32 *result, const vss_uint32 *content, vss_uint32 len);
void mem_cpy8(vss_uint8 *result, const vss_uint8 *content, vss_uint32 len);
vss_uint8 mem_cmp(const vss_uint32 *data1, const vss_uint32 *data2, vss_uint32 len);
vss_uint8 mem_cmp8(const vss_uint8 *data1, const vss_uint8 *data2, vss_uint32 len);
vss_uint8 mem_cmp2(const vss_uint32 *data1, const vss_uint32 data2, vss_uint32 len);
void mem_swap(vss_uint32 *data1, vss_uint32 *data2, vss_uint32 len);
void mem_and(vss_uint32 *result, const vss_uint32 *data1, const vss_uint32 *data2, vss_uint32 len);
void mem_or(vss_uint32 *result, const vss_uint32 *data1, const vss_uint32 *data2, vss_uint32 len);
void mem_xor(vss_uint32 *result, const vss_uint32 *data1, const vss_uint32 *data2, vss_uint32 len);
vss_sint32 VssStrLen(const char*  content);
#endif
