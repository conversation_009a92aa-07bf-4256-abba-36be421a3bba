/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_Type.h
 *           Config:  PGM_BswCfg.dpa
 *      ECU-Project:  PGM_BswCfg
 *
 *        Generator:  MICROSAR RTE Generator Version 4.21.0
 *                    RTE Core Version 1.21.0
 *          License:  CBD1900770
 *
 *      Description:  Header file containing user defined AUTOSAR types and RTE structures
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_TYPE_H
# define RTE_TYPE_H

# include "Rte.h"

/* PRQA S 1039 EOF */ /* MD_Rte_1039 */

/**********************************************************************************************************************
 * Data type definitions
 *********************************************************************************************************************/

# define Rte_TypeDef_IdtAct_PGM_Consist_Fault
typedef boolean IdtAct_PGM_Consist_Fault;

# define Rte_TypeDef_IdtAppAct_PGM_Intn_Diags
typedef uint16 IdtAppAct_PGM_Intn_Diags;

# define Rte_TypeDef_IdtAppComU_BatDisconSts
typedef boolean IdtAppComU_BatDisconSts;

# define Rte_TypeDef_IdtAppComU_BatVolt_ErrSts
typedef boolean IdtAppComU_BatVolt_ErrSts;

# define Rte_TypeDef_IdtAppComU_BusOff_Sts
typedef boolean IdtAppComU_BusOff_Sts;

# define Rte_TypeDef_IdtAppComU_CondAutoDrv
typedef boolean IdtAppComU_CondAutoDrv;

# define Rte_TypeDef_IdtAppComU_EngSts
typedef uint8 IdtAppComU_EngSts;

# define Rte_TypeDef_IdtAppComU_HwInit_Sts
typedef uint8 IdtAppComU_HwInit_Sts;

# define Rte_TypeDef_IdtAppComU_Int_Fault
typedef boolean IdtAppComU_Int_Fault;

# define Rte_TypeDef_IdtAppComU_SC_Sts
typedef boolean IdtAppComU_SC_Sts;

# define Rte_TypeDef_IdtAppComU_SHWAIndSts
typedef uint8 IdtAppComU_SHWAIndSts;

# define Rte_TypeDef_IdtAppComU_SysRunSts
typedef boolean IdtAppComU_SysRunSts;

# define Rte_TypeDef_IdtAppCom_ADASAvlbly
typedef boolean IdtAppCom_ADASAvlbly;

# define Rte_TypeDef_IdtAppCom_APAAvlbly
typedef boolean IdtAppCom_APAAvlbly;

# define Rte_TypeDef_IdtAppCom_APASts
typedef uint8 IdtAppCom_APASts;

# define Rte_TypeDef_IdtAppCom_BatCrnt
typedef uint16 IdtAppCom_BatCrnt;

# define Rte_TypeDef_IdtAppCom_BatSOC
typedef uint8 IdtAppCom_BatSOC;

# define Rte_TypeDef_IdtAppCom_BatSOFVol1
typedef uint8 IdtAppCom_BatSOFVol1;

# define Rte_TypeDef_IdtAppCom_BatSOFVol1Sts
typedef uint8 IdtAppCom_BatSOFVol1Sts;

# define Rte_TypeDef_IdtAppCom_BatSOFVol2
typedef uint8 IdtAppCom_BatSOFVol2;

# define Rte_TypeDef_IdtAppCom_BatSOFVol2Sts
typedef uint8 IdtAppCom_BatSOFVol2Sts;

# define Rte_TypeDef_IdtAppCom_BatTem
typedef uint8 IdtAppCom_BatTem;

# define Rte_TypeDef_IdtAppCom_BatVol
typedef uint16 IdtAppCom_BatVol;

# define Rte_TypeDef_IdtAppCom_CalendarDay
typedef uint8 IdtAppCom_CalendarDay;

# define Rte_TypeDef_IdtAppCom_CalendarYear
typedef uint8 IdtAppCom_CalendarYear;

# define Rte_TypeDef_IdtAppCom_EBSBatSOFVol_ASIL
typedef uint8 IdtAppCom_EBSBatSOFVol_ASIL;

# define Rte_TypeDef_IdtAppCom_EBS_100ms_PDU03_CRC
typedef uint8 IdtAppCom_EBS_100ms_PDU03_CRC;

# define Rte_TypeDef_IdtAppCom_EBS_100ms_PDU03_RC
typedef uint8 IdtAppCom_EBS_100ms_PDU03_RC;

# define Rte_TypeDef_IdtAppCom_EBS_100ms_PDU04_CRC
typedef uint8 IdtAppCom_EBS_100ms_PDU04_CRC;

# define Rte_TypeDef_IdtAppCom_EBS_100ms_PDU04_RC
typedef uint8 IdtAppCom_EBS_100ms_PDU04_RC;

# define Rte_TypeDef_IdtAppCom_ECMAvlbly
typedef boolean IdtAppCom_ECMAvlbly;

# define Rte_TypeDef_IdtAppCom_En12VoltStrMotCmddOn
typedef boolean IdtAppCom_En12VoltStrMotCmddOn;

# define Rte_TypeDef_IdtAppCom_EnRunA
typedef boolean IdtAppCom_EnRunA;

# define Rte_TypeDef_IdtAppCom_HADS_020ms_PDU00_CRC
typedef uint8 IdtAppCom_HADS_020ms_PDU00_CRC;

# define Rte_TypeDef_IdtAppCom_HADS_020ms_PDU00_RC
typedef uint8 IdtAppCom_HADS_020ms_PDU00_RC;

# define Rte_TypeDef_IdtAppCom_HourOfDay
typedef uint8 IdtAppCom_HourOfDay;

# define Rte_TypeDef_IdtAppCom_MinuteOfHour
typedef uint8 IdtAppCom_MinuteOfHour;

# define Rte_TypeDef_IdtAppCom_PGM_050ms_PDU00_CRC
typedef uint8 IdtAppCom_PGM_050ms_PDU00_CRC;

# define Rte_TypeDef_IdtAppCom_PGM_050ms_PDU00_RC
typedef uint8 IdtAppCom_PGM_050ms_PDU00_RC;

# define Rte_TypeDef_IdtAppCom_PGM_NM_BSMtoRMS
typedef boolean IdtAppCom_PGM_NM_BSMtoRMS;

# define Rte_TypeDef_IdtAppCom_PGM_NM_NOSSta
typedef boolean IdtAppCom_PGM_NM_NOSSta;

# define Rte_TypeDef_IdtAppCom_PGM_NM_NOStoRMS
typedef boolean IdtAppCom_PGM_NM_NOStoRMS;

# define Rte_TypeDef_IdtAppCom_PGM_NM_PBSMtoRMS
typedef boolean IdtAppCom_PGM_NM_PBSMtoRMS;

# define Rte_TypeDef_IdtAppCom_PGM_NM_RMSSta
typedef boolean IdtAppCom_PGM_NM_RMSSta;

# define Rte_TypeDef_IdtAppCom_PGM_NM_RMStoNOS
typedef boolean IdtAppCom_PGM_NM_RMStoNOS;

# define Rte_TypeDef_IdtAppCom_PGM_NM_RSStoNOS
typedef boolean IdtAppCom_PGM_NM_RSStoNOS;

# define Rte_TypeDef_IdtAppCom_PGM_NM_RSStoRMS
typedef boolean IdtAppCom_PGM_NM_RSStoRMS;

# define Rte_TypeDef_IdtAppCom_SCSAvlbly
typedef boolean IdtAppCom_SCSAvlbly;

# define Rte_TypeDef_IdtAppCom_SecsOfMinute
typedef uint8 IdtAppCom_SecsOfMinute;

# define Rte_TypeDef_IdtAppCom_SysBPMEnbd
typedef boolean IdtAppCom_SysBPMEnbd;

# define Rte_TypeDef_IdtAppCom_VehOdo
typedef uint32 IdtAppCom_VehOdo;

# define Rte_TypeDef_IdtAppCom_VehSpdAvg
typedef uint16 IdtAppCom_VehSpdAvg;

# define Rte_TypeDef_IdtAppCom_VehSpdAvgDrvn
typedef uint16 IdtAppCom_VehSpdAvgDrvn;

# define Rte_TypeDef_IdtDCA_DCRsn_FwdOC
typedef boolean IdtDCA_DCRsn_FwdOC;

# define Rte_TypeDef_IdtDCA_DCRsn_KL30_OV
typedef boolean IdtDCA_DCRsn_KL30_OV;

# define Rte_TypeDef_IdtDCA_DCRsn_KL30_UV
typedef boolean IdtDCA_DCRsn_KL30_UV;

# define Rte_TypeDef_IdtDCA_DCRsn_RvsOC
typedef boolean IdtDCA_DCRsn_RvsOC;

# define Rte_TypeDef_IdtDCA_Mos_DCIns
typedef boolean IdtDCA_Mos_DCIns;

# define Rte_TypeDef_IdtFJAD_KL30R_Volt
typedef uint16 IdtFJAD_KL30R_Volt;

# define Rte_TypeDef_IdtFJAD_KL30_Volt
typedef uint16 IdtFJAD_KL30_Volt;

# define Rte_TypeDef_IdtFJAD_PGM_Crnt
typedef sint16 IdtFJAD_PGM_Crnt;

# define Rte_TypeDef_IdtFJ_ExtFault_NoCondDrv
typedef boolean IdtFJ_ExtFault_NoCondDrv;

# define Rte_TypeDef_IdtFJ_KL30R_OV_Diag
typedef boolean IdtFJ_KL30R_OV_Diag;

# define Rte_TypeDef_IdtFJ_KL30R_OverVoltLv1
typedef boolean IdtFJ_KL30R_OverVoltLv1;

# define Rte_TypeDef_IdtFJ_KL30R_OverVoltLv2
typedef boolean IdtFJ_KL30R_OverVoltLv2;

# define Rte_TypeDef_IdtFJ_KL30R_OverVoltLv3
typedef boolean IdtFJ_KL30R_OverVoltLv3;

# define Rte_TypeDef_IdtFJ_KL30R_UV_Diag
typedef boolean IdtFJ_KL30R_UV_Diag;

# define Rte_TypeDef_IdtFJ_KL30R_UnderVoltLv1
typedef boolean IdtFJ_KL30R_UnderVoltLv1;

# define Rte_TypeDef_IdtFJ_KL30R_UnderVoltLv2
typedef boolean IdtFJ_KL30R_UnderVoltLv2;

# define Rte_TypeDef_IdtFJ_KL30R_UnderVoltLv3
typedef boolean IdtFJ_KL30R_UnderVoltLv3;

# define Rte_TypeDef_IdtFJ_KL30_OV_Diag
typedef boolean IdtFJ_KL30_OV_Diag;

# define Rte_TypeDef_IdtFJ_KL30_OverVoltLv1
typedef boolean IdtFJ_KL30_OverVoltLv1;

# define Rte_TypeDef_IdtFJ_KL30_OverVoltLv2
typedef boolean IdtFJ_KL30_OverVoltLv2;

# define Rte_TypeDef_IdtFJ_KL30_OverVoltLv3
typedef boolean IdtFJ_KL30_OverVoltLv3;

# define Rte_TypeDef_IdtFJ_KL30_UV_Diag
typedef boolean IdtFJ_KL30_UV_Diag;

# define Rte_TypeDef_IdtFJ_KL30_UnderVoltLv1
typedef boolean IdtFJ_KL30_UnderVoltLv1;

# define Rte_TypeDef_IdtFJ_KL30_UnderVoltLv2
typedef boolean IdtFJ_KL30_UnderVoltLv2;

# define Rte_TypeDef_IdtFJ_KL30_UnderVoltLv3
typedef boolean IdtFJ_KL30_UnderVoltLv3;

# define Rte_TypeDef_IdtFJ_PGM_OverCrntLv1
typedef boolean IdtFJ_PGM_OverCrntLv1;

# define Rte_TypeDef_IdtFJ_PGM_OverCrntLv2
typedef boolean IdtFJ_PGM_OverCrntLv2;

# define Rte_TypeDef_IdtFJ_PGM_OverCrntLv3
typedef boolean IdtFJ_PGM_OverCrntLv3;

# define Rte_TypeDef_IdtFJ_PGM_OverTem
typedef boolean IdtFJ_PGM_OverTem;

# define Rte_TypeDef_IdtFJ_PGM_ShortCircuit
typedef boolean IdtFJ_PGM_ShortCircuit;

# define Rte_TypeDef_IdtFJ_RddErr
typedef boolean IdtFJ_RddErr;

# define Rte_TypeDef_IdtSR_Mos_CIns
typedef boolean IdtSR_Mos_CIns;

# define Rte_TypeDef_IdtSensorAD_KL15_Volt
typedef uint16 IdtSensorAD_KL15_Volt;

# define Rte_TypeDef_IdtSensorAD_KL30R_VoltA
typedef uint16 IdtSensorAD_KL30R_VoltA;

# define Rte_TypeDef_IdtSensorAD_KL30R_VoltB
typedef uint16 IdtSensorAD_KL30R_VoltB;

# define Rte_TypeDef_IdtSensorAD_KL30_VoltA
typedef uint16 IdtSensorAD_KL30_VoltA;

# define Rte_TypeDef_IdtSensorAD_KL30_VoltB
typedef uint16 IdtSensorAD_KL30_VoltB;

# define Rte_TypeDef_IdtSensorAD_MosS1_Volt
typedef uint16 IdtSensorAD_MosS1_Volt;

# define Rte_TypeDef_IdtSensorAD_MosS2_Volt
typedef uint16 IdtSensorAD_MosS2_Volt;

# define Rte_TypeDef_IdtSensorAD_Mos_Tem
typedef uint16 IdtSensorAD_Mos_Tem;

# define Rte_TypeDef_IdtSensorAD_PGM_CrntA1
typedef sint16 IdtSensorAD_PGM_CrntA1;

# define Rte_TypeDef_IdtSensorAD_PGM_CrntA2
typedef sint16 IdtSensorAD_PGM_CrntA2;

# define Rte_TypeDef_IdtSensorAD_PGM_CrntB1
typedef sint16 IdtSensorAD_PGM_CrntB1;

# define Rte_TypeDef_IdtSensorAD_PGM_CrntB2
typedef sint16 IdtSensorAD_PGM_CrntB2;

# define Rte_TypeDef_IdtSensorAD_VCC1V3
typedef uint16 IdtSensorAD_VCC1V3;

# define Rte_TypeDef_IdtSensorAD_VCC3V3
typedef uint16 IdtSensorAD_VCC3V3;

# define Rte_TypeDef_IdtSensorAD_VCC5V0
typedef uint16 IdtSensorAD_VCC5V0;

# define Rte_TypeDef_IdtSensorDio_KL15
typedef boolean IdtSensorDio_KL15;

# define Rte_TypeDef_IdtSensorDio_KL30R_OV
typedef boolean IdtSensorDio_KL30R_OV;

# define Rte_TypeDef_IdtSensorDio_KL30R_UV
typedef boolean IdtSensorDio_KL30R_UV;

# define Rte_TypeDef_IdtSensorDio_KL30_OV
typedef boolean IdtSensorDio_KL30_OV;

# define Rte_TypeDef_IdtSensorDio_KL30_UV
typedef boolean IdtSensorDio_KL30_UV;

# define Rte_TypeDef_IdtSensorDio_PGM_FwdOC
typedef boolean IdtSensorDio_PGM_FwdOC;

# define Rte_TypeDef_IdtSensorDio_PGM_RvsOC
typedef boolean IdtSensorDio_PGM_RvsOC;

# define Rte_TypeDef_IdtSensorErr_ADG0Validation
typedef boolean IdtSensorErr_ADG0Validation;

# define Rte_TypeDef_IdtSensorErr_ADG1Validation
typedef boolean IdtSensorErr_ADG1Validation;

# define Rte_TypeDef_IdtSensor_PGM_SwtStat
typedef boolean IdtSensor_PGM_SwtStat;

# define Rte_TypeDef_IdtVFF_BatVolPrd
typedef uint16 IdtVFF_BatVolPrd;

# define Rte_TypeDef_dtRef_VOID
typedef void * dtRef_VOID;

# define Rte_TypeDef_dtRef_const_VOID
typedef const void * dtRef_const_VOID;

# define Rte_TypeDef_IdtAppCom_APAAFnInd
typedef uint8 IdtAppCom_APAAFnInd;

# define Rte_TypeDef_IdtAppCom_BatCrntSts
typedef uint8 IdtAppCom_BatCrntSts;

# define Rte_TypeDef_IdtAppCom_BatSOCSts
typedef uint8 IdtAppCom_BatSOCSts;

# define Rte_TypeDef_IdtAppCom_BatTemSts
typedef uint8 IdtAppCom_BatTemSts;

# define Rte_TypeDef_IdtAppCom_BatVolSts
typedef uint8 IdtAppCom_BatVolSts;

# define Rte_TypeDef_IdtAppCom_CalendarMonth
typedef uint8 IdtAppCom_CalendarMonth;

# define Rte_TypeDef_IdtAppCom_DTCInfomationPGM
typedef uint64 IdtAppCom_DTCInfomationPGM;

# define Rte_TypeDef_IdtAppCom_EBSBatDisconSts_ASIL
typedef uint8 IdtAppCom_EBSBatDisconSts_ASIL;

# define Rte_TypeDef_IdtAppCom_EBS_100ms_FrP03_Reserve01
typedef uint8 IdtAppCom_EBS_100ms_FrP03_Reserve01;

# define Rte_TypeDef_IdtAppCom_EBS_100ms_FrP03_Reserve02
typedef uint16 IdtAppCom_EBS_100ms_FrP03_Reserve02;

# define Rte_TypeDef_IdtAppCom_EBS_100ms_FrP03_Reserve03
typedef uint16 IdtAppCom_EBS_100ms_FrP03_Reserve03;

# define Rte_TypeDef_IdtAppCom_EBS_100ms_FrP03_Reserve04
typedef uint16 IdtAppCom_EBS_100ms_FrP03_Reserve04;

# define Rte_TypeDef_IdtAppCom_EBS_100ms_FrP04_Reserve01
typedef uint8 IdtAppCom_EBS_100ms_FrP04_Reserve01;

# define Rte_TypeDef_IdtAppCom_EBS_100ms_FrP04_Reserve02
typedef uint8 IdtAppCom_EBS_100ms_FrP04_Reserve02;

# define Rte_TypeDef_IdtAppCom_EBS_100ms_FrP04_Reserve03
typedef uint16 IdtAppCom_EBS_100ms_FrP04_Reserve03;

# define Rte_TypeDef_IdtAppCom_EBS_100ms_FrP04_Reserve04
typedef uint16 IdtAppCom_EBS_100ms_FrP04_Reserve04;

# define Rte_TypeDef_IdtAppCom_EnASSSta
typedef uint8 IdtAppCom_EnASSSta;

# define Rte_TypeDef_IdtAppCom_FOTASts
typedef uint8 IdtAppCom_FOTASts;

# define Rte_TypeDef_IdtAppCom_FOTAStsV
typedef uint8 IdtAppCom_FOTAStsV;

# define Rte_TypeDef_IdtAppCom_GenrSta
typedef uint8 IdtAppCom_GenrSta;

# define Rte_TypeDef_IdtAppCom_HADS_020ms_PDU00_Reserve01
typedef uint8 IdtAppCom_HADS_020ms_PDU00_Reserve01;

# define Rte_TypeDef_IdtAppCom_HADS_020ms_PDU00_Reserve02
typedef uint8 IdtAppCom_HADS_020ms_PDU00_Reserve02;

# define Rte_TypeDef_IdtAppCom_HADS_020ms_PDU00_Reserve03
typedef uint32 IdtAppCom_HADS_020ms_PDU00_Reserve03;

# define Rte_TypeDef_IdtAppCom_LADSAFnInd
typedef uint8 IdtAppCom_LADSAFnInd;

# define Rte_TypeDef_IdtAppCom_MainPwrFltRsn
typedef uint8 IdtAppCom_MainPwrFltRsn;

# define Rte_TypeDef_IdtAppCom_PGMDiags
typedef uint8 IdtAppCom_PGMDiags;

# define Rte_TypeDef_IdtAppCom_PGMFltRsn
typedef uint8 IdtAppCom_PGMFltRsn;

# define Rte_TypeDef_IdtAppCom_PGMSts
typedef uint8 IdtAppCom_PGMSts;

# define Rte_TypeDef_IdtAppCom_PGMSwCtrl
typedef uint8 IdtAppCom_PGMSwCtrl;

# define Rte_TypeDef_IdtAppCom_PGMSwSts
typedef uint8 IdtAppCom_PGMSwSts;

# define Rte_TypeDef_IdtAppCom_PGM_050ms_PDU00_Reserve01
typedef uint8 IdtAppCom_PGM_050ms_PDU00_Reserve01;

# define Rte_TypeDef_IdtAppCom_PGM_050ms_PDU00_Reserve02
typedef uint8 IdtAppCom_PGM_050ms_PDU00_Reserve02;

# define Rte_TypeDef_IdtAppCom_PGM_050ms_PDU00_Reserve03
typedef uint16 IdtAppCom_PGM_050ms_PDU00_Reserve03;

# define Rte_TypeDef_IdtAppCom_PGM_050ms_PDU00_Reserve04
typedef uint16 IdtAppCom_PGM_050ms_PDU00_Reserve04;

# define Rte_TypeDef_IdtAppCom_PMDCSta
typedef uint8 IdtAppCom_PMDCSta;

# define Rte_TypeDef_IdtAppCom_PwrSysStsInfoToAutoDrvng
typedef uint8 IdtAppCom_PwrSysStsInfoToAutoDrvng;

# define Rte_TypeDef_IdtAppCom_PwrSysStsToAutoDrvng
typedef uint8 IdtAppCom_PwrSysStsToAutoDrvng;

# define Rte_TypeDef_IdtAppCom_RednPwrFltRsn
typedef uint8 IdtAppCom_RednPwrFltRsn;

# define Rte_TypeDef_IdtAppCom_SHWAEPBAppdReq
typedef uint8 IdtAppCom_SHWAEPBAppdReq;

# define Rte_TypeDef_IdtAppCom_SHWAIndSts
typedef uint8 IdtAppCom_SHWAIndSts;

# define Rte_TypeDef_IdtAppCom_SHWASysFltSts
typedef uint8 IdtAppCom_SHWASysFltSts;

# define Rte_TypeDef_IdtAppCom_SHWASysMsg
typedef uint8 IdtAppCom_SHWASysMsg;

# define Rte_TypeDef_IdtAppCom_SHWASysReqHzrdLghtReqSts
typedef uint8 IdtAppCom_SHWASysReqHzrdLghtReqSts;

# define Rte_TypeDef_IdtAppCom_SHWASysSts
typedef uint8 IdtAppCom_SHWASysSts;

# define Rte_TypeDef_IdtAppCom_SHWASysTakeOver
typedef uint8 IdtAppCom_SHWASysTakeOver;

# define Rte_TypeDef_IdtAppCom_SysBPM
typedef uint8 IdtAppCom_SysBPM;

# define Rte_TypeDef_IdtAppCom_SysPwrMd
typedef uint8 IdtAppCom_SysPwrMd;

# define Rte_TypeDef_IdtAppCom_VehOdoV
typedef uint8 IdtAppCom_VehOdoV;

# define Rte_TypeDef_IdtAppCom_VehSpdAvgDrvnV
typedef uint8 IdtAppCom_VehSpdAvgDrvnV;

# define Rte_TypeDef_DataArrayType_uint8_1
typedef uint8 DataArrayType_uint8_1[1];

# define Rte_TypeDef_DataArrayType_uint8_14
typedef uint8 DataArrayType_uint8_14[14];

# define Rte_TypeDef_DataArrayType_uint8_2
typedef uint8 DataArrayType_uint8_2[2];

# define Rte_TypeDef_DataArrayType_uint8_3
typedef uint8 DataArrayType_uint8_3[3];

# define Rte_TypeDef_DataArrayType_uint8_6
typedef uint8 DataArrayType_uint8_6[6];

# define Rte_TypeDef_DataArrayType_uint8_8
typedef uint8 DataArrayType_uint8_8[8];

# define Rte_TypeDef_Dcm_Data10ByteType
typedef uint8 Dcm_Data10ByteType[10];

# define Rte_TypeDef_Dcm_Data110ByteType
typedef uint8 Dcm_Data110ByteType[110];

# define Rte_TypeDef_Dcm_Data113ByteType
typedef uint8 Dcm_Data113ByteType[113];

# define Rte_TypeDef_Dcm_Data11ByteType
typedef uint8 Dcm_Data11ByteType[11];

# define Rte_TypeDef_Dcm_Data14ByteType
typedef uint8 Dcm_Data14ByteType[14];

# define Rte_TypeDef_Dcm_Data15ByteType
typedef uint8 Dcm_Data15ByteType[15];

# define Rte_TypeDef_Dcm_Data16ByteType
typedef uint8 Dcm_Data16ByteType[16];

# define Rte_TypeDef_Dcm_Data17ByteType
typedef uint8 Dcm_Data17ByteType[17];

# define Rte_TypeDef_Dcm_Data1ByteType
typedef uint8 Dcm_Data1ByteType[1];

# define Rte_TypeDef_Dcm_Data20ByteType
typedef uint8 Dcm_Data20ByteType[20];

# define Rte_TypeDef_Dcm_Data28ByteType
typedef uint8 Dcm_Data28ByteType[28];

# define Rte_TypeDef_Dcm_Data2ByteType
typedef uint8 Dcm_Data2ByteType[2];

# define Rte_TypeDef_Dcm_Data3ByteType
typedef uint8 Dcm_Data3ByteType[3];

# define Rte_TypeDef_Dcm_Data4ByteType
typedef uint8 Dcm_Data4ByteType[4];

# define Rte_TypeDef_Dcm_Data5ByteType
typedef uint8 Dcm_Data5ByteType[5];

# define Rte_TypeDef_Dcm_Data6ByteType
typedef uint8 Dcm_Data6ByteType[6];

# define Rte_TypeDef_Dcm_Data7ByteType
typedef uint8 Dcm_Data7ByteType[7];

# define Rte_TypeDef_Dcm_Data8ByteType
typedef uint8 Dcm_Data8ByteType[8];

# define Rte_TypeDef_Dem_MaxDataValueType
typedef uint8 Dem_MaxDataValueType[14];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize1
typedef uint8 IdtNvM_u8AryNvBlkSize1[1];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize10
typedef uint8 IdtNvM_u8AryNvBlkSize10[10];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize11
typedef uint8 IdtNvM_u8AryNvBlkSize11[11];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize110
typedef uint8 IdtNvM_u8AryNvBlkSize110[110];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize14
typedef uint8 IdtNvM_u8AryNvBlkSize14[14];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize15
typedef uint8 IdtNvM_u8AryNvBlkSize15[15];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize16
typedef uint8 IdtNvM_u8AryNvBlkSize16[16];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize17
typedef uint8 IdtNvM_u8AryNvBlkSize17[17];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize2
typedef uint8 IdtNvM_u8AryNvBlkSize2[2];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize20
typedef uint8 IdtNvM_u8AryNvBlkSize20[20];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize3
typedef uint8 IdtNvM_u8AryNvBlkSize3[3];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize4
typedef uint8 IdtNvM_u8AryNvBlkSize4[4];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize40
typedef uint8 IdtNvM_u8AryNvBlkSize40[40];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize5
typedef uint8 IdtNvM_u8AryNvBlkSize5[5];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize64
typedef uint8 IdtNvM_u8AryNvBlkSize64[64];

# define Rte_TypeDef_IdtNvM_u8AryNvBlkSize8
typedef uint8 IdtNvM_u8AryNvBlkSize8[8];

# define Rte_TypeDef_IdtAppCom_EBS_100ms_FrP03
typedef struct
{
  IdtAppCom_EBS_100ms_FrP03_Reserve01 EBS_100ms_FrP03_Reserve01;
  IdtAppCom_EBS_100ms_FrP03_Reserve02 EBS_100ms_FrP03_Reserve02;
  IdtAppCom_EBS_100ms_FrP03_Reserve03 EBS_100ms_FrP03_Reserve03;
  IdtAppCom_EBS_100ms_FrP03_Reserve04 EBS_100ms_FrP03_Reserve04;
  IdtAppCom_EBS_100ms_PDU03_CRC EBS_100ms_PDU03_CRC;
  IdtAppCom_EBS_100ms_PDU03_RC EBS_100ms_PDU03_RC;
  IdtAppCom_EBSBatDisconSts_ASIL EBSBatDisconSts_ASIL;
} IdtAppCom_EBS_100ms_FrP03;

# define Rte_TypeDef_IdtAppCom_EBS_100ms_FrP04
typedef struct
{
  IdtAppCom_EBS_100ms_FrP04_Reserve01 EBS_100ms_FrP04_Reserve01;
  IdtAppCom_EBS_100ms_FrP04_Reserve02 EBS_100ms_FrP04_Reserve02;
  IdtAppCom_EBS_100ms_FrP04_Reserve03 EBS_100ms_FrP04_Reserve03;
  IdtAppCom_EBS_100ms_FrP04_Reserve04 EBS_100ms_FrP04_Reserve04;
  IdtAppCom_EBS_100ms_PDU04_CRC EBS_100ms_PDU04_CRC;
  IdtAppCom_EBS_100ms_PDU04_RC EBS_100ms_PDU04_RC;
  IdtAppCom_EBSBatSOFVol_ASIL EBSBatSOFVol_ASIL;
} IdtAppCom_EBS_100ms_FrP04;

# define Rte_TypeDef_IdtAppCom_HADS_020ms_PDU00
typedef struct
{
  IdtAppCom_HADS_020ms_PDU00_CRC HADS_020ms_PDU00_CRC;
  IdtAppCom_HADS_020ms_PDU00_RC HADS_020ms_PDU00_RC;
  IdtAppCom_HADS_020ms_PDU00_Reserve01 HADS_020ms_PDU00_Reserve01;
  IdtAppCom_HADS_020ms_PDU00_Reserve02 HADS_020ms_PDU00_Reserve02;
  IdtAppCom_HADS_020ms_PDU00_Reserve03 HADS_020ms_PDU00_Reserve03;
  IdtAppCom_SHWAEPBAppdReq SHWAEPBAppdReq;
  IdtAppCom_SHWAIndSts SHWAIndSts;
  IdtAppCom_SHWASysFltSts SHWASysFltSts;
  IdtAppCom_SHWASysMsg SHWASysMsg;
  IdtAppCom_SHWASysReqHzrdLghtReqSts SHWASysReqHzrdLghtReqSts;
  IdtAppCom_SHWASysSts SHWASysSts;
  IdtAppCom_SHWASysTakeOver SHWASysTakeOver;
} IdtAppCom_HADS_020ms_PDU00;

# define Rte_TypeDef_IdtAppCom_PGM_050ms_PDU00
typedef struct
{
  IdtAppCom_MainPwrFltRsn MainPwrFltRsn;
  IdtAppCom_PGM_050ms_PDU00_CRC PGM_050ms_PDU00_CRC;
  IdtAppCom_PGM_050ms_PDU00_RC PGM_050ms_PDU00_RC;
  IdtAppCom_PGM_050ms_PDU00_Reserve01 PGM_050ms_PDU00_Reserve01;
  IdtAppCom_PGM_050ms_PDU00_Reserve02 PGM_050ms_PDU00_Reserve02;
  IdtAppCom_PGM_050ms_PDU00_Reserve03 PGM_050ms_PDU00_Reserve03;
  IdtAppCom_PGM_050ms_PDU00_Reserve04 PGM_050ms_PDU00_Reserve04;
  IdtAppCom_PGMDiags PGMDiags;
  IdtAppCom_PGMFltRsn PGMFltRsn;
  IdtAppCom_PGMSts PGMSts;
  IdtAppCom_PGMSwSts PGMSwSts;
  IdtAppCom_PwrSysStsInfoToAutoDrvng PwrSysStsInfoToAutoDrvng;
  IdtAppCom_PwrSysStsToAutoDrvng PwrSysStsToAutoDrvng;
  IdtAppCom_RednPwrFltRsn RednPwrFltRsn;
} IdtAppCom_PGM_050ms_PDU00;

# define Rte_TypeDef_ComM_InhibitionStatusType
typedef uint8 ComM_InhibitionStatusType;

# define Rte_TypeDef_ComM_UserHandleType
typedef uint16 ComM_UserHandleType;

# define Rte_TypeDef_Dcm_SpecificCauseCodeType
typedef uint8 Dcm_SpecificCauseCodeType;

# define Rte_TypeDef_Dem_DTCGroupType
typedef uint32 Dem_DTCGroupType;

# define Rte_TypeDef_Dem_DTCStatusMaskType
typedef uint8 Dem_DTCStatusMaskType;

# define Rte_TypeDef_Dem_EventIdType
typedef uint16 Dem_EventIdType;

# define Rte_TypeDef_Dem_RatioIdType
typedef uint16 Dem_RatioIdType;

# define Rte_TypeDef_EcuM_TimeType
typedef uint32 EcuM_TimeType;

# define Rte_TypeDef_EcuM_UserType
typedef uint8 EcuM_UserType;

# define Rte_TypeDef_IOHWAB_UINT16
typedef uint16 IOHWAB_UINT16;

# define Rte_TypeDef_IOHWAB_UINT8
typedef uint8 IOHWAB_UINT8;

# define Rte_TypeDef_NvM_BlockIdType
typedef uint16 NvM_BlockIdType;

# define Rte_TypeDef_TimeInMicrosecondsType
typedef uint32 TimeInMicrosecondsType;

# define Rte_TypeDef_BswM_ESH_Mode
typedef uint8 BswM_ESH_Mode;

# define Rte_TypeDef_BswM_ESH_RunRequest
typedef uint8 BswM_ESH_RunRequest;

# define Rte_TypeDef_ComM_ModeType
typedef uint8 ComM_ModeType;

# define Rte_TypeDef_Dcm_CommunicationModeType
typedef uint8 Dcm_CommunicationModeType;

# define Rte_TypeDef_Dcm_ConfirmationStatusType
typedef uint8 Dcm_ConfirmationStatusType;

# define Rte_TypeDef_Dcm_ControlDtcSettingType
typedef uint8 Dcm_ControlDtcSettingType;

# define Rte_TypeDef_Dcm_DiagnosticSessionControlType
typedef uint8 Dcm_DiagnosticSessionControlType;

# define Rte_TypeDef_Dcm_EcuResetType
typedef uint8 Dcm_EcuResetType;

# define Rte_TypeDef_Dcm_NegativeResponseCodeType
typedef uint8 Dcm_NegativeResponseCodeType;

# define Rte_TypeDef_Dcm_OpStatusType
typedef uint8 Dcm_OpStatusType;

# define Rte_TypeDef_Dcm_ProtocolType
typedef uint8 Dcm_ProtocolType;

# define Rte_TypeDef_Dcm_RequestKindType
typedef uint8 Dcm_RequestKindType;

# define Rte_TypeDef_Dcm_SecLevelType
typedef uint8 Dcm_SecLevelType;

# define Rte_TypeDef_Dcm_SesCtrlType
typedef uint8 Dcm_SesCtrlType;

# define Rte_TypeDef_Dem_DTCFormatType
typedef uint8 Dem_DTCFormatType;

# define Rte_TypeDef_Dem_DTCKindType
typedef uint8 Dem_DTCKindType;

# define Rte_TypeDef_Dem_DTCOriginType
typedef uint16 Dem_DTCOriginType;

# define Rte_TypeDef_Dem_DTCSeverityType
typedef uint8 Dem_DTCSeverityType;

# define Rte_TypeDef_Dem_DTRControlType
typedef uint8 Dem_DTRControlType;

# define Rte_TypeDef_Dem_DebounceResetStatusType
typedef uint8 Dem_DebounceResetStatusType;

# define Rte_TypeDef_Dem_DebouncingStateType
typedef uint8 Dem_DebouncingStateType;

# define Rte_TypeDef_Dem_EventStatusType
typedef uint8 Dem_EventStatusType;

# define Rte_TypeDef_Dem_IndicatorStatusType
typedef uint8 Dem_IndicatorStatusType;

# define Rte_TypeDef_Dem_InitMonitorReasonType
typedef uint8 Dem_InitMonitorReasonType;

# define Rte_TypeDef_Dem_IumprDenomCondIdType
typedef uint8 Dem_IumprDenomCondIdType;

# define Rte_TypeDef_Dem_IumprDenomCondStatusType
typedef uint8 Dem_IumprDenomCondStatusType;

# define Rte_TypeDef_Dem_IumprReadinessGroupType
typedef uint8 Dem_IumprReadinessGroupType;

# define Rte_TypeDef_Dem_MonitorStatusType
typedef uint8 Dem_MonitorStatusType;

# define Rte_TypeDef_Dem_OperationCycleStateType
typedef uint8 Dem_OperationCycleStateType;

# define Rte_TypeDef_Dem_UdsStatusByteType
typedef uint8 Dem_UdsStatusByteType;

# define Rte_TypeDef_EcuM_BootTargetType
typedef uint8 EcuM_BootTargetType;

# define Rte_TypeDef_EcuM_ModeType
typedef uint8 EcuM_ModeType;

# define Rte_TypeDef_EcuM_ShutdownCauseType
typedef uint8 EcuM_ShutdownCauseType;

# define Rte_TypeDef_EcuM_StateType
typedef uint8 EcuM_StateType;

# define Rte_TypeDef_NvM_RequestResultType
typedef uint8 NvM_RequestResultType;


# ifndef RTE_SUPPRESS_UNUSED_DATATYPES
/**********************************************************************************************************************
 * Unused Data type definitions
 *********************************************************************************************************************/

#  define Rte_TypeDef_IdtDCA_DCRsn_KL30R_OV
typedef boolean IdtDCA_DCRsn_KL30R_OV;

#  define Rte_TypeDef_IdtDCA_DCRsn_KL30R_UV
typedef boolean IdtDCA_DCRsn_KL30R_UV;

#  define Rte_TypeDef_IdtFJ_PGM_SWCtrl_Consist
typedef boolean IdtFJ_PGM_SWCtrl_Consist;

#  define Rte_TypeDef_DTCInfomationPGM
typedef uint64 DTCInfomationPGM;

#  define Rte_TypeDef_Dem_OperationCycleIdType
typedef uint8 Dem_OperationCycleIdType;

#  define Rte_TypeDef_NvM_ServiceIdType
typedef uint8 NvM_ServiceIdType;

# endif


/**********************************************************************************************************************
 * Constant value definitions
 *********************************************************************************************************************/

# define RTE_START_SEC_CONST_UNSPECIFIED
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize1_0;

extern CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize1_2;

extern CONST(IdtNvM_u8AryNvBlkSize10, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize10_0;

extern CONST(IdtNvM_u8AryNvBlkSize11, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize11_0;

extern CONST(IdtNvM_u8AryNvBlkSize110, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize110_0;

extern CONST(IdtNvM_u8AryNvBlkSize14, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize14_0;

extern CONST(IdtNvM_u8AryNvBlkSize15, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize15_0;

extern CONST(IdtNvM_u8AryNvBlkSize16, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize16_0;

extern CONST(IdtNvM_u8AryNvBlkSize17, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize17_0;

extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize2_0;

extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize2_1;

extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize2_6;

extern CONST(IdtNvM_u8AryNvBlkSize20, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize20_0;

extern CONST(IdtNvM_u8AryNvBlkSize3, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize3_0;

extern CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize4_0;

extern CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize4_1;

extern CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize4_2;

extern CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize4_4;

extern CONST(IdtNvM_u8AryNvBlkSize40, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize40_0;
extern CONST(IdtNvM_u8AryNvBlkSize5, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize5_0;

extern CONST(IdtNvM_u8AryNvBlkSize64, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize64_0;

extern CONST(IdtNvM_u8AryNvBlkSize8, RTE_CONST) Rte_C_IdtNvM_u8AryNvBlkSize8_0;

extern CONST(IdtAppCom_EBS_100ms_FrP03, RTE_CONST) Rte_C_IdtAppCom_EBS_100ms_FrP03_0;

extern CONST(IdtAppCom_EBS_100ms_FrP04, RTE_CONST) Rte_C_IdtAppCom_EBS_100ms_FrP04_0;

extern CONST(IdtAppCom_HADS_020ms_PDU00, RTE_CONST) Rte_C_IdtAppCom_HADS_020ms_PDU00_0;

extern CONST(IdtAppCom_PGM_050ms_PDU00, RTE_CONST) Rte_C_IdtAppCom_PGM_050ms_PDU00_0;

# define RTE_STOP_SEC_CONST_UNSPECIFIED
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
# include "Rte_DataHandleType.h"

# ifdef RTE_MICROSAR_PIM_EXPORT


/**********************************************************************************************************************
 * Calibration component and SW-C local calibration parameters
 *********************************************************************************************************************/

#  define RTE_START_SEC_CONST_DEFAULT_RTE_CDATA_GROUP_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_AltDiscon_BatUnderVolDuration_ROM_AltDiscon_BatUnderVolDuration;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_BatDiscon_BatOpenDuration_ROM_BatDiscon_BatOpenDuration;
extern CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_Bat_Normal_SOC_Threshold_ROM_Bat_Normal_SOC_Threshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_Bat_UnderVoltage_Threshold_ROM_Bat_UnderVoltage_Threshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve18_C_ROM_DIDReserve18_C;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve19_C_ROM_DIDReserve19_C;
extern CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve20_ROM_DIDReserve20;
extern CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve21_ROM_DIDReserve21;
extern CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve22_ROM_DIDReserve22;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30OverVoltageHigherThreshold_ROM_KL30OverVoltageHigherThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30OverVoltageHigherThresholdTimeout_ROM_KL30OverVoltageHigherThresholdTimeout;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30OverVoltageLowerThreshold_ROM_KL30OverVoltageLowerThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30OverVoltageLowerThresholdTimeout_ROM_KL30OverVoltageLowerThresholdTimeout;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30UnderVoltageHigherThreshold_ROM_KL30UnderVoltageHigherThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30UnderVoltageHigherThresholdTimeout_ROM_KL30UnderVoltageHigherThresholdTimeout;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30UnderVoltageLowerThreshold_ROM_KL30UnderVoltageLowerThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30UnderVoltageLowerThresholdTimeout_ROM_KL30UnderVoltageLowerThresholdTimeout;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_ROverVoltageHigherThreshold_ROM_KL30_ROverVoltageHigherThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_ROverVoltageHigherThresholdTimeout_ROM_KL30_ROverVoltageHigherThresholdTimeout;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_ROverVoltageLowerThreshold_ROM_KL30_ROverVoltageLowerThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_ROverVoltageLowerThresholdTimeout_ROM_KL30_ROverVoltageLowerThresholdTimeout;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_RUnderVoltageHigherThreshold_ROM_KL30_RUnderVoltageHigherThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_RUnderVoltageHigherThresholdTimeout_ROM_KL30_RUnderVoltageHigherThresholdTimeout;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_RUnderVoltageLowerThreshold_ROM_KL30_RUnderVoltageLowerThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_RUnderVoltageLowerThresholdTimeout_ROM_KL30_RUnderVoltageLowerThresholdTimeout;
extern CONST(IdtNvM_u8AryNvBlkSize110, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_OCV_Map_ROM_OCV_Map;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverCurrentHigherThreshold_ROM_PGMMosfetOverCurrentHigherThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverCurrentHigherThresholdTimeout_ROM_PGMMosfetOverCurrentHigherThresholdTimeout;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverCurrentLowerThreshold_ROM_PGMMosfetOverCurrentLowerThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverCurrentLowerThresholdTimeout_ROM_PGMMosfetOverCurrentLowerThresholdTimeout;
extern CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverTemperatureThreshold_ROM_PGMMosfetOverTemperatureThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMVoltageStabilizationFilterTimeAfterItOpen_ROM_PGMVoltageStabilizationFilterTimeAfterItOpen;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGM_Recovery_Normal_Voltage_Filter_Time_ROM_PGM_Recovery_Normal_Voltage_Filter_Time;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoveryKL30OverVoltageThreshold_ROM_PGMrecoveryKL30OverVoltageThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoveryKL30UnderVoltageThreshold_ROM_PGMrecoveryKL30UnderVoltageThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoveryKL30_ROverVoltageThreshold_ROM_PGMrecoveryKL30_ROverVoltageThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoveryKL30_RUnderVoltageThreshold_ROM_PGMrecoveryKL30_RUnderVoltageThreshold;
extern CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoverytimes_ROM_PGMrecoverytimes;
extern CONST(IdtNvM_u8AryNvBlkSize16, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PIF_F115_ROM_PIF_F115;
extern CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_SecurityAttemptCounter_ROM_SecurityAttemptCounter;

#  define RTE_STOP_SEC_CONST_DEFAULT_RTE_CDATA_GROUP_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# endif

/**********************************************************************************************************************
 *  LOCAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

typedef unsigned int Rte_BitType;
/**********************************************************************************************************************
 * type and extern declarations of RTE internal variables
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * Rte Init State Variable
 *********************************************************************************************************************/

# define RTE_STATE_UNINIT    (0U)
# define RTE_STATE_SCHM_INIT (1U)
# define RTE_STATE_INIT      (2U)

# ifdef RTE_CORE

/**********************************************************************************************************************
 * Calibration Parameters (SW-C local and calibration component calibration parameters)
 *********************************************************************************************************************/

#  define RTE_START_SEC_CONST_DEFAULT_RTE_CDATA_GROUP_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_AltDiscon_BatUnderVolDuration_ROM_AltDiscon_BatUnderVolDuration; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_BatDiscon_BatOpenDuration_ROM_BatDiscon_BatOpenDuration; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_Bat_Normal_SOC_Threshold_ROM_Bat_Normal_SOC_Threshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_Bat_UnderVoltage_Threshold_ROM_Bat_UnderVoltage_Threshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve18_C_ROM_DIDReserve18_C; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve19_C_ROM_DIDReserve19_C; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve20_ROM_DIDReserve20; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve21_ROM_DIDReserve21; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize4, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_DIDReserve22_ROM_DIDReserve22; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30OverVoltageHigherThreshold_ROM_KL30OverVoltageHigherThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30OverVoltageHigherThresholdTimeout_ROM_KL30OverVoltageHigherThresholdTimeout; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30OverVoltageLowerThreshold_ROM_KL30OverVoltageLowerThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30OverVoltageLowerThresholdTimeout_ROM_KL30OverVoltageLowerThresholdTimeout; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30UnderVoltageHigherThreshold_ROM_KL30UnderVoltageHigherThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30UnderVoltageHigherThresholdTimeout_ROM_KL30UnderVoltageHigherThresholdTimeout; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30UnderVoltageLowerThreshold_ROM_KL30UnderVoltageLowerThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30UnderVoltageLowerThresholdTimeout_ROM_KL30UnderVoltageLowerThresholdTimeout; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_ROverVoltageHigherThreshold_ROM_KL30_ROverVoltageHigherThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_ROverVoltageHigherThresholdTimeout_ROM_KL30_ROverVoltageHigherThresholdTimeout; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_ROverVoltageLowerThreshold_ROM_KL30_ROverVoltageLowerThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_ROverVoltageLowerThresholdTimeout_ROM_KL30_ROverVoltageLowerThresholdTimeout; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_RUnderVoltageHigherThreshold_ROM_KL30_RUnderVoltageHigherThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_RUnderVoltageHigherThresholdTimeout_ROM_KL30_RUnderVoltageHigherThresholdTimeout; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_RUnderVoltageLowerThreshold_ROM_KL30_RUnderVoltageLowerThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_KL30_RUnderVoltageLowerThresholdTimeout_ROM_KL30_RUnderVoltageLowerThresholdTimeout; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize110, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_OCV_Map_ROM_OCV_Map; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverCurrentHigherThreshold_ROM_PGMMosfetOverCurrentHigherThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverCurrentHigherThresholdTimeout_ROM_PGMMosfetOverCurrentHigherThresholdTimeout; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverCurrentLowerThreshold_ROM_PGMMosfetOverCurrentLowerThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverCurrentLowerThresholdTimeout_ROM_PGMMosfetOverCurrentLowerThresholdTimeout; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMMosfetOverTemperatureThreshold_ROM_PGMMosfetOverTemperatureThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMVoltageStabilizationFilterTimeAfterItOpen_ROM_PGMVoltageStabilizationFilterTimeAfterItOpen; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGM_Recovery_Normal_Voltage_Filter_Time_ROM_PGM_Recovery_Normal_Voltage_Filter_Time; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoveryKL30OverVoltageThreshold_ROM_PGMrecoveryKL30OverVoltageThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoveryKL30UnderVoltageThreshold_ROM_PGMrecoveryKL30UnderVoltageThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoveryKL30_ROverVoltageThreshold_ROM_PGMrecoveryKL30_ROverVoltageThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize2, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoveryKL30_RUnderVoltageThreshold_ROM_PGMrecoveryKL30_RUnderVoltageThreshold; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PGMrecoverytimes_ROM_PGMrecoverytimes; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize16, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_PIF_F115_ROM_PIF_F115; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern CONST(IdtNvM_u8AryNvBlkSize1, RTE_CONST_DEFAULT_RTE_CDATA_GROUP) Rte_CtApNvM_SecurityAttemptCounter_ROM_SecurityAttemptCounter; /* PRQA S 3408 */ /* MD_Rte_3408 */

#  define RTE_STOP_SEC_CONST_DEFAULT_RTE_CDATA_GROUP_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * Buffers for unqueued S/R
 *********************************************************************************************************************/

#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(IdtAct_PGM_Consist_Fault, RTE_VAR_NOINIT) Rte_AppActuator_PiSRAppAct_PGM_Consist_Fault_PGM_Consist_Fault; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtAppAct_PGM_Intn_Diags, RTE_VAR_NOINIT) Rte_AppActuator_PpSRAppActO_PGM_Intn_Diags_PGM_Intn_Diags; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtAppComU_BatDisconSts, RTE_VAR_NOINIT) Rte_AppCom_PiSRAppComU_BatDisconSts_BatDisconSts; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtAppComU_BatVolt_ErrSts, RTE_VAR_NOINIT) Rte_AppCom_PiSRAppComU_BatVolt_ErrSts_BatVolt_ErrSts; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtAppComU_BusOff_Sts, RTE_VAR_NOINIT) Rte_AppCom_PiSRAppComU_BusOff_Status_BusOffSts; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtAppComU_CondAutoDrv, RTE_VAR_NOINIT) Rte_AppCom_PiSRAppComU_CondAutoDrv_CondAutoDrv; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtAppComU_EngSts, RTE_VAR_NOINIT) Rte_AppCom_PiSRAppComU_EngSts_EngSts; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtAppComU_HwInit_Sts, RTE_VAR_NOINIT) Rte_AppCom_PiSRAppComU_HwInitSts_HwInitSts; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtAppComU_Int_Fault, RTE_VAR_NOINIT) Rte_AppCom_PiSRAppComU_Int_Fault_Int_Fault; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtAppComU_SC_Sts, RTE_VAR_NOINIT) Rte_AppCom_PiSRAppComU_SC_Sts_SCSts; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtAppComU_SHWAIndSts, RTE_VAR_NOINIT) Rte_AppCom_PiSRAppComU_SHWAIndSts_AppComU_SHWAIndSts; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtAppComU_SysRunSts, RTE_VAR_NOINIT) Rte_AppCom_PiSRAppComU_SysRunSts_SysRunSts; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtAppCom_PGM_050ms_PDU00, RTE_VAR_NOINIT) Rte_igPGM_050ms_PDU00_68c457ed_Tx; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_AltDiscon_BatUnderVolDuration; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_BatDiscon_BatOpenDuration; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_Bat_Normal_SOC_Threshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_Bat_UnderVoltage_Threshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize15, RTE_VAR_NOINIT) Rte_CtApNvM_BeforeLastPGMOpenInformation_B043; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize11, RTE_VAR_NOINIT) Rte_CtApNvM_ConfigurationTraceabilityField_F198; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve18_C; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve19_C; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve20; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve21; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve22; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve23; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve24; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve25; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve26; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize4, RTE_VAR_NOINIT) Rte_CtApNvM_DIDReserve27; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize5, RTE_VAR_NOINIT) Rte_CtApNvM_ECUConfigurationFileNumber_F1A9; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize5, RTE_VAR_NOINIT) Rte_CtApNvM_ECUHardwareNumber_F191; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize3, RTE_VAR_NOINIT) Rte_CtApNvM_ECUIndexInformation_F1A5; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize3, RTE_VAR_NOINIT) Rte_CtApNvM_ECUManufactureDate_F18B; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize5, RTE_VAR_NOINIT) Rte_CtApNvM_ECUPartNumber_F187; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize5, RTE_VAR_NOINIT) Rte_CtApNvM_ECUProgrammingProcessFileNumber_F1AA; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_ECUSerialNumber_F18C; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30OverVoltageHigherThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30OverVoltageHigherThresholdTimeout; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30OverVoltageLowerThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30OverVoltageLowerThresholdTimeout; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30UnderVoltageHigherThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30UnderVoltageHigherThresholdTimeout; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30UnderVoltageLowerThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30UnderVoltageLowerThresholdTimeout; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_ROverVoltageHigherThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_ROverVoltageHigherThresholdTimeout; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_ROverVoltageLowerThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_ROverVoltageLowerThresholdTimeout; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_RUnderVoltageHigherThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_RUnderVoltageHigherThresholdTimeout; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_RUnderVoltageLowerThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_KL30_RUnderVoltageLowerThresholdTimeout; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize15, RTE_VAR_NOINIT) Rte_CtApNvM_LastPGMOpenInformation_B042; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_NIF_CurrentData_F121; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_NIF_FactoryData_F120; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize110, RTE_VAR_NOINIT) Rte_CtApNvM_OCV_Map; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMMosfetOverCurrentHigherThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMMosfetOverCurrentHigherThresholdTimeout; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMMosfetOverCurrentLowerThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMMosfetOverCurrentLowerThresholdTimeout; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_PGMMosfetOverTemperatureThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMVoltageStabilizationFilterTimeAfterItOpen; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGM_Recovery_Normal_Voltage_Filter_Time; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMrecoveryKL30OverVoltageThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMrecoveryKL30UnderVoltageThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMrecoveryKL30_ROverVoltageThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize2, RTE_VAR_NOINIT) Rte_CtApNvM_PGMrecoveryKL30_RUnderVoltageThreshold; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_PGMrecoverytimes; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F110; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F111; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F112; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F113; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F114; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F115; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F116; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F117; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F118; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F119; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F11A; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F11B; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F11C; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F11D; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F11E; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize16, RTE_VAR_NOINIT) Rte_CtApNvM_PIF_F11F; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize64, RTE_VAR_NOINIT) Rte_CtApNvM_ReseveforAPP; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize64, RTE_VAR_NOINIT) Rte_CtApNvM_ReseveforFBL; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize8, RTE_VAR_NOINIT) Rte_CtApNvM_ST_ResetBlockInfo_T; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize8, RTE_VAR_NOINIT) Rte_CtApNvM_ST_ResetMngInfo_T; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_SecurityAttemptCounter; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_Softwarecompatibilitystatus_AFFE; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_Softwarecompatibilitystatus_AFFF; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize1, RTE_VAR_NOINIT) Rte_CtApNvM_Softwareintegritystatus_AFFD; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize10, RTE_VAR_NOINIT) Rte_CtApNvM_SupplierECUHardwareReferenceNumbe_F192; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize14, RTE_VAR_NOINIT) Rte_CtApNvM_SwitchOpenReasonRecord_B044; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize5, RTE_VAR_NOINIT) Rte_CtApNvM_SystemSupplierIdentifier_F18A; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize17, RTE_VAR_NOINIT) Rte_CtApNvM_VIN_F190; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtNvM_u8AryNvBlkSize20, RTE_VAR_NOINIT) Rte_CtApNvM_VehicleFeatureInformation_F1A8; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtDCA_Mos_DCIns, RTE_VAR_NOINIT) Rte_AppDisConnectArbitrate_PiSRDCA_Mos_Force_On_Mos_DCIns; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtDCA_DCRsn_FwdOC, RTE_VAR_NOINIT) Rte_AppDisConnectArbitrate_PpSRDCAO_DCRsn_FwdOC_DCRsn_FwdOC; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtDCA_DCRsn_KL30_OV, RTE_VAR_NOINIT) Rte_AppDisConnectArbitrate_PpSRDCAO_DCRsn_KL30_OV_DCRsn_KL30_OV; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtDCA_DCRsn_KL30_UV, RTE_VAR_NOINIT) Rte_AppDisConnectArbitrate_PpSRDCAO_DCRsn_KL30_UV_DCRsn_KL30_UV; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtDCA_DCRsn_RvsOC, RTE_VAR_NOINIT) Rte_AppDisConnectArbitrate_PpSRDCAO_DCRsn_RvsOC_DCRsn_FwdOC; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtDCA_Mos_DCIns, RTE_VAR_NOINIT) Rte_AppDisConnectArbitrate_PpSRDCAO_Mos_DClns_Mos_DCIns; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_ExtFault_NoCondDrv, RTE_VAR_NOINIT) Rte_AppFaultJudge_PiSRFJ_KL30_OverVoltLv1_NoCondDrv_KL30_OverVoltLv1_NoCondDrv; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_ExtFault_NoCondDrv, RTE_VAR_NOINIT) Rte_AppFaultJudge_PiSRFJ_KL30_OverVoltLv2_NoCondDrv_KL30_OverVoltLv2_NoCondDrv; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_ExtFault_NoCondDrv, RTE_VAR_NOINIT) Rte_AppFaultJudge_PiSRFJ_KL30_OverVoltLv3_NoCondDrv_KL30_OverVoltLv3_NoCondDrv; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_ExtFault_NoCondDrv, RTE_VAR_NOINIT) Rte_AppFaultJudge_PiSRFJ_KL30_UnderVoltLv1_NoCondDrv_KL30_UnderVoltLv1_NoCondDrv; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_ExtFault_NoCondDrv, RTE_VAR_NOINIT) Rte_AppFaultJudge_PiSRFJ_KL30_UnderVoltLv2_NoCondDrv_KL30_UnderVoltLv2_NoCondDrv; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_ExtFault_NoCondDrv, RTE_VAR_NOINIT) Rte_AppFaultJudge_PiSRFJ_KL30_UnderVoltLv3_NoCondDrv_KL30_UnderVoltLv3_NoCondDrv; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_ExtFault_NoCondDrv, RTE_VAR_NOINIT) Rte_AppFaultJudge_PiSRFJ_PGM_FwdOverCrntLv1_NoCondDrv_PGM_OverCrntLv1_NoCondDrv; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_ExtFault_NoCondDrv, RTE_VAR_NOINIT) Rte_AppFaultJudge_PiSRFJ_PGM_FwdOverCrntLv2_NoCondDrv_PGM_OverCrntLv2_NoCondDrv; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_ExtFault_NoCondDrv, RTE_VAR_NOINIT) Rte_AppFaultJudge_PiSRFJ_PGM_FwdOverCrntLv3_NoCondDrv_PGM_OverCrntLv3_NoCondDrv; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_ExtFault_NoCondDrv, RTE_VAR_NOINIT) Rte_AppFaultJudge_PiSRFJ_PGM_RvsOverCrntLv1_NoCondDrv_PGM_OverCrntLv1_NoCondDrv; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_ExtFault_NoCondDrv, RTE_VAR_NOINIT) Rte_AppFaultJudge_PiSRFJ_PGM_RvsOverCrntLv2_NoCondDrv_PGM_OverCrntLv2_NoCondDrv; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_ExtFault_NoCondDrv, RTE_VAR_NOINIT) Rte_AppFaultJudge_PiSRFJ_PGM_RvsOverCrntLv3_NoCondDrv_PGM_OverCrntLv3_NoCondDrv; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_RddErr, RTE_VAR_NOINIT) Rte_AppFaultJudge_PiSRFJ_Temp_RddErr_Temp_RddErr; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_RddErr, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_Crnt_RddErr_Crnt_RddErr; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30R_OverVoltLv1, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30R_OverVoltLv1_KL30R_OverVoltLv1; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30R_OverVoltLv2, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30R_OverVoltLv2_KL30R_OverVoltLv2; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30R_OverVoltLv3, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30R_OverVoltLv3_KL30R_OverVoltLv3; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_RddErr, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30R_RddErr_KL30R_RddErr; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_PGM_ShortCircuit, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30R_ShortCircuit_KL30R_ShortCircuit; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30R_UnderVoltLv1, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30R_UnderVoltLv1_KL30R_UnderVoltLv1; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30R_UnderVoltLv2, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30R_UnderVoltLv2_KL30R_UnderVoltLv2; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30R_UnderVoltLv3, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30R_UnderVoltLv3_KL30R_UnderVoltLv3; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJAD_KL30R_Volt, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30R_Volt_KL30R_Volt; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30_OverVoltLv1, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30_OverVoltLv1_KL30_OverVoltLv1; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30_OverVoltLv2, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30_OverVoltLv2_KL30_OverVoltLv2; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30_OverVoltLv3, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30_OverVoltLv3_KL30_OverVoltLv3; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_RddErr, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30_RddErr_KL30_RddErr; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_PGM_ShortCircuit, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30_ShortCircuit_KL30_ShortCircuit; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30_UnderVoltLv1, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30_UnderVoltLv1_KL30_UnderVoltLv1; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30_UnderVoltLv2, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30_UnderVoltLv2_KL30_UnderVoltLv2; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30_UnderVoltLv3, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30_UnderVoltLv3_KL30_UnderVoltLv3; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJAD_KL30_Volt, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_KL30_Volt_KL30_Volt; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJAD_PGM_Crnt, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_PGM_Crnt_PGM_Crnt; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_PGM_OverCrntLv1, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_PGM_FwdOverCrntLv1_PGM_OverCrntLv1; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_PGM_OverCrntLv2, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_PGM_FwdOverCrntLv2_PGM_OverCrntLv2; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_PGM_OverCrntLv3, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_PGM_FwdOverCrntLv3_PGM_OverCrntLv3; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_PGM_OverTem, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_PGM_OverTem_PGM_OverTem; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_PGM_OverCrntLv1, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_PGM_RvsOverCrntLv1_PGM_OverCrntLv1; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_PGM_OverCrntLv2, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_PGM_RvsOverCrntLv2_PGM_OverCrntLv2; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_PGM_OverCrntLv3, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJO_PGM_RvsOverCrntLv3_PGM_OverCrntLv3; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30R_OV_Diag, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJ_KL30R_OV_Diag_KL30R_OV_Diag; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30R_UV_Diag, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJ_KL30R_UV_Diag_KL30R_UV_Diag; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30_OV_Diag, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJ_KL30_OV_Diag_KL30_OV_Diag; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtFJ_KL30_UV_Diag, RTE_VAR_NOINIT) Rte_AppFaultJudge_PpSRFJ_KL30_UV_Diag_KL30_UV_Diag; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSR_Mos_CIns, RTE_VAR_NOINIT) Rte_AppSelfRecover_PpSRSRO_Mos_Clns_Mos_CIns; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_KL15_Volt, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_KL15_Volt_KL15_Volt; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_KL30R_VoltA, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_KL30R_VoltA_KL30R_VoltA; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_KL30R_VoltB, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_KL30R_VoltB_KL30R_VoltB; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_KL30_VoltA, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_KL30_VoltA_KL30_VoltA; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_KL30_VoltB, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_KL30_VoltB_KL30_VoltB; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_MosS1_Volt, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_MosS1_Volt_MosS1_Volt; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_MosS2_Volt, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_MosS2_Volt_MosS2_Volt; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_Mos_Tem, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_Mos_TemA_Mos_TemA; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_Mos_Tem, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_Mos_TemB_Mos_TemB; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_Mos_Tem, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_Mos_TemMax_Mos_TemMax; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_PGM_CrntA1, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_PGM_CrntA1_PGM_CrntA1; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_PGM_CrntA2, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_PGM_CrntA2_PGM_CrntA2; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_PGM_CrntB1, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_PGM_CrntB1_PGM_CrntB1; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_PGM_CrntB2, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_PGM_CrntB2_PGM_CrntB2; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_VCC1V3, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_VCC1V3_VCC1V3; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_VCC3V3, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_VCC3V3_VCC3V3; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorAD_VCC5V0, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorADO_VCC5V0_VCC5V0; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorDio_KL15, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorDioO_KL15_KL15; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorDio_KL30R_OV, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorDioO_KL30R_OV_KL30R_OV; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorDio_KL30R_UV, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorDioO_KL30R_UV_KL30R_UV; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorDio_KL30_OV, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorDioO_KL30_OV_KL30_OV; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorDio_KL30_UV, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorDioO_KL30_UV_KL30_UV; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorDio_PGM_FwdOC, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorDioO_PGM_FwdOC_PGM_FwdOC; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorDio_PGM_RvsOC, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorDioO_PGM_RvsOC_PGM_RvsOC; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorErr_ADG0Validation, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorErrO_ADG0Validation_ADG0Validation; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensorErr_ADG1Validation, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorErrO_ADG1Validation_ADG1Validation; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(IdtSensor_PGM_SwtStat, RTE_VAR_NOINIT) Rte_AppSensor_PpSRSensorO_PGM_SwtStat_PGM_SwtStat; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(BswM_ESH_RunRequest, RTE_VAR_NOINIT) Rte_CpApMyCore_Request_ESH_PostRunRequest_0_requestedMode; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(BswM_ESH_RunRequest, RTE_VAR_NOINIT) Rte_CpApMyCore_Request_ESH_RunRequest_0_requestedMode; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

typedef struct
{
  Rte_BitType Rte_ModeSwitchAck_BswM_Switch_ESH_ModeSwitch_BswM_MDGP_ESH_Mode_Ack : 1;
  Rte_BitType Rte_ModeSwitchAck_Dcm_DcmCommunicationControl_ComMConf_ComMChannel_CFCANConnector_43dac980_DcmCommunicationControl_ComMConf_ComMChannel_CFCANConnector_43dac980_Ack : 1;
  Rte_BitType Rte_ModeSwitchAck_Dcm_DcmControlDtcSetting_DcmControlDtcSetting_Ack : 1;
  Rte_BitType Rte_ModeSwitchAck_Dcm_DcmDiagnosticSessionControl_DcmDiagnosticSessionControl_Ack : 1;
  Rte_BitType Rte_ModeSwitchAck_Dcm_DcmEcuReset_DcmEcuReset_Ack : 1;
} Rte_AckFlagsType;

#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_AckFlagsType, RTE_VAR_NOINIT) Rte_AckFlags;

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

typedef struct
{
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_BatDiscon_BatOpenDuration : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_Bat_Normal_SOC_Threshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_Bat_UnderVoltage_Threshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_ConfigurationTraceabilityField_F198 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_DIDReserve18_C : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_DIDReserve19_C : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_DIDReserve20 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_DIDReserve21 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_DIDReserve22 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_DIDReserve23 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_DIDReserve24 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_DIDReserve25 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_DIDReserve26 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_DIDReserve27 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_ECUHardwareNumber_F191 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_ECUIndexInformation_F1A5 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_ECUManufactureDate_F18B : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_ECUPartNumber_F187 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_ECUSerialNumber_F18C : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30OverVoltageHigherThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30OverVoltageLowerThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_LastPGMOpenInformation_B042 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_NIF_CurrentData_F121 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_NIF_FactoryData_F120 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_OCV_Map : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PGMrecoverytimes : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F110 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F111 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F112 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F113 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F114 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F115 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F116 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F117 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F118 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F119 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F11A : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F11B : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F11C : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F11D : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F11E : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_PIF_F11F : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_ReseveforAPP : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_ReseveforFBL : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_ST_ResetBlockInfo_T : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_ST_ResetMngInfo_T : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_SecurityAttemptCounter : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_Softwareintegritystatus_AFFD : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_SwitchOpenReasonRecord_B044 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_SystemSupplierIdentifier_F18A : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_VIN_F190 : 1;
  Rte_BitType Rte_DirtyFlag_CtApNvM_NvSWC_VehicleFeatureInformation_F1A8 : 1;
} Rte_DirtyFlagsType;

#  define RTE_START_SEC_VAR_ZERO_INIT_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_DirtyFlagsType, RTE_VAR_ZERO_INIT) Rte_DirtyFlags;

#  define RTE_STOP_SEC_VAR_ZERO_INIT_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


typedef struct
{
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_BatDiscon_BatOpenDuration : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_Bat_Normal_SOC_Threshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_Bat_UnderVoltage_Threshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_ConfigurationTraceabilityField_F198 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_DIDReserve18_C : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_DIDReserve19_C : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_DIDReserve20 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_DIDReserve21 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_DIDReserve22 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_DIDReserve23 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_DIDReserve24 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_DIDReserve25 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_DIDReserve26 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_DIDReserve27 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_ECUHardwareNumber_F191 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_ECUIndexInformation_F1A5 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_ECUManufactureDate_F18B : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_ECUPartNumber_F187 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_ECUSerialNumber_F18C : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30OverVoltageHigherThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30OverVoltageLowerThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_LastPGMOpenInformation_B042 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_OCV_Map : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PGMrecoverytimes : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F110 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F111 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F112 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F113 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F114 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F115 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F116 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F117 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F118 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F119 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F11A : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F11B : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F11C : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F11D : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F11E : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_PIF_F11F : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_ReseveforFBL : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_SecurityAttemptCounter : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_Softwareintegritystatus_AFFD : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_SwitchOpenReasonRecord_B044 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_SystemSupplierIdentifier_F18A : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_VIN_F190 : 1;
  Rte_BitType Rte_NvBlockPendingFlag_CtApNvM_NvSWC_VehicleFeatureInformation_F1A8 : 1;
} Rte_NvBlockPendingFlagsType;

#  define RTE_START_SEC_VAR_ZERO_INIT_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_NvBlockPendingFlagsType, RTE_VAR_ZERO_INIT) Rte_NvBlockPendingFlags;

#  define RTE_STOP_SEC_VAR_ZERO_INIT_UNSPECIFIED
#  include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */



# endif /* defined(RTE_CORE) */

/**********************************************************************************************************************
 * extern declaration of RTE Update Flags for optimized macro implementation
 *********************************************************************************************************************/
typedef struct
{
  Rte_BitType Rte_RxUpdate_AppCom_PiSRAppCom_EBS_100ms_FrP03_PiSRAppCom_EBS_100ms_FrP03 : 1;
  Rte_BitType Rte_RxUpdate_AppCom_PiSRAppCom_EBS_100ms_FrP04_PiSRAppCom_EBS_100ms_FrP04 : 1;
  Rte_BitType Rte_RxUpdate_AppCom_PiSRAppCom_HADS_020ms_PDU00_PiSRAppCom_HADS_020ms_PDU00 : 1;
} Rte_RxUpdateFlagsType;

# define RTE_START_SEC_VAR_ZERO_INIT_UNSPECIFIED
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_RxUpdateFlagsType, RTE_VAR_ZERO_INIT) Rte_RxUpdateFlags;

# define RTE_STOP_SEC_VAR_ZERO_INIT_UNSPECIFIED
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#endif /* RTE_TYPE_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_1039:  MISRA rule: Rule1.2
     Reason:     Same macro and function names are required to meet AUTOSAR spec.
     Risk:       No functional risk. Macro will be undefined before function definition.
     Prevention: Not required.

   MD_Rte_3408:  MISRA rule: Rule8.4
     Reason:     For the purpose of monitoring during calibration or debugging it is necessary to use non-static declarations.
                 This is covered in the MISRA C compliance section of the Rte specification.
     Risk:       No functional risk.
     Prevention: Not required.

*/
