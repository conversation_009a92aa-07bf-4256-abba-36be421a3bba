#ifndef _MIZAR_VSS_ECC_H_
#define _MIZAR_VSS_ECC_H_

#include "ecc.h"
#include "calc.h"
#include "ecdsa.h"
#include "vssconf.h"
extern const vss_uint32 mizar_ecc_g_ecdsa_para_a[8];
extern const vss_uint32 mizar_ecc_g_ecdsa_para_b[8];
extern const vss_uint32 mizar_ecc_g_ecdsa_para_p[8];
extern const vss_uint32 mizar_ecc_g_ecdsa_para_n[8];
extern const vss_uint32 mizar_ecc_g_ecdsa_para_G[16];
extern const vss_uint32 mizar_ecc_g_ecdsa_para_h;

vss_uint32 ecc_gen_key(vss_uint8* sk, vss_uint8* x, vss_uint8* y);
vss_uint8 ecc_pri_dec(vss_uint32* priKey, vss_uint8 wlen, vss_uint8* in, vss_uint32 inLen, vss_uint8* out);
vss_uint8 ecc_pub_enc(vss_uint32* x, vss_uint32* y, vss_uint8 wlen, vss_uint8* in, vss_uint32 inLen, vss_uint8* out);
vss_uint32 my_ecc_sign(vss_uint8* sk, vss_uint8* data, vss_uint8* sig_data);
vss_uint32 my_ecc_verify(vss_uint8* x, vss_uint8* y, vss_uint8* data, vss_uint8* sig_data);

#endif
