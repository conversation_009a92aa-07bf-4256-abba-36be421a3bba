/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 * @file    CanIf_Cfg.c
 * @brief
 *
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *
 * <AUTHOR>
 * @date    2013-5-22
 * 
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20130522    WBN       Initial version
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/
#include "CanIf_Cfg.h"
#include "Uds_CanIf.h"
#include <stdio.h>
#include "CanTp.h"

/*=======[E X T E R N A L   D A T A]==========================================*/
const CanIf_TxChannelType CanIfTxCh[CANIF_TX_CHANNEL_NUM] =
{
		{0x00,0x01,0x00004728,0x02u,CanTp_TxConfirmation},
};

const CanIf_RxChannelType CanIfRxCh[CANIF_RX_CHANNEL_NUM] =
{
		  {0x00,0x11,0x00000720,0x00u,CanTp_RxIndication},
		  {0x01,0x12,0x000007df,0x01u,CanTp_RxIndication},
};

/*=======[E N D   O F   F I L E]==============================================*/
