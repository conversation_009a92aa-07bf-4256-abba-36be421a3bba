# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\Cmake3.28\bin\cmake.exe

# The command to remove a file.
RM = D:\Cmake3.28\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = "D:\yeah\new requirement\AP31BOOT20250725\SBL\ESC30_SAIC_AP31_BootV1.03.01"

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = "D:\yeah\new requirement\AP31BOOT20250725\SBL\ESC30_SAIC_AP31_BootV1.03.01\build"

# Utility rule file for tasking_info.

# Include any custom commands dependencies for this target.
include CMakeFiles/tasking_info.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/tasking_info.dir/progress.make

CMakeFiles/tasking_info:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir="D:\yeah\new requirement\AP31BOOT20250725\SBL\ESC30_SAIC_AP31_BootV1.03.01\build\CMakeFiles" --progress-num=$(CMAKE_PROGRESS_1) "Displaying Tasking compiler configuration"
	D:\Cmake3.28\bin\cmake.exe -E echo "Tasking TriCore Compiler Information:"
	D:\Cmake3.28\bin\cmake.exe -E echo "  PRODDIR: C:/Program Files (x86)/TASKING/TriCore v4.2r2/ctc"
	D:\Cmake3.28\bin\cmake.exe -E echo "  Compiler: cctc"
	D:\Cmake3.28\bin\cmake.exe -E echo "  Linker: cctc with LSL script"
	D:\Cmake3.28\bin\cmake.exe -E echo "  Target CPU: userdef16x (TC23x)"
	D:\Cmake3.28\bin\cmake.exe -E echo "  Output: AP31_ESC_PBL.elf, AP31_ESC_PBL.hex"

tasking_info: CMakeFiles/tasking_info
tasking_info: CMakeFiles/tasking_info.dir/build.make
.PHONY : tasking_info

# Rule to build all files generated by this target.
CMakeFiles/tasking_info.dir/build: tasking_info
.PHONY : CMakeFiles/tasking_info.dir/build

CMakeFiles/tasking_info.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\tasking_info.dir\cmake_clean.cmake
.PHONY : CMakeFiles/tasking_info.dir/clean

CMakeFiles/tasking_info.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" "D:\yeah\new requirement\AP31BOOT20250725\SBL\ESC30_SAIC_AP31_BootV1.03.01" "D:\yeah\new requirement\AP31BOOT20250725\SBL\ESC30_SAIC_AP31_BootV1.03.01" "D:\yeah\new requirement\AP31BOOT20250725\SBL\ESC30_SAIC_AP31_BootV1.03.01\build" "D:\yeah\new requirement\AP31BOOT20250725\SBL\ESC30_SAIC_AP31_BootV1.03.01\build" "D:\yeah\new requirement\AP31BOOT20250725\SBL\ESC30_SAIC_AP31_BootV1.03.01\build\CMakeFiles\tasking_info.dir\DependInfo.cmake" "--color=$(COLOR)"
.PHONY : CMakeFiles/tasking_info.dir/depend

