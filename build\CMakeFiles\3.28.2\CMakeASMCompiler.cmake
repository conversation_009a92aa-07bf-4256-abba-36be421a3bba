set(CMAKE_ASM_COMPILER "D:/mingw64/bin/gcc.exe")
set(CMAKE_ASM_COMPILER_ARG1 "")
set(CMAKE_AR "D:/mingw64/bin/ar.exe")
set(CMAKE_ASM_COMPILER_AR "D:/mingw64/bin/gcc-ar.exe")
set(CMAKE_RANLIB "D:/mingw64/bin/ranlib.exe")
set(CMAKE_ASM_COMPILER_RANLIB "D:/mingw64/bin/gcc-ranlib.exe")
set(CMAKE_LINKER "D:/mingw64/bin/ld.exe")
set(CMAKE_MT "")
set(CMAKE_TAPI "CMAKE_TAPI-NOTFOUND")
set(CMAKE_ASM_COMPILER_LOADED 1)
set(CMAKE_ASM_COMPILER_ID "GNU")
set(CMAKE_ASM_COMPILER_VERSION "")
set(CMAKE_ASM_COMPILER_ENV_VAR "ASM")




set(CMAKE_ASM_IGNORE_EXTENSIONS h;H;o;O;obj;OBJ;def;DEF;rc;RC)
set(CMAKE_ASM_LINKER_PREFERENCE 0)
set(CMAKE_ASM_LINKER_DEPFILE_SUPPORTED )


