/*
 * Ext_Fls.h
 *
 *  Created on: 2020-11-12
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#ifndef EXT_FLS_H_
#define EXT_FLS_H_
#include "Std_Types.h"
#include "Fls.h"
#include "EyeQFls_Drive.h"
#include "FL.h"
#include "SecM.h"
#include "Appl.h"
extern void extFls_CheckErase(tFlashParam* flashParam);
extern void extFls_WriteLastData (tFlashParam* flashParam);
extern SecM_StatusType extFls_CheckSumForProgramedData (SecM_CRCType* crc);
extern void extFls_Program(tFlashParam* flashParam);
extern SecM_StatusType extFls_CheckSumFor37(SecM_CRCType* crc32ForS37,FL_SegmentInfoType* remaindata);
#endif /* EXT_FLS_H_ */
