#include "kzuc.h"
#include "vsscommon.h"


#pragma section code "vss_api_code" 

#if (defined (_ENABLE_MIZAR_ZUC_)&&(_ENABLE_MIZAR_ZUC_ == 1U))

extern const vss_uint8 KZUC_S0[256];
extern const vss_uint8 KZUC_S1[256];
extern const vss_uint32 KZUC_d[16];

/************************************************************
Function: AddMod
Description: calculate a+b mod 2^31-1
Calls:
Called By: LFSRWithInitMode
Input: a,b: vss_uint32(32bit)
Output:
Return: c, c=a+b mod 2^31-1
Others:
************************************************************/
vss_uint32 AddMod(vss_uint32 a, vss_uint32 b)
{
	vss_uint32 c = a + b;
	if(c >> 31)
	{
		c= (c & 0x7fffffff) + 1;
	}
	return c;
}

/************************************************************
Function: PowMod
Description: calculate x*2^k mod 2^31-1
Calls: Called By: LFSRWithInitMode
Input: x: input
k: exponential
Output:
Return: x*2^k mod 2^31-1
Others:
************************************************************/
vss_uint32 PowMod(vss_uint32 x, vss_uint32 k)
{
	return (((x << k) | (x >> (31-k))) & 0x7fffffff);
}

/************************************************************
Function: L1
Description: linear transformation L1
Calls:
Called By: F
Input: X: input
Output:
Return: X^(X<<< 2)^(X<<<10)^(X<<<18)^(X<<<24)
Others:
************************************************************/
vss_uint32 L1(vss_uint32 X)
{
	return X ^ ZUC_rotl32(X, 2) ^ ZUC_rotl32(X, 10) ^ ZUC_rotl32(X, 18) ^ ZUC_rotl32(X, 24);
}

/************************************************************
Function: L2
Description: linear transformation L2
Calls:
Called By: F
Input: X: input
Output:
Return: X^(X<<< 8)^(X<<<14)^(X<<<22)^(X<<<30)
Others:
************************************************************/
vss_uint32 L2(vss_uint32 X)
{
	return X ^ ZUC_rotl32(X, 8) ^ ZUC_rotl32(X, 14) ^ ZUC_rotl32(X, 22) ^ ZUC_rotl32(X, 30);
}

/************************************************************
Function: BitValue
Description: test if the value of M at the position i equals 0
Calls:
Called By: ZUC_Integrity
Input: M: message
i: the position i
Output:
Return: 0:the value of M at the position i equals 0
1:the value of M at the position i equals 1
Others:
************************************************************/
vss_uint8 BitValue(vss_uint32 M[], vss_uint32 i)
{
	vss_uint32 j, k;
	j = i >> 5;
	k = i & 0x1f;
	if (M[j] & (0x1 << (31-k)))
		return 1;
	else
		return 0;
}

/************************************************************
Function: GetWord
Description: get a 32bit word ki from bit strings k[i],k[i+1]...,namely
ki=k[i]||k[i+1]||��||k[i+31]
Calls:
Called By: ZUC_Integrity
Input: k[]:
i: the position i
Output:
Return: ki=k[i]||k[i+1]||��||k[i+31]
Others:
************************************************************/
vss_uint32 GetWord(vss_uint32 k[], vss_uint32 i)
{
	vss_uint32 j, m;
	vss_uint32 word;
	j = i >> 5;
	m = i & 0x1f;
	if(m == 0)
		word = k[j];
	else
		word = (k[j] << m) | (k[j+1] >> (32 - m));
		
	return word;
}

/************************************************************
Function: LFSRWithInitMode
Description: Initialisation mode,refresh the current state of LFSR
Calls: AddMod,PowMod
Called By: ZUC_Init
Input: LFSR_S:current state of LFSR
u:u=W>>1
Output: Null
Return: Null
Others:
************************************************************/
void LFSRWithInitMode(vss_uint32 LFSR_S[],vss_uint32 u)
{
	vss_uint32 v = LFSR_S[0],i;
	v = AddMod(v, PowMod(LFSR_S[15], 15));
	v = AddMod(v, PowMod(LFSR_S[13], 17));
	v = AddMod(v, PowMod(LFSR_S[10], 21));
	v = AddMod(v, PowMod(LFSR_S[4] , 20));
	v = AddMod(v, PowMod(LFSR_S[0] , 8));
	for(i=0;i<15;i++)
	{
		LFSR_S[i]=LFSR_S[i+1];
	}
	
	LFSR_S[15]=AddMod(v, u);
	
	if (!LFSR_S[15])
	{
		LFSR_S[15] = 0x7fffffff;
	}
}

/************************************************************
Function: LFSRWithWorkMode
Description: working mode,refresh the current state of LFSR
Calls: AddMod,PowMod
Called By: ZUC_Work
Input: LFSR_S:current state of LFSR
Output: Null
Return: Null
Others:
************************************************************/
void LFSRWithWorkMode(vss_uint32 LFSR_S[])
{
	vss_uint32 v = LFSR_S[0],i;
	v = AddMod(v, PowMod(LFSR_S[15], 15));
	v = AddMod(v, PowMod(LFSR_S[13], 17));
	v = AddMod(v, PowMod(LFSR_S[10], 21));
	v = AddMod(v, PowMod(LFSR_S[4] , 20));
	v = AddMod(v, PowMod(LFSR_S[0] , 8));
	for(i=0;i<15;i++)
	{
		LFSR_S[i]=LFSR_S[i+1];
	}
	
	LFSR_S[15]=v;
	
	if (!LFSR_S[15])
	{
		LFSR_S[15] = 0x7fffffff;
	}
}

/************************************************************
Function: BR
Description: Bit Reconstruction
Calls:
Called By: ZUC_Init,ZUC_Work
Input: LFSR_S:current state of LFSR
Output: BR_X[]:achieve X0,X1,X2,X3
Return: Null
Others:
************************************************************/
void BR(vss_uint32 LFSR_S[],vss_uint32 BR_X[])
{
	BR_X[0] = ((LFSR_S[15] & 0x7fff8000) << 1) | (LFSR_S[14] & 0x0000ffff);
	BR_X[1] = ((LFSR_S[11] & 0x0000ffff) << 16)| ((LFSR_S[9] & 0x7fff8000) >> 15);
	BR_X[2] = ((LFSR_S[7] & 0x0000ffff) << 16) | ((LFSR_S[5] & 0x7fff8000) >> 15);
	BR_X[3] = ((LFSR_S[2] & 0x0000ffff) << 16) | ((LFSR_S[0] & 0x7fff8000) >> 15);
}

/************************************************************
Function: F
Description: nonlinear function
Calls:
Called By: ZUC_Init,ZUC_Work
Input: BR_X[]:words X0,X1,X2,X3 from BR
F_R[]:F_R[0]=R1,F_R[1]=R2
Output:
Return: W
Others:
************************************************************/
vss_uint32 F(vss_uint32 BR_X[],vss_uint32 F_R[])
{
	vss_uint32 W, W1, W2;
	W = (BR_X[0] ^ F_R[0]) + F_R[1];
	W1 = F_R[0] + BR_X[1];
	W2 = F_R[1] ^ BR_X[2];
	F_R[0] = L1((W1 << 16) | (W2 >> 16));
	F_R[0]= (KZUC_S0[(F_R[0] >> 24) & 0xFF]) << 24 |(KZUC_S1[(F_R[0] >> 16) & 0xFF]) << 16 |(KZUC_S0[(F_R[0] >> 8) & 0xFF]) << 8 |(KZUC_S1[F_R[0] & 0xFF]);
	F_R[1] = L2((W2 << 16) | (W1 >> 16));
	F_R[1]= (KZUC_S0[(F_R[1] >> 24) & 0xFF]) << 24 |(KZUC_S1[(F_R[1] >> 16) & 0xFF]) << 16 |(KZUC_S0[(F_R[1] >> 8) & 0xFF]) << 8 |(KZUC_S1[F_R[1] & 0xFF]);
	return W;
}

/************************************************************
Function: ZUC_Init
Description: Initialisation process of ZUC
Calls: ZUC_LinkToS,BR,F,LFSRWithInitMode
Called By: ZUC_GenKeyStream
Input: k:initial key
iv:initial vector
Output: LFSR_S[]:the state of LFSR after initialisation:s0,s1,s2,..s15
BR_X[] : the current value:X0,X1,X2,X3
F_R[]:the current value:R1,R2,F_R[0]=R1,F_R[1]=R2
Return: Null
Others:
************************************************************/
void ZUC_Init(vss_uint8 k[], vss_uint8 iv[],vss_uint32 LFSR_S[],vss_uint32 BR_X[],vss_uint32 F_R[])
{
	vss_uint8 count;
	vss_uint32 i;

	count = 32;
	for(i=0;i<16;i++)
	{
		LFSR_S[i]=ZUC_LinkToS(k[i], KZUC_d[i], iv[i]);
	}
	F_R[0]=0x00;
	F_R[1]=0x00;
	while (count)
	{
		vss_uint32 W;
		BR( LFSR_S,BR_X);
		W = F(BR_X,F_R);
		LFSRWithInitMode(LFSR_S,W >> 1);
		count--;
	}
}

/************************************************************
Function: ZUC_work
Description: working stage of ZUC
Calls: BR,F,LFSRWithWorkMode
Called By: ZUC_GenKeyStream
Input: LFSR_S[]:the state of LFSR after initialisation:s0,s1,s2,..s15
BR_X[] : X0,X1,X2,X3
F_R[]:R1,R2
Output: pKeyStream[]:key stream
KeyStreamLen:the length of KeyStream,exporting 32bit for a beat
Return: Null
Others:
************************************************************/
void ZUC_Work(vss_uint32 LFSR_S[],vss_uint32 BR_X[],vss_uint32 F_R[], vss_uint32 pKeyStream[],vss_uint32 KeyStreamLen)
{
	vss_uint32 i = 0;
	BR(LFSR_S,BR_X);
	F(BR_X,F_R);
	LFSRWithWorkMode(LFSR_S);
	while(i < KeyStreamLen)
	{
		BR( LFSR_S,BR_X);
		pKeyStream[i] = F(BR_X,F_R) ^ BR_X[3];
		LFSRWithWorkMode(LFSR_S);
		i++;
	}
}

/****************************************************************
Function: ZUC_GenKeyStream
Description: generate key stream
Calls: ZUC_Init,ZUC_Work
Called By: ZUC_SelfCheck
Input: k[] //initial key,128bit
iv[] //initial iv,128bit
KeyStreamLen //the byte length of KeyStream,exporting 32bit for a beat
Output: KeyStream[] // key strem to be outputed
Return: null
Others:
****************************************************************/
void ZUC_GenKeyStream(vss_uint8 k[], vss_uint8 iv[],vss_uint32 KeyStream[], vss_uint32 KeyStreamLen)
{
	vss_uint32 LFSR_S[16];
	vss_uint32 BR_X[4];
	vss_uint32 F_R[2];
	ZUC_Init(k, iv,LFSR_S,BR_X,F_R);
	ZUC_Work(LFSR_S,BR_X,F_R, KeyStream,KeyStreamLen);
}

/**
 * ���֮�㷨���㣬��ʼ����Կ
 * key[in]	--��Կ������16�ֽ�
 * zuc_key[out]	--ZUC��Կ
 * return 0 �ɹ���-1 ����ηǷ�
**/
void ZucSetKey(vss_uint8* key, TZucKey* zuc_key)
{
	vss_uint32 COUNT = 0;
	vss_uint8 BEARER = 0;
	vss_uint8 DIRECTION = 0;
	vss_uint32 LENGTH = 64;	
	vss_uint32 L;
	vss_uint8 iv[16];

	mem_set8(iv, 0, 16);

	iv[0] = (vss_uint8)(COUNT >> 24);
	iv[1] = (vss_uint8)((COUNT >> 16) & 0xff);
	iv[2] = (vss_uint8)((COUNT >> 8) & 0xff);
	iv[3] = (vss_uint8)(COUNT & 0xff);
	iv[4] = (((BEARER << 3) | (DIRECTION << 2)) & 0xfc);
	iv[5] = 0x00;
	iv[6] = 0x00;
	iv[7] = 0x00;
	iv[8] = iv[0];
	iv[9] = iv[1];
	iv[10] = iv[2];
	iv[11] = iv[3];
	iv[12] = iv[4];
	iv[13] = iv[5];
	iv[14] = iv[6];
	iv[15] = iv[7];

	L = (LENGTH + 31) / 32;
	ZUC_GenKeyStream(key, iv, zuc_key->k, L);
}

void ConvertBig2Little(vss_uint8* src, vss_uint8* dst)
{
	vss_uint32 i = 0;
	for(i = 0; i < 4; i++)
		dst[3-i] = src[i];
}

vss_uint32 ZucCalc(TZucKey* zuc_key, vss_uint8* in, vss_uint8* out)
{
	vss_uint32 *k = VSS_NULL;
	vss_uint32 i = 0;
	vss_uint32 L = 2;
#if (defined (_BIG_ENDIAN_)&&(_BIG_ENDIAN_ == 1U))
	vss_uint32 OBS[2];
	vss_uint32 IBS[2];
#else
	vss_uint32 *OBS = (vss_uint32 *)out;
	vss_uint32 *IBS = (vss_uint32 *)in;
#endif
		
	k = zuc_key->k;

#if (defined (_BIG_ENDIAN_)&&(_BIG_ENDIAN_ == 1U))
	ConvertBig2Little(in, (vss_uint8*)&IBS[0]);
	ConvertBig2Little(in+4, (vss_uint8*)&IBS[1]);
#endif
	
	for(i = 0; i < L; i++)
	{
		OBS[i] = IBS[i] ^ k[i];
	}

#if (defined (_BIG_ENDIAN_)&&(_BIG_ENDIAN_ == 1U))
	ConvertBig2Little((vss_uint8*)&OBS[0], out);
	ConvertBig2Little((vss_uint8*)&OBS[1], out+4);
#endif

	return 0;
}

#endif

#pragma section code restore


