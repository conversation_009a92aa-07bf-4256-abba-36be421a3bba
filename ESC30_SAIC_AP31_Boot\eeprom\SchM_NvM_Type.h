/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  SchM_NvM_Type.h
 *           Config:  PGM_BswCfg.dpa
 *      ECU-Project:  PGM_BswCfg
 *
 *        Generator:  MICROSAR RTE Generator Version 4.21.0
 *                    RTE Core Version 1.21.0
 *          License:  CBD1900770
 *
 *      Description:  Module Interlink Types Header for BSW Module <NvM>
 *********************************************************************************************************************/
#ifndef SCHM_NVM_TYPE_H
# define SCHM_NVM_TYPE_H

# ifdef __cplusplus
extern "C" {
# endif  /* __cplusplus */

# include "Rte_Type.h"

# ifdef __cplusplus
} /* extern "C" */
# endif  /* __cplusplus */

#endif /* SCHM_NVM_TYPE_H */
