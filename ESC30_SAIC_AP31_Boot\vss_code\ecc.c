#include "EccInternal.h"
#include "calc.h"
#include "vsskeym.h"
#include "vsscommon.h"

#define debug 0
extern wdt_rst_cb* g_vssWdtcb;

#define SIZE_NUM 8

#define ECC_VERSION 0x00000001
extern const vss_uint32 vss_ZERO[SIZE_NUM];
extern vss_uint8 k[129 + 1];
extern vss_uint32 RR_x[8];
extern vss_uint32 RR_y[8];
extern vss_uint32 RR_z[8];


#pragma section code "vss_api_code" 

#if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U)) || \
	(defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))


#define ecc_size_w_NIST256 5
extern const vss_uint32 ecc_precompute_data_table_NIST256[384];

#define ecc_size_w_SM2 5
extern const vss_uint32 ecc_precompute_data_table_SM2[384];
extern const ecc_point_j ecc_precompute_point_table_SM2[16];
extern const ecc_point_j ecc_precompute_point_table_NIST256[16];

void ecc_ModmulP(vss_uint32 *result, vss_uint32 *op_a, vss_uint32 *op_b, vss_uint32 *op_p, vss_uint8 wlen)
{
if ( op_p[7] == MODMULP_OPTIMIZE_SM2)
  calc_modmul4sm2(result, op_a, op_b, op_p, wlen);
else if ( op_p[7] == MODMULP_OPTIMIZE_NISTP256)
  calc_modmul4nist256(result, op_a, op_b, op_p, wlen);
else
  calc_modmul(result, op_a, op_b, op_p, wlen);
}

vss_uint32 ecc_version(void)
{
  return ECC_VERSION;
}


/*************************************************************
Description:  ecc_verifypoint,  this function can verify a point is on
              this elliptic curve or not

Return Value: FAILED: the return value denotes this point is not on the elliptic curve,
              SUCCESS: the return value denotes this point is on the elliptic curve
Input:    para->p:    the modulous of this elliptic curve
          para->a:    the factor a of elliptic curve
          para->b:    the factor b of elliptic curve
          para->p_c:  the constant c of modulous p
          para->p_q:  the adjustment factor of modulous p
          wlen:  the word length of the elliptic curve
          op_a: the check point
Output:   none
Note:     none
**************************************************************/
vss_uint32 ecc_verifypoint(ecc_fp *para, ecc_point_a *op_a)
{
  vss_uint32 tmp1[8],tmp2[8];

  ecc_ModmulP(tmp1, op_a->x, op_a->x, para->p, para->wlen);
	ecc_ModmulP(tmp1, tmp1, op_a->x, para->p, para->wlen);
  ecc_ModmulP(tmp2, para->a, op_a->x, para->p, para->wlen);
  calc_modadd(tmp1, tmp1, tmp2, para->p, para->wlen);
  calc_modadd(tmp1, tmp1, para->b, para->p, para->wlen);

  ecc_ModmulP(tmp2, op_a->y, op_a->y, para->p, para->wlen);
  
  if (calc_mem_cmp(tmp1, tmp2, para->wlen) != CALC_EQUAL)
  {
     return RET_ECC_IMPLEMENT_FAILED;
  }
  
  return RET_ECC_IMPLEMENT_SUCCESS;
}

void ecc_j2a(ecc_point_a *result,ecc_point_j *a, ecc_fp *para)
{
	vss_uint32 tmp[8];
	
  ecc_ModmulP(tmp,a->z,a->z,para->p,para->wlen); 
  ecc_ModmulP(tmp,tmp,a->z,para->p,para->wlen);
  calc_modinv(tmp,tmp,para->wlen, para->p,para->wlen);  

  ecc_ModmulP(result->y,tmp,a->y,para->p,para->wlen); 
  ecc_ModmulP(tmp,a->x,tmp,para->p,para->wlen);
  ecc_ModmulP(result->x,tmp,a->z,para->p,para->wlen); 
}

vss_uint32 ecc_pdbl_a(ecc_point_a *result,ecc_point_a *a,ecc_fp *para)
{
    vss_uint32 t1[8],t2[8];

	if(g_vssWdtcb) 
		g_vssWdtcb();

	if (calc_mem_cmp2(a->y,0,para->wlen)!=CALC_EQUAL) 
	{

    }
    else
    {
        return RET_ECC_INFINITEFAR_POINT;
    }

    ecc_ModmulP(t1,a->x,a->x,para->p,para->wlen);         
    calc_modadd(t2,t1,t1,para->p,para->wlen);               
    calc_modadd(t1,t2,t1,para->p,para->wlen);               
    calc_modadd(t1,t1,para->a,para->p,para->wlen);           
    calc_modadd(t2,a->y,a->y,para->p,para->wlen);            
    calc_modinv(t2,t2,para->wlen, para->p,para->wlen);                   

    ecc_ModmulP(t1,t2,t1,para->p,para->wlen);             
    ecc_ModmulP(t2,t1,t1,para->p,para->wlen);             
    calc_modadd(result->x,a->x,a->x,para->p,para->wlen);     
    calc_modsub(result->x,t2,result->x,para->p,para->wlen);  
    calc_modsub(t2,a->x,result->x,para->p,para->wlen);       
    ecc_ModmulP(t1,t2,t1,para->p,para->wlen);             
    calc_modsub(result->y,t1,a->y,para->p,para->wlen);      

    return RET_ECC_IMPLEMENT_SUCCESS;
}

/**
Only for a=-3, otherwise incorrect
*/
vss_uint32 ecc_pdbl_j(ecc_point_j *result,ecc_point_j *a,ecc_fp *para)
{
    vss_uint32 t1[8],t2[8],t3[8];
    vss_uint32 carry;

	if(g_vssWdtcb) 
		g_vssWdtcb();

	if (calc_mem_cmp2(a->z,0,para->wlen)!=CALC_EQUAL)  
	{

	}
	else
	{
		calc_mem_set(result->x,0,para->wlen);
		result->x[0] = 1;
		calc_mem_set(result->y,0,para->wlen);
		result->y[0] = 1;
			
        calc_mem_set(result->z,0,para->wlen);
        return RET_ECC_INFINITEFAR_POINT;
    }

    ecc_ModmulP(t1,a->z,a->z,para->p,para->wlen);                   
    calc_modsub(t2,a->x,t1,para->p,para->wlen);                        
    calc_modadd(t1,a->x,t1,para->p,para->wlen);                        
    ecc_ModmulP(t2,t2,t1,para->p,para->wlen);                       
    calc_modadd(t3,t2,t2,para->p,para->wlen);                      
    calc_modadd(t2,t3,t2,para->p,para->wlen);                      
    calc_modadd(result->y,a->y,a->y,para->p,para->wlen);           
    ecc_ModmulP(result->z,result->y,a->z,para->p,para->wlen);      
    ecc_ModmulP(result->y,result->y,result->y,para->p,para->wlen); 
    ecc_ModmulP(t3,result->y,a->x,para->p,para->wlen);             
    ecc_ModmulP(result->y,result->y,result->y,para->p,para->wlen); 
    if ((result->y[0] & 0x1)==0)                                   
        calc_lsr(result->y,result->y,para->wlen,1);                
    else
    {
        carry=calc_add(result->y,result->y,para->wlen, para->p,para->wlen);  
        calc_lsr(result->y,result->y,para->wlen,1);                
        if (carry==1)
            result->y[para->wlen-1] |= 0x80000000;
    }
    ecc_ModmulP(result->x,t2,t2,para->p,para->wlen);               
    calc_modadd(t1,t3,t3,para->p,para->wlen);                      
    calc_modsub(result->x,result->x,t1,para->p,para->wlen);        
    calc_modsub(t1,t3,result->x,para->p,para->wlen);               
    ecc_ModmulP(t1,t1,t2,para->p,para->wlen);                      
    calc_modsub(result->y,t1,result->y,para->p,para->wlen);        

    return RET_ECC_IMPLEMENT_SUCCESS;
}

vss_uint32 ecc_padd_a(ecc_point_a *result,ecc_point_a *a,ecc_point_a *b,ecc_fp *para)
{
	vss_uint32 t1[8],t2[8];
	if(g_vssWdtcb) 
		g_vssWdtcb();

    if (calc_mem_cmp(a->x,b->x,para->wlen)==CALC_EQUAL)  
    {
        if (calc_mem_cmp(a->y,b->y,para->wlen)!=CALC_EQUAL) 
        {
            return RET_ECC_INFINITEFAR_POINT;
        }
        else
        {
            return ecc_pdbl_a(result,a,para);  
        }
    }

    calc_modsub(t1,a->x,b->x,para->p,para->wlen);      
    calc_modinv(t1,t1,para->wlen, para->p,para->wlen); 

    calc_modsub(t2,a->y,b->y,para->p,para->wlen);      
    ecc_ModmulP(t1,t2,t1,para->p,para->wlen);       
    ecc_ModmulP(t2,t1,t1,para->p,para->wlen);     
    calc_modsub(t2,t2,a->x,para->p,para->wlen);        
    calc_modsub(result->x,t2,b->x,para->p,para->wlen); 
    calc_modsub(t2,a->x,result->x,para->p,para->wlen); 
    ecc_ModmulP(t1,t1,t2,para->p,para->wlen);     
    calc_modsub(result->y,t1,a->y,para->p,para->wlen); 

    return RET_ECC_IMPLEMENT_SUCCESS;
}

vss_uint32 ecc_padd_ja(ecc_point_j *result,ecc_point_j *a,ecc_point_a *b,ecc_fp *para)
{
	vss_uint32 t1[8],t2[8],t3[8],t4[8];
	if(g_vssWdtcb) 
		g_vssWdtcb();

    if (calc_mem_cmp2(a->z,0,para->wlen)==CALC_EQUAL) 
    {
        calc_mem_cpy(result->x,b->x,para->wlen);      
        calc_mem_cpy(result->y,b->y,para->wlen);     
        calc_mem_set(result->z, 0, para->wlen);
        result->z[0] = 1;                  
			
        return RET_ECC_IMPLEMENT_SUCCESS;
    }

    ecc_ModmulP(t1,a->z,a->z,para->p,para->wlen); 
    ecc_ModmulP(t2,t1,a->z,para->p,para->wlen);   
    ecc_ModmulP(t1,t1,b->x,para->p,para->wlen);   
    ecc_ModmulP(t2,t2,b->y,para->p,para->wlen);   
    calc_modsub(t1,t1,a->x,para->p,para->wlen);   
    calc_modsub(t2,t2,a->y,para->p,para->wlen);   

    if(calc_mem_cmp2(t1,0,para->wlen)!=CALC_EQUAL)
		{
			
		}
    else
    {
        if (calc_mem_cmp2(t2,0,para->wlen)==CALC_EQUAL)  
        {
           return ecc_pdbl_j(result,a,para);
        }
        else
        {

            calc_mem_set(result->x,0,para->wlen);
            result->x[0] = 1;
            calc_mem_set(result->y,0,para->wlen);
            result->y[0] = 1;
            calc_mem_set(result->z,0,para->wlen);
            return RET_ECC_INFINITEFAR_POINT;
        }
    }

    ecc_ModmulP(result->z,a->z,t1,para->p,para->wlen);    
    ecc_ModmulP(t3,t1,t1,para->p,para->wlen);             
    ecc_ModmulP(t4,t3,t1,para->p,para->wlen);             
    ecc_ModmulP(t3,t3,a->x,para->p,para->wlen);           
    calc_modadd(t1,t3,t3,para->p,para->wlen);             
    ecc_ModmulP(result->x,t2,t2,para->p,para->wlen);      
    calc_modsub(result->x,result->x,t1,para->p,para->wlen); 
    calc_modsub(result->x,result->x,t4,para->p,para->wlen); 
    calc_modsub(t3,t3,result->x,para->p,para->wlen);        
    ecc_ModmulP(t3,t3,t2,para->p,para->wlen);          
    ecc_ModmulP(t4,t4,a->y,para->p,para->wlen);        
    calc_modsub(result->y,t3,t4,para->p,para->wlen);        

    return RET_ECC_IMPLEMENT_SUCCESS;
}

vss_uint32 ecc_naf(vss_sint8 *p,vss_uint32 *k,vss_uint32 wlen)
{
    vss_uint32 r;
    vss_sint32 u;

    r=0;
    while(calc_mem_cmp2(k,1,wlen)==CALC_BIGGER)
    {
	if(g_vssWdtcb) 
			g_vssWdtcb();
        u=(k[0] & 0x1f)-16;
        k[0]-=u;
        calc_lsr(k,k,wlen,4);
        p[r]=u & 0xff;
        p[r+1]=0;
        p[r+2]=0;
        p[r+3]=0;
        r+=4;
    }
    p[r]=1;

    return r;
}

vss_uint32 ecc_pmul_calc(ecc_point_a *result,ecc_point_a *a,vss_sint8 *knaf,vss_uint32 len,vss_uint32 oe, ecc_fp *para)
{
    vss_sint32 i;
    vss_uint32 h;
    volatile vss_uint32 rt;
    volatile vss_uint32 cnt;
    vss_uint32 p_x[8][8],p_y[8][8],np_y[8][8];
    vss_uint32 dp_x[8],dp_y[8];
    vss_uint32 q_x[8],q_y[8],q_z[8] /*,q_t[8]*/;
    ecc_point_a tmp_a1,tmp_a2,tmp_a3;
    ecc_point_j tmp_j;

    tmp_a1.x=dp_x;
    tmp_a1.y=dp_y;
    ecc_pdbl_a(&tmp_a1,a,para);   


    calc_mem_cpy(p_x[0],a->x,para->wlen);
    calc_mem_cpy(p_y[0],a->y,para->wlen);
    calc_sub(np_y[0],para->p,para->wlen, p_y[0],para->wlen);

    for (i=1;i<8;i++)
    {
        tmp_a2.x=p_x[i-1];
        tmp_a2.y=p_y[i-1];
        tmp_a3.x=p_x[i];
        tmp_a3.y=p_y[i];
        ecc_padd_a(&tmp_a3,&tmp_a2,&tmp_a1,para);
        calc_sub(np_y[i],para->p,para->wlen, p_y[i],para->wlen);
    }


    calc_mem_set(q_x,0,para->wlen);
		q_x[0] = 1;
    calc_mem_set(q_y,0,para->wlen);
		q_y[0] = 1;
    calc_mem_set(q_z,0,para->wlen);


    tmp_j.x=q_x;
    tmp_j.y=q_y;
    tmp_j.z=q_z;


    cnt=0; 
    for (i=len;i>=0;i--)
    {

        rt = ecc_pdbl_j(&tmp_j,&tmp_j,para);  

#if debug
if(rt == RET_ECC_IMPLEMENT_SUCCESS)
{
	ecc_j2a(&buf,&tmp_j,para);
}
#endif			
			

        if (knaf[i]!=0)
        {
            if (knaf[i]>0) 
            {

                h=knaf[i]>>1;
                tmp_a1.y=p_y[h];
            }
            else
            {

                h=(-knaf[i])>>1;
                tmp_a1.y=np_y[h];
            }
            tmp_a1.x=p_x[h];
            rt=ecc_padd_ja(&tmp_j,&tmp_j,&tmp_a1,para);
						
#if debug
if(rt == RET_ECC_IMPLEMENT_SUCCESS)
{
	ecc_j2a(&buf,&tmp_j,para);
}
#endif						
        }

        if ((rt != RET_ECC_IMPLEMENT_SUCCESS) && (i != 0)) 
        {
            return RET_ECC_IMPLEMENT_FAILED; 
        }

        cnt++;
    }


    if (oe!=ECC_SCALAR_NOT_EVEN)  
    {
        tmp_a1.x=p_x[0];
        tmp_a1.y=np_y[0];
        rt = ecc_padd_ja(&tmp_j,&tmp_j,&tmp_a1,para);

#if debug
if(rt == RET_ECC_IMPLEMENT_SUCCESS)
{
	ecc_j2a(&buf,&tmp_j,para);
}
#endif

    }



    if (rt!=RET_ECC_IMPLEMENT_SUCCESS)
    {
        return RET_ECC_INFINITEFAR_POINT;
    }


    ecc_j2a(result,&tmp_j,para);

    return RET_ECC_IMPLEMENT_SUCCESS;
}

vss_uint32 ecc_pointinv(ecc_point_a *result, ecc_point_a *op_a, ecc_fp *para)
{
  vss_uint32 ret = RET_ECC_IMPLEMENT_FAILED;


	calc_mem_cpy(result->x, op_a->x, para->wlen);
  ret = calc_sub(result->y, para->p, para->wlen, op_a->y, para->wlen);
  if(ret != RET_CALC_IMPLEMENT_SUCCESS)
    return RET_ECC_IMPLEMENT_FAILED;

  return RET_ECC_IMPLEMENT_SUCCESS;
}

vss_uint32 ecc_pointadd (ecc_point_a *result, ecc_point_a *op_a, ecc_point_a *op_b, ecc_fp *para)
{
  return ecc_padd_a(result, op_a, op_b, para);
}
 
vss_uint32 ecc_pointdbl (ecc_point_a *result, ecc_point_a *op_a, ecc_fp *para)
{
  return ecc_pdbl_a(result, op_a, para);
}

static vss_uint32 mbedtls_mpi_get_bit( const vss_uint32 *X, vss_size pos )
{
		if( SIZE_NUM * 32 <= pos )
        return( 0 );
		
    return( ( X[pos / 32] >> ( pos % 32 ) ) & 0x01 );
}


static vss_uint32 mbedtls_mpi_safe_cond_assign( vss_uint32 *X, const vss_uint32 *Y, vss_uint8 assign )
{
    vss_uint32 ret = 0;
    vss_size i;


    assign = (assign | (vss_uint8)-assign) >> 7;



    for( i = 0; i < SIZE_NUM; i++ )
        X[i] = X[i] * ( 1 - assign ) + Y[i] * assign;



    return( ret );
}


static vss_uint32 mbedtls_mpi_lset( vss_uint32 *X, vss_sint32 z )
{
	calc_mem_set(&X[0],0,SIZE_NUM);
  X[0] = z;
	return 0;
}


static vss_uint32 ecp_safe_invert_jac( const ecc_fp *grp,
                            ecc_point_a *Q,
                            vss_uint8 inv )
{
    vss_uint8 nonzero;
    vss_uint32 mQY[SIZE_NUM];

    calc_sub( mQY, grp->p, SIZE_NUM, Q->y, SIZE_NUM );
    nonzero = calc_cmp( Q->y, (vss_uint32*)vss_ZERO, SIZE_NUM ) != RET_CALC_EQUAL;
    mbedtls_mpi_safe_cond_assign( Q->y, mQY, inv & nonzero );

    return 0;
}


static vss_uint32 ecp_normalize_jac_many( ecc_fp *grp,
                                   ecc_point_j *T[], vss_size T_size )
{
    vss_size i;
    vss_uint32 tmp_x[8];
    vss_uint32 tmp_y[8];
    ecc_point_a tmp;

	tmp.x = &tmp_x[0];
 	tmp.y = &tmp_y[0];

		for(i=0;i<T_size;i++)
		{
			ecc_j2a(&tmp, T[i], grp);
			calc_mem_cpy( T[i]->x, tmp.x, SIZE_NUM );
			calc_mem_cpy( T[i]->y, tmp.y, SIZE_NUM );
			mbedtls_mpi_lset(T[i]->z, 1);
		}

    return 0;
}


static vss_uint32 ecp_select_comb( const ecc_fp *grp, ecc_point_a *R,
                            const ecc_point_j T[], vss_uint8 T_size,
                            vss_uint8 i )
{
    vss_uint8 ii, j;


    ii =  ( i & 0x7Fu ) >> 1;


    for( j = 0; j < T_size; j++ )
    {
        mbedtls_mpi_safe_cond_assign( R->x, T[j].x, j == ii );
        mbedtls_mpi_safe_cond_assign( R->y, T[j].y, j == ii );
    }


    ecp_safe_invert_jac( grp, R, i >> 7 );

    return 0;
}


static vss_uint32 ecp_mul_comb_core( ecc_fp *grp, ecc_point_j *R,
                              const ecc_point_j T[], vss_uint8 T_size,
                              const vss_uint8 x[], vss_size d)
{
    vss_uint32 Txi_x[8];
    vss_uint32 Txi_y[8];
    ecc_point_a Txi;
    vss_size i;
    Txi.x = &Txi_x[0];
    Txi.y = &Txi_y[0];

     {

        i = d;
        ecp_select_comb( grp, (ecc_point_a*)R, T, T_size, x[i] );
        mbedtls_mpi_lset( R->z, 1 );
    }

    while( i != 0 )
    {
        --i;

        ecc_pdbl_j( R, R, grp );
        ecp_select_comb( grp, &Txi, T, T_size, x[i] );
        ecc_padd_ja( R, R, &Txi, grp );
    }

    return 0;
}



static void ecp_comb_recode_core( vss_uint8 x[], vss_size d,
                                  vss_uint8 w, const vss_uint32 *m )
{
    vss_size i, j;
    vss_uint8 c, cc, adjust;

    mem_set8( x, 0, d+1 );


    for( i = 0; i < d; i++ )
        for( j = 0; j < w; j++ )
            x[i] |= mbedtls_mpi_get_bit( m, i + d * j ) << j;


    c = 0;
    for( i = 1; i <= d; i++ )
    {

        cc   = x[i] & c;
        x[i] = x[i] ^ c;
        c = cc;


        adjust = 1 - ( x[i] & 0x01 );
        c   |= x[i] & ( x[i-1] * adjust );
        x[i] = x[i] ^ ( x[i-1] * adjust );
        x[i-1] |= adjust << 7;
    }
}


static vss_uint32 ecp_comb_recode_scalar( ecc_fp *grp,
                                   vss_uint32 *m,
                                   vss_uint8 *k,
                                   vss_size d,
                                   vss_uint8 w,
                                   vss_uint8 *parity_trick )
{
    vss_uint32 M[SIZE_NUM], mm[SIZE_NUM];


    if( mbedtls_mpi_get_bit( grp->n, 0 ) != 1 )
        return( RET_ECC_IMPLEMENT_FAILED );


    *parity_trick = ( mbedtls_mpi_get_bit( m, 0 ) == 0 );


    calc_mem_cpy( M, m, SIZE_NUM );
    calc_sub( mm, grp->n, SIZE_NUM, m, SIZE_NUM );
    mbedtls_mpi_safe_cond_assign( M, mm, *parity_trick );


    ecp_comb_recode_core( k, d, w, M );

    return 0;
}





static vss_uint32 ecp_mul_comb_after_precomp( ecc_fp *grp,
                                ecc_point_a *R,
                                vss_uint32 *m,
                                const ecc_point_j *T,
                                vss_uint8 T_size,
                                vss_uint8 w,
                                vss_size d)
{
    vss_uint8 parity_trick;
    ecc_point_j RR;
	RR.x = &RR_x[0];
	RR.y = &RR_y[0];
	RR.z = &RR_z[0];

    ecp_comb_recode_scalar( grp, m, k, d, w, &parity_trick );
    ecp_mul_comb_core( grp, &RR, T, T_size, k, d);
    ecp_safe_invert_jac( grp, (ecc_point_a*)&RR, parity_trick);

		ecc_j2a(R,&RR,grp);

    return( RET_ECC_IMPLEMENT_SUCCESS );
}

vss_uint32 ecc_pointmul4G(ecc_point_a *result, ecc_point_a *a, vss_uint32 *k, ecc_fp *para)
{
	vss_uint32 ret;
	vss_uint8 w/*, p_eq_g, i*/;
	vss_size d;
	vss_uint8 T_size/*, T_ok*/;
	ecc_point_j *T;
	
	if(para->p[7] == MODMULP_OPTIMIZE_NISTP256)
	{
		T = (ecc_point_j*)&ecc_precompute_point_table_NIST256[0];
		w = ecc_size_w_NIST256;
	}
	else if (para->p[7] == MODMULP_OPTIMIZE_SM2)
	{
		T = (ecc_point_j*)&ecc_precompute_point_table_SM2[0];
		w = ecc_size_w_SM2;
	}
	else
	{
		return RET_ECC_IMPLEMENT_FAILED;
	}
	

    
    T_size = 1U << ( w - 1 );
    d = ( 256 + w - 1 ) / w;

    ret = ecp_mul_comb_after_precomp( para, result, k,
                                T, T_size, w, d);

    return( ret );
}

vss_uint32 ecc_pointmul(ecc_point_a *result, ecc_point_a *a, vss_uint32 *k, ecc_fp *para)
{
  vss_uint32 rt;
  vss_uint32 bitlen;
  vss_uint32 b[9];
  vss_uint32 oe;
  vss_sint8 knaf[258];

  calc_mem_cpy(b, k, para->wlen);
  

  if (b[0] & 1)  
      oe=ECC_SCALAR_NOT_EVEN;
  else 
  {
      b[0]++;  
      oe=ECC_SCALAR_IS_EVEN;
  }
  
  bitlen=ecc_naf(knaf,b,para->wlen);  
  

  rt=ecc_pmul_calc(result,a,knaf,bitlen,oe,para);
	
	if (rt!=RET_ECC_IMPLEMENT_SUCCESS)
  {
      return RET_ECC_IMPLEMENT_FAILED;
  }
	
	return RET_ECC_IMPLEMENT_SUCCESS;

}

#endif

#pragma section code restore


