/*
 * CDD_Mcrion_Master.h
 *
 *  Created on: 2020-6-16
 *      Author: gaoguan<PERSON>
 */

#ifndef EYEQFLS_DRIVE_H
#define EYEQFLS_DRIVE_H

#include "Std_Types.h"
#include "Appl.h"
#define EXTFLS_MIN_ADDR    0x00000000
#define EXTFLS_MAX_ADDR    0x03FFFFFF
#define EXTFLS_LENGTH      0x04000000
#define EXTFLS_NO_ERR 0
#define EXTFLS_SPI_ERR 1
#define EXTFLS_ERASE_ERR 2
#define EXTFLS_PROGRAM_ERR 3
#define EXTFLS_OUTOFRANGE_ERR 4

#define EXTFLS_SECTORSIZE 0x
typedef enum
{
 extfls_erase_4kb =   0x21,
 extfls_erase_32kb =  0x5C,
 extfls_erase_64kb =  0xDC,
 extfls_erase_all =   0x60
}eraseCommandType;
//extern uint8 CDD_Mcrion_StatusRegisterRead(void);
extern uint8 CDD_Micron_ReadCode(uint32 addr, uint16 lenth, uint8 *rx_Data);
extern uint8 CDD_Micron_Erase(void);
extern uint8 CDD_Micron_Sector_Erase(uint32 addr,eraseCommandType commandType);
extern uint8 CDD_Micron_Program_256Byte(uint32 addr,uint8* data);
extern uint8 CDD_Micron_BULK_ERASE(void);
#endif /* EYEQFLS_DRIVE_H */
