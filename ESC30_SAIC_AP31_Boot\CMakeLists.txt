# ESC30_SAIC_AP31_Boot 子项目 CMakeLists.txt

# 定义当前目录
set(CURRENT_DIR ${CMAKE_CURRENT_SOURCE_DIR})

# 主要源文件
set(MAIN_SOURCES
    ${CURRENT_DIR}/main.c
    ${CURRENT_DIR}/main.h
    ${CURRENT_DIR}/Appl.c
    ${CURRENT_DIR}/Appl.h
    ${CURRENT_DIR}/cstart.c
    ${CURRENT_DIR}/cstart.h
    ${CURRENT_DIR}/ICM_Bootloader_Version.h
)

# MCU模块源文件
file(GLOB MCU_SOURCES
    "${CURRENT_DIR}/MCU/*.c"
    "${CURRENT_DIR}/MCU/*.h"
)

# eeprom模块源文件
file(GLOB EEPROM_SOURCES
    "${CURRENT_DIR}/eeprom/*.c"
    "${CURRENT_DIR}/eeprom/*.h"
)

# Secure模块源文件
file(GLOB SECURE_SOURCES
    "${CURRENT_DIR}/Secure/*.c"
    "${CURRENT_DIR}/Secure/*.h"
)

# Src_file模块源文件
file(GLOB SRC_FILE_SOURCES
    "${CURRENT_DIR}/Src_file/*.c"
    "${CURRENT_DIR}/Src_file/*.h"
)

# Flash模块源文件
file(GLOB_RECURSE FLASH_SOURCES
    "${CURRENT_DIR}/flash/*.c"
    "${CURRENT_DIR}/flash/*.h"
)

# UDS模块源文件
file(GLOB UDS_SOURCES
    "${CURRENT_DIR}/uds/*.c"
    "${CURRENT_DIR}/uds/*.h"
)

# WDG模块源文件
file(GLOB WDG_SOURCES
    "${CURRENT_DIR}/wdg/*.c"
    "${CURRENT_DIR}/wdg/*.h"
)

# MCAL配置文件
file(GLOB MCAL_CFG_SOURCES
    "${CURRENT_DIR}/mcal_cfg/*.c"
    "${CURRENT_DIR}/mcal_cfg/*.h"
)

# MCAL源文件
file(GLOB_RECURSE MCAL_SRC_SOURCES
    "${CURRENT_DIR}/mcal_src/*.c"
    "${CURRENT_DIR}/mcal_src/*.h"
)

# 排除Debug目录
list(FILTER MCAL_SRC_SOURCES EXCLUDE REGEX ".*/Debug/.*")
list(FILTER FLASH_SOURCES EXCLUDE REGEX ".*/Debug/.*")

# 合并所有源文件
set(ALL_SOURCES
    ${MAIN_SOURCES}
    ${EEPROM_SOURCES}
    ${MCU_SOURCES}
    ${SECURE_SOURCES}
    ${SRC_FILE_SOURCES}
    ${FLASH_SOURCES}
    ${UDS_SOURCES}
    ${WDG_SOURCES}
    ${MCAL_CFG_SOURCES}
    ${MCAL_SRC_SOURCES}
)

# 创建库目标
add_library(ESC30_SAIC_AP31_Boot_LIB STATIC ${ALL_SOURCES})

# 设置目标属性
set_target_properties(ESC30_SAIC_AP31_Boot_LIB PROPERTIES
    LINKER_LANGUAGE C
    EXCLUDE_FROM_ALL TRUE
)

# 添加编译定义
target_compile_definitions(ESC30_SAIC_AP31_Boot_LIB PRIVATE
    _TASKING_C_TRICORE_=1
    STATUSTYPEDEFINED
    E_OK=0U
    E_NOT_OK=1U
    STD_ON=1U
    STD_OFF=0U
    STD_HIGH=1U
    STD_LOW=0U
    STD_ACTIVE=1U
    STD_IDLE=0U
    ON=1U
    OFF=0U
)

# 设置包含目录
target_include_directories(ESC30_SAIC_AP31_Boot_LIB PRIVATE
    ${CURRENT_DIR}
    ${CURRENT_DIR}/MCU
    ${CURRENT_DIR}/eeprom
    ${CURRENT_DIR}/Secure
    ${CURRENT_DIR}/Src_file
    ${CURRENT_DIR}/flash
    ${CURRENT_DIR}/flash/flsloader
    ${CURRENT_DIR}/uds
    ${CURRENT_DIR}/wdg
    ${CURRENT_DIR}/mcal_cfg
    ${CURRENT_DIR}/mcal_src
    ${CURRENT_DIR}/mcal_src/dma_infineon_tricore
    ${CURRENT_DIR}/mcal_src/dma_infineon_tricore/inc
    ${CURRENT_DIR}/mcal_src/dma_infineon_tricore/src
    ${CURRENT_DIR}/mcal_src/integration_general
    ${CURRENT_DIR}/mcal_src/integration_general/inc
    ${CURRENT_DIR}/mcal_src/integration_general/src
    ${CURRENT_DIR}/mcal_src/spi_infineon_tricore
    ${CURRENT_DIR}/mcal_src/spi_infineon_tricore/inc
    ${CURRENT_DIR}/mcal_src/spi_infineon_tricore/src
    ${CURRENT_DIR}/MCAL
    ${CURRENT_DIR}/MCAL/config
    ${CURRENT_DIR}/DConfig
    ${CURRENT_DIR}/MConfig
)

# 创建源文件组（用于IDE显示）
source_group("Main" FILES ${MAIN_SOURCES})
source_group("MCU" FILES ${MCU_SOURCES})
source_group("Secure" FILES ${SECURE_SOURCES})
source_group("Src_file" FILES ${SRC_FILE_SOURCES})
source_group("Flash" FILES ${FLASH_SOURCES})
source_group("UDS" FILES ${UDS_SOURCES})
source_group("WDG" FILES ${WDG_SOURCES})
source_group("MCAL_Config" FILES ${MCAL_CFG_SOURCES})
source_group("MCAL_Source" FILES ${MCAL_SRC_SOURCES})

# 打印统计信息
list(LENGTH ALL_SOURCES TOTAL_FILES)
message(STATUS "ESC30_SAIC_AP31_Boot: Found ${TOTAL_FILES} source files")
