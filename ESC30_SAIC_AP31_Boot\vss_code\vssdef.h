#ifndef _VSS_DEF_H_
#define _VSS_DEF_H_
#ifdef __cplusplus
extern "C" {
#endif

#define NULL 0

extern void* memcpy(void* dst, const void* src, unsigned int size);
extern int memcmp(const void* dst, const void* src, unsigned int size);
extern void* memset(void* p, int v, unsigned int size);
extern int sprintf(char* dst, const char* format, ...);
extern char* strcat(char* dst, const char* src);
extern int atoi(const char* src);
extern unsigned int strlen(const char* src);
extern int strcmp(const char* dst, const char* src);

#ifdef __cplusplus
}
#endif
#endif
