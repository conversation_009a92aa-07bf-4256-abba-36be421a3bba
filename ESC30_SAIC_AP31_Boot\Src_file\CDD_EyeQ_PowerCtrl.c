/*******************************************************************************
|  File Name:  SM_PwrMng.c
|  Description:  Implementation of the System Power Manage
|-------------------------------------------------------------------------------
| (c) This software is the proprietary of DIAS.
|     All rights are reserved by DIAS.
|-------------------------------------------------------------------------------
| Initials      Name                   Company
| --------      --------------------   -----------------------------------------
| XG           XiaoGang           		DIAS
|-------------------------------------------------------------------------------
|               R E V I S I O N   H I S T O R Y
|-------------------------------------------------------------------------------
| Date          Version      Author    Description
| ------------  --------     -------   ------------------------------------
| 2020-04-10    01.00.00     XiaoGang       Creation
| 2020-04-11    01.00.01     Bruce          modify all
|
|
|******************************************************************************/

/*******************************************************************************
|    Other Header File Inclusion
|******************************************************************************/
#include "Std_Types.h"
#include "Dio_Cfg.h"
#include "Port.h"
#include "CDD_EyeQ_PowerCtrl.h"
//#include "EyeQ_ValSig.h"
#include "Std_Types.h"
#include "Platform_Types.h"
#include "Appl.h"
/*******************************************************************************
|    Macro Definition
|******************************************************************************/

#define IIC_A_MaxIndex            15
#define IIC_B_MaxIndex            33

#define PortInput_Mode            0x00
#define PortOutput_Mode           0x80

#define ProtPinDirIn_ChipA        0x212
#define ProtPinDirIn_ChipB        0x210
#define EyeQ_POR                  DIO_CHANNEL_10_1
#define EyeQ_PRB                  DIO_CHANNEL_10_2
#define Res_ChipA                 DIO_CHANNEL_33_6
#define Res_ChipB                 DIO_CHANNEL_33_7
#define En_ChipA                  DIO_CHANNEL_33_3
#define En_ChipB                  DIO_CHANNEL_15_0
#define EnMicron_Spi              DIO_CHANNEL_11_2
#define EnCamPower                DIO_CHANNEL_15_3
#define EthPinDirIn               0x0A3
#define Eth_Res                   DIO_CHANNEL_10_3
#define Adc_0_StartAdd            0xF0020700
#define Adc_1_StartAdd            0xF0020B00


#define MicronClock_Id  0x0B6
#define MicronCs_Id     0x0B8
#define MicronMosi_Id   0x0B9


/*******************************************************************************
|    Enum Definition
|******************************************************************************/

/*******************************************************************************
|    Typedef Definition
|******************************************************************************/

/*******************************************************************************
|    Global variables Declaration
|******************************************************************************/

uint8 SM_EthH_Flag=0;
uint8 EyeQPower_Flag=0;

static uint8  IIC_A_Register[IIC_A_MaxIndex]={0};
static uint8  IIC_B_Register[IIC_B_MaxIndex]={0};
uint8  IIC_A_RegVal[IIC_A_MaxIndex]={
		                    			0xC7, 0x85, 0x00, 0x00, /*0xDE*/0x5E, 0x91, 0x01, 0x55, 0x55, 0x05,
		                    			0x00, 0x02, 0x01, 0xD6, 0x71
									};
uint8  IIC_B_RegVal[IIC_B_MaxIndex]={
		                    			0xC7, 0x3C, 0xC8, 0x3C, 0xC6, 0x3C, 0xC6, 0x3C, 0x61, 0x61,
		                    			0x61, 0x61, 0xB1, 0xB1, 0xFC, 0xFC, 0x1B, 0x1B/*0x1F*/, /*0x5A*/0x58, /*0xB0*/0xB1,
		                    			0x00, 0x00, /*0xDE*/0x5E, 0x95, 0x00, 0x55, 0x55, 0x05, 0x00, 0x06,
		                    			0x01, 0xD6, 0x71
                         	 	 	 };

uint8  IIC_A_RegAdd[IIC_A_MaxIndex]={
		                   	   	   	   0x02, 0x12, 0x16, 0x17, 0x19, 0x21, 0x22, 0x23, 0x24, 0x25,
		                   	   	   	   0x28, 0x29, 0x2B, 0x2C, 0x2D
									};
uint8  IIC_B_RegAdd[IIC_B_MaxIndex]={
		                   	   	   	   0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B,
		                   	   	   	   0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15,
		                   	   	   	   0x16, 0x17, 0x19, 0x21, 0x22, 0x23, 0x24, 0x25, 0x28, 0x29,
		                   	   	   	   0x2B, 0x2C, 0x2D
                         	 	 	 };

uint8 IIC_A_diag[3]={0};
uint8 IIC_B_diag[3]={0};
/*******************************************************************************
|    Static local variables Declaration
|******************************************************************************/	


/*******************************************************************************
|    Function Source Code
|******************************************************************************/
void CDD_IIC_COM_Delay(uint32 Delaytime)
{

	uint32 u32t_Counter=0;
	uint32 u32t_CounterOver=0xffffffff-Delaytime;
	u32t_Counter = Stm0_GetSysTickTime();
	if(u32t_Counter>=(0xffffffff-Delaytime))
	{
		while(((0xffffffff-u32t_Counter+Stm0_GetSysTickTime())<=Delaytime)|(Stm0_GetSysTickTime()>u32t_Counter))
		{;}
	}

	else
	{
		while((Stm0_GetSysTickTime()-u32t_Counter)<=Delaytime)
		{;}
	}
}

/*----------------------------------------------------------------*/

/* delay 100us*/

/*----------------------------------------------------------------*/

static void CDD_IIC_Start(uint8 channel)

{
	switch(channel)
	{
		case IIC_A :
			Dio_WriteChannel(SDA_A,STD_HIGH);
			CDD_IIC_COM_Delay(100);
			Dio_WriteChannel(SCL_A,STD_HIGH);
			CDD_IIC_COM_Delay(100);
			Dio_WriteChannel(SDA_A,STD_LOW);
			CDD_IIC_COM_Delay(200);

			break;
		case IIC_B:
			Dio_WriteChannel(SDA_B,STD_HIGH);
			CDD_IIC_COM_Delay(100);
			Dio_WriteChannel(SCL_B,STD_HIGH);
			CDD_IIC_COM_Delay(100);
			Dio_WriteChannel(SDA_B,STD_LOW);
			CDD_IIC_COM_Delay(200);

			break;
		default:
			break;

	}

}

/*----------------------------------------------------------------



----------------------------------------------------------------*/

static void CDD_IIC_Stop(uint8 channel)

{
	switch(channel)
	{
		case IIC_A :
			Dio_WriteChannel(SDA_A,STD_LOW);
			CDD_IIC_COM_Delay(100);
			Dio_WriteChannel(SCL_A,STD_HIGH);
			CDD_IIC_COM_Delay(500);
			Dio_WriteChannel(SDA_A,STD_HIGH);
			CDD_IIC_COM_Delay(200);

			break;
		case IIC_B :
			Dio_WriteChannel(SDA_B,STD_LOW);
			CDD_IIC_COM_Delay(100);
			Dio_WriteChannel(SCL_B,STD_HIGH);
			CDD_IIC_COM_Delay(500);
			Dio_WriteChannel(SDA_B,STD_HIGH);
			CDD_IIC_COM_Delay(200);

			break;
		default:
			break;

	}

}


/*----------------------------------------------------------------

----------------------------------------------------------------*/

void sendAck(uint8 channel)

{
	switch(channel)
	{
	    case IIC_A :
	    	Dio_WriteChannel(SCL_A,STD_LOW);
	    	CDD_IIC_COM_Delay(100);
	    	Dio_WriteChannel(SDA_A,STD_LOW);
	    	CDD_IIC_COM_Delay(100);
	    	Dio_WriteChannel(SCL_A,STD_HIGH);
	    	CDD_IIC_COM_Delay(100);
	    	break;
	    case IIC_B :
	    	Dio_WriteChannel(SCL_B,STD_LOW);
	    	CDD_IIC_COM_Delay(100);
	    	Dio_WriteChannel(SDA_B,STD_LOW);
	    	CDD_IIC_COM_Delay(100);
	    	Dio_WriteChannel(SCL_B,STD_HIGH);
	    	CDD_IIC_COM_Delay(100);
	    	break;
	    default:
	    	break;

	}

}

/*----------------------------------------------------------------

----------------------------------------------------------------*/

static void sendNoAck(uint8 channel)

{
	switch(channel)
	{
	    case IIC_A :
	    	Dio_WriteChannel(SCL_A,STD_LOW);
	    	CDD_IIC_COM_Delay(200);
	    	Dio_WriteChannel(SDA_A,STD_HIGH);
	    	CDD_IIC_COM_Delay(600);
	    	Dio_WriteChannel(SCL_A,STD_HIGH);
	    	CDD_IIC_COM_Delay(600);
	    	break;
	    case IIC_B :
	    	Dio_WriteChannel(SCL_B,STD_LOW);
	    	CDD_IIC_COM_Delay(200);
	    	Dio_WriteChannel(SDA_B,STD_HIGH);
	    	CDD_IIC_COM_Delay(600);
	    	Dio_WriteChannel(SCL_B,STD_HIGH);
	    	CDD_IIC_COM_Delay(600);
	    	break;
	    default:
	    	break;
	}

}

/*----------------------------------------------------------------

 0 = noACK; 1 = ACK ;

----------------------------------------------------------------*/

static uint8 checkAck(uint8 channel)

{
	uint8 tempbit=0;
	switch(channel)
	{
	    case IIC_A :
	    	CDD_IIC_COM_Delay(200);
//	    	Port_SetPinDirection(ProtPinDirIn_ChipA,PortInput_Mode);
	    	CDD_IIC_COM_Delay(200);
	    	Dio_WriteChannel(SCL_A,STD_HIGH);
	    	CDD_IIC_COM_Delay(100);

	    	tempbit = Dio_ReadChannel(SDA_A);
	    	CDD_IIC_COM_Delay(500);
	    	Dio_WriteChannel(SCL_A,STD_LOW);
	    	CDD_IIC_COM_Delay(160);
//	    	Port_SetPinDirection(ProtPinDirIn_ChipA,PortOutput_Mode);

	    	break;
	    case IIC_B :

	    	CDD_IIC_COM_Delay(200);
//	    	Port_SetPinDirection(ProtPinDirIn_ChipB,PortInput_Mode);
	    	CDD_IIC_COM_Delay(200);

	    	Dio_WriteChannel(SCL_B,STD_HIGH);
	        CDD_IIC_COM_Delay(100);

	        tempbit = Dio_ReadChannel(SDA_B);
	        CDD_IIC_COM_Delay(500);
	    	Dio_WriteChannel(SCL_B,STD_LOW);
	    	CDD_IIC_COM_Delay(160);


//	        Port_SetPinDirection(ProtPinDirIn_ChipB,PortOutput_Mode);


	    	break;
	    default:

	    	break;
	}


	if(tempbit==1)
	{
		return E_OK; /*noACK*/
	}
	else
	{
		return E_NOT_OK; /*ACK*/
	}

}

/*----------------------------------------------------------------

OK

 a positive clock edge clock a bit into the ROM

----------------------------------------------------------------*/

static uint8 writeByte(uint8 channel,uint8 datum)

{
	uint8 ret=E_OK;
	uint8 bitCnt = 0 ;
	switch(channel)
	{
		case IIC_A :
			for(bitCnt=0; bitCnt<8; bitCnt++)
			{
				Dio_WriteChannel(SCL_A,STD_LOW);
				CDD_IIC_COM_Delay(100);
				if ((datum&0x80) == 0x80)
				{
					Dio_WriteChannel(SDA_A,STD_HIGH);
				}
				else
				{
					Dio_WriteChannel(SDA_A,STD_LOW);
				}
				CDD_IIC_COM_Delay(360);
				Dio_WriteChannel(SCL_A,STD_HIGH);
				CDD_IIC_COM_Delay(600);
				datum<<=1 ;
			}

			Dio_WriteChannel(SCL_A,STD_LOW);



			break;

		case IIC_B :
			for(bitCnt=0; bitCnt<8; bitCnt++)
			{
				Dio_WriteChannel(SCL_B,STD_LOW);
				CDD_IIC_COM_Delay(100);
				if ((datum&0x80) == 0x80)
				{
					Dio_WriteChannel(SDA_B,STD_HIGH);
				}
				else
				{
					Dio_WriteChannel(SDA_B,STD_LOW);
				}
				CDD_IIC_COM_Delay(360);
				Dio_WriteChannel(SCL_B,STD_HIGH);
				CDD_IIC_COM_Delay(600);
				datum<<=1 ;
			}
			Dio_WriteChannel(SCL_B,STD_LOW);


			break;
		default:
			ret=E_NOT_OK;
			break;
	}


	return ret;
}

/*----------------------------------------------------------------

OK

----------------------------------------------------------------*/

static uint8 readByte(uint8 channel)

{
	uint8 tempbit = 1 ;
	uint8 temp = 0 ;
	uint8 bitCnt ;

	switch(channel)
	{
		case IIC_A :

			Dio_WriteChannel(SDA_A,STD_HIGH);
			CDD_IIC_COM_Delay(450);


//			Port_SetPinDirection(ProtPinDirIn_ChipA,PortInput_Mode);
			CDD_IIC_COM_Delay(450);
			for(bitCnt=0; bitCnt<8; bitCnt++)
			{

				Dio_WriteChannel(SCL_A,STD_HIGH);
				CDD_IIC_COM_Delay(450);
				tempbit = Dio_ReadChannel(SDA_A) ;
				if (tempbit)
				{
					temp |= 0x01 ;
				}

				else
				{
					temp &= 0xfe ;
				}

				Dio_WriteChannel(SCL_A,STD_LOW);
				CDD_IIC_COM_Delay(450);

				if(bitCnt<7)
				{
					temp <<= 1 ;
				}
			}
//			Port_SetPinDirection(ProtPinDirIn_ChipA,PortOutput_Mode);
			break;
		case IIC_B :

			Dio_WriteChannel(SDA_B,STD_HIGH);
			CDD_IIC_COM_Delay(450);

//			Port_SetPinDirection(ProtPinDirIn_ChipB,PortInput_Mode);
			CDD_IIC_COM_Delay(450);
			for(bitCnt=0; bitCnt<8; bitCnt++)
			{
				Dio_WriteChannel(SCL_B,STD_HIGH);
				CDD_IIC_COM_Delay(450);
				tempbit = Dio_ReadChannel(SDA_B) ;
				if (tempbit)
				{
					temp |= 0x01 ;
				}

				else
				{
					temp &= 0xfe ;
				}

				Dio_WriteChannel(SCL_B,STD_LOW);
				CDD_IIC_COM_Delay(450);

				if(bitCnt<7)
				{
					temp <<= 1 ;
				}
			}

//			Port_SetPinDirection(ProtPinDirIn_ChipB,PortOutput_Mode);
			break;

		default:

			break;
	}

	return(temp) ;
}

/*~~~~~~~~~~~~~~~~~~~~~~~ API ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~*/

/*-----------------------------------------------------------------

wirte one byte to ROM --random write

-----------------------------------------------------------------*/

static uint8 writeOneByte(uint8 channel,uint8 addr, uint8 datum)

{
	uint8 ret=E_OK;
	uint8 tempbit ;

	switch(channel)
	{
		case IIC_A :

			CDD_IIC_Start(IIC_A);
			writeByte(IIC_A,0xC0);
			tempbit = checkAck(IIC_A);
			if(tempbit==0)
			{
				ret=E_NOT_OK;
				return ret;
			}
			writeByte(IIC_A,addr);
			tempbit = checkAck(IIC_A);
			if(tempbit==0)
			{
				ret=E_NOT_OK;
				return ret;
			}
			writeByte(IIC_A,datum);
			tempbit = checkAck(IIC_A);
			if(tempbit==0)
			{
				ret=E_NOT_OK;
				return ret;
			}
			CDD_IIC_Stop(IIC_A);
			CDD_IIC_COM_Delay(4000);

			break;
		case IIC_B :

			CDD_IIC_Start(IIC_B);
			writeByte(IIC_B,0xC8);
			tempbit = checkAck(IIC_B);
			if(tempbit==0)
			{
				ret=E_NOT_OK;
				return ret;
			}
			writeByte(IIC_B,addr);
			tempbit = checkAck(IIC_B);
			if(tempbit==0)
			{
				ret=E_NOT_OK;
				return ret;
			}
			writeByte(IIC_B,datum);
			tempbit = checkAck(IIC_B);
			if(tempbit==0)
			{
				ret=E_NOT_OK;
				return ret;
			}
			CDD_IIC_Stop(IIC_B);
			CDD_IIC_COM_Delay(4000);

			break;
		default:
			ret=E_NOT_OK;
			break;

	}

	return ret;
}

/*-----------------------------------------------------------------

read one byte from rom --random read

-----------------------------------------------------------------*/

static uint8 readOneByte(uint8 channel,uint8 addr,uint8*datum)

{
	uint8  ret=E_OK;
	uint8 tempbit = 1;

	switch(channel)
	{
		case IIC_A:

			CDD_IIC_Start(IIC_A);
			writeByte(IIC_A,0xC0);
			tempbit = checkAck(IIC_A);
			if(tempbit==0)
			{
				ret=E_NOT_OK;
				return ret;
			}
			writeByte(IIC_A,addr);
			tempbit = checkAck(IIC_A);
			if(tempbit==0)
			{
				ret=E_NOT_OK;
				return ret;

			}
			CDD_IIC_Start(IIC_A);
			writeByte(IIC_A,0xC1);
			tempbit = checkAck(IIC_A);
			if(tempbit==0)
			{
				ret=E_NOT_OK;
				return ret;
			}
			*datum = readByte(IIC_A);
			sendNoAck(IIC_A);
			CDD_IIC_Stop(IIC_A);

			break;

		case IIC_B:

			CDD_IIC_Start(IIC_B);
			writeByte(IIC_B,0xC8);
			tempbit = checkAck(IIC_B);
			if(tempbit==0)
			{
				ret=E_NOT_OK;
				return ret;
			}
			writeByte(IIC_B,addr);
			tempbit = checkAck(IIC_B);
			if(tempbit==0)
			{
				ret=E_NOT_OK;
				return ret;

			}
			CDD_IIC_Start(IIC_B);
			writeByte(IIC_B,0xC9);
			tempbit = checkAck(IIC_B);


			if(tempbit==0)
			{
				ret=E_NOT_OK;
				return ret;
			}

			*datum = readByte(IIC_B);
			sendNoAck(IIC_B);

			CDD_IIC_Stop(IIC_B);

			break;
		default:
			ret=E_NOT_OK;
			break;
	}

	return ret;
}
static uint8 SM_EyeQPwrRegSet(void)
{

	uint8 IIC_Index=0;
	uint8 ret=E_OK;

	/*   LP875701    */
	for(IIC_Index=0;IIC_Index < IIC_A_MaxIndex;IIC_Index++)
	{
		ret=writeOneByte(IIC_A,IIC_A_RegAdd[IIC_Index],IIC_A_RegVal[IIC_Index]);
		if(1==ret)
		{
			return ret;
		}
		else
		{;}
		CDD_IIC_COM_Delay(100);
	}

	/*    LP87563    */
	for(IIC_Index=0;IIC_Index < IIC_B_MaxIndex;IIC_Index++)
	{
		ret=writeOneByte(IIC_B,IIC_B_RegAdd[IIC_Index],IIC_B_RegVal[IIC_Index]);
		if(1==ret)
		{
			return ret;
		}
		else
		{;}
		CDD_IIC_COM_Delay(100);
	}
	return ret;
}
static uint8 SM_EyeQPwrRegRead(void)
{
	uint8 IIC_Index=0;
	uint8 ret=E_OK;

	/*   LP875701    */
	for(IIC_Index=0;IIC_Index<IIC_A_MaxIndex;IIC_Index++)
	{
		ret=readOneByte(IIC_A,IIC_A_RegAdd[IIC_Index],&IIC_A_Register[IIC_Index]);
		if(1==ret)
		{
			return ret;
		}
		else
		{;}
		if(IIC_A_Register[IIC_Index]==IIC_A_RegVal[IIC_Index])
		{;}
		else
		{
			ret=E_NOT_OK;
			return ret;
		}
		CDD_IIC_COM_Delay(100);
	}

	/*    LP87563    */
	for(IIC_Index=0;IIC_Index<IIC_B_MaxIndex;IIC_Index++)
	{
		ret=readOneByte(IIC_B,IIC_B_RegAdd[IIC_Index],&IIC_B_Register[IIC_Index]);
		if(1==ret)
		{
			return ret;
		}
		else
		{;}
		if(IIC_B_Register[IIC_Index]==IIC_B_RegVal[IIC_Index])
		{;}
		else
		{
			ret=E_NOT_OK;
			return ret;
		}
		CDD_IIC_COM_Delay(100);
	}
	return ret;
}
uint8 SM_PwrInit(void)
{
	uint8 u8t_ret=E_OK;
	return u8t_ret;
}

uint8 SM_EyeQPwrMainFunc(void)
{
	uint8 ret=E_OK;

	ret= SM_EyeQPwrRegSet();
	if(1==ret)
	{
		return ret;
	}
	else{;}

	ret=SM_EyeQPwrRegRead();
	if(1==ret)
	{
		return ret;
	}
	else{;}
	return ret;
}
uint8 SM_EyeQPwrDiagMainFunc(void)
{
	uint8 ret=E_OK;
	ret=readOneByte(IIC_A,IIC_A_RegAdd[0],&IIC_A_diag[0]);
	if(1==ret)
	{
		return ret;
	}else
	{;}
	ret=readOneByte(IIC_A,IIC_A_RegAdd[1],&IIC_A_diag[1]);
	if(1==ret)
	{
		return ret;
	}else
	{;}
	ret=readOneByte(IIC_A,IIC_A_RegAdd[2],&IIC_A_diag[2]);
	if(1==ret)
	{
		return ret;
	}else
	{;}
	ret=readOneByte(IIC_B,IIC_B_RegAdd[0],&IIC_B_diag[0]);
	if(1==ret)
	{
		return ret;
	}else
	{;}
	ret=readOneByte(IIC_B,IIC_B_RegAdd[0],&IIC_B_diag[1]);
	if(1==ret)
	{
		return ret;
	}else
	{;}
	ret=readOneByte(IIC_B,IIC_B_RegAdd[0],&IIC_B_diag[2]);
	if(1==ret)
	{
		return ret;
	}else
	{;}

	return ret;
}
uint8 SM_EyeQPwrUpMainFunc(void)
{
	uint8 ret=E_OK;

	Dio_WriteChannel(EyeQ_POR,STD_LOW);                      /*POR*/
	Dio_WriteChannel(Res_ChipA,STD_HIGH);                    
	Dio_WriteChannel(Res_ChipB,STD_HIGH);                    
	CDD_IIC_COM_Delay(120000);                               

	ret=SM_EyeQPwrMainFunc();
	if(1==ret)
	{
		return ret;
	}
	else{;}

	CDD_IIC_COM_Delay(50000);
	Dio_WriteChannel(En_ChipB,STD_HIGH);	           
	Dio_WriteChannel(En_ChipA,STD_HIGH);                
	Dio_WriteChannel(EnCamPower,STD_HIGH);                  
	CDD_IIC_COM_Delay(700000);                            
	Dio_WriteChannel(EyeQ_POR,STD_LOW);                       /*POR*/
	Dio_WriteChannel(EyeQ_PRB,STD_HIGH);                      /*PRB*/
	Dio_WriteChannel(EnMicron_Spi,STD_HIGH);        
	return ret;
}
//uint8 SM_EyeQPwrOnEnMainFunc(void)
//{
//	uint8 ret=E_OK;
//	return ret;///////////////////////////////////////////////
//	if( ValSigValid == EyeQ_GetValSigStatus() )
//	{
//
//		Port_SetPinDirection(MicronClock_Id,PortInput_Mode);
//		Port_SetPinDirection(MicronCs_Id,PortInput_Mode);
//		Port_SetPinDirection(MicronMosi_Id,PortInput_Mode);
//		Dio_WriteChannel(EnMicron_Spi,STD_LOW);
//
//		CDD_IIC_COM_Delay(1000000);                       //delay 10ms
//		Dio_WriteChannel(EyeQ_POR,STD_HIGH);              /*POR*/
//		Dio_WriteChannel(EyeQ_PRB,STD_LOW);               /*PRB*/
//
//		EyeQPower_Flag=1;
//	}
//	else
//	{
//		ret=E_NOT_OK;
//	}
//
//	return ret;
//}
//
//void SM_EthEnable(void)
//{
//	if(1==EyeQPower_Flag)
//	{
//		Port_SetPinDirection(EthPinDirIn,PortOutput_Mode);
//		if(SM_EthH_Flag>0)
//		{
//			SM_EthH_Flag++;
//			if(SM_EthH_Flag>28)
//			{
//				Dio_WriteChannel(Eth_Res,STD_LOW);
//				if(SM_EthH_Flag>38)
//				{
//					Dio_WriteChannel(Eth_Res,STD_HIGH);
//					SM_EthH_Flag=0;
//				}
//
//			}
//
//
//
//		}
//	}
//}

uint8 SM_EyeQPwrDownMainFunc(void)
{
	uint8 ret=E_OK;
	Dio_WriteChannel(En_ChipB,STD_LOW);	           /*Disable power B*/
	Dio_WriteChannel(En_ChipA,STD_LOW);            /*Disable power A*/
	ret = Dio_ReadChannel(En_ChipB);
	if(1==ret)
	{
		return ret;
	}
	else{;}
	ret = Dio_ReadChannel(En_ChipB);
	if(1==ret)
	{
		return ret;
	}
	else{;}
	return ret;
}

/*******************************************************************************
|    Function Source Code End
|******************************************************************************/
