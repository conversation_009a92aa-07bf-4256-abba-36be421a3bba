/**
 * \file IfxLmu_bf.h
 * \brief
 * \copyright Copyright (c) 2016 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC23XADAS_UM_V1.1.R1
 * Specification: tc23xadas_um_sfrs_MCSFR.xml (Revision: UM_V1.1)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Lmu_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Lmu
 * 
 */
#ifndef IFXLMU_BF_H
#define IFXLMU_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Lmu_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN0 */
#define IFX_LMU_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN0 */
#define IFX_LMU_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN0 */
#define IFX_LMU_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN10 */
#define IFX_LMU_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN10 */
#define IFX_LMU_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN10 */
#define IFX_LMU_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN11 */
#define IFX_LMU_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN11 */
#define IFX_LMU_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN11 */
#define IFX_LMU_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN12 */
#define IFX_LMU_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN12 */
#define IFX_LMU_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN12 */
#define IFX_LMU_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN13 */
#define IFX_LMU_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN13 */
#define IFX_LMU_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN13 */
#define IFX_LMU_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN14 */
#define IFX_LMU_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN14 */
#define IFX_LMU_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN14 */
#define IFX_LMU_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN15 */
#define IFX_LMU_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN15 */
#define IFX_LMU_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN15 */
#define IFX_LMU_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN16 */
#define IFX_LMU_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN16 */
#define IFX_LMU_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN16 */
#define IFX_LMU_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN17 */
#define IFX_LMU_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN17 */
#define IFX_LMU_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN17 */
#define IFX_LMU_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN18 */
#define IFX_LMU_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN18 */
#define IFX_LMU_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN18 */
#define IFX_LMU_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN19 */
#define IFX_LMU_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN19 */
#define IFX_LMU_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN19 */
#define IFX_LMU_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN1 */
#define IFX_LMU_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN1 */
#define IFX_LMU_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN1 */
#define IFX_LMU_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN20 */
#define IFX_LMU_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN20 */
#define IFX_LMU_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN20 */
#define IFX_LMU_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN21 */
#define IFX_LMU_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN21 */
#define IFX_LMU_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN21 */
#define IFX_LMU_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN22 */
#define IFX_LMU_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN22 */
#define IFX_LMU_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN22 */
#define IFX_LMU_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN23 */
#define IFX_LMU_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN23 */
#define IFX_LMU_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN23 */
#define IFX_LMU_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN24 */
#define IFX_LMU_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN24 */
#define IFX_LMU_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN24 */
#define IFX_LMU_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN25 */
#define IFX_LMU_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN25 */
#define IFX_LMU_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN25 */
#define IFX_LMU_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN26 */
#define IFX_LMU_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN26 */
#define IFX_LMU_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN26 */
#define IFX_LMU_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN27 */
#define IFX_LMU_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN27 */
#define IFX_LMU_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN27 */
#define IFX_LMU_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN28 */
#define IFX_LMU_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN28 */
#define IFX_LMU_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN28 */
#define IFX_LMU_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN29 */
#define IFX_LMU_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN29 */
#define IFX_LMU_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN29 */
#define IFX_LMU_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN2 */
#define IFX_LMU_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN2 */
#define IFX_LMU_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN2 */
#define IFX_LMU_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN30 */
#define IFX_LMU_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN30 */
#define IFX_LMU_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN30 */
#define IFX_LMU_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN31 */
#define IFX_LMU_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN31 */
#define IFX_LMU_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN31 */
#define IFX_LMU_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN3 */
#define IFX_LMU_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN3 */
#define IFX_LMU_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN3 */
#define IFX_LMU_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN4 */
#define IFX_LMU_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN4 */
#define IFX_LMU_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN4 */
#define IFX_LMU_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN5 */
#define IFX_LMU_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN5 */
#define IFX_LMU_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN5 */
#define IFX_LMU_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN6 */
#define IFX_LMU_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN6 */
#define IFX_LMU_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN6 */
#define IFX_LMU_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN7 */
#define IFX_LMU_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN7 */
#define IFX_LMU_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN7 */
#define IFX_LMU_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN8 */
#define IFX_LMU_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN8 */
#define IFX_LMU_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN8 */
#define IFX_LMU_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_LMU_ACCEN0_Bits.EN9 */
#define IFX_LMU_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_LMU_ACCEN0_Bits.EN9 */
#define IFX_LMU_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_ACCEN0_Bits.EN9 */
#define IFX_LMU_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_LMU_BUFCON_Bits.EN1 */
#define IFX_LMU_BUFCON_EN1_LEN (1u)

/** \brief  Mask for Ifx_LMU_BUFCON_Bits.EN1 */
#define IFX_LMU_BUFCON_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_BUFCON_Bits.EN1 */
#define IFX_LMU_BUFCON_EN1_OFF (30u)

/** \brief  Length for Ifx_LMU_BUFCON_Bits.EN2 */
#define IFX_LMU_BUFCON_EN2_LEN (1u)

/** \brief  Mask for Ifx_LMU_BUFCON_Bits.EN2 */
#define IFX_LMU_BUFCON_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_BUFCON_Bits.EN2 */
#define IFX_LMU_BUFCON_EN2_OFF (31u)

/** \brief  Length for Ifx_LMU_BUFCON_Bits.EPEN */
#define IFX_LMU_BUFCON_EPEN_LEN (1u)

/** \brief  Mask for Ifx_LMU_BUFCON_Bits.EPEN */
#define IFX_LMU_BUFCON_EPEN_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_BUFCON_Bits.EPEN */
#define IFX_LMU_BUFCON_EPEN_OFF (23u)

/** \brief  Length for Ifx_LMU_BUFCON_Bits.EREN */
#define IFX_LMU_BUFCON_EREN_LEN (1u)

/** \brief  Mask for Ifx_LMU_BUFCON_Bits.EREN */
#define IFX_LMU_BUFCON_EREN_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_BUFCON_Bits.EREN */
#define IFX_LMU_BUFCON_EREN_OFF (22u)

/** \brief  Length for Ifx_LMU_BUFCON_Bits.TAG1 */
#define IFX_LMU_BUFCON_TAG1_LEN (6u)

/** \brief  Mask for Ifx_LMU_BUFCON_Bits.TAG1 */
#define IFX_LMU_BUFCON_TAG1_MSK (0x3fu)

/** \brief  Offset for Ifx_LMU_BUFCON_Bits.TAG1 */
#define IFX_LMU_BUFCON_TAG1_OFF (0u)

/** \brief  Length for Ifx_LMU_BUFCON_Bits.TAG2 */
#define IFX_LMU_BUFCON_TAG2_LEN (6u)

/** \brief  Mask for Ifx_LMU_BUFCON_Bits.TAG2 */
#define IFX_LMU_BUFCON_TAG2_MSK (0x3fu)

/** \brief  Offset for Ifx_LMU_BUFCON_Bits.TAG2 */
#define IFX_LMU_BUFCON_TAG2_OFF (8u)

/** \brief  Length for Ifx_LMU_CLC_Bits.DISR */
#define IFX_LMU_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_LMU_CLC_Bits.DISR */
#define IFX_LMU_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_CLC_Bits.DISR */
#define IFX_LMU_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_LMU_CLC_Bits.DISS */
#define IFX_LMU_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_LMU_CLC_Bits.DISS */
#define IFX_LMU_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_CLC_Bits.DISS */
#define IFX_LMU_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_LMU_MEMCON_Bits.ADDERR */
#define IFX_LMU_MEMCON_ADDERR_LEN (1u)

/** \brief  Mask for Ifx_LMU_MEMCON_Bits.ADDERR */
#define IFX_LMU_MEMCON_ADDERR_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_MEMCON_Bits.ADDERR */
#define IFX_LMU_MEMCON_ADDERR_OFF (7u)

/** \brief  Length for Ifx_LMU_MEMCON_Bits.DATAERR */
#define IFX_LMU_MEMCON_DATAERR_LEN (1u)

/** \brief  Mask for Ifx_LMU_MEMCON_Bits.DATAERR */
#define IFX_LMU_MEMCON_DATAERR_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_MEMCON_Bits.DATAERR */
#define IFX_LMU_MEMCON_DATAERR_OFF (6u)

/** \brief  Length for Ifx_LMU_MEMCON_Bits.ERERR */
#define IFX_LMU_MEMCON_ERERR_LEN (1u)

/** \brief  Mask for Ifx_LMU_MEMCON_Bits.ERERR */
#define IFX_LMU_MEMCON_ERERR_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_MEMCON_Bits.ERERR */
#define IFX_LMU_MEMCON_ERERR_OFF (3u)

/** \brief  Length for Ifx_LMU_MEMCON_Bits.ERRDIS */
#define IFX_LMU_MEMCON_ERRDIS_LEN (1u)

/** \brief  Mask for Ifx_LMU_MEMCON_Bits.ERRDIS */
#define IFX_LMU_MEMCON_ERRDIS_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_MEMCON_Bits.ERRDIS */
#define IFX_LMU_MEMCON_ERRDIS_OFF (9u)

/** \brief  Length for Ifx_LMU_MEMCON_Bits.EWERR */
#define IFX_LMU_MEMCON_EWERR_LEN (1u)

/** \brief  Mask for Ifx_LMU_MEMCON_Bits.EWERR */
#define IFX_LMU_MEMCON_EWERR_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_MEMCON_Bits.EWERR */
#define IFX_LMU_MEMCON_EWERR_OFF (5u)

/** \brief  Length for Ifx_LMU_MEMCON_Bits.FFTPFT */
#define IFX_LMU_MEMCON_FFTPFT_LEN (1u)

/** \brief  Mask for Ifx_LMU_MEMCON_Bits.FFTPFT */
#define IFX_LMU_MEMCON_FFTPFT_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_MEMCON_Bits.FFTPFT */
#define IFX_LMU_MEMCON_FFTPFT_OFF (10u)

/** \brief  Length for Ifx_LMU_MEMCON_Bits.INTERR */
#define IFX_LMU_MEMCON_INTERR_LEN (1u)

/** \brief  Mask for Ifx_LMU_MEMCON_Bits.INTERR */
#define IFX_LMU_MEMCON_INTERR_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_MEMCON_Bits.INTERR */
#define IFX_LMU_MEMCON_INTERR_OFF (2u)

/** \brief  Length for Ifx_LMU_MEMCON_Bits.OLDAEN */
#define IFX_LMU_MEMCON_OLDAEN_LEN (1u)

/** \brief  Mask for Ifx_LMU_MEMCON_Bits.OLDAEN */
#define IFX_LMU_MEMCON_OLDAEN_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_MEMCON_Bits.OLDAEN */
#define IFX_LMU_MEMCON_OLDAEN_OFF (0u)

/** \brief  Length for Ifx_LMU_MEMCON_Bits.PMIC */
#define IFX_LMU_MEMCON_PMIC_LEN (1u)

/** \brief  Mask for Ifx_LMU_MEMCON_Bits.PMIC */
#define IFX_LMU_MEMCON_PMIC_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_MEMCON_Bits.PMIC */
#define IFX_LMU_MEMCON_PMIC_OFF (8u)

/** \brief  Length for Ifx_LMU_MEMCON_Bits.POLDAEN */
#define IFX_LMU_MEMCON_POLDAEN_LEN (1u)

/** \brief  Mask for Ifx_LMU_MEMCON_Bits.POLDAEN */
#define IFX_LMU_MEMCON_POLDAEN_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_MEMCON_Bits.POLDAEN */
#define IFX_LMU_MEMCON_POLDAEN_OFF (1u)

/** \brief  Length for Ifx_LMU_MEMCON_Bits.RMWERR */
#define IFX_LMU_MEMCON_RMWERR_LEN (1u)

/** \brief  Mask for Ifx_LMU_MEMCON_Bits.RMWERR */
#define IFX_LMU_MEMCON_RMWERR_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_MEMCON_Bits.RMWERR */
#define IFX_LMU_MEMCON_RMWERR_OFF (4u)

/** \brief  Length for Ifx_LMU_MEMCON_Bits.WSTATES */
#define IFX_LMU_MEMCON_WSTATES_LEN (4u)

/** \brief  Mask for Ifx_LMU_MEMCON_Bits.WSTATES */
#define IFX_LMU_MEMCON_WSTATES_MSK (0xfu)

/** \brief  Offset for Ifx_LMU_MEMCON_Bits.WSTATES */
#define IFX_LMU_MEMCON_WSTATES_OFF (12u)

/** \brief  Length for Ifx_LMU_MODID_Bits.ID_VALUE */
#define IFX_LMU_MODID_ID_VALUE_LEN (32u)

/** \brief  Mask for Ifx_LMU_MODID_Bits.ID_VALUE */
#define IFX_LMU_MODID_ID_VALUE_MSK (0xffffffffu)

/** \brief  Offset for Ifx_LMU_MODID_Bits.ID_VALUE */
#define IFX_LMU_MODID_ID_VALUE_OFF (0u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN0 */
#define IFX_LMU_RGN_ACCENA_EN0_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN0 */
#define IFX_LMU_RGN_ACCENA_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN0 */
#define IFX_LMU_RGN_ACCENA_EN0_OFF (0u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN10 */
#define IFX_LMU_RGN_ACCENA_EN10_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN10 */
#define IFX_LMU_RGN_ACCENA_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN10 */
#define IFX_LMU_RGN_ACCENA_EN10_OFF (10u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN11 */
#define IFX_LMU_RGN_ACCENA_EN11_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN11 */
#define IFX_LMU_RGN_ACCENA_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN11 */
#define IFX_LMU_RGN_ACCENA_EN11_OFF (11u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN12 */
#define IFX_LMU_RGN_ACCENA_EN12_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN12 */
#define IFX_LMU_RGN_ACCENA_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN12 */
#define IFX_LMU_RGN_ACCENA_EN12_OFF (12u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN13 */
#define IFX_LMU_RGN_ACCENA_EN13_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN13 */
#define IFX_LMU_RGN_ACCENA_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN13 */
#define IFX_LMU_RGN_ACCENA_EN13_OFF (13u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN14 */
#define IFX_LMU_RGN_ACCENA_EN14_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN14 */
#define IFX_LMU_RGN_ACCENA_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN14 */
#define IFX_LMU_RGN_ACCENA_EN14_OFF (14u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN15 */
#define IFX_LMU_RGN_ACCENA_EN15_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN15 */
#define IFX_LMU_RGN_ACCENA_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN15 */
#define IFX_LMU_RGN_ACCENA_EN15_OFF (15u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN16 */
#define IFX_LMU_RGN_ACCENA_EN16_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN16 */
#define IFX_LMU_RGN_ACCENA_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN16 */
#define IFX_LMU_RGN_ACCENA_EN16_OFF (16u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN17 */
#define IFX_LMU_RGN_ACCENA_EN17_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN17 */
#define IFX_LMU_RGN_ACCENA_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN17 */
#define IFX_LMU_RGN_ACCENA_EN17_OFF (17u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN18 */
#define IFX_LMU_RGN_ACCENA_EN18_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN18 */
#define IFX_LMU_RGN_ACCENA_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN18 */
#define IFX_LMU_RGN_ACCENA_EN18_OFF (18u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN19 */
#define IFX_LMU_RGN_ACCENA_EN19_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN19 */
#define IFX_LMU_RGN_ACCENA_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN19 */
#define IFX_LMU_RGN_ACCENA_EN19_OFF (19u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN1 */
#define IFX_LMU_RGN_ACCENA_EN1_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN1 */
#define IFX_LMU_RGN_ACCENA_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN1 */
#define IFX_LMU_RGN_ACCENA_EN1_OFF (1u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN20 */
#define IFX_LMU_RGN_ACCENA_EN20_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN20 */
#define IFX_LMU_RGN_ACCENA_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN20 */
#define IFX_LMU_RGN_ACCENA_EN20_OFF (20u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN21 */
#define IFX_LMU_RGN_ACCENA_EN21_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN21 */
#define IFX_LMU_RGN_ACCENA_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN21 */
#define IFX_LMU_RGN_ACCENA_EN21_OFF (21u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN22 */
#define IFX_LMU_RGN_ACCENA_EN22_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN22 */
#define IFX_LMU_RGN_ACCENA_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN22 */
#define IFX_LMU_RGN_ACCENA_EN22_OFF (22u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN23 */
#define IFX_LMU_RGN_ACCENA_EN23_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN23 */
#define IFX_LMU_RGN_ACCENA_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN23 */
#define IFX_LMU_RGN_ACCENA_EN23_OFF (23u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN24 */
#define IFX_LMU_RGN_ACCENA_EN24_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN24 */
#define IFX_LMU_RGN_ACCENA_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN24 */
#define IFX_LMU_RGN_ACCENA_EN24_OFF (24u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN25 */
#define IFX_LMU_RGN_ACCENA_EN25_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN25 */
#define IFX_LMU_RGN_ACCENA_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN25 */
#define IFX_LMU_RGN_ACCENA_EN25_OFF (25u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN26 */
#define IFX_LMU_RGN_ACCENA_EN26_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN26 */
#define IFX_LMU_RGN_ACCENA_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN26 */
#define IFX_LMU_RGN_ACCENA_EN26_OFF (26u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN27 */
#define IFX_LMU_RGN_ACCENA_EN27_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN27 */
#define IFX_LMU_RGN_ACCENA_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN27 */
#define IFX_LMU_RGN_ACCENA_EN27_OFF (27u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN28 */
#define IFX_LMU_RGN_ACCENA_EN28_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN28 */
#define IFX_LMU_RGN_ACCENA_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN28 */
#define IFX_LMU_RGN_ACCENA_EN28_OFF (28u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN29 */
#define IFX_LMU_RGN_ACCENA_EN29_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN29 */
#define IFX_LMU_RGN_ACCENA_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN29 */
#define IFX_LMU_RGN_ACCENA_EN29_OFF (29u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN2 */
#define IFX_LMU_RGN_ACCENA_EN2_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN2 */
#define IFX_LMU_RGN_ACCENA_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN2 */
#define IFX_LMU_RGN_ACCENA_EN2_OFF (2u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN30 */
#define IFX_LMU_RGN_ACCENA_EN30_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN30 */
#define IFX_LMU_RGN_ACCENA_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN30 */
#define IFX_LMU_RGN_ACCENA_EN30_OFF (30u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN31 */
#define IFX_LMU_RGN_ACCENA_EN31_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN31 */
#define IFX_LMU_RGN_ACCENA_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN31 */
#define IFX_LMU_RGN_ACCENA_EN31_OFF (31u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN3 */
#define IFX_LMU_RGN_ACCENA_EN3_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN3 */
#define IFX_LMU_RGN_ACCENA_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN3 */
#define IFX_LMU_RGN_ACCENA_EN3_OFF (3u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN4 */
#define IFX_LMU_RGN_ACCENA_EN4_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN4 */
#define IFX_LMU_RGN_ACCENA_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN4 */
#define IFX_LMU_RGN_ACCENA_EN4_OFF (4u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN5 */
#define IFX_LMU_RGN_ACCENA_EN5_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN5 */
#define IFX_LMU_RGN_ACCENA_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN5 */
#define IFX_LMU_RGN_ACCENA_EN5_OFF (5u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN6 */
#define IFX_LMU_RGN_ACCENA_EN6_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN6 */
#define IFX_LMU_RGN_ACCENA_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN6 */
#define IFX_LMU_RGN_ACCENA_EN6_OFF (6u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN7 */
#define IFX_LMU_RGN_ACCENA_EN7_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN7 */
#define IFX_LMU_RGN_ACCENA_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN7 */
#define IFX_LMU_RGN_ACCENA_EN7_OFF (7u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN8 */
#define IFX_LMU_RGN_ACCENA_EN8_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN8 */
#define IFX_LMU_RGN_ACCENA_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN8 */
#define IFX_LMU_RGN_ACCENA_EN8_OFF (8u)

/** \brief  Length for Ifx_LMU_RGN_ACCENA_Bits.EN9 */
#define IFX_LMU_RGN_ACCENA_EN9_LEN (1u)

/** \brief  Mask for Ifx_LMU_RGN_ACCENA_Bits.EN9 */
#define IFX_LMU_RGN_ACCENA_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_LMU_RGN_ACCENA_Bits.EN9 */
#define IFX_LMU_RGN_ACCENA_EN9_OFF (9u)

/** \brief  Length for Ifx_LMU_RGN_LA_Bits.ADDR */
#define IFX_LMU_RGN_LA_ADDR_LEN (23u)

/** \brief  Mask for Ifx_LMU_RGN_LA_Bits.ADDR */
#define IFX_LMU_RGN_LA_ADDR_MSK (0x7fffffu)

/** \brief  Offset for Ifx_LMU_RGN_LA_Bits.ADDR */
#define IFX_LMU_RGN_LA_ADDR_OFF (5u)

/** \brief  Length for Ifx_LMU_RGN_UA_Bits.ADDR */
#define IFX_LMU_RGN_UA_ADDR_LEN (24u)

/** \brief  Mask for Ifx_LMU_RGN_UA_Bits.ADDR */
#define IFX_LMU_RGN_UA_ADDR_MSK (0xffffffu)

/** \brief  Offset for Ifx_LMU_RGN_UA_Bits.ADDR */
#define IFX_LMU_RGN_UA_ADDR_OFF (5u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXLMU_BF_H */
