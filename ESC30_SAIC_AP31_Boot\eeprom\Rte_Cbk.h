/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_Cbk.h
 *           Config:  PGM_BswCfg.dpa
 *      ECU-Project:  PGM_BswCfg
 *
 *        Generator:  MICROSAR RTE Generator Version 4.21.0
 *                    RTE Core Version 1.21.0
 *          License:  CBD1900770
 *
 *      Description:  Callback header file
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_CBK_H
# define RTE_CBK_H

# include "Com.h"


# include "Rte_Type.h"

# define RTE_START_SEC_CODE
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * COM Callbacks for Rx Indication
 *********************************************************************************************************************/

FUNC(void, RTE_CODE) Rte_COMCbk_igEBS_100ms_FrP03_57e6f0a4_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_igEBS_100ms_FrP04_c9826507_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_igHADS_020ms_PDU00_a6f0555b_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isADASAvlbly_09adfd17_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isAPAAFnInd_19513047_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isAPAAvlbly_7751e710_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isAPASts_d5be586c_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isBatCrntSts_80bb68ff_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isBatCrnt_fd4c77d1_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isBatSOCSts_a0f93a2c_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isBatSOC_12f56cf3_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isBatSOFVol1Sts_c4bf1651_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isBatSOFVol1_93b34723_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isBatSOFVol2Sts_d60ab9bf_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isBatSOFVol2_0aba1699_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isBatTemSts_7660e53c_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isBatTem_a407bb91_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isBatVolSts_c9a43bf3_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isBatVol_2a6bb7e3_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isCalendarDay_d222b6be_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isCalendarMonth_e501a07d_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isCalendarYear_6763fc67_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isECMAvlbly_1f29eaa0_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isEn12VoltStrMotCmddOn_5f511cff_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isEnASSSta_e579d682_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isEnRunA_56a875e5_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isFOTAStsV_81f9d72e_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isFOTASts_48ab93a4_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isGenrSta_b25d3860_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isHourOfDay_133f9a54_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isLADSAFnInd_9024eadb_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isMinuteOfHour_b4f24d5a_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isPGMSwCtrl_572cd2da_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isPMDCSta_383db51a_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isSCSAvlbly_f71236c8_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isSecsOfMinute_f5d212eb_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isSysBPMEnbd_c4174813_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isSysBPM_203eb3e9_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isSysPwrMd_9bf7fd93_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isVehOdoV_ce5ae746_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isVehOdo_3469a907_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isVehSpdAvgDrvnV_f443c14e_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isVehSpdAvgDrvn_981e6f66_Rx(void);
FUNC(void, RTE_CODE) Rte_COMCbk_isVehSpdAvg_5c3ba820_Rx(void);

/**********************************************************************************************************************
 * NvM-Callback for synchronous copying of the mirror buffer to and from the NvM
 *********************************************************************************************************************/

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_BatDiscon_BatOpenDuration(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_BatDiscon_BatOpenDuration(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_Bat_Normal_SOC_Threshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_Bat_Normal_SOC_Threshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_Bat_UnderVoltage_Threshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_Bat_UnderVoltage_Threshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ConfigurationTraceabilityField_F198(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ConfigurationTraceabilityField_F198(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve18_C(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve18_C(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve19_C(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve19_C(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve20(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve20(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve21(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve21(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve22(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve22(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve23(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve23(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve24(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve24(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve25(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve25(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve26(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve26(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_DIDReserve27(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_DIDReserve27(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUHardwareNumber_F191(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUHardwareNumber_F191(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUIndexInformation_F1A5(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUIndexInformation_F1A5(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUManufactureDate_F18B(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUManufactureDate_F18B(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUPartNumber_F187(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUPartNumber_F187(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ECUSerialNumber_F18C(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ECUSerialNumber_F18C(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30OverVoltageHigherThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30OverVoltageHigherThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30OverVoltageLowerThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30OverVoltageLowerThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_LastPGMOpenInformation_B042(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_LastPGMOpenInformation_B042(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_NIF_CurrentData_F121(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_NIF_CurrentData_F121(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_NIF_FactoryData_F120(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_NIF_FactoryData_F120(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_OCV_Map(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_OCV_Map(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PGMrecoverytimes(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PGMrecoverytimes(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F110(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F110(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F111(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F111(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F112(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F112(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F113(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F113(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F114(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F114(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F115(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F115(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F116(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F116(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F117(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F117(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F118(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F118(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F119(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F119(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F11A(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F11A(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F11B(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F11B(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F11C(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F11C(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F11D(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F11D(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F11E(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F11E(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_PIF_F11F(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_PIF_F11F(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ReseveforAPP(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ReseveforAPP(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ReseveforFBL(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ReseveforFBL(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ST_ResetBlockInfo_T(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ST_ResetBlockInfo_T(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_ST_ResetMngInfo_T(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_ST_ResetMngInfo_T(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_SecurityAttemptCounter(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_SecurityAttemptCounter(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_SecurityLog(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_SecurityLog(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_Softwareintegritystatus_AFFD(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_Softwareintegritystatus_AFFD(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_SwitchOpenReasonRecord_B044(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_SwitchOpenReasonRecord_B044(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_SystemSupplierIdentifier_F18A(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_SystemSupplierIdentifier_F18A(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_Usg_Md(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_Usg_Md(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_VIN_F190(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_VIN_F190(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);

FUNC(Std_ReturnType, RTE_CODE) Rte_SetMirror_CtApNvM_NvSWC_VehicleFeatureInformation_F1A8(P2CONST(void, AUTOMATIC, RTE_APPL_DATA) NVMBuffer); /* PRQA S 3112 */ /* MD_Rte_3112 */
FUNC(Std_ReturnType, RTE_CODE) Rte_GetMirror_CtApNvM_NvSWC_VehicleFeatureInformation_F1A8(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) NVMBuffer);


/**********************************************************************************************************************
 * NvM-Callbacks for forwarding notifications from the NvM to the SW-Cs
 *********************************************************************************************************************/

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_AltDiscon_BatUnderVolDuration(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_BatDiscon_BatOpenDuration(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Bat_Normal_SOC_Threshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Bat_UnderVoltage_Threshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ConfigurationTraceabilityField_F198(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve18_C(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve19_C(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve20(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve21(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve22(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve23(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve24(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve25(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve26(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_DIDReserve27(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUHardwareNumber_F191(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUIndexInformation_F1A5(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUManufactureDate_F18B(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUPartNumber_F187(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ECUSerialNumber_F18C(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30OverVoltageHigherThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30OverVoltageLowerThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30UnderVoltageHigherThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30UnderVoltageLowerThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_LastPGMOpenInformation_B042(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_OCV_Map(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGM_Recovery_Normal_Voltage_Filter_Time(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PGMrecoverytimes(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F110(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F111(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F112(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F113(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F114(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F115(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F116(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F117(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F118(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F119(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11A(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11B(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11C(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11D(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11E(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_PIF_F11F(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_ReseveforFBL(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SecurityAttemptCounter(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SecurityLog(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Softwareintegritystatus_AFFD(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SwitchOpenReasonRecord_B044(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_SystemSupplierIdentifier_F18A(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_Usg_Md(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_VIN_F190(uint8 ServiceId, NvM_RequestResultType JobResult);

FUNC(Std_ReturnType, RTE_CODE) Rte_NvMNotifyJobFinished_CtApNvM_NvSWC_VehicleFeatureInformation_F1A8(uint8 ServiceId, NvM_RequestResultType JobResult);


# define RTE_STOP_SEC_CODE
# include "MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#endif /* RTE_CBK_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_3112:  MISRA rule: Rule2.2
     Reason:     Used to simplify code generation.
     Risk:       No functional risk. There is no side effect.
     Prevention: Not required.

*/
