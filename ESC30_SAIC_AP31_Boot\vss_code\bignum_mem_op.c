
#include "calc.h"
#include "vsscommon.h"
	

#pragma section code "vss_api_code" 

#define CALC_NULL 0

#if (defined (_ENABLE_MIZAR_SM2_) && (_ENABLE_MIZAR_SM2_ == 1U))	|| \
	(defined(_ENABLE_MIZAR_ECC_) && (_ENABLE_MIZAR_ECC_ == 1U))


void calc_bigtolit(vss_uint8* dst, vss_uint8 *src, vss_uint32 blen)
{
#if (defined (_BIG_ENDIAN_)&&(_BIG_ENDIAN_ == 1U))
	vss_sint32 i = 0;
	vss_uint32 tmp32;
	vss_uint32* src32 = (vss_uint32*)src;
	vss_uint32* dst32 = (vss_uint32*)dst;
	vss_uint32 blen32 = blen/4;

	if(dst == CALC_NULL || src == CALC_NULL)
		return;
		
	if(dst == src)
	{	
		for(i = 0; i < (blen32 >> 1); i++)
		{
			tmp32 = src32[i];
			dst32[i] = src32[blen32 - i - 1];
			dst32[blen32 - i - 1] = tmp32;
		}	
	}
	else
		for(i = 0; i < blen32; i++)
			dst32[blen32-1-i] = src32[i];
#else
	vss_sint32 i = 0;
	vss_uint8 tmp;

	if(dst == CALC_NULL || src == CALC_NULL)
		return;
		
	if(dst == src)
	{	
		for(i = 0; i < (blen >> 1); i++)
		{
			tmp = src[i];
			dst[i] = src[blen - i - 1];
			dst[blen - i - 1] = tmp;
		}	
	}
	else
		for(i = 0; i < blen; i++)
			dst[blen-1-i] = src[i];
#endif			
}

void calc_mem_set(vss_uint32 *data1,vss_uint32 data2,vss_uint32 wlen)
{
	vss_sint32 i;
	
	for (i=wlen-1;i>=0;i--)
		data1[i] = data2;
}

vss_uint32 calc_mem_cmp(vss_uint32 *data1,vss_uint32 *data2,vss_uint32 wlen)
{
	vss_sint32 i;
	
	for (i=wlen-1;i>=0;i--)
	{
		if (data1[i]>data2[i])
			return CALC_BIGGER;
		else if (data1[i]<data2[i])
			return CALC_SMALLER;
	}

	return CALC_EQUAL;
}

vss_uint32 calc_mem_cmp2(vss_uint32 *data1,vss_uint32 data2,vss_uint32 wlen)
{
  vss_sint32 i;

  for (i=wlen-1;i>=1;i--)
	{
    if (data1[i]!=0)
      return CALC_BIGGER;
	}
	
	if (data1[0]==data2)
		return CALC_EQUAL;
	else if (data1[0]>data2)
		return CALC_BIGGER;
	else
		return CALC_SMALLER;
}

vss_uint32 calc_mem_cmp0(vss_uint32 *data1, vss_uint32 wlen)
{
	return calc_mem_cmp2(data1, 0, wlen);
}

void calc_mem_cpy(vss_uint32* dst, vss_uint32 *src, vss_uint32 wlen)
{
	vss_sint32 i;

	for (i=0;i<wlen;i++)
		dst[i] = src[i];
		
}

void calc_mem_invcpy(vss_uint32 *dst, vss_uint32 *src, vss_uint32 wlen)
{
	
	vss_uint32 i = 0, tmp = 0;
	
	if(dst == CALC_NULL || src == CALC_NULL)
		return;
	
	if(dst == src){
		for(i = 0; i < (wlen >> 1); i++){
			tmp = src[i];
			dst[i] = src[wlen - i - 1];
			dst[wlen - i - 1] = tmp;
		}
	}
	else{
		for(i = 0; i < wlen; i++)
			dst[wlen - i - 1] = src[i];
	}
		
}

#endif

#pragma section code restore



