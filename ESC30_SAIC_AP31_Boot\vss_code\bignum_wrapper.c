#include "bignum_wrapper.h"
#include "bignum.h"
#include "vsscommon.h"

extern mbedtls_mpi vss_mc_op1;
extern mbedtls_mpi vss_mc_op2;
extern mbedtls_mpi vss_mc_opp;
extern mbedtls_mpi_uint vss_mc_len;
extern mbedtls_mpi vss_mc_res;
extern mbedtls_mpi_uint vss_mc_len_tmp1;
extern mbedtls_mpi vss_mc_tmp1;
extern mbedtls_mpi_uint vss_mc_len_tmp2;
extern mbedtls_mpi vss_mc_tmp2;
extern mbedtls_mpi_uint sm2_p_data[8];
extern mbedtls_mpi sm2_p;
extern mbedtls_mpi_uint nist256_p_data[8];
extern mbedtls_mpi nist256_p;
	

#pragma section code "vss_api_code" 


#if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U))	||\
	(defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))
vss_uint32 calc_add(vss_uint32* result, vss_uint32* a, vss_uint32 a_wlen, vss_uint32* b, vss_uint32 b_wlen)
{
	vss_mc_op1.n = a_wlen;
	vss_mc_op1.p = a;
	vss_mc_op2.n = b_wlen;
	vss_mc_op2.p = b;

	if(a_wlen > b_wlen)
	{
		vss_mc_len = a_wlen;
	}
	else
	{
		vss_mc_len = b_wlen;
	}
	
	vss_mc_res.n = a_wlen;
	
	mbedtls_mpi_add_mpi(&vss_mc_res, &vss_mc_op1, &vss_mc_op2);
	
	if(vss_mc_res.n != a_wlen)
	{
		mem_cpy8(result, vss_mc_res.p, a_wlen*4);
		return RET_CALC_ADD_CARRY;
	}
	else
		mem_cpy8(result, vss_mc_res.p, vss_mc_res.n*4);
	
	
	return RET_CALC_IMPLEMENT_SUCCESS;
}

vss_uint32 calc_sub(vss_uint32* result, vss_uint32* a, vss_uint32 a_wlen, vss_uint32* b, vss_uint32 b_wlen)
{
	vss_mc_op1.n = a_wlen;
	vss_mc_op1.p = a;
	vss_mc_op2.n = b_wlen;
	vss_mc_op2.p = b;

	if(a_wlen > b_wlen)
	{
		vss_mc_len = a_wlen;
	}
	else
	{
		vss_mc_len = b_wlen;
	}
	vss_mc_res.n = a_wlen;
	
	mbedtls_mpi_sub_mpi(&vss_mc_res, &vss_mc_op1, &vss_mc_op2);
	
	
	mem_cpy8(result, vss_mc_res.p, vss_mc_res.n*4);
	
	if(vss_mc_res.s != 1)
		return RET_CALC_SUB_BORROW;
	
	
	return RET_CALC_IMPLEMENT_SUCCESS;
}

vss_uint32 calc_modadd(vss_uint32* result, vss_uint32* a, vss_uint32* b, vss_uint32* p, vss_uint32 wlen)
{
	vss_mc_op1.n = wlen;
	vss_mc_op1.p = a;
	vss_mc_op2.n = wlen;
	vss_mc_op2.p = b;
	vss_mc_opp.n = wlen;
	vss_mc_opp.p = p;
	vss_mc_res.n = wlen;
	vss_mc_tmp1.n = wlen;
	
	mbedtls_mpi_add_mpi(&vss_mc_tmp1, &vss_mc_op1, &vss_mc_op2);
	mbedtls_mpi_mod_mpi(&vss_mc_res, &vss_mc_tmp1, &vss_mc_opp);
	
	mem_cpy8(result, vss_mc_res.p, vss_mc_res.n*4);
	
	return RET_CALC_IMPLEMENT_SUCCESS;
}

vss_uint32 calc_modsub(vss_uint32* result, vss_uint32* a, vss_uint32* b, vss_uint32* p, vss_uint32 wlen)
{
	vss_mc_op1.n = wlen;
	vss_mc_op1.p = a;
	vss_mc_op2.n = wlen;
	vss_mc_op2.p = b;
	vss_mc_opp.n = wlen;
	vss_mc_opp.p = p;
	vss_mc_res.n = wlen;
	vss_mc_tmp1.n = wlen;

	mbedtls_mpi_sub_mpi(&vss_mc_tmp1, &vss_mc_op1, &vss_mc_op2);
	mbedtls_mpi_mod_mpi(&vss_mc_res, &vss_mc_tmp1, &vss_mc_opp);
	
	mem_cpy8(result, vss_mc_res.p, vss_mc_res.n*4);
	
	return RET_CALC_IMPLEMENT_SUCCESS;
}

vss_uint32 calc_div(vss_uint32* result_q, vss_uint32* result_r, vss_uint32* a, vss_uint32 a_wlen, vss_uint32* b, vss_uint32 b_wlen)
{
	vss_mc_op1.n = a_wlen;
	vss_mc_op1.p = a;
	vss_mc_op2.n = b_wlen;
	vss_mc_op2.p = b;

	if(a_wlen > b_wlen )
	{
		vss_mc_res.n = a_wlen;
	}
	else
	{
		vss_mc_res.n = b_wlen;
	}

	if(a_wlen > b_wlen )
	{
		vss_mc_tmp1.n = a_wlen;
	}
	else
	{
		vss_mc_tmp1.n = b_wlen;
	}	
	
	
	mbedtls_mpi_div_mpi(&vss_mc_res, &vss_mc_tmp1, &vss_mc_op1, &vss_mc_op2);
	
	
	mem_cpy8(result_q, vss_mc_res.p, vss_mc_res.n*4);
	mem_cpy8(result_r, vss_mc_tmp1.p, vss_mc_tmp1.n*4);
	
	return RET_CALC_IMPLEMENT_SUCCESS;
}

vss_uint32 calc_mul(vss_uint32* result, vss_uint32* a, vss_uint32 a_wlen, vss_uint32* b, vss_uint32 b_wlen)
{
	vss_mc_op1.n = a_wlen;
	vss_mc_op1.p = a;
	vss_mc_op2.n = b_wlen;
	vss_mc_op2.p = b;
	vss_mc_res.n = a_wlen + b_wlen;
	
	mbedtls_mpi_mul_mpi(&vss_mc_res, &vss_mc_op1, &vss_mc_op2 );
	
	mem_cpy8(result, vss_mc_res.p, vss_mc_res.n*4);
	
	return RET_CALC_IMPLEMENT_SUCCESS;
}

vss_uint32 calc_cmp(vss_uint32* a, vss_uint32* b, vss_uint32 wlen)
{
	vss_slong tmp;
	vss_mc_op1.n = wlen;
	vss_mc_op1.p = a;
	vss_mc_op2.n = wlen;
	vss_mc_op2.p = b;
	
	tmp = mbedtls_mpi_cmp_mpi(&vss_mc_op1, &vss_mc_op2 );
	
	if(tmp == -1) return RET_CALC_BIGGER;
	else if (tmp == 1) return RET_CALC_SMALLER;
	else if (tmp == 0) return RET_CALC_EQUAL;
	else return RET_CALC_IMPLEMENT_ERROR;
}

vss_uint32 calc_lsr(vss_uint32* result, vss_uint32* a, vss_uint32 wlen, vss_uint32 bitlen)
{
	vss_mc_res.n = wlen;
	mem_cpy8(vss_mc_res.p, a, wlen*4);
	
	mbedtls_mpi_shift_r( &vss_mc_res, bitlen );
	
	mem_cpy8(result, vss_mc_res.p, vss_mc_res.n*4);
	
	return RET_CALC_IMPLEMENT_SUCCESS;
}

vss_uint32 calc_bitlen(vss_uint32* a, vss_uint32 wlen)
{
	vss_mc_op1.n = wlen;
	vss_mc_op1.p = a;
	
	return mbedtls_mpi_bitlen( &vss_mc_op1);
}

vss_uint32 calc_mod(vss_uint32* result, vss_uint32* a, vss_uint32 a_wlen, vss_uint32* b, vss_uint32 b_wlen)
{
	vss_mc_op1.n = a_wlen;
	vss_mc_op1.p = a;
	vss_mc_opp.n = b_wlen;
	vss_mc_opp.p = b;
	vss_mc_res.n = b_wlen;
	
	mbedtls_mpi_mod_mpi(&vss_mc_res, &vss_mc_op1, &vss_mc_opp );
	
	mem_cpy8(result, vss_mc_res.p, vss_mc_res.n*4);
	
	return RET_CALC_IMPLEMENT_SUCCESS;
}

vss_uint32 calc_modinv(vss_uint32 *result, vss_uint32 *a, vss_uint32 a_wlen, vss_uint32 *b, vss_uint32 b_wlen)
{
	vss_mc_op1.n = a_wlen;
	vss_mc_op1.p = a;
	vss_mc_opp.n = b_wlen;
	vss_mc_opp.p = b;
	vss_mc_res.n = b_wlen;
	
	mbedtls_mpi_inv_mod(&vss_mc_res, &vss_mc_op1, &vss_mc_opp );
	
	mem_cpy8(result, vss_mc_res.p, vss_mc_res.n*4);
	
	return RET_CALC_IMPLEMENT_SUCCESS;
}

vss_uint32 calc_modmul(vss_uint32* result, vss_uint32* a, vss_uint32* b, vss_uint32* p, vss_uint32 wlen)
{
	vss_mc_op1.n = wlen;
	vss_mc_op1.p = a;
	vss_mc_op2.n = wlen;
	vss_mc_op2.p = b;
	vss_mc_opp.n = wlen;
	vss_mc_opp.p = p;
	vss_mc_res.n = wlen;
	vss_mc_tmp1.n = wlen*2;
	
	mbedtls_mpi_mul_mpi(&vss_mc_tmp1, &vss_mc_op1, &vss_mc_op2);
	mbedtls_mpi_mod_mpi(&vss_mc_res, &vss_mc_tmp1, &vss_mc_opp);
	
	mem_cpy8(result, vss_mc_res.p, vss_mc_res.n*4);
	
	return RET_CALC_IMPLEMENT_SUCCESS;
}

vss_uint32 calc_modmul4sm2(vss_uint32* result, vss_uint32* a, vss_uint32* b, vss_uint32* p, vss_uint32 wlen)
{
	vss_mc_op1.n = wlen;
	vss_mc_op1.p = a;
	vss_mc_op2.n = wlen;
	vss_mc_op2.p = b;
	vss_mc_opp.n = wlen;
	vss_mc_opp.p = p;
	vss_mc_res.n = wlen;
	vss_mc_tmp1.n = wlen*2;
	
	mbedtls_mpi_mul_mpi(&vss_mc_tmp1, &vss_mc_op1, &vss_mc_op2);
	ecp_mod_sm2(&vss_mc_tmp1);
	
	/* N->s < 0 is a much faster test, which fails only if N is 0 */
    while( vss_mc_tmp1.s < 0 && mbedtls_mpi_cmp_int( &vss_mc_tmp1, 0 ) != 0 )
        mbedtls_mpi_add_mpi( &vss_mc_tmp1, &vss_mc_tmp1, &sm2_p );

    while( mbedtls_mpi_cmp_mpi( &vss_mc_tmp1, &sm2_p ) >= 0 )
        /* we known P, N and the result are positive */
        mbedtls_mpi_sub_abs( &vss_mc_tmp1, &vss_mc_tmp1, &sm2_p );
	
	mem_cpy8(result, vss_mc_tmp1.p, vss_mc_res.n*4);
	
	return RET_CALC_IMPLEMENT_SUCCESS;
}

vss_uint32 calc_modmul4nist256(vss_uint32* result, vss_uint32* a, vss_uint32* b, vss_uint32* p, vss_uint32 wlen)
{
	vss_mc_op1.n = wlen;
	vss_mc_op1.p = a;
	vss_mc_op2.n = wlen;
	vss_mc_op2.p = b;
	vss_mc_opp.n = wlen;
	vss_mc_opp.p = p;
	vss_mc_res.n = wlen;
	vss_mc_tmp1.n = wlen*2;
	
	mbedtls_mpi_mul_mpi(&vss_mc_tmp1, &vss_mc_op1, &vss_mc_op2);
	ecp_mod_p256(&vss_mc_tmp1);
	
	/* N->s < 0 is a much faster test, which fails only if N is 0 */
    while( vss_mc_tmp1.s < 0 && mbedtls_mpi_cmp_int( &vss_mc_tmp1, 0 ) != 0 )
        mbedtls_mpi_add_mpi( &vss_mc_tmp1, &vss_mc_tmp1, &nist256_p );

    while( mbedtls_mpi_cmp_mpi( &vss_mc_tmp1, &nist256_p ) >= 0 )
        /* we known P, N and the result are positive */
        mbedtls_mpi_sub_abs( &vss_mc_tmp1, &vss_mc_tmp1, &nist256_p );
	
	mem_cpy8(result, vss_mc_tmp1.p, vss_mc_res.n*4);
	
	return RET_CALC_IMPLEMENT_SUCCESS;
}

/*
//released calc function
void calc_version(vss_uint8* result);
vss_uint32 calc_modmul(vss_uint32* result, vss_uint32* a, vss_uint32* b, vss_uint32* p, vss_uint32 wlen);
vss_uint32 calc_coprime(vss_uint32 *a, vss_uint32 a_wlen, vss_uint32* b, vss_uint32 b_wlen);
*/
#endif
#pragma section code restore


