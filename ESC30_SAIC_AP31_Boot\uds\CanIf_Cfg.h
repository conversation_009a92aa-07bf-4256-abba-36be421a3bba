/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 * @file 	CanIf_Cfg.h
 * @brief
 *
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *  
 * <AUTHOR>
 * @date 	2013-5-22
 * 
 */
/*============================================================================*/

#ifndef CANIF_CFG_H_
#define CANIF_CFG_H_

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20130522    WBN       Initial version
 */
/*============================================================================*/

#ifdef __cplusplus
extern "C" {
#endif  /* __cplusplus */

/*=======[M A C R O S]========================================================*/
#define CANIF_TX_CHANNEL_NUM    1u
#define CANIF_RX_CHANNEL_NUM    2u

//#define CAN_INTERRUPT          0
//#define CAN_POLLING            1
#define CANTP_DIAGFRAME_LENTH 8u
#define CAN_BUSOFF_PROCESSING  CAN_INTERRUPT
#define CAN_WAKEUP_PROCESSING  CAN_INTERRUPT
#define CAN_RX_PROCESSING      CAN_POLLING
#define CAN_TX_PROCESSING      CAN_POLLING


#ifdef __cplusplus
}
#endif  /* __cplusplus */

#endif /* CANIF_CFG_H_ */

/*=======[E N D   O F   F I L E]==============================================*/
