/*******************************************************************************
 * Copyright (C) 2023 Technomous. All rights reserved         *
 ******************************************************************************/
/**
 *  \file
 *      Vss.c
 *  \brief
 *      Vss basic function.
 *	\author
 *		Ou Hengyue
 */

#include "Vss.h"
#include "VSS_RW.h"
#include "VssApiIndirect.h"
#include "Appl.h"

static Vss_Bypass_Flag = 1;

Std_ReturnType Vss_Init(void)
{
    uint8 vsnChkResult[32];
    Std_ReturnType ret;
    uint32 vss_flag;

   // Appl_Memcpy((uint8*)0x7003B000, (uint8*)0xA0118000, 4096);
    VssCryptoInit(0, 0, flash_cb, NULL);
    ret = VssKeyCodeFeedback(&vsnChkResult);
    
    // VssGenerateKeyByCode(32,vsn_code,idList,0);
    if(0u == ret)
    {
        if((0x00u == vsnChkResult[30]))
        {
            ret = VssGetAlgFlag(&vss_flag);
            if((0u == ret) && (1u == vss_flag))
            {
                Vss_Bypass_Flag = 0;
            }
        }
    }

    return ret;
}

uint8 Vss_GetBypassFlag(void)
{
    return Vss_Bypass_Flag;
}




