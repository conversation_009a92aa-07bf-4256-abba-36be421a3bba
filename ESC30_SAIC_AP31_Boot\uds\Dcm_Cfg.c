/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *
 *  All rights reserved. This software is iSOFT property. Duplication
 *  or disclosure without iSOFT written authorization is prohibited.
 *
 *  @file       <Dcm_Cfg.c>
 *  @brief      <UDS Service - ISO14229>
 *
 *  <This Diagnostic Communication Manager file contained UDS services
 *   which used for bootloader project>
 *
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *
 *  <AUTHOR> <PERSON>>
 *  @date       <2012-11-09>
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20121109    Gary       Initial version
 *
 *  V1.1    20130517    liuyp      modified version
 *
 *  V1.2    20130913    ccl        update
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/
#include "Dcm_Types.h"
#include "Dcm_Dsp.h"
#include "Dcm_Internel.h"
#include <stdio.h>
#include "eeprom.h"
#include "Seedkey.h"

#ifndef SECM_CRC_LENGTH
#define SECM_CRC_LENGTH     0x00u
#endif
/*=======[E X T E R N A L   D A T A]==========================================*/
const Dcm_ServiceTableType Dcm_ServiceTable[DCM_SERVICE_NUM] =
    {
        /* SID, funcAddrSupport,  sessionSupp,      ServiceFct,  pendingFct */
        /* @type:uint8 range:0x00~0xFF note:select unique service Id */
        /* @type:boolean range:TRUE,FALSE note:NONE */
        /* @type:uint8 range:session value note:select defined session value */
        /* @type:API range:NONE note:auto generate */
        /* @type:API range:NONE note:auto generate */
        { 0x10u, DCM_PHYSICAL_ADDRESSING|DCM_FUNCTIONAL_ADDRESSING,
         DCM_SESSION_DEFAULT|DCM_SESSION_PROGRAMMING|DCM_SESSION_EXTEND ,
            &Dcm_RecvMsg10, NULL_PTR
         },
#if(DCM_SERVICE_11_ENABLE == STD_ON)         
        { 0x11u, DCM_PHYSICAL_ADDRESSING|DCM_FUNCTIONAL_ADDRESSING,
         DCM_SESSION_DEFAULT|DCM_SESSION_PROGRAMMING|DCM_SESSION_EXTEND ,
            &Dcm_RecvMsg11, NULL_PTR
        },
#endif
#if(DCM_SERVICE_22_ENABLE == STD_ON)
        { 0x22u, DCM_PHYSICAL_ADDRESSING|DCM_FUNCTIONAL_ADDRESSING,
         DCM_SESSION_DEFAULT|DCM_SESSION_PROGRAMMING|DCM_SESSION_EXTEND ,
            &Dcm_RecvMsg22, NULL_PTR
        },
#endif        
        { 0x27u, DCM_PHYSICAL_ADDRESSING,
         DCM_SESSION_PROGRAMMING ,
            &Dcm_RecvMsg27, NULL_PTR
        },
        { 0x28u, DCM_PHYSICAL_ADDRESSING|DCM_FUNCTIONAL_ADDRESSING,
         DCM_SESSION_EXTEND ,
            &Dcm_RecvMsg28, NULL_PTR
        },
        { 0x2Eu, DCM_PHYSICAL_ADDRESSING,
         DCM_SESSION_PROGRAMMING ,
            &Dcm_RecvMsg2E, &Dcm_Pending2E
        },
        { 0x31u, DCM_PHYSICAL_ADDRESSING,
         DCM_SESSION_PROGRAMMING ,
            &Dcm_RecvMsg31, &Dcm_Pending31
        },
        { 0x34u, DCM_PHYSICAL_ADDRESSING,
         DCM_SESSION_PROGRAMMING ,
            &Dcm_RecvMsg34, &Dcm_Pending34
        },
        { 0x36u, DCM_PHYSICAL_ADDRESSING,
         DCM_SESSION_PROGRAMMING ,
            &Dcm_RecvMsg36, &Dcm_Pending36
        },
        { 0x37u, DCM_PHYSICAL_ADDRESSING,
         DCM_SESSION_PROGRAMMING ,
            &Dcm_RecvMsg37, &Dcm_Pending37
        },
        { 0x3Eu, DCM_PHYSICAL_ADDRESSING|DCM_FUNCTIONAL_ADDRESSING,
         DCM_SESSION_DEFAULT|DCM_SESSION_PROGRAMMING|DCM_SESSION_EXTEND ,
            &Dcm_RecvMsg3E, NULL_PTR
        },
        { 0x85u, DCM_PHYSICAL_ADDRESSING|DCM_FUNCTIONAL_ADDRESSING,
         DCM_SESSION_EXTEND ,
            &Dcm_RecvMsg85, NULL_PTR
        },
    };

const Dcm_SessionRowType Dcm_SessionRow[DCM_SESSION_NUM] =
    {
        /* sessionType, sessionSupp,     securitySupp */
        /* @type:uint8 range:0x00~0xFF note:select unique session value */
        /* @type:uint8 range:session value note:select defined session value */
        /* @type:uint8 range:security value note:select defined security value */
        {
            DCM_SESSION_DEFAULT,
            DCM_SESSION_DEFAULT|DCM_SESSION_PROGRAMMING|DCM_SESSION_EXTEND,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1
        },
        {
            DCM_SESSION_PROGRAMMING,
            DCM_SESSION_PROGRAMMING|DCM_SESSION_EXTEND|DCM_SESSION_DEFAULT,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1
        },
        {
            DCM_SESSION_EXTEND,
            DCM_SESSION_DEFAULT|DCM_SESSION_EXTEND,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1
        },

    };
#if(DCM_SERVICE_11_ENABLE == STD_ON)
const Dcm_ResetRowType Dcm_ResetRow[DCM_RESET_NUM] =
    {
        /* resetType, sessionSupp,     securitySupp */
        /* @type:uint8 range:0x00~0xFF note:NONE */
        /* @type:uint8 range:session value note:select defined session value */
        /* @type:uint8 range:security value note:select defined security value */
        {
            DCM_HARD_RESET,
            DCM_SESSION_DEFAULT|DCM_SESSION_PROGRAMMING|DCM_SESSION_EXTEND,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1
        },
    };
#endif    
#if((DCM_READDID_NUM > 0) && (DCM_SERVICE_22_ENABLE == STD_ON))
const Dcm_ReadDidRowType Dcm_ReadDidRow[DCM_READDID_NUM] =
    {
        /* DID, securitySupp,readDataFct */
        /* @type:uint16 range:0x0000~0xFFFF note:NONE */
        /* @type:uint8 range:security value note:select defined security value */
        /* @type:API range:NONE note:input APIname */
		{
			0xF100u,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
			&EEP_ReadDID
    	},
        {
            0xF110u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
            0xF111u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
         {
            0xF112u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
         {
            0xF113u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
            0xF114u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
         {
            0xF115u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
            0xF116u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
         {
            0xF117u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
           &EEP_ReadDID
        },
         {
            0xF118u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
         {
            0xF119u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
         {
            0xF11Au,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
         {
            0xF11Bu,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
         {
            0xF11Cu,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
         {
            0xF11Du,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
         {
            0xF11Eu,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
         {
            0xF11Fu,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
			0xF120u,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
			&EEP_ReadDID
		},
		{
			0xF121u,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
			&EEP_ReadDID
		},
         {
            0xF183u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
         {
            0xF187u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
            0xF18Au,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
			0xF18Bu,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
			&EEP_ReadDID
        },
        {
			0xF18Cu,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
			&EEP_ReadDID
        },
        {
            0xF190u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
             &EEP_ReadDID
        },
        {
            0xF191u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
            0xF192u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
            0xF194u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
			0xF198u,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
			&EEP_ReadDID
        },
        {
            0xF1A0u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
            0xF1A1u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
            0xF1A2u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
            0xF1A5u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
            0xF1A8u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &EEP_ReadDID
        },
        {
            0xF1A9u,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
           &EEP_ReadDID
        },
        {
			0xF1AAu,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
		   &EEP_ReadDID
		},
		{
			0xAFFCu,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
			&EEP_ReadDID
		},
		{
			0xAFFDu,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
			&EEP_ReadDID
		},
		{
			0xAFFEu,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
			&EEP_ReadDID
		},
		{
			0xAFFFu,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
			&EEP_ReadDID
		},
		{
			0xF186u,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
			&EEP_ReadCurrent_session
		},


    };
#endif

const Dcm_SecurityRowType Dcm_SecurityRow[DCM_SECURITY_NUM] =
    {
        /* seedId,keyId,secAccessLevel,   sessionSupp,     securitySupp */
        /* @type:uint8 range:0x00~0xFF note:NONE */
        /* @type:uint8 range:0x00~0xFF note:NONE */
        /* @type:define range:DCM_SECURITY_LEV1~DCM_SECURITY_LEV2 note:NONE */
        /* @type:uint8 range:session value note:select defined session value */
        /* @type:uint8 range:security value note:select defined security value */
        /* @type:API range:NONE note:input APIname */
        /* @type:API range:NONE note:input APIname */
        {
            0x05u, 0x06u, DCM_SECURITY_LEV1,
            DCM_SESSION_PROGRAMMING,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &SecM_GenerateSeed, &SecM_CompareKey
        },
        {
			0x01u, 0x02u, DCM_SECURITY_LEV1,
			DCM_SESSION_EXTEND,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
			&SecM_GenerateSeed, &SecM_CompareKey
        },
    };

#if (DCM_COM_CONTROL_NUM > 0)
const Dcm_ComControlRowType Dcm_ComControlRow[DCM_COM_CONTROL_NUM] =
    {
        /* controlType,sessionSupp,     securitySupp */
        /* @type:uint8 range:0x00~0xFF note:NONE */
        /* @type:uint8 range:session value note:select defined session value */
        /* @type:uint8 range:security value note:select defined security value */
        {
            DCM_ENABLE_RXANDTX,
            DCM_SESSION_EXTEND,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1
        },
        {
        	DCM_ENABLE_RXANDDISTX,
			DCM_SESSION_EXTEND,
			DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1
        }
        ,
        {
            DCM_DISABLE_RXANDTX,
            DCM_SESSION_EXTEND,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1
        } ,
    };
#endif

const Dcm_WriteDidRowType Dcm_WriteDidRow[DCM_WRITEDID_NUM] =
    {
        /* DID,  dataLength,securitySupp,writeDataFct */
        /* @type:uint16 range:0x0000~0xFFFF note:NONE */
        /* @type:uint16 range:0x0001~0xFFFF note:NONE */
        /* @type:uint8 range:security value note:select defined security value */
        /* @type:API range:NONE note:input APIname */
        {
            0xF110u, 0x10u,
            DCM_SECURITY_LEV1,
            &EEP_WriteDID
        },
        {
            0xF187u, 0x05u,
            DCM_SECURITY_LEV1,
            &EEP_WriteDID
        },
        {
			0xF121u, 0x10u,
			DCM_SECURITY_LEV1,
			&EEP_WriteDID
        },
        {
			0xF1AAu, 0x5u,
			DCM_SECURITY_LEV1,
			&EEP_WriteDID
        },
    };

const Dcm_RoutineControlRowType Dcm_RoutineControlRow[DCM_ROUTINE_CONTROL_NUM] =
    {
        /* routineId,optionLength,funcAddrSupp,sessionSupp,    securitySupp,routineControl */
        /* @type:uint16 range:0x0000~0xFFFF note:select unique routine Id */
        /* @type:uint16 range:0x0000~0xFFFF note:auto generate */
        /* @type:boolean range:TRUE,FALSE note:auto generate */
        /* @type:uint8 range:session value note:select defined session value */
        /* @type:uint8 range:security value note:select defined security value */
        /* @type:API range:NONE note:auto generate */
        {
            0xDFFDu, 0x00u, TRUE,
            DCM_SESSION_EXTEND,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1,
            &Dcm_CheckProgPreCondition
        },
        {
            0xFF00u, 0x09u, FALSE,
            DCM_SESSION_PROGRAMMING,
            DCM_SECURITY_LEV1,
            &Dcm_EraseMemory
        },
        {
            0xDFFFu, SECM_CRC_LENGTH, FALSE,
            DCM_SESSION_PROGRAMMING,
            DCM_SECURITY_LEV1,
            &Dcm_CheckMemory
        },
        {
            0xFF01u, 0x00u, FALSE,
            DCM_SESSION_PROGRAMMING,
            DCM_SECURITY_LEV1,
            &Dcm_CheckProgDependencies
        },
        {
            0xDFFEu, 0x09u, FALSE,
            DCM_SESSION_PROGRAMMING,
            DCM_SECURITY_LEV1,
            &Dcm_CheckSWVerification
        },
    };

const Dcm_DownloadRowType Dcm_DownloadRow =
    {
        /* securitySupp */
        /* @type:uint8 range:security value note:select defined security value */
        {
            DCM_SECURITY_LEV1
        },
    };
    
const Dcm_testPresentRowType Dcm_testPresentRow[DCM_TESTPRESENT_NUM] =
    {
        /* testPresentSupp */
        /* @type:uint8 range:zeroSubFunc value note:select defined zeroSubFunc value */
        {
            DCM_ZERO_SUB_FUNC
        },
    };

#if (DCM_DTC_SET_NUM > 0)
const Dcm_DTCSettingRowType Dcm_DTCSettingRow[DCM_DTC_SET_NUM] =
    {
        /* DTCSet,sessionSupp,     securitySupp */
        /* @type:uint8 range:0x00~0xFF note:NONE */
        /* @type:uint8 range:session value note:select defined session value */
        /* @type:uint8 range:security value note:select defined security value */
        {
            DCM_DTC_SETTING_ON,
            DCM_SESSION_EXTEND,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1
        },
        {
            DCM_DTC_SETTING_OFF,
            DCM_SESSION_EXTEND,
            DCM_SECURITY_LOCKED|DCM_SECURITY_LEV1
        },
    };
#endif

/*=======[E N D   O F   F I L E]==============================================*/
