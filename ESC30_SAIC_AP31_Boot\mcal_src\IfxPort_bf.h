/**
 * \file IfxPort_bf.h
 * \brief
 * \copyright Copyright (c) 2016 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC23XADAS_UM_V1.1.R1
 * Specification: tc23xadas_um_sfrs_MCSFR.xml (Revision: UM_V1.1)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Port_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Port
 * 
 */
#ifndef IFXPORT_BF_H
#define IFXPORT_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Port_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN0 */
#define IFX_P_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN0 */
#define IFX_P_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN0 */
#define IFX_P_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN10 */
#define IFX_P_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN10 */
#define IFX_P_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN10 */
#define IFX_P_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN11 */
#define IFX_P_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN11 */
#define IFX_P_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN11 */
#define IFX_P_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN12 */
#define IFX_P_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN12 */
#define IFX_P_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN12 */
#define IFX_P_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN13 */
#define IFX_P_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN13 */
#define IFX_P_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN13 */
#define IFX_P_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN14 */
#define IFX_P_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN14 */
#define IFX_P_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN14 */
#define IFX_P_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN15 */
#define IFX_P_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN15 */
#define IFX_P_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN15 */
#define IFX_P_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN16 */
#define IFX_P_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN16 */
#define IFX_P_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN16 */
#define IFX_P_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN17 */
#define IFX_P_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN17 */
#define IFX_P_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN17 */
#define IFX_P_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN18 */
#define IFX_P_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN18 */
#define IFX_P_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN18 */
#define IFX_P_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN19 */
#define IFX_P_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN19 */
#define IFX_P_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN19 */
#define IFX_P_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN1 */
#define IFX_P_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN1 */
#define IFX_P_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN1 */
#define IFX_P_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN20 */
#define IFX_P_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN20 */
#define IFX_P_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN20 */
#define IFX_P_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN21 */
#define IFX_P_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN21 */
#define IFX_P_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN21 */
#define IFX_P_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN22 */
#define IFX_P_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN22 */
#define IFX_P_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN22 */
#define IFX_P_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN23 */
#define IFX_P_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN23 */
#define IFX_P_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN23 */
#define IFX_P_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN24 */
#define IFX_P_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN24 */
#define IFX_P_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN24 */
#define IFX_P_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN25 */
#define IFX_P_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN25 */
#define IFX_P_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN25 */
#define IFX_P_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN26 */
#define IFX_P_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN26 */
#define IFX_P_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN26 */
#define IFX_P_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN27 */
#define IFX_P_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN27 */
#define IFX_P_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN27 */
#define IFX_P_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN28 */
#define IFX_P_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN28 */
#define IFX_P_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN28 */
#define IFX_P_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN29 */
#define IFX_P_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN29 */
#define IFX_P_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN29 */
#define IFX_P_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN2 */
#define IFX_P_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN2 */
#define IFX_P_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN2 */
#define IFX_P_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN30 */
#define IFX_P_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN30 */
#define IFX_P_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN30 */
#define IFX_P_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN31 */
#define IFX_P_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN31 */
#define IFX_P_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN31 */
#define IFX_P_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN3 */
#define IFX_P_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN3 */
#define IFX_P_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN3 */
#define IFX_P_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN4 */
#define IFX_P_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN4 */
#define IFX_P_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN4 */
#define IFX_P_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN5 */
#define IFX_P_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN5 */
#define IFX_P_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN5 */
#define IFX_P_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN6 */
#define IFX_P_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN6 */
#define IFX_P_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN6 */
#define IFX_P_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN7 */
#define IFX_P_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN7 */
#define IFX_P_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN7 */
#define IFX_P_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN8 */
#define IFX_P_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN8 */
#define IFX_P_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN8 */
#define IFX_P_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_P_ACCEN0_Bits.EN9 */
#define IFX_P_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_P_ACCEN0_Bits.EN9 */
#define IFX_P_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_P_ACCEN0_Bits.EN9 */
#define IFX_P_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_P_ESR_Bits.EN0 */
#define IFX_P_ESR_EN0_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN0 */
#define IFX_P_ESR_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN0 */
#define IFX_P_ESR_EN0_OFF (0u)

/** \brief  Length for Ifx_P_ESR_Bits.EN10 */
#define IFX_P_ESR_EN10_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN10 */
#define IFX_P_ESR_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN10 */
#define IFX_P_ESR_EN10_OFF (10u)

/** \brief  Length for Ifx_P_ESR_Bits.EN11 */
#define IFX_P_ESR_EN11_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN11 */
#define IFX_P_ESR_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN11 */
#define IFX_P_ESR_EN11_OFF (11u)

/** \brief  Length for Ifx_P_ESR_Bits.EN12 */
#define IFX_P_ESR_EN12_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN12 */
#define IFX_P_ESR_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN12 */
#define IFX_P_ESR_EN12_OFF (12u)

/** \brief  Length for Ifx_P_ESR_Bits.EN13 */
#define IFX_P_ESR_EN13_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN13 */
#define IFX_P_ESR_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN13 */
#define IFX_P_ESR_EN13_OFF (13u)

/** \brief  Length for Ifx_P_ESR_Bits.EN14 */
#define IFX_P_ESR_EN14_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN14 */
#define IFX_P_ESR_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN14 */
#define IFX_P_ESR_EN14_OFF (14u)

/** \brief  Length for Ifx_P_ESR_Bits.EN15 */
#define IFX_P_ESR_EN15_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN15 */
#define IFX_P_ESR_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN15 */
#define IFX_P_ESR_EN15_OFF (15u)

/** \brief  Length for Ifx_P_ESR_Bits.EN1 */
#define IFX_P_ESR_EN1_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN1 */
#define IFX_P_ESR_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN1 */
#define IFX_P_ESR_EN1_OFF (1u)

/** \brief  Length for Ifx_P_ESR_Bits.EN2 */
#define IFX_P_ESR_EN2_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN2 */
#define IFX_P_ESR_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN2 */
#define IFX_P_ESR_EN2_OFF (2u)

/** \brief  Length for Ifx_P_ESR_Bits.EN3 */
#define IFX_P_ESR_EN3_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN3 */
#define IFX_P_ESR_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN3 */
#define IFX_P_ESR_EN3_OFF (3u)

/** \brief  Length for Ifx_P_ESR_Bits.EN4 */
#define IFX_P_ESR_EN4_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN4 */
#define IFX_P_ESR_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN4 */
#define IFX_P_ESR_EN4_OFF (4u)

/** \brief  Length for Ifx_P_ESR_Bits.EN5 */
#define IFX_P_ESR_EN5_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN5 */
#define IFX_P_ESR_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN5 */
#define IFX_P_ESR_EN5_OFF (5u)

/** \brief  Length for Ifx_P_ESR_Bits.EN6 */
#define IFX_P_ESR_EN6_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN6 */
#define IFX_P_ESR_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN6 */
#define IFX_P_ESR_EN6_OFF (6u)

/** \brief  Length for Ifx_P_ESR_Bits.EN7 */
#define IFX_P_ESR_EN7_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN7 */
#define IFX_P_ESR_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN7 */
#define IFX_P_ESR_EN7_OFF (7u)

/** \brief  Length for Ifx_P_ESR_Bits.EN8 */
#define IFX_P_ESR_EN8_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN8 */
#define IFX_P_ESR_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN8 */
#define IFX_P_ESR_EN8_OFF (8u)

/** \brief  Length for Ifx_P_ESR_Bits.EN9 */
#define IFX_P_ESR_EN9_LEN (1u)

/** \brief  Mask for Ifx_P_ESR_Bits.EN9 */
#define IFX_P_ESR_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_P_ESR_Bits.EN9 */
#define IFX_P_ESR_EN9_OFF (9u)

/** \brief  Length for Ifx_P_ID_Bits.MODNUMBER */
#define IFX_P_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_P_ID_Bits.MODNUMBER */
#define IFX_P_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_P_ID_Bits.MODNUMBER */
#define IFX_P_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_P_ID_Bits.MODREV */
#define IFX_P_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_P_ID_Bits.MODREV */
#define IFX_P_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_P_ID_Bits.MODREV */
#define IFX_P_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_P_ID_Bits.MODTYPE */
#define IFX_P_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_P_ID_Bits.MODTYPE */
#define IFX_P_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_P_ID_Bits.MODTYPE */
#define IFX_P_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_P_IN_Bits.P0 */
#define IFX_P_IN_P0_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P0 */
#define IFX_P_IN_P0_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P0 */
#define IFX_P_IN_P0_OFF (0u)

/** \brief  Length for Ifx_P_IN_Bits.P10 */
#define IFX_P_IN_P10_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P10 */
#define IFX_P_IN_P10_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P10 */
#define IFX_P_IN_P10_OFF (10u)

/** \brief  Length for Ifx_P_IN_Bits.P11 */
#define IFX_P_IN_P11_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P11 */
#define IFX_P_IN_P11_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P11 */
#define IFX_P_IN_P11_OFF (11u)

/** \brief  Length for Ifx_P_IN_Bits.P12 */
#define IFX_P_IN_P12_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P12 */
#define IFX_P_IN_P12_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P12 */
#define IFX_P_IN_P12_OFF (12u)

/** \brief  Length for Ifx_P_IN_Bits.P13 */
#define IFX_P_IN_P13_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P13 */
#define IFX_P_IN_P13_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P13 */
#define IFX_P_IN_P13_OFF (13u)

/** \brief  Length for Ifx_P_IN_Bits.P14 */
#define IFX_P_IN_P14_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P14 */
#define IFX_P_IN_P14_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P14 */
#define IFX_P_IN_P14_OFF (14u)

/** \brief  Length for Ifx_P_IN_Bits.P15 */
#define IFX_P_IN_P15_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P15 */
#define IFX_P_IN_P15_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P15 */
#define IFX_P_IN_P15_OFF (15u)

/** \brief  Length for Ifx_P_IN_Bits.P1 */
#define IFX_P_IN_P1_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P1 */
#define IFX_P_IN_P1_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P1 */
#define IFX_P_IN_P1_OFF (1u)

/** \brief  Length for Ifx_P_IN_Bits.P2 */
#define IFX_P_IN_P2_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P2 */
#define IFX_P_IN_P2_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P2 */
#define IFX_P_IN_P2_OFF (2u)

/** \brief  Length for Ifx_P_IN_Bits.P3 */
#define IFX_P_IN_P3_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P3 */
#define IFX_P_IN_P3_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P3 */
#define IFX_P_IN_P3_OFF (3u)

/** \brief  Length for Ifx_P_IN_Bits.P4 */
#define IFX_P_IN_P4_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P4 */
#define IFX_P_IN_P4_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P4 */
#define IFX_P_IN_P4_OFF (4u)

/** \brief  Length for Ifx_P_IN_Bits.P5 */
#define IFX_P_IN_P5_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P5 */
#define IFX_P_IN_P5_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P5 */
#define IFX_P_IN_P5_OFF (5u)

/** \brief  Length for Ifx_P_IN_Bits.P6 */
#define IFX_P_IN_P6_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P6 */
#define IFX_P_IN_P6_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P6 */
#define IFX_P_IN_P6_OFF (6u)

/** \brief  Length for Ifx_P_IN_Bits.P7 */
#define IFX_P_IN_P7_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P7 */
#define IFX_P_IN_P7_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P7 */
#define IFX_P_IN_P7_OFF (7u)

/** \brief  Length for Ifx_P_IN_Bits.P8 */
#define IFX_P_IN_P8_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P8 */
#define IFX_P_IN_P8_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P8 */
#define IFX_P_IN_P8_OFF (8u)

/** \brief  Length for Ifx_P_IN_Bits.P9 */
#define IFX_P_IN_P9_LEN (1u)

/** \brief  Mask for Ifx_P_IN_Bits.P9 */
#define IFX_P_IN_P9_MSK (0x1u)

/** \brief  Offset for Ifx_P_IN_Bits.P9 */
#define IFX_P_IN_P9_OFF (9u)

/** \brief  Length for Ifx_P_IOCR0_Bits.PC0 */
#define IFX_P_IOCR0_PC0_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR0_Bits.PC0 */
#define IFX_P_IOCR0_PC0_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR0_Bits.PC0 */
#define IFX_P_IOCR0_PC0_OFF (3u)

/** \brief  Length for Ifx_P_IOCR0_Bits.PC1 */
#define IFX_P_IOCR0_PC1_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR0_Bits.PC1 */
#define IFX_P_IOCR0_PC1_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR0_Bits.PC1 */
#define IFX_P_IOCR0_PC1_OFF (11u)

/** \brief  Length for Ifx_P_IOCR0_Bits.PC2 */
#define IFX_P_IOCR0_PC2_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR0_Bits.PC2 */
#define IFX_P_IOCR0_PC2_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR0_Bits.PC2 */
#define IFX_P_IOCR0_PC2_OFF (19u)

/** \brief  Length for Ifx_P_IOCR0_Bits.PC3 */
#define IFX_P_IOCR0_PC3_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR0_Bits.PC3 */
#define IFX_P_IOCR0_PC3_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR0_Bits.PC3 */
#define IFX_P_IOCR0_PC3_OFF (27u)

/** \brief  Length for Ifx_P_IOCR12_Bits.PC12 */
#define IFX_P_IOCR12_PC12_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR12_Bits.PC12 */
#define IFX_P_IOCR12_PC12_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR12_Bits.PC12 */
#define IFX_P_IOCR12_PC12_OFF (3u)

/** \brief  Length for Ifx_P_IOCR12_Bits.PC13 */
#define IFX_P_IOCR12_PC13_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR12_Bits.PC13 */
#define IFX_P_IOCR12_PC13_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR12_Bits.PC13 */
#define IFX_P_IOCR12_PC13_OFF (11u)

/** \brief  Length for Ifx_P_IOCR12_Bits.PC14 */
#define IFX_P_IOCR12_PC14_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR12_Bits.PC14 */
#define IFX_P_IOCR12_PC14_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR12_Bits.PC14 */
#define IFX_P_IOCR12_PC14_OFF (19u)

/** \brief  Length for Ifx_P_IOCR12_Bits.PC15 */
#define IFX_P_IOCR12_PC15_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR12_Bits.PC15 */
#define IFX_P_IOCR12_PC15_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR12_Bits.PC15 */
#define IFX_P_IOCR12_PC15_OFF (27u)

/** \brief  Length for Ifx_P_IOCR4_Bits.PC4 */
#define IFX_P_IOCR4_PC4_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR4_Bits.PC4 */
#define IFX_P_IOCR4_PC4_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR4_Bits.PC4 */
#define IFX_P_IOCR4_PC4_OFF (3u)

/** \brief  Length for Ifx_P_IOCR4_Bits.PC5 */
#define IFX_P_IOCR4_PC5_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR4_Bits.PC5 */
#define IFX_P_IOCR4_PC5_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR4_Bits.PC5 */
#define IFX_P_IOCR4_PC5_OFF (11u)

/** \brief  Length for Ifx_P_IOCR4_Bits.PC6 */
#define IFX_P_IOCR4_PC6_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR4_Bits.PC6 */
#define IFX_P_IOCR4_PC6_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR4_Bits.PC6 */
#define IFX_P_IOCR4_PC6_OFF (19u)

/** \brief  Length for Ifx_P_IOCR4_Bits.PC7 */
#define IFX_P_IOCR4_PC7_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR4_Bits.PC7 */
#define IFX_P_IOCR4_PC7_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR4_Bits.PC7 */
#define IFX_P_IOCR4_PC7_OFF (27u)

/** \brief  Length for Ifx_P_IOCR8_Bits.PC10 */
#define IFX_P_IOCR8_PC10_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR8_Bits.PC10 */
#define IFX_P_IOCR8_PC10_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR8_Bits.PC10 */
#define IFX_P_IOCR8_PC10_OFF (19u)

/** \brief  Length for Ifx_P_IOCR8_Bits.PC11 */
#define IFX_P_IOCR8_PC11_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR8_Bits.PC11 */
#define IFX_P_IOCR8_PC11_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR8_Bits.PC11 */
#define IFX_P_IOCR8_PC11_OFF (27u)

/** \brief  Length for Ifx_P_IOCR8_Bits.PC8 */
#define IFX_P_IOCR8_PC8_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR8_Bits.PC8 */
#define IFX_P_IOCR8_PC8_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR8_Bits.PC8 */
#define IFX_P_IOCR8_PC8_OFF (3u)

/** \brief  Length for Ifx_P_IOCR8_Bits.PC9 */
#define IFX_P_IOCR8_PC9_LEN (5u)

/** \brief  Mask for Ifx_P_IOCR8_Bits.PC9 */
#define IFX_P_IOCR8_PC9_MSK (0x1fu)

/** \brief  Offset for Ifx_P_IOCR8_Bits.PC9 */
#define IFX_P_IOCR8_PC9_OFF (11u)

/** \brief  Length for Ifx_P_OMCR0_Bits.PCL0 */
#define IFX_P_OMCR0_PCL0_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR0_Bits.PCL0 */
#define IFX_P_OMCR0_PCL0_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR0_Bits.PCL0 */
#define IFX_P_OMCR0_PCL0_OFF (16u)

/** \brief  Length for Ifx_P_OMCR0_Bits.PCL1 */
#define IFX_P_OMCR0_PCL1_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR0_Bits.PCL1 */
#define IFX_P_OMCR0_PCL1_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR0_Bits.PCL1 */
#define IFX_P_OMCR0_PCL1_OFF (17u)

/** \brief  Length for Ifx_P_OMCR0_Bits.PCL2 */
#define IFX_P_OMCR0_PCL2_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR0_Bits.PCL2 */
#define IFX_P_OMCR0_PCL2_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR0_Bits.PCL2 */
#define IFX_P_OMCR0_PCL2_OFF (18u)

/** \brief  Length for Ifx_P_OMCR0_Bits.PCL3 */
#define IFX_P_OMCR0_PCL3_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR0_Bits.PCL3 */
#define IFX_P_OMCR0_PCL3_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR0_Bits.PCL3 */
#define IFX_P_OMCR0_PCL3_OFF (19u)

/** \brief  Length for Ifx_P_OMCR12_Bits.PCL12 */
#define IFX_P_OMCR12_PCL12_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR12_Bits.PCL12 */
#define IFX_P_OMCR12_PCL12_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR12_Bits.PCL12 */
#define IFX_P_OMCR12_PCL12_OFF (28u)

/** \brief  Length for Ifx_P_OMCR12_Bits.PCL13 */
#define IFX_P_OMCR12_PCL13_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR12_Bits.PCL13 */
#define IFX_P_OMCR12_PCL13_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR12_Bits.PCL13 */
#define IFX_P_OMCR12_PCL13_OFF (29u)

/** \brief  Length for Ifx_P_OMCR12_Bits.PCL14 */
#define IFX_P_OMCR12_PCL14_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR12_Bits.PCL14 */
#define IFX_P_OMCR12_PCL14_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR12_Bits.PCL14 */
#define IFX_P_OMCR12_PCL14_OFF (30u)

/** \brief  Length for Ifx_P_OMCR12_Bits.PCL15 */
#define IFX_P_OMCR12_PCL15_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR12_Bits.PCL15 */
#define IFX_P_OMCR12_PCL15_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR12_Bits.PCL15 */
#define IFX_P_OMCR12_PCL15_OFF (31u)

/** \brief  Length for Ifx_P_OMCR4_Bits.PCL4 */
#define IFX_P_OMCR4_PCL4_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR4_Bits.PCL4 */
#define IFX_P_OMCR4_PCL4_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR4_Bits.PCL4 */
#define IFX_P_OMCR4_PCL4_OFF (20u)

/** \brief  Length for Ifx_P_OMCR4_Bits.PCL5 */
#define IFX_P_OMCR4_PCL5_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR4_Bits.PCL5 */
#define IFX_P_OMCR4_PCL5_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR4_Bits.PCL5 */
#define IFX_P_OMCR4_PCL5_OFF (21u)

/** \brief  Length for Ifx_P_OMCR4_Bits.PCL6 */
#define IFX_P_OMCR4_PCL6_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR4_Bits.PCL6 */
#define IFX_P_OMCR4_PCL6_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR4_Bits.PCL6 */
#define IFX_P_OMCR4_PCL6_OFF (22u)

/** \brief  Length for Ifx_P_OMCR4_Bits.PCL7 */
#define IFX_P_OMCR4_PCL7_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR4_Bits.PCL7 */
#define IFX_P_OMCR4_PCL7_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR4_Bits.PCL7 */
#define IFX_P_OMCR4_PCL7_OFF (23u)

/** \brief  Length for Ifx_P_OMCR8_Bits.PCL10 */
#define IFX_P_OMCR8_PCL10_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR8_Bits.PCL10 */
#define IFX_P_OMCR8_PCL10_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR8_Bits.PCL10 */
#define IFX_P_OMCR8_PCL10_OFF (26u)

/** \brief  Length for Ifx_P_OMCR8_Bits.PCL11 */
#define IFX_P_OMCR8_PCL11_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR8_Bits.PCL11 */
#define IFX_P_OMCR8_PCL11_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR8_Bits.PCL11 */
#define IFX_P_OMCR8_PCL11_OFF (27u)

/** \brief  Length for Ifx_P_OMCR8_Bits.PCL8 */
#define IFX_P_OMCR8_PCL8_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR8_Bits.PCL8 */
#define IFX_P_OMCR8_PCL8_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR8_Bits.PCL8 */
#define IFX_P_OMCR8_PCL8_OFF (24u)

/** \brief  Length for Ifx_P_OMCR8_Bits.PCL9 */
#define IFX_P_OMCR8_PCL9_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR8_Bits.PCL9 */
#define IFX_P_OMCR8_PCL9_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR8_Bits.PCL9 */
#define IFX_P_OMCR8_PCL9_OFF (25u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL0 */
#define IFX_P_OMCR_PCL0_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL0 */
#define IFX_P_OMCR_PCL0_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL0 */
#define IFX_P_OMCR_PCL0_OFF (16u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL10 */
#define IFX_P_OMCR_PCL10_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL10 */
#define IFX_P_OMCR_PCL10_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL10 */
#define IFX_P_OMCR_PCL10_OFF (26u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL11 */
#define IFX_P_OMCR_PCL11_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL11 */
#define IFX_P_OMCR_PCL11_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL11 */
#define IFX_P_OMCR_PCL11_OFF (27u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL12 */
#define IFX_P_OMCR_PCL12_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL12 */
#define IFX_P_OMCR_PCL12_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL12 */
#define IFX_P_OMCR_PCL12_OFF (28u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL13 */
#define IFX_P_OMCR_PCL13_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL13 */
#define IFX_P_OMCR_PCL13_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL13 */
#define IFX_P_OMCR_PCL13_OFF (29u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL14 */
#define IFX_P_OMCR_PCL14_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL14 */
#define IFX_P_OMCR_PCL14_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL14 */
#define IFX_P_OMCR_PCL14_OFF (30u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL15 */
#define IFX_P_OMCR_PCL15_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL15 */
#define IFX_P_OMCR_PCL15_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL15 */
#define IFX_P_OMCR_PCL15_OFF (31u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL1 */
#define IFX_P_OMCR_PCL1_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL1 */
#define IFX_P_OMCR_PCL1_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL1 */
#define IFX_P_OMCR_PCL1_OFF (17u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL2 */
#define IFX_P_OMCR_PCL2_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL2 */
#define IFX_P_OMCR_PCL2_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL2 */
#define IFX_P_OMCR_PCL2_OFF (18u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL3 */
#define IFX_P_OMCR_PCL3_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL3 */
#define IFX_P_OMCR_PCL3_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL3 */
#define IFX_P_OMCR_PCL3_OFF (19u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL4 */
#define IFX_P_OMCR_PCL4_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL4 */
#define IFX_P_OMCR_PCL4_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL4 */
#define IFX_P_OMCR_PCL4_OFF (20u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL5 */
#define IFX_P_OMCR_PCL5_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL5 */
#define IFX_P_OMCR_PCL5_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL5 */
#define IFX_P_OMCR_PCL5_OFF (21u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL6 */
#define IFX_P_OMCR_PCL6_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL6 */
#define IFX_P_OMCR_PCL6_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL6 */
#define IFX_P_OMCR_PCL6_OFF (22u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL7 */
#define IFX_P_OMCR_PCL7_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL7 */
#define IFX_P_OMCR_PCL7_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL7 */
#define IFX_P_OMCR_PCL7_OFF (23u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL8 */
#define IFX_P_OMCR_PCL8_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL8 */
#define IFX_P_OMCR_PCL8_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL8 */
#define IFX_P_OMCR_PCL8_OFF (24u)

/** \brief  Length for Ifx_P_OMCR_Bits.PCL9 */
#define IFX_P_OMCR_PCL9_LEN (1u)

/** \brief  Mask for Ifx_P_OMCR_Bits.PCL9 */
#define IFX_P_OMCR_PCL9_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMCR_Bits.PCL9 */
#define IFX_P_OMCR_PCL9_OFF (25u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL0 */
#define IFX_P_OMR_PCL0_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL0 */
#define IFX_P_OMR_PCL0_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL0 */
#define IFX_P_OMR_PCL0_OFF (16u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL10 */
#define IFX_P_OMR_PCL10_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL10 */
#define IFX_P_OMR_PCL10_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL10 */
#define IFX_P_OMR_PCL10_OFF (26u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL11 */
#define IFX_P_OMR_PCL11_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL11 */
#define IFX_P_OMR_PCL11_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL11 */
#define IFX_P_OMR_PCL11_OFF (27u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL12 */
#define IFX_P_OMR_PCL12_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL12 */
#define IFX_P_OMR_PCL12_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL12 */
#define IFX_P_OMR_PCL12_OFF (28u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL13 */
#define IFX_P_OMR_PCL13_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL13 */
#define IFX_P_OMR_PCL13_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL13 */
#define IFX_P_OMR_PCL13_OFF (29u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL14 */
#define IFX_P_OMR_PCL14_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL14 */
#define IFX_P_OMR_PCL14_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL14 */
#define IFX_P_OMR_PCL14_OFF (30u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL15 */
#define IFX_P_OMR_PCL15_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL15 */
#define IFX_P_OMR_PCL15_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL15 */
#define IFX_P_OMR_PCL15_OFF (31u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL1 */
#define IFX_P_OMR_PCL1_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL1 */
#define IFX_P_OMR_PCL1_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL1 */
#define IFX_P_OMR_PCL1_OFF (17u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL2 */
#define IFX_P_OMR_PCL2_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL2 */
#define IFX_P_OMR_PCL2_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL2 */
#define IFX_P_OMR_PCL2_OFF (18u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL3 */
#define IFX_P_OMR_PCL3_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL3 */
#define IFX_P_OMR_PCL3_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL3 */
#define IFX_P_OMR_PCL3_OFF (19u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL4 */
#define IFX_P_OMR_PCL4_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL4 */
#define IFX_P_OMR_PCL4_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL4 */
#define IFX_P_OMR_PCL4_OFF (20u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL5 */
#define IFX_P_OMR_PCL5_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL5 */
#define IFX_P_OMR_PCL5_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL5 */
#define IFX_P_OMR_PCL5_OFF (21u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL6 */
#define IFX_P_OMR_PCL6_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL6 */
#define IFX_P_OMR_PCL6_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL6 */
#define IFX_P_OMR_PCL6_OFF (22u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL7 */
#define IFX_P_OMR_PCL7_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL7 */
#define IFX_P_OMR_PCL7_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL7 */
#define IFX_P_OMR_PCL7_OFF (23u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL8 */
#define IFX_P_OMR_PCL8_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL8 */
#define IFX_P_OMR_PCL8_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL8 */
#define IFX_P_OMR_PCL8_OFF (24u)

/** \brief  Length for Ifx_P_OMR_Bits.PCL9 */
#define IFX_P_OMR_PCL9_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PCL9 */
#define IFX_P_OMR_PCL9_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PCL9 */
#define IFX_P_OMR_PCL9_OFF (25u)

/** \brief  Length for Ifx_P_OMR_Bits.PS0 */
#define IFX_P_OMR_PS0_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS0 */
#define IFX_P_OMR_PS0_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS0 */
#define IFX_P_OMR_PS0_OFF (0u)

/** \brief  Length for Ifx_P_OMR_Bits.PS10 */
#define IFX_P_OMR_PS10_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS10 */
#define IFX_P_OMR_PS10_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS10 */
#define IFX_P_OMR_PS10_OFF (10u)

/** \brief  Length for Ifx_P_OMR_Bits.PS11 */
#define IFX_P_OMR_PS11_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS11 */
#define IFX_P_OMR_PS11_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS11 */
#define IFX_P_OMR_PS11_OFF (11u)

/** \brief  Length for Ifx_P_OMR_Bits.PS12 */
#define IFX_P_OMR_PS12_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS12 */
#define IFX_P_OMR_PS12_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS12 */
#define IFX_P_OMR_PS12_OFF (12u)

/** \brief  Length for Ifx_P_OMR_Bits.PS13 */
#define IFX_P_OMR_PS13_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS13 */
#define IFX_P_OMR_PS13_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS13 */
#define IFX_P_OMR_PS13_OFF (13u)

/** \brief  Length for Ifx_P_OMR_Bits.PS14 */
#define IFX_P_OMR_PS14_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS14 */
#define IFX_P_OMR_PS14_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS14 */
#define IFX_P_OMR_PS14_OFF (14u)

/** \brief  Length for Ifx_P_OMR_Bits.PS15 */
#define IFX_P_OMR_PS15_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS15 */
#define IFX_P_OMR_PS15_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS15 */
#define IFX_P_OMR_PS15_OFF (15u)

/** \brief  Length for Ifx_P_OMR_Bits.PS1 */
#define IFX_P_OMR_PS1_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS1 */
#define IFX_P_OMR_PS1_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS1 */
#define IFX_P_OMR_PS1_OFF (1u)

/** \brief  Length for Ifx_P_OMR_Bits.PS2 */
#define IFX_P_OMR_PS2_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS2 */
#define IFX_P_OMR_PS2_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS2 */
#define IFX_P_OMR_PS2_OFF (2u)

/** \brief  Length for Ifx_P_OMR_Bits.PS3 */
#define IFX_P_OMR_PS3_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS3 */
#define IFX_P_OMR_PS3_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS3 */
#define IFX_P_OMR_PS3_OFF (3u)

/** \brief  Length for Ifx_P_OMR_Bits.PS4 */
#define IFX_P_OMR_PS4_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS4 */
#define IFX_P_OMR_PS4_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS4 */
#define IFX_P_OMR_PS4_OFF (4u)

/** \brief  Length for Ifx_P_OMR_Bits.PS5 */
#define IFX_P_OMR_PS5_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS5 */
#define IFX_P_OMR_PS5_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS5 */
#define IFX_P_OMR_PS5_OFF (5u)

/** \brief  Length for Ifx_P_OMR_Bits.PS6 */
#define IFX_P_OMR_PS6_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS6 */
#define IFX_P_OMR_PS6_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS6 */
#define IFX_P_OMR_PS6_OFF (6u)

/** \brief  Length for Ifx_P_OMR_Bits.PS7 */
#define IFX_P_OMR_PS7_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS7 */
#define IFX_P_OMR_PS7_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS7 */
#define IFX_P_OMR_PS7_OFF (7u)

/** \brief  Length for Ifx_P_OMR_Bits.PS8 */
#define IFX_P_OMR_PS8_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS8 */
#define IFX_P_OMR_PS8_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS8 */
#define IFX_P_OMR_PS8_OFF (8u)

/** \brief  Length for Ifx_P_OMR_Bits.PS9 */
#define IFX_P_OMR_PS9_LEN (1u)

/** \brief  Mask for Ifx_P_OMR_Bits.PS9 */
#define IFX_P_OMR_PS9_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMR_Bits.PS9 */
#define IFX_P_OMR_PS9_OFF (9u)

/** \brief  Length for Ifx_P_OMSR0_Bits.PS0 */
#define IFX_P_OMSR0_PS0_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR0_Bits.PS0 */
#define IFX_P_OMSR0_PS0_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR0_Bits.PS0 */
#define IFX_P_OMSR0_PS0_OFF (0u)

/** \brief  Length for Ifx_P_OMSR0_Bits.PS1 */
#define IFX_P_OMSR0_PS1_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR0_Bits.PS1 */
#define IFX_P_OMSR0_PS1_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR0_Bits.PS1 */
#define IFX_P_OMSR0_PS1_OFF (1u)

/** \brief  Length for Ifx_P_OMSR0_Bits.PS2 */
#define IFX_P_OMSR0_PS2_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR0_Bits.PS2 */
#define IFX_P_OMSR0_PS2_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR0_Bits.PS2 */
#define IFX_P_OMSR0_PS2_OFF (2u)

/** \brief  Length for Ifx_P_OMSR0_Bits.PS3 */
#define IFX_P_OMSR0_PS3_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR0_Bits.PS3 */
#define IFX_P_OMSR0_PS3_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR0_Bits.PS3 */
#define IFX_P_OMSR0_PS3_OFF (3u)

/** \brief  Length for Ifx_P_OMSR12_Bits.PS12 */
#define IFX_P_OMSR12_PS12_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR12_Bits.PS12 */
#define IFX_P_OMSR12_PS12_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR12_Bits.PS12 */
#define IFX_P_OMSR12_PS12_OFF (12u)

/** \brief  Length for Ifx_P_OMSR12_Bits.PS13 */
#define IFX_P_OMSR12_PS13_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR12_Bits.PS13 */
#define IFX_P_OMSR12_PS13_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR12_Bits.PS13 */
#define IFX_P_OMSR12_PS13_OFF (13u)

/** \brief  Length for Ifx_P_OMSR12_Bits.PS14 */
#define IFX_P_OMSR12_PS14_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR12_Bits.PS14 */
#define IFX_P_OMSR12_PS14_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR12_Bits.PS14 */
#define IFX_P_OMSR12_PS14_OFF (14u)

/** \brief  Length for Ifx_P_OMSR12_Bits.PS15 */
#define IFX_P_OMSR12_PS15_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR12_Bits.PS15 */
#define IFX_P_OMSR12_PS15_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR12_Bits.PS15 */
#define IFX_P_OMSR12_PS15_OFF (15u)

/** \brief  Length for Ifx_P_OMSR4_Bits.PS4 */
#define IFX_P_OMSR4_PS4_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR4_Bits.PS4 */
#define IFX_P_OMSR4_PS4_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR4_Bits.PS4 */
#define IFX_P_OMSR4_PS4_OFF (4u)

/** \brief  Length for Ifx_P_OMSR4_Bits.PS5 */
#define IFX_P_OMSR4_PS5_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR4_Bits.PS5 */
#define IFX_P_OMSR4_PS5_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR4_Bits.PS5 */
#define IFX_P_OMSR4_PS5_OFF (5u)

/** \brief  Length for Ifx_P_OMSR4_Bits.PS6 */
#define IFX_P_OMSR4_PS6_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR4_Bits.PS6 */
#define IFX_P_OMSR4_PS6_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR4_Bits.PS6 */
#define IFX_P_OMSR4_PS6_OFF (6u)

/** \brief  Length for Ifx_P_OMSR4_Bits.PS7 */
#define IFX_P_OMSR4_PS7_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR4_Bits.PS7 */
#define IFX_P_OMSR4_PS7_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR4_Bits.PS7 */
#define IFX_P_OMSR4_PS7_OFF (7u)

/** \brief  Length for Ifx_P_OMSR8_Bits.PS10 */
#define IFX_P_OMSR8_PS10_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR8_Bits.PS10 */
#define IFX_P_OMSR8_PS10_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR8_Bits.PS10 */
#define IFX_P_OMSR8_PS10_OFF (10u)

/** \brief  Length for Ifx_P_OMSR8_Bits.PS11 */
#define IFX_P_OMSR8_PS11_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR8_Bits.PS11 */
#define IFX_P_OMSR8_PS11_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR8_Bits.PS11 */
#define IFX_P_OMSR8_PS11_OFF (11u)

/** \brief  Length for Ifx_P_OMSR8_Bits.PS8 */
#define IFX_P_OMSR8_PS8_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR8_Bits.PS8 */
#define IFX_P_OMSR8_PS8_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR8_Bits.PS8 */
#define IFX_P_OMSR8_PS8_OFF (8u)

/** \brief  Length for Ifx_P_OMSR8_Bits.PS9 */
#define IFX_P_OMSR8_PS9_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR8_Bits.PS9 */
#define IFX_P_OMSR8_PS9_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR8_Bits.PS9 */
#define IFX_P_OMSR8_PS9_OFF (9u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS0 */
#define IFX_P_OMSR_PS0_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS0 */
#define IFX_P_OMSR_PS0_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS0 */
#define IFX_P_OMSR_PS0_OFF (0u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS10 */
#define IFX_P_OMSR_PS10_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS10 */
#define IFX_P_OMSR_PS10_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS10 */
#define IFX_P_OMSR_PS10_OFF (10u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS11 */
#define IFX_P_OMSR_PS11_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS11 */
#define IFX_P_OMSR_PS11_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS11 */
#define IFX_P_OMSR_PS11_OFF (11u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS12 */
#define IFX_P_OMSR_PS12_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS12 */
#define IFX_P_OMSR_PS12_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS12 */
#define IFX_P_OMSR_PS12_OFF (12u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS13 */
#define IFX_P_OMSR_PS13_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS13 */
#define IFX_P_OMSR_PS13_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS13 */
#define IFX_P_OMSR_PS13_OFF (13u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS14 */
#define IFX_P_OMSR_PS14_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS14 */
#define IFX_P_OMSR_PS14_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS14 */
#define IFX_P_OMSR_PS14_OFF (14u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS15 */
#define IFX_P_OMSR_PS15_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS15 */
#define IFX_P_OMSR_PS15_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS15 */
#define IFX_P_OMSR_PS15_OFF (15u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS1 */
#define IFX_P_OMSR_PS1_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS1 */
#define IFX_P_OMSR_PS1_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS1 */
#define IFX_P_OMSR_PS1_OFF (1u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS2 */
#define IFX_P_OMSR_PS2_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS2 */
#define IFX_P_OMSR_PS2_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS2 */
#define IFX_P_OMSR_PS2_OFF (2u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS3 */
#define IFX_P_OMSR_PS3_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS3 */
#define IFX_P_OMSR_PS3_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS3 */
#define IFX_P_OMSR_PS3_OFF (3u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS4 */
#define IFX_P_OMSR_PS4_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS4 */
#define IFX_P_OMSR_PS4_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS4 */
#define IFX_P_OMSR_PS4_OFF (4u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS5 */
#define IFX_P_OMSR_PS5_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS5 */
#define IFX_P_OMSR_PS5_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS5 */
#define IFX_P_OMSR_PS5_OFF (5u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS6 */
#define IFX_P_OMSR_PS6_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS6 */
#define IFX_P_OMSR_PS6_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS6 */
#define IFX_P_OMSR_PS6_OFF (6u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS7 */
#define IFX_P_OMSR_PS7_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS7 */
#define IFX_P_OMSR_PS7_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS7 */
#define IFX_P_OMSR_PS7_OFF (7u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS8 */
#define IFX_P_OMSR_PS8_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS8 */
#define IFX_P_OMSR_PS8_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS8 */
#define IFX_P_OMSR_PS8_OFF (8u)

/** \brief  Length for Ifx_P_OMSR_Bits.PS9 */
#define IFX_P_OMSR_PS9_LEN (1u)

/** \brief  Mask for Ifx_P_OMSR_Bits.PS9 */
#define IFX_P_OMSR_PS9_MSK (0x1u)

/** \brief  Offset for Ifx_P_OMSR_Bits.PS9 */
#define IFX_P_OMSR_PS9_OFF (9u)

/** \brief  Length for Ifx_P_OUT_Bits.P0 */
#define IFX_P_OUT_P0_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P0 */
#define IFX_P_OUT_P0_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P0 */
#define IFX_P_OUT_P0_OFF (0u)

/** \brief  Length for Ifx_P_OUT_Bits.P10 */
#define IFX_P_OUT_P10_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P10 */
#define IFX_P_OUT_P10_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P10 */
#define IFX_P_OUT_P10_OFF (10u)

/** \brief  Length for Ifx_P_OUT_Bits.P11 */
#define IFX_P_OUT_P11_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P11 */
#define IFX_P_OUT_P11_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P11 */
#define IFX_P_OUT_P11_OFF (11u)

/** \brief  Length for Ifx_P_OUT_Bits.P12 */
#define IFX_P_OUT_P12_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P12 */
#define IFX_P_OUT_P12_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P12 */
#define IFX_P_OUT_P12_OFF (12u)

/** \brief  Length for Ifx_P_OUT_Bits.P13 */
#define IFX_P_OUT_P13_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P13 */
#define IFX_P_OUT_P13_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P13 */
#define IFX_P_OUT_P13_OFF (13u)

/** \brief  Length for Ifx_P_OUT_Bits.P14 */
#define IFX_P_OUT_P14_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P14 */
#define IFX_P_OUT_P14_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P14 */
#define IFX_P_OUT_P14_OFF (14u)

/** \brief  Length for Ifx_P_OUT_Bits.P15 */
#define IFX_P_OUT_P15_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P15 */
#define IFX_P_OUT_P15_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P15 */
#define IFX_P_OUT_P15_OFF (15u)

/** \brief  Length for Ifx_P_OUT_Bits.P1 */
#define IFX_P_OUT_P1_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P1 */
#define IFX_P_OUT_P1_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P1 */
#define IFX_P_OUT_P1_OFF (1u)

/** \brief  Length for Ifx_P_OUT_Bits.P2 */
#define IFX_P_OUT_P2_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P2 */
#define IFX_P_OUT_P2_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P2 */
#define IFX_P_OUT_P2_OFF (2u)

/** \brief  Length for Ifx_P_OUT_Bits.P3 */
#define IFX_P_OUT_P3_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P3 */
#define IFX_P_OUT_P3_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P3 */
#define IFX_P_OUT_P3_OFF (3u)

/** \brief  Length for Ifx_P_OUT_Bits.P4 */
#define IFX_P_OUT_P4_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P4 */
#define IFX_P_OUT_P4_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P4 */
#define IFX_P_OUT_P4_OFF (4u)

/** \brief  Length for Ifx_P_OUT_Bits.P5 */
#define IFX_P_OUT_P5_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P5 */
#define IFX_P_OUT_P5_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P5 */
#define IFX_P_OUT_P5_OFF (5u)

/** \brief  Length for Ifx_P_OUT_Bits.P6 */
#define IFX_P_OUT_P6_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P6 */
#define IFX_P_OUT_P6_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P6 */
#define IFX_P_OUT_P6_OFF (6u)

/** \brief  Length for Ifx_P_OUT_Bits.P7 */
#define IFX_P_OUT_P7_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P7 */
#define IFX_P_OUT_P7_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P7 */
#define IFX_P_OUT_P7_OFF (7u)

/** \brief  Length for Ifx_P_OUT_Bits.P8 */
#define IFX_P_OUT_P8_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P8 */
#define IFX_P_OUT_P8_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P8 */
#define IFX_P_OUT_P8_OFF (8u)

/** \brief  Length for Ifx_P_OUT_Bits.P9 */
#define IFX_P_OUT_P9_LEN (1u)

/** \brief  Mask for Ifx_P_OUT_Bits.P9 */
#define IFX_P_OUT_P9_MSK (0x1u)

/** \brief  Offset for Ifx_P_OUT_Bits.P9 */
#define IFX_P_OUT_P9_OFF (9u)

/** \brief  Length for Ifx_P_PCSR_Bits.LCK */
#define IFX_P_PCSR_LCK_LEN (1u)

/** \brief  Mask for Ifx_P_PCSR_Bits.LCK */
#define IFX_P_PCSR_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_P_PCSR_Bits.LCK */
#define IFX_P_PCSR_LCK_OFF (31u)

/** \brief  Length for Ifx_P_PCSR_Bits.SEL10 */
#define IFX_P_PCSR_SEL10_LEN (1u)

/** \brief  Mask for Ifx_P_PCSR_Bits.SEL10 */
#define IFX_P_PCSR_SEL10_MSK (0x1u)

/** \brief  Offset for Ifx_P_PCSR_Bits.SEL10 */
#define IFX_P_PCSR_SEL10_OFF (10u)

/** \brief  Length for Ifx_P_PCSR_Bits.SEL1 */
#define IFX_P_PCSR_SEL1_LEN (1u)

/** \brief  Mask for Ifx_P_PCSR_Bits.SEL1 */
#define IFX_P_PCSR_SEL1_MSK (0x1u)

/** \brief  Offset for Ifx_P_PCSR_Bits.SEL1 */
#define IFX_P_PCSR_SEL1_OFF (1u)

/** \brief  Length for Ifx_P_PCSR_Bits.SEL2 */
#define IFX_P_PCSR_SEL2_LEN (1u)

/** \brief  Mask for Ifx_P_PCSR_Bits.SEL2 */
#define IFX_P_PCSR_SEL2_MSK (0x1u)

/** \brief  Offset for Ifx_P_PCSR_Bits.SEL2 */
#define IFX_P_PCSR_SEL2_OFF (2u)

/** \brief  Length for Ifx_P_PCSR_Bits.SEL9 */
#define IFX_P_PCSR_SEL9_LEN (1u)

/** \brief  Mask for Ifx_P_PCSR_Bits.SEL9 */
#define IFX_P_PCSR_SEL9_MSK (0x1u)

/** \brief  Offset for Ifx_P_PCSR_Bits.SEL9 */
#define IFX_P_PCSR_SEL9_OFF (9u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS0 */
#define IFX_P_PDISC_PDIS0_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS0 */
#define IFX_P_PDISC_PDIS0_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS0 */
#define IFX_P_PDISC_PDIS0_OFF (0u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS10 */
#define IFX_P_PDISC_PDIS10_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS10 */
#define IFX_P_PDISC_PDIS10_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS10 */
#define IFX_P_PDISC_PDIS10_OFF (10u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS11 */
#define IFX_P_PDISC_PDIS11_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS11 */
#define IFX_P_PDISC_PDIS11_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS11 */
#define IFX_P_PDISC_PDIS11_OFF (11u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS12 */
#define IFX_P_PDISC_PDIS12_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS12 */
#define IFX_P_PDISC_PDIS12_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS12 */
#define IFX_P_PDISC_PDIS12_OFF (12u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS13 */
#define IFX_P_PDISC_PDIS13_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS13 */
#define IFX_P_PDISC_PDIS13_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS13 */
#define IFX_P_PDISC_PDIS13_OFF (13u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS14 */
#define IFX_P_PDISC_PDIS14_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS14 */
#define IFX_P_PDISC_PDIS14_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS14 */
#define IFX_P_PDISC_PDIS14_OFF (14u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS15 */
#define IFX_P_PDISC_PDIS15_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS15 */
#define IFX_P_PDISC_PDIS15_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS15 */
#define IFX_P_PDISC_PDIS15_OFF (15u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS1 */
#define IFX_P_PDISC_PDIS1_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS1 */
#define IFX_P_PDISC_PDIS1_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS1 */
#define IFX_P_PDISC_PDIS1_OFF (1u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS2 */
#define IFX_P_PDISC_PDIS2_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS2 */
#define IFX_P_PDISC_PDIS2_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS2 */
#define IFX_P_PDISC_PDIS2_OFF (2u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS3 */
#define IFX_P_PDISC_PDIS3_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS3 */
#define IFX_P_PDISC_PDIS3_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS3 */
#define IFX_P_PDISC_PDIS3_OFF (3u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS4 */
#define IFX_P_PDISC_PDIS4_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS4 */
#define IFX_P_PDISC_PDIS4_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS4 */
#define IFX_P_PDISC_PDIS4_OFF (4u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS5 */
#define IFX_P_PDISC_PDIS5_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS5 */
#define IFX_P_PDISC_PDIS5_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS5 */
#define IFX_P_PDISC_PDIS5_OFF (5u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS6 */
#define IFX_P_PDISC_PDIS6_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS6 */
#define IFX_P_PDISC_PDIS6_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS6 */
#define IFX_P_PDISC_PDIS6_OFF (6u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS7 */
#define IFX_P_PDISC_PDIS7_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS7 */
#define IFX_P_PDISC_PDIS7_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS7 */
#define IFX_P_PDISC_PDIS7_OFF (7u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS8 */
#define IFX_P_PDISC_PDIS8_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS8 */
#define IFX_P_PDISC_PDIS8_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS8 */
#define IFX_P_PDISC_PDIS8_OFF (8u)

/** \brief  Length for Ifx_P_PDISC_Bits.PDIS9 */
#define IFX_P_PDISC_PDIS9_LEN (1u)

/** \brief  Mask for Ifx_P_PDISC_Bits.PDIS9 */
#define IFX_P_PDISC_PDIS9_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDISC_Bits.PDIS9 */
#define IFX_P_PDISC_PDIS9_OFF (9u)

/** \brief  Length for Ifx_P_PDR0_Bits.PD0 */
#define IFX_P_PDR0_PD0_LEN (3u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PD0 */
#define IFX_P_PDR0_PD0_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PD0 */
#define IFX_P_PDR0_PD0_OFF (0u)

/** \brief  Length for Ifx_P_PDR0_Bits.PD1 */
#define IFX_P_PDR0_PD1_LEN (3u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PD1 */
#define IFX_P_PDR0_PD1_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PD1 */
#define IFX_P_PDR0_PD1_OFF (4u)

/** \brief  Length for Ifx_P_PDR0_Bits.PD2 */
#define IFX_P_PDR0_PD2_LEN (3u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PD2 */
#define IFX_P_PDR0_PD2_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PD2 */
#define IFX_P_PDR0_PD2_OFF (8u)

/** \brief  Length for Ifx_P_PDR0_Bits.PD3 */
#define IFX_P_PDR0_PD3_LEN (3u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PD3 */
#define IFX_P_PDR0_PD3_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PD3 */
#define IFX_P_PDR0_PD3_OFF (12u)

/** \brief  Length for Ifx_P_PDR0_Bits.PD4 */
#define IFX_P_PDR0_PD4_LEN (3u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PD4 */
#define IFX_P_PDR0_PD4_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PD4 */
#define IFX_P_PDR0_PD4_OFF (16u)

/** \brief  Length for Ifx_P_PDR0_Bits.PD5 */
#define IFX_P_PDR0_PD5_LEN (3u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PD5 */
#define IFX_P_PDR0_PD5_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PD5 */
#define IFX_P_PDR0_PD5_OFF (20u)

/** \brief  Length for Ifx_P_PDR0_Bits.PD6 */
#define IFX_P_PDR0_PD6_LEN (3u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PD6 */
#define IFX_P_PDR0_PD6_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PD6 */
#define IFX_P_PDR0_PD6_OFF (24u)

/** \brief  Length for Ifx_P_PDR0_Bits.PD7 */
#define IFX_P_PDR0_PD7_LEN (3u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PD7 */
#define IFX_P_PDR0_PD7_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PD7 */
#define IFX_P_PDR0_PD7_OFF (28u)

/** \brief  Length for Ifx_P_PDR0_Bits.PL0 */
#define IFX_P_PDR0_PL0_LEN (1u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PL0 */
#define IFX_P_PDR0_PL0_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PL0 */
#define IFX_P_PDR0_PL0_OFF (3u)

/** \brief  Length for Ifx_P_PDR0_Bits.PL1 */
#define IFX_P_PDR0_PL1_LEN (1u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PL1 */
#define IFX_P_PDR0_PL1_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PL1 */
#define IFX_P_PDR0_PL1_OFF (7u)

/** \brief  Length for Ifx_P_PDR0_Bits.PL2 */
#define IFX_P_PDR0_PL2_LEN (1u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PL2 */
#define IFX_P_PDR0_PL2_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PL2 */
#define IFX_P_PDR0_PL2_OFF (11u)

/** \brief  Length for Ifx_P_PDR0_Bits.PL3 */
#define IFX_P_PDR0_PL3_LEN (1u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PL3 */
#define IFX_P_PDR0_PL3_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PL3 */
#define IFX_P_PDR0_PL3_OFF (15u)

/** \brief  Length for Ifx_P_PDR0_Bits.PL4 */
#define IFX_P_PDR0_PL4_LEN (1u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PL4 */
#define IFX_P_PDR0_PL4_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PL4 */
#define IFX_P_PDR0_PL4_OFF (19u)

/** \brief  Length for Ifx_P_PDR0_Bits.PL5 */
#define IFX_P_PDR0_PL5_LEN (1u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PL5 */
#define IFX_P_PDR0_PL5_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PL5 */
#define IFX_P_PDR0_PL5_OFF (23u)

/** \brief  Length for Ifx_P_PDR0_Bits.PL6 */
#define IFX_P_PDR0_PL6_LEN (1u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PL6 */
#define IFX_P_PDR0_PL6_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PL6 */
#define IFX_P_PDR0_PL6_OFF (27u)

/** \brief  Length for Ifx_P_PDR0_Bits.PL7 */
#define IFX_P_PDR0_PL7_LEN (1u)

/** \brief  Mask for Ifx_P_PDR0_Bits.PL7 */
#define IFX_P_PDR0_PL7_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR0_Bits.PL7 */
#define IFX_P_PDR0_PL7_OFF (31u)

/** \brief  Length for Ifx_P_PDR1_Bits.PD10 */
#define IFX_P_PDR1_PD10_LEN (3u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PD10 */
#define IFX_P_PDR1_PD10_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PD10 */
#define IFX_P_PDR1_PD10_OFF (8u)

/** \brief  Length for Ifx_P_PDR1_Bits.PD11 */
#define IFX_P_PDR1_PD11_LEN (3u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PD11 */
#define IFX_P_PDR1_PD11_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PD11 */
#define IFX_P_PDR1_PD11_OFF (12u)

/** \brief  Length for Ifx_P_PDR1_Bits.PD12 */
#define IFX_P_PDR1_PD12_LEN (3u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PD12 */
#define IFX_P_PDR1_PD12_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PD12 */
#define IFX_P_PDR1_PD12_OFF (16u)

/** \brief  Length for Ifx_P_PDR1_Bits.PD13 */
#define IFX_P_PDR1_PD13_LEN (3u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PD13 */
#define IFX_P_PDR1_PD13_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PD13 */
#define IFX_P_PDR1_PD13_OFF (20u)

/** \brief  Length for Ifx_P_PDR1_Bits.PD14 */
#define IFX_P_PDR1_PD14_LEN (3u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PD14 */
#define IFX_P_PDR1_PD14_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PD14 */
#define IFX_P_PDR1_PD14_OFF (24u)

/** \brief  Length for Ifx_P_PDR1_Bits.PD15 */
#define IFX_P_PDR1_PD15_LEN (3u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PD15 */
#define IFX_P_PDR1_PD15_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PD15 */
#define IFX_P_PDR1_PD15_OFF (28u)

/** \brief  Length for Ifx_P_PDR1_Bits.PD8 */
#define IFX_P_PDR1_PD8_LEN (3u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PD8 */
#define IFX_P_PDR1_PD8_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PD8 */
#define IFX_P_PDR1_PD8_OFF (0u)

/** \brief  Length for Ifx_P_PDR1_Bits.PD9 */
#define IFX_P_PDR1_PD9_LEN (3u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PD9 */
#define IFX_P_PDR1_PD9_MSK (0x7u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PD9 */
#define IFX_P_PDR1_PD9_OFF (4u)

/** \brief  Length for Ifx_P_PDR1_Bits.PL10 */
#define IFX_P_PDR1_PL10_LEN (1u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PL10 */
#define IFX_P_PDR1_PL10_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PL10 */
#define IFX_P_PDR1_PL10_OFF (11u)

/** \brief  Length for Ifx_P_PDR1_Bits.PL11 */
#define IFX_P_PDR1_PL11_LEN (1u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PL11 */
#define IFX_P_PDR1_PL11_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PL11 */
#define IFX_P_PDR1_PL11_OFF (15u)

/** \brief  Length for Ifx_P_PDR1_Bits.PL12 */
#define IFX_P_PDR1_PL12_LEN (1u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PL12 */
#define IFX_P_PDR1_PL12_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PL12 */
#define IFX_P_PDR1_PL12_OFF (19u)

/** \brief  Length for Ifx_P_PDR1_Bits.PL13 */
#define IFX_P_PDR1_PL13_LEN (1u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PL13 */
#define IFX_P_PDR1_PL13_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PL13 */
#define IFX_P_PDR1_PL13_OFF (23u)

/** \brief  Length for Ifx_P_PDR1_Bits.PL14 */
#define IFX_P_PDR1_PL14_LEN (1u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PL14 */
#define IFX_P_PDR1_PL14_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PL14 */
#define IFX_P_PDR1_PL14_OFF (27u)

/** \brief  Length for Ifx_P_PDR1_Bits.PL15 */
#define IFX_P_PDR1_PL15_LEN (1u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PL15 */
#define IFX_P_PDR1_PL15_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PL15 */
#define IFX_P_PDR1_PL15_OFF (31u)

/** \brief  Length for Ifx_P_PDR1_Bits.PL8 */
#define IFX_P_PDR1_PL8_LEN (1u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PL8 */
#define IFX_P_PDR1_PL8_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PL8 */
#define IFX_P_PDR1_PL8_OFF (3u)

/** \brief  Length for Ifx_P_PDR1_Bits.PL9 */
#define IFX_P_PDR1_PL9_LEN (1u)

/** \brief  Mask for Ifx_P_PDR1_Bits.PL9 */
#define IFX_P_PDR1_PL9_MSK (0x1u)

/** \brief  Offset for Ifx_P_PDR1_Bits.PL9 */
#define IFX_P_PDR1_PL9_OFF (7u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXPORT_BF_H */
