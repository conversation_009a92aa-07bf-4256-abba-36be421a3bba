/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Os
 *           Program: MSR_Vector_SLP4
 *          Customer: DIAS Automotive Electronic Systems Co. Ltd.
 *       Expiry Date: Not restricted
 *  Ordered Derivat.: Aurix TC234L
 *    License Scope : The usage is restricted to CBD1900770_D01
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: Os_Compiler_Cfg.h
 *   Generation Time: 2021-04-20 16:25:14
 *           Project: PGM_BswCfg - Version 0.0.0
 *          Delivery: CBD1900770_D01
 *      Tool Version: DaVinci Configurator  5.20.35
 *
 *
 *********************************************************************************************************************/

#ifndef OS_COMPILER_CFG_H
# define OS_COMPILER_CFG_H

/**********************************************************************************************************************
 *  OS USER CALLOUT CODE SECTIONS
 *********************************************************************************************************************/

# define OS_BACKGROUNDTASK_OSCORE0_CODE
# define OS_CANISR_8_CODE
# define OS_OSTASK_CORE0_1000MS_CODE
# define OS_OSTASK_CORE0_100MS_CODE
# define OS_OSTASK_CORE0_10MSA_CODE
# define OS_OSTASK_CORE0_10MSB_CODE
# define OS_OSTASK_CORE0_1MS_CODE
# define OS_OSTASK_CORE0_200MS_CODE
# define OS_OSTASK_CORE0_20MS_CODE
# define OS_OSTASK_CORE0_500MS_CODE
# define OS_OSTASK_CORE0_50MS_CODE
# define OS_OSTASK_CORE0_5MS_CODE
# define OS_OSTASK_CORE0_APPINIT_CODE
# define OS_OSTASK_CORE0_INIT_CODE


#endif /* OS_COMPILER_CFG_H */

/**********************************************************************************************************************
 *  END OF FILE: Os_Compiler_Cfg.h
 *********************************************************************************************************************/
 
