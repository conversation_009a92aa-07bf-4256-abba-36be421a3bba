
#include "bignum.h"
#include "vsstype.h"
#include "vsskeym.h"
#include "vssconf.h"
#include "vssapi.h"

#define VSS_RAM          __attribute__((section(".vss_api_data_ROM"), used))




#pragma section code "vss_api_data_ROM"

#define VSS_CACHE72_NUM 6
#define VSS_CACHE32_NUM 12
 VSS_RAM volatile vss_uint8 g_vssBnMem72[VSS_CACHE72_NUM][72] = {0};
 VSS_RAM volatile vss_uint8 g_vssBnMem32[VSS_CACHE32_NUM][32] = {0};
 VSS_RAM volatile vss_uint8 g_vssCflagMem72[VSS_CACHE72_NUM] = {0};
 VSS_RAM volatile vss_uint8 g_vssCflagMem32[VSS_CACHE32_NUM] = {0};
 VSS_RAM mbedtls_mpi_uint vss_bn_dp[4][64] = {0};

 VSS_RAM volatile mbedtls_mpi vss_mc_op1 = {1,0,VSS_NULL};
 VSS_RAM volatile mbedtls_mpi vss_mc_op2 = {1,0,VSS_NULL};
 VSS_RAM volatile mbedtls_mpi vss_mc_opp = {1,0,VSS_NULL};
 VSS_RAM volatile vss_uint32 vss_mc_len = 0;
 VSS_RAM volatile mbedtls_mpi vss_mc_res = {1,0,vss_bn_dp[0]};
 VSS_RAM volatile vss_uint32 vss_mc_len_tmp1 = 0;
 VSS_RAM volatile mbedtls_mpi vss_mc_tmp1 = {1,0,vss_bn_dp[1]};
 VSS_RAM volatile vss_uint32 vss_mc_len_tmp2 = 0;
 VSS_RAM volatile mbedtls_mpi vss_mc_tmp2 = {1,0,vss_bn_dp[2]};

 VSS_RAM volatile vss_uint8 k[129 + 1];
 VSS_RAM volatile vss_uint32 RR_x[8];
 VSS_RAM volatile vss_uint32 RR_y[8];
 VSS_RAM volatile vss_uint32 RR_z[8];

 VSS_RAM mbedtls_mpi_uint sm2_p_data[8] = {
	0xFFFFFFFF,
	0xFFFFFFFF,
	0x00000000,
	0xFFFFFFFF, 
	0xFFFFFFFF, 
	0xFFFFFFFF, 
	0xFFFFFFFF,
	0xFFFFFFFE
};
 VSS_RAM mbedtls_mpi sm2_p= {1,8,&sm2_p_data[0]};
 VSS_RAM vss_uint8 sm2_kdf_len = 0;

 VSS_RAM mbedtls_mpi_uint nist256_p_data[8] = {
	0xFFFFFFFF,
	0xFFFFFFFF,
	0xFFFFFFFF,
	0x00000000, 
	0x00000000, 
	0x00000000, 
	0x00000001,
	0xFFFFFFFF
};
 VSS_RAM mbedtls_mpi nist256_p= {1,8,&nist256_p_data[0]};


 VSS_RAM vss_uint8 g_vssRandSeed[32] = {
	0x23, 0x54, 0xDF, 0xAB, 0x89, 0x00, 0x12, 0x62, 
	0xBF, 0x43, 0x84, 0x27, 0x01, 0xED, 0xBC, 0xBB, 
	0xFF, 0xFE, 0xDF, 0x77, 0x25, 0x83, 0x71, 0x56, 
	0x90, 0x10, 0x49, 0xCA, 0xA7, 0xD8, 0xB1, 0xDD, 
};
 VSS_RAM vss_uint8 g_vssLastRand = 0;


 VSS_RAM flash_io_cb* g_vssIocb = VSS_NULL;
 VSS_RAM wdt_rst_cb* g_vssWdtcb = VSS_NULL;
 VSS_RAM volatile vss_uint8 g_cVssAlg = 1;
 VSS_RAM volatile vss_uint8 g_cVssEnv = 1;
 VSS_RAM volatile vss_uint8 g_cVssChip = 0;
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
 VSS_RAM volatile vss_uint8 g_vssRootCert[ROOT_CERT_SIZE] = {0};
 VSS_RAM volatile vss_uint8 g_vssUserCert[USER_CERT_SIZE] = {0};
 VSS_RAM volatile vss_uint8 g_vssAsymmKey[ASYMM_KEY_LEN] = {0};
 VSS_RAM volatile vss_uint8 g_vssSymmKey[SYMM_KEY_NUM][SYMM_CIPHER_LEN] = {0};
 VSS_RAM volatile vss_uint8 g_vssSessKey[SESS_KEY_NUM][SYMM_CIPHER_LEN] = {0};
#endif
 VSS_RAM someip_io_cb* g_someip_io_cb = VSS_NULL;


#pragma section code restore


