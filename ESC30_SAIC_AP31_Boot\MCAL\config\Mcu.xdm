<?xml version='1.0'?>
<datamodel version="3.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/08/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/08/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd" >
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Mcu" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Mcu" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/AURIX/Mcu"/>
              <a:a name="IMPORTER_INFO" value="@PRE"/>
              <d:ctr name="McuPublishedInformation" type="IDENTIFIABLE">
                <a:a name="IMPORTER_INFO" value="@PRE"/>
                <a:a name="READONLY" value="true"/>
                <d:lst name="McuResetReasonConf" type="MAP">
                  <d:ctr name="MCU_ESR0_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_ESR1_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="1">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_SMU_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="2">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_SW_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="3">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_STM0_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="4">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_POWER_ON_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="7">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_CB0_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="8">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_CB1_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="9">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_CB3_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="10">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_TP_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="11">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_EVR13_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="12">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_EVR33_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="13">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_SUPPLY_WDOG_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="14">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_STBYR_RESET" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="15">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuildSelectable">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="McuGeneralConfiguration" type="IDENTIFIABLE">
                <d:var name="McuMainOscillatorFrequency" type="INTEGER" 
                       value="20">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuPllInitDelay" type="INTEGER" value="10">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuErayPllInitDelay" type="INTEGER" value="10">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuInitClock" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuNoPll" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuPerformResetApi" type="BOOLEAN" value="true"/>
                <d:var name="McuVersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuFmPllEnable" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuErayPllDisable" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuPBFixedAddress" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuGetRamStateApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuClearColdResetApi" type="BOOLEAN" value="true"/>
                <d:var name="McuDeInitApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuResetSfrAtInit" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuRunningInUser0Mode" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuUserModeInitApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuUserModeDeInitApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuUserModeRuntimeApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:lst name="McuModuleConfiguration" type="MAP">
                <d:ctr name="McuModuleConfiguration_0" type="IDENTIFIABLE">
                  <d:var name="McuClockSrcFailureNotification" 
                         type="ENUMERATION" value="DISABLED">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuNumberOfMcuModes" type="INTEGER" value="1">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuRamSectors" type="INTEGER" value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuStm0ResetDisable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuResetSetting" type="INTEGER" value="1"/>
                  <d:ctr name="McuTriggerReset" type="IDENTIFIABLE">
                    <d:var name="SMUResetTrigger" type="INTEGER" value="1"/>
                    <d:var name="ESR0ResetTrigger" type="INTEGER" value="2"/>
                    <d:var name="ESR1ResetTrigger" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="STM0ResetTrigger" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:lst name="McuClockSettingConfig" type="MAP">
                    <d:ctr name="McuClockSettingConfig_0" type="IDENTIFIABLE">
                      <d:var name="McuClockSettingId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:ctr name="McuClockReferencePoint" type="IDENTIFIABLE">
                        <d:var name="McuClockSettingMode" type="ENUMERATION" 
                               value="NORMAL_MODE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllPDivider" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllNDivider" type="INTEGER" value="29">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllK1Divider" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPllK2Divider" type="INTEGER" value="2"/>
                        <d:var name="McuPllK3Divider" type="INTEGER" value="5">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuK2DivSteps" type="INTEGER" value="4"/>
                        <d:var name="McuK2DivRampToPllConfDelay" type="INTEGER" 
                               value="10">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuK2DivRampToBackUpConfDelay" 
                               type="INTEGER" value="10">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuFMPllModAmp" type="FLOAT" value="1.25">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockReferencePointFrequency" 
                               type="FLOAT" value="2.0E8"/>
                        <d:var name="McuClockReferencePoint2Frequency" 
                               type="FLOAT" value="1.0E8">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSRIDivider" type="INTEGER" value="1"/>
                        <d:var name="McuClockSRIFrequency" type="FLOAT" 
                               value="2.0E8"/>
                        <d:var name="McuCPU0Divider" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockCPU0Frequency" type="FLOAT" 
                               value="2.0E8"/>
                        <d:var name="McuSPBDivider" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockSPBFrequency" type="FLOAT" 
                               value="1.0E8"/>
                        <d:var name="McuFSIDivider" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockFSIFrequency" type="FLOAT" 
                               value="1.0E8"/>
                        <d:var name="McuFSI2Divider" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockFSI2Frequency" type="FLOAT" 
                               value="1.0E8"/>
                        <d:var name="McuMAXDivider" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockMAXFrequency" type="FLOAT" 
                               value="2.0E8"/>
                        <d:var name="McuGTMDivider" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockGTMFrequency" type="FLOAT" 
                               value="1.0E8"/>
                        <d:var name="McuSTMDivider" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockSTMFrequency" type="FLOAT" 
                               value="1.0E8"/>
                        <d:var name="McuBBBDivider" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockBBBFrequency" type="FLOAT" 
                               value="1.0E8"/>
                        <d:var name="McuBAUD1Divider" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockBAUD1Frequency" type="FLOAT" 
                               value="6.0E7">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuBAUD2Divider" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockBAUD2Frequency" type="FLOAT" 
                               value="1.0E8"/>
                        <d:var name="McuCANDivider" type="INTEGER" value="5"/>
                        <d:var name="McuClockCANFrequency" type="FLOAT" 
                               value="4.0E7"/>
                        <d:var name="McuASCLINFDivider" type="INTEGER" value="2">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockASCLINFFrequency" type="FLOAT" 
                               value="1.0E8"/>
                        <d:var name="McuASCLINSDivider" type="INTEGER" value="8">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockASCLINSFrequency" type="FLOAT" 
                               value="2.5E7"/>
                        <d:var name="McuErayPllPDivider" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuErayPllNDivider" type="INTEGER" 
                               value="23"/>
                        <d:var name="McuErayPllK2Divider" type="INTEGER" 
                               value="2"/>
                        <d:var name="McuErayPllK3Divider" type="INTEGER" 
                               value="4">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClockErayPllFrequency" type="FLOAT" 
                               value="1.6E8"/>
                        <d:var name="McuClockErayPll2Frequency" type="FLOAT" 
                               value="9.6E7"/>
                        <d:var name="McuErayDivider" type="INTEGER" value="2"/>
                        <d:var name="McuClockErayFrequency" type="FLOAT" 
                               value="8.0E7">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuETHDivider" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuETHFrequency" type="FLOAT" value="2.0E7">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:lst>
                  <d:lst name="McuDemEventParameterRefs" type="MAP"/>
                  <d:lst name="McuModeSettingConf" type="MAP">
                    <d:ctr name="McuModeSettingConf_0" type="IDENTIFIABLE">
                      <d:var name="McuMode" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuIdleReqAckSeqDisable" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ctr name="McuStandbySettingConf" type="IDENTIFIABLE">
                        <d:var name="McuESR1WakeUpEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuESR1DigitalFilterEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuESR1EdgeDetection" type="ENUMERATION" 
                               value="TRIGGER_ANY_EDGE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPinAWakeUpEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPinADigitalFilterEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPinAEdgeDetection" type="ENUMERATION" 
                               value="TRIGGER_ANY_EDGE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPinBWakeUpEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPinBDigitalFilterEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPinBEdgeDetection" type="ENUMERATION" 
                               value="TRIGGER_ANY_EDGE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuESR0TristateEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPORSTDigitalFilterEnable" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuRampToBackupFreqApi" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuSetStandbyWakeupControlApi" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuStandbyRAMSupplyEnable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuRARCrcCheckEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                  </d:lst>
                  <d:lst name="McuRamSectorSettingConf" type="MAP"/>
                  <d:lst name="GtmConfiguration" type="MAP">
                    <d:ctr name="GtmConfiguration_0" type="IDENTIFIABLE">
                      <d:ctr name="GtmGeneral" type="IDENTIFIABLE">
                        <d:var name="GtmSleepMode" type="ENUMERATION" 
                               value="GTM_SLEEP_ENABLE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="GtmGlobalClkDivNumerator" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="GtmGlobalClkDivDenominator" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="GtmEnRstAndForceIntFunctionality" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="GtmAeiInterface" type="IDENTIFIABLE">
                        <d:var name="GtmAeiTimeOutMode" type="ENUMERATION" 
                               value="OBSERVE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="GtmAeiTimeOutValue" type="INTEGER" 
                               value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="GtmAeiTimeoutExceptionIntEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="GtmAeiUnsupportedAddressIntEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="GtmAeiIllegalModuleAddIntEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="GtmAeiUnsupportedByteEnIntEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="GtmAeiTimeoutExceptionErrIntEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="GtmAeiUnsupportedAddressErrIntEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="GtmAeiIllegalModuleAddErrIntEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="GtmAeiUnsupportedByteEnErrIntEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="GtmAeiInterruptMode" type="ENUMERATION" 
                               value="GTM_INTERRUPT_LEVEL_MODE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:lst name="Cmu" type="MAP">
                        <d:ctr name="Cmu_0" type="IDENTIFIABLE">
                          <d:ctr name="CmuFixedClock" type="IDENTIFIABLE">
                            <d:var name="CmuEnableAllFixedClocks" 
                                   type="BOOLEAN" value="true"/>
                            <d:var name="CmuFxdClkSourceSelect" 
                                   type="ENUMERATION" 
                                   value="CMU_CLOCK_0_DIVIDER"/>
                          </d:ctr>
                          <d:ctr name="CmuConfigurableClock" 
                                 type="IDENTIFIABLE">
                            <d:var name="CmuEnableConfigurableClk0" 
                                   type="BOOLEAN" value="true"/>
                            <d:var name="CmuConfigurableClk0Div" type="INTEGER" 
                                   value="99"/>
                            <d:var name="CmuEnableConfigurableClk1" 
                                   type="BOOLEAN" value="true"/>
                            <d:var name="CmuConfigurableClk1Div" type="INTEGER" 
                                   value="9"/>
                            <d:var name="CmuEnableConfigurableClk2" 
                                   type="BOOLEAN" value="false">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuConfigurableClk2Div" type="INTEGER" 
                                   value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuEnableConfigurableClk3" 
                                   type="BOOLEAN" value="false">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuConfigurableClk3Div" type="INTEGER" 
                                   value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuEnableConfigurableClk4" 
                                   type="BOOLEAN" value="false">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuConfigurableClk4Div" type="INTEGER" 
                                   value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuEnableConfigurableClk5" 
                                   type="BOOLEAN" value="false">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuConfigurableClk5Div" type="INTEGER" 
                                   value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuEnableConfigurableClk6" 
                                   type="BOOLEAN" value="false">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuClk6SourceSelect" 
                                   type="ENUMERATION" value="CMU_CLK_6_DIVIDER">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuConfigurableClk6Div" type="INTEGER" 
                                   value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuEnableConfigurableClk7" 
                                   type="BOOLEAN" value="false">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuClk7SourceSelect" 
                                   type="ENUMERATION" value="CMU_CLK_7_DIVIDER">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuConfigurableClk7Div" type="INTEGER" 
                                   value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                          </d:ctr>
                          <d:ctr name="CmuExternalClock" type="IDENTIFIABLE">
                            <d:var name="CmuEnableExternalClk0" type="BOOLEAN" 
                                   value="false">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuExternalClk0Numerator" 
                                   type="INTEGER" value="1">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuExternalClk0Denominator" 
                                   type="INTEGER" value="1">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuEnableExternalClk1" type="BOOLEAN" 
                                   value="false">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuExternalClk1Numerator" 
                                   type="INTEGER" value="1">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuExternalClk1Denominator" 
                                   type="INTEGER" value="1">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuEnableExternalClk2" type="BOOLEAN" 
                                   value="false">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuExternalClk2Numerator" 
                                   type="INTEGER" value="1">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="CmuExternalClk2Denominator" 
                                   type="INTEGER" value="1">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                          </d:ctr>
                        </d:ctr>
                      </d:lst>
                      <d:lst name="Tbu" type="MAP"/>
                      <d:lst name="Tom" type="MAP">
                        <d:ctr name="Tom_0" type="IDENTIFIABLE">
                          <d:lst name="TomTriggersForTgc" type="MAP">
                            <d:ctr name="TomTriggersForTgc_0" 
                                   type="IDENTIFIABLE">
                              <d:ctr name="TomTgcTriggerByTimebase" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomTgcTimeBaseTriggerEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcTbuTimebaseSelect" 
                                       type="ENUMERATION" 
                                       value="TBU_TS0_TIMEBASE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcTimebaseMatchValue" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomTgcInternalTriggerSelect" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomTgcIntTriggerFromChannel0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel2" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel3" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel4" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel5" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel6" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel7" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomTriggersForTgc_1" 
                                   type="IDENTIFIABLE">
                              <d:ctr name="TomTgcTriggerByTimebase" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomTgcTimeBaseTriggerEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcTbuTimebaseSelect" 
                                       type="ENUMERATION" 
                                       value="TBU_TS0_TIMEBASE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcTimebaseMatchValue" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomTgcInternalTriggerSelect" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomTgcIntTriggerFromChannel0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel2" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel3" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel4" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel5" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel6" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel7" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                          </d:lst>
                          <d:lst name="TomChannel" type="MAP">
                            <d:ctr name="TomChannel_0" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_GTM_DRIVER"/>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT76_SELA_PORT15_PIN5">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="50000"/>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="50000"/>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="2500"/>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_1" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT86_SELA_PORT14_PIN6">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_2" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT107_SELA_PORT10_PIN5">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_3" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT108_SELA_PORT10_PIN6">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_4" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT9_SELC_PORT00_PIN0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_5" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT2_SELC_PORT02_PIN2">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_6" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT4_SELC_PORT02_PIN4">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_7" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT6_SELC_PORT02_PIN6">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_8" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT9_SELA_PORT00_PIN0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_9" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT1_SELA_PORT02_PIN1">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_10" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT2_SELA_PORT02_PIN2">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_11" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT3_SELA_PORT02_PIN3">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_12" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT4_SELA_PORT02_PIN4">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_13" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT5_SELA_PORT02_PIN5">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_14" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT6_SELA_PORT02_PIN6">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_15" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT7_SELA_PORT02_PIN7">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                          </d:lst>
                        </d:ctr>
                        <d:ctr name="Tom_1" type="IDENTIFIABLE">
                          <d:lst name="TomTriggersForTgc" type="MAP">
                            <d:ctr name="TomTriggersForTgc_0" 
                                   type="IDENTIFIABLE">
                              <d:ctr name="TomTgcTriggerByTimebase" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomTgcTimeBaseTriggerEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcTbuTimebaseSelect" 
                                       type="ENUMERATION" 
                                       value="TBU_TS0_TIMEBASE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcTimebaseMatchValue" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomTgcInternalTriggerSelect" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomTgcIntTriggerFromChannel0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel2" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel3" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel4" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel5" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel6" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel7" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomTriggersForTgc_1" 
                                   type="IDENTIFIABLE">
                              <d:ctr name="TomTgcTriggerByTimebase" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomTgcTimeBaseTriggerEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcTbuTimebaseSelect" 
                                       type="ENUMERATION" 
                                       value="TBU_TS0_TIMEBASE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcTimebaseMatchValue" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomTgcInternalTriggerSelect" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomTgcIntTriggerFromChannel0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel2" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel3" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel4" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel5" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel6" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomTgcIntTriggerFromChannel7" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                          </d:lst>
                          <d:lst name="TomChannel" type="MAP">
                            <d:ctr name="TomChannel_0" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT9_SELB_PORT00_PIN0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_1" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT95_SELB_PORT11_PIN2">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_2" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT96_SELB_PORT11_PIN3">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_3" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT97_SELB_PORT11_PIN6">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_4" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT9_SELD_PORT00_PIN0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_5" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT2_SELD_PORT02_PIN2">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_6" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT4_SELD_PORT02_PIN4">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_7" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT6_SELD_PORT02_PIN6">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_8" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT0_SELB_PORT02_PIN0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_9" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT1_SELB_PORT02_PIN1">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_10" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT2_SELB_PORT02_PIN2">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_11" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT3_SELB_PORT02_PIN3">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_12" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT4_SELB_PORT02_PIN4">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_13" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT5_SELB_PORT02_PIN5">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_14" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT6_SELB_PORT02_PIN6">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TomChannel_15" type="IDENTIFIABLE">
                              <d:ctr name="TomChannelUsage" type="IDENTIFIABLE">
                                <d:var name="TomChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelEnable" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelEnable" 
                                       type="ENUMERATION" value="AT_START">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDisableOnTgcTrigger" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelOutput" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelOutputControl" 
                                       type="ENUMERATION" 
                                       value="DONT_ENABLE_CHANNEL_OUTPUT">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChOutputDisableOnTgcTrig" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOutputSignalLevel" 
                                       type="ENUMERATION" value="LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPortPinSelect" 
                                       type="ENUMERATION" 
                                       value="TOUT7_SELB_PORT02_PIN7">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelTimerParameters" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomChannelCounterValCn0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelPeriodCompareValCm0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelDutyCycleCompareValCm1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChPeriodCompareShadowValSr0" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChDCycCompareShadowValSr1" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChUpdateEnableOnCn0Reset" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChEnableForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChResetCn0OnForceUpdate" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_FIXED_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="ON_COMPARE_MATCH_ON_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtCounterCn0Reset" 
                                       type="ENUMERATION" 
                                       value="TRIG_ON_PREV_COMPARE_MATCH">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelExtTriggerOutput" 
                                       type="ENUMERATION" 
                                       value="EXT_TRIG_FROM_PREVIOUS_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelTrigOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomChannelBitReversalMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TomChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TomIntEnableOnPeriodMatchCcu0" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomIntEnableOnDutyCycleMatchCcu1" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TomNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                          </d:lst>
                        </d:ctr>
                      </d:lst>
                      <d:lst name="Tim" type="MAP">
                        <d:ctr name="Tim_0" type="IDENTIFIABLE">
                          <d:lst name="TimChannel" type="MAP">
                            <d:ctr name="TimChannel_0" type="IDENTIFIABLE">
                              <d:ctr name="TimChannelGeneral" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEnable" type="BOOLEAN" 
                                       value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelInputSelect" 
                                       type="ENUMERATION" 
                                       value="INPUT_OF_CURRENT_TIM_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelPortPinSelect" 
                                       type="ENUMERATION" value="NONE_SEL0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var name="TimChannelValInitSignal" 
                                       type="ENUMERATION" value="VAL_LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelSignalEdgeSelect" 
                                       type="ENUMERATION" value="FALLING_EDGE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr0InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr1InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsInputSelect" 
                                       type="ENUMERATION" value="CNT_VALUE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsValue" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelTbu0Ts0BitsSelect" 
                                       type="ENUMERATION" value="BITS_0_TO_23">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelModeSelect" 
                                       type="ENUMERATION" 
                                       value="PWM_MEASUREMENT_MODE_TPWM">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureModeEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureSrc" 
                                       type="ENUMERATION" 
                                       value="SEL0_NEW_VAL_IRQ">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntResetSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_ECNT_IN_WRAP_AROUND_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntOvflwSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_OVRFLOW_SIG_ON_ECNT_BITWIDTH_8">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelFilterConfig" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelFilterEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterCounterFreqSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForRisingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForFallingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForRisingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForFallingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelTimeoutDetection" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChTimeoutDetectionEnable" 
                                       type="ENUMERATION" 
                                       value="SEL0_TIMOUT_DISABLED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutDuration" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewVal" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelErrorInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewValErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TimChannel_1" type="IDENTIFIABLE">
                              <d:ctr name="TimChannelGeneral" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEnable" type="BOOLEAN" 
                                       value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelInputSelect" 
                                       type="ENUMERATION" 
                                       value="INPUT_OF_CURRENT_TIM_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelPortPinSelect" 
                                       type="ENUMERATION" value="NONE_SEL0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var name="TimChannelValInitSignal" 
                                       type="ENUMERATION" value="VAL_LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelSignalEdgeSelect" 
                                       type="ENUMERATION" value="FALLING_EDGE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr0InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr1InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsInputSelect" 
                                       type="ENUMERATION" value="CNT_VALUE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsValue" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelTbu0Ts0BitsSelect" 
                                       type="ENUMERATION" value="BITS_0_TO_23">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelModeSelect" 
                                       type="ENUMERATION" 
                                       value="PWM_MEASUREMENT_MODE_TPWM">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureModeEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureSrc" 
                                       type="ENUMERATION" 
                                       value="SEL0_NEW_VAL_IRQ">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntResetSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_ECNT_IN_WRAP_AROUND_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntOvflwSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_OVRFLOW_SIG_ON_ECNT_BITWIDTH_8">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelFilterConfig" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelFilterEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterCounterFreqSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForRisingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForFallingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForRisingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForFallingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelTimeoutDetection" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChTimeoutDetectionEnable" 
                                       type="ENUMERATION" 
                                       value="SEL0_TIMOUT_DISABLED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutDuration" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewVal" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelErrorInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewValErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TimChannel_2" type="IDENTIFIABLE">
                              <d:ctr name="TimChannelGeneral" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEnable" type="BOOLEAN" 
                                       value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelInputSelect" 
                                       type="ENUMERATION" 
                                       value="INPUT_OF_CURRENT_TIM_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelPortPinSelect" 
                                       type="ENUMERATION" value="NONE_SEL0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var name="TimChannelValInitSignal" 
                                       type="ENUMERATION" value="VAL_LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelSignalEdgeSelect" 
                                       type="ENUMERATION" value="FALLING_EDGE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr0InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr1InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsInputSelect" 
                                       type="ENUMERATION" value="CNT_VALUE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsValue" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelTbu0Ts0BitsSelect" 
                                       type="ENUMERATION" value="BITS_0_TO_23">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelModeSelect" 
                                       type="ENUMERATION" 
                                       value="PWM_MEASUREMENT_MODE_TPWM">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureModeEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureSrc" 
                                       type="ENUMERATION" 
                                       value="SEL0_NEW_VAL_IRQ">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntResetSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_ECNT_IN_WRAP_AROUND_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntOvflwSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_OVRFLOW_SIG_ON_ECNT_BITWIDTH_8">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelFilterConfig" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelFilterEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterCounterFreqSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForRisingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForFallingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForRisingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForFallingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelTimeoutDetection" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChTimeoutDetectionEnable" 
                                       type="ENUMERATION" 
                                       value="SEL0_TIMOUT_DISABLED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutDuration" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewVal" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelErrorInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewValErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TimChannel_3" type="IDENTIFIABLE">
                              <d:ctr name="TimChannelGeneral" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEnable" type="BOOLEAN" 
                                       value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelInputSelect" 
                                       type="ENUMERATION" 
                                       value="INPUT_OF_CURRENT_TIM_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelPortPinSelect" 
                                       type="ENUMERATION" value="NONE_SEL0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var name="TimChannelValInitSignal" 
                                       type="ENUMERATION" value="VAL_LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelSignalEdgeSelect" 
                                       type="ENUMERATION" value="FALLING_EDGE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr0InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr1InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsInputSelect" 
                                       type="ENUMERATION" value="CNT_VALUE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsValue" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelTbu0Ts0BitsSelect" 
                                       type="ENUMERATION" value="BITS_0_TO_23">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelModeSelect" 
                                       type="ENUMERATION" 
                                       value="PWM_MEASUREMENT_MODE_TPWM">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureModeEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureSrc" 
                                       type="ENUMERATION" 
                                       value="SEL0_NEW_VAL_IRQ">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntResetSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_ECNT_IN_WRAP_AROUND_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntOvflwSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_OVRFLOW_SIG_ON_ECNT_BITWIDTH_8">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelFilterConfig" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelFilterEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterCounterFreqSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForRisingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForFallingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForRisingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForFallingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelTimeoutDetection" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChTimeoutDetectionEnable" 
                                       type="ENUMERATION" 
                                       value="SEL0_TIMOUT_DISABLED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutDuration" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewVal" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelErrorInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewValErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TimChannel_4" type="IDENTIFIABLE">
                              <d:ctr name="TimChannelGeneral" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEnable" type="BOOLEAN" 
                                       value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelInputSelect" 
                                       type="ENUMERATION" 
                                       value="INPUT_OF_CURRENT_TIM_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelPortPinSelect" 
                                       type="ENUMERATION" value="NONE_SEL0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var name="TimChannelValInitSignal" 
                                       type="ENUMERATION" value="VAL_LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelSignalEdgeSelect" 
                                       type="ENUMERATION" value="FALLING_EDGE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr0InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr1InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsInputSelect" 
                                       type="ENUMERATION" value="CNT_VALUE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsValue" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelTbu0Ts0BitsSelect" 
                                       type="ENUMERATION" value="BITS_0_TO_23">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelModeSelect" 
                                       type="ENUMERATION" 
                                       value="PWM_MEASUREMENT_MODE_TPWM">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureModeEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureSrc" 
                                       type="ENUMERATION" 
                                       value="SEL0_NEW_VAL_IRQ">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntResetSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_ECNT_IN_WRAP_AROUND_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntOvflwSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_OVRFLOW_SIG_ON_ECNT_BITWIDTH_8">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelFilterConfig" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelFilterEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterCounterFreqSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForRisingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForFallingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForRisingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForFallingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelTimeoutDetection" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChTimeoutDetectionEnable" 
                                       type="ENUMERATION" 
                                       value="SEL0_TIMOUT_DISABLED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutDuration" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewVal" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelErrorInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewValErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TimChannel_5" type="IDENTIFIABLE">
                              <d:ctr name="TimChannelGeneral" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEnable" type="BOOLEAN" 
                                       value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelInputSelect" 
                                       type="ENUMERATION" 
                                       value="INPUT_OF_CURRENT_TIM_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelPortPinSelect" 
                                       type="ENUMERATION" value="NONE_SEL0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var name="TimChannelValInitSignal" 
                                       type="ENUMERATION" value="VAL_LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelSignalEdgeSelect" 
                                       type="ENUMERATION" value="FALLING_EDGE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr0InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr1InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsInputSelect" 
                                       type="ENUMERATION" value="CNT_VALUE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsValue" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelTbu0Ts0BitsSelect" 
                                       type="ENUMERATION" value="BITS_0_TO_23">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelModeSelect" 
                                       type="ENUMERATION" 
                                       value="PWM_MEASUREMENT_MODE_TPWM">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureModeEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureSrc" 
                                       type="ENUMERATION" 
                                       value="SEL0_NEW_VAL_IRQ">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntResetSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_ECNT_IN_WRAP_AROUND_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntOvflwSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_OVRFLOW_SIG_ON_ECNT_BITWIDTH_8">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelFilterConfig" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelFilterEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterCounterFreqSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForRisingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForFallingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForRisingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForFallingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelTimeoutDetection" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChTimeoutDetectionEnable" 
                                       type="ENUMERATION" 
                                       value="SEL0_TIMOUT_DISABLED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutDuration" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewVal" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelErrorInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewValErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TimChannel_6" type="IDENTIFIABLE">
                              <d:ctr name="TimChannelGeneral" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEnable" type="BOOLEAN" 
                                       value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelInputSelect" 
                                       type="ENUMERATION" 
                                       value="INPUT_OF_CURRENT_TIM_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelPortPinSelect" 
                                       type="ENUMERATION" value="NONE_SEL0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var name="TimChannelValInitSignal" 
                                       type="ENUMERATION" value="VAL_LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelSignalEdgeSelect" 
                                       type="ENUMERATION" value="FALLING_EDGE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr0InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr1InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsInputSelect" 
                                       type="ENUMERATION" value="CNT_VALUE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsValue" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelTbu0Ts0BitsSelect" 
                                       type="ENUMERATION" value="BITS_0_TO_23">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelModeSelect" 
                                       type="ENUMERATION" 
                                       value="PWM_MEASUREMENT_MODE_TPWM">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureModeEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureSrc" 
                                       type="ENUMERATION" 
                                       value="SEL0_NEW_VAL_IRQ">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntResetSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_ECNT_IN_WRAP_AROUND_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntOvflwSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_OVRFLOW_SIG_ON_ECNT_BITWIDTH_8">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelFilterConfig" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelFilterEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterCounterFreqSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForRisingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForFallingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForRisingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForFallingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelTimeoutDetection" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChTimeoutDetectionEnable" 
                                       type="ENUMERATION" 
                                       value="SEL0_TIMOUT_DISABLED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutDuration" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewVal" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelErrorInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewValErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                            <d:ctr name="TimChannel_7" type="IDENTIFIABLE">
                              <d:ctr name="TimChannelGeneral" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelUsage" 
                                       type="ENUMERATION" 
                                       value="USED_BY_OTHER_DRIVERS_OR_UNUSED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEnable" type="BOOLEAN" 
                                       value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelInputSelect" 
                                       type="ENUMERATION" 
                                       value="INPUT_OF_CURRENT_TIM_CHANNEL">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelPortPinSelect" 
                                       type="ENUMERATION" value="NONE_SEL0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                                <d:var name="TimChannelValInitSignal" 
                                       type="ENUMERATION" value="VAL_LOW">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelSignalEdgeSelect" 
                                       type="ENUMERATION" value="FALLING_EDGE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr0InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelGpr1InputSelect" 
                                       type="ENUMERATION" 
                                       value="TIMEBASE_TBU_TS0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsInputSelect" 
                                       type="ENUMERATION" value="CNT_VALUE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelCntsValue" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelTbu0Ts0BitsSelect" 
                                       type="ENUMERATION" value="BITS_0_TO_23">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelModeSelect" 
                                       type="ENUMERATION" 
                                       value="PWM_MEASUREMENT_MODE_TPWM">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelOneShotMode" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureModeEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelExtCaptureSrc" 
                                       type="ENUMERATION" 
                                       value="SEL0_NEW_VAL_IRQ">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntResetSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_ECNT_IN_WRAP_AROUND_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChannelEcntOvflwSetting" 
                                       type="ENUMERATION" 
                                       value="SEL0_OVRFLOW_SIG_ON_ECNT_BITWIDTH_8">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelFilterConfig" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChannelFilterEnable" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterCounterFreqSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForRisingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterModeForFallingEdge" 
                                       type="ENUMERATION" 
                                       value="IMMEDIATE_EDGE_PROPAGATION_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForRisingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChFilterTimeForFallingEdge" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelTimeoutDetection" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimChTimeoutDetectionEnable" 
                                       type="ENUMERATION" 
                                       value="SEL0_TIMOUT_DISABLED">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutClockSelect" 
                                       type="ENUMERATION" 
                                       value="GTM_CONFIGURABLE_CLOCK_0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimChTimeoutDuration" 
                                       type="INTEGER" value="0">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewVal" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOfl" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDet" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptMode" 
                                       type="ENUMERATION" 
                                       value="GTM_INTERRUPT_LEVEL_MODE">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimNotification" 
                                       type="FUNCTION-NAME" value="NULL_PTR">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="TimChannelErrorInterrupt" 
                                     type="IDENTIFIABLE">
                                <d:var name="TimInterruptEnableOnNewValErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnEcntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnCntOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGprxOflErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnToDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                                <d:var name="TimInterruptEnableOnGlitchDetErr" 
                                       type="BOOLEAN" value="false">
                                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                                </d:var>
                              </d:ctr>
                            </d:ctr>
                          </d:lst>
                        </d:ctr>
                      </d:lst>
                      <d:lst name="GtmConnections" type="MAP"/>
                      <d:lst name="GtmAccessEnable" type="MAP"/>
                    </d:ctr>
                  </d:lst>
                  <d:lst name="EruConfiguration" type="MAP"/>
                  <d:lst name="CcuConfiguration" type="MAP">
                    <d:ctr name="CcuConfiguration_0" type="IDENTIFIABLE">
                      <d:lst name="Ccu6" type="MAP">
                        <d:ctr name="Ccu60" type="IDENTIFIABLE">
                          <d:ctr name="General" type="IDENTIFIABLE">
                            <d:var name="Ccu6ModuleUsage" type="ENUMERATION" 
                                   value="USED_BY_ADC_DRIVER"/>
                          </d:ctr>
                          <d:ctr name="T12" type="IDENTIFIABLE">
                            <d:ctr name="General" type="IDENTIFIABLE">
                              <d:var name="T12ClkSelection" type="INTEGER" 
                                     value="0">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                              <d:var name="T12PrescalerEnabled" type="BOOLEAN" 
                                     value="false">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                            </d:ctr>
                            <d:lst name="Cc6" type="MAP">
                              <d:ctr name="Cc60" type="IDENTIFIABLE">
                                <d:var name="CCChannelInputSelection" 
                                       type="ENUMERATION" 
                                       value="CCINA_PORT2_PIN0">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="Cc61" type="IDENTIFIABLE">
                                <d:var name="CCChannelInputSelection" 
                                       type="ENUMERATION" 
                                       value="CCINA_PORT2_PIN2">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="Cc62" type="IDENTIFIABLE">
                                <d:var name="CCChannelInputSelection" 
                                       type="ENUMERATION" 
                                       value="CCINA_PORT2_PIN4">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                            </d:lst>
                          </d:ctr>
                          <d:ctr name="T13" type="IDENTIFIABLE">
                            <d:ctr name="General" type="IDENTIFIABLE">
                              <d:var name="T13ClkSelection" type="INTEGER" 
                                     value="0">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                              <d:var name="T13PrescalerEnabled" type="BOOLEAN" 
                                     value="false">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                            </d:ctr>
                          </d:ctr>
                        </d:ctr>
                        <d:ctr name="Ccu61" type="IDENTIFIABLE">
                          <d:ctr name="General" type="IDENTIFIABLE">
                            <d:var name="Ccu6ModuleUsage" type="ENUMERATION" 
                                   value="UNUSED"/>
                          </d:ctr>
                          <d:ctr name="T12" type="IDENTIFIABLE">
                            <d:ctr name="General" type="IDENTIFIABLE">
                              <d:var name="T12ClkSelection" type="INTEGER" 
                                     value="0">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                              <d:var name="T12PrescalerEnabled" type="BOOLEAN" 
                                     value="false">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                            </d:ctr>
                            <d:lst name="Cc6" type="MAP">
                              <d:ctr name="Cc60" type="IDENTIFIABLE">
                                <d:var name="CCChannelInputSelection" 
                                       type="ENUMERATION" 
                                       value="CCINA_PORT0_PIN1">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="Cc61" type="IDENTIFIABLE">
                                <d:var name="CCChannelInputSelection" 
                                       type="ENUMERATION" 
                                       value="CCINA_PORT0_PIN3">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                              <d:ctr name="Cc62" type="IDENTIFIABLE">
                                <d:var name="CCChannelInputSelection" 
                                       type="ENUMERATION" 
                                       value="CCINA_PORT0_PIN5">
                                  <a:a name="IMPORTER_INFO">
                                    <a:v>@DEF</a:v>
                                    <a:v>@CALC</a:v>
                                  </a:a>
                                </d:var>
                              </d:ctr>
                            </d:lst>
                          </d:ctr>
                          <d:ctr name="T13" type="IDENTIFIABLE">
                            <d:ctr name="General" type="IDENTIFIABLE">
                              <d:var name="T13ClkSelection" type="INTEGER" 
                                     value="0">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                              <d:var name="T13PrescalerEnabled" type="BOOLEAN" 
                                     value="false">
                                <a:a name="IMPORTER_INFO" value="@DEF"/>
                              </d:var>
                            </d:ctr>
                          </d:ctr>
                        </d:ctr>
                      </d:lst>
                    </d:ctr>
                  </d:lst>
                  <d:lst name="DmaConfiguration" type="MAP"/>
                  <d:ctr name="AscLinConfiguration" type="IDENTIFIABLE">
                    <d:var name="AscLin0" type="ENUMERATION" 
                           value="USE_FOR_NONE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="AscLin1" type="ENUMERATION" 
                           value="USE_FOR_NONE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:ctr name="McuSafety" type="IDENTIFIABLE">
                <d:var name="McuSafetyEnable" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuInitCheckApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuGetModeApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuClockMonitoringEnable" type="BOOLEAN" 
                       value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArMajorVersion" type="INTEGER" value="3"/>
                <d:var name="ArMinorVersion" type="INTEGER" value="2"/>
                <d:var name="ArPatchVersion" type="INTEGER" value="0"/>
                <d:var name="SwMajorVersion" type="INTEGER" value="4"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="9"/>
                <d:var name="SwPatchVersion" type="INTEGER" value="2"/>
                <d:var name="ModuleId" type="INTEGER" value="101">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="17">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="Release" type="STRING" value="_TRICORE_TC233">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
