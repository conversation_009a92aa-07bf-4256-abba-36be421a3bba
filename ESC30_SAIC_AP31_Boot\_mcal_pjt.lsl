
// Include the CPU specific .lsl file
// The CPU is specified by the __CPU__ macro
#ifndef __CPU__
# error No CPU defined, please link with -D__CPU__=<cpu>
#endif

//#define HSM_RESERVED1_PMU_FLASH0 0x80018000
//#define HSM_RESERVED1_SIZE       16k
//#define SBST_PMU_PFLASH0         0x80020000
#define PMU_PFLASH0 0x80001000
//#define HSM_RESERVED2_PMU_FLASH0 0x80070000
//#define HSM_RESERVED2_SIZE       128k
//#define PMUECCEDC_SAFETLIB_TVT_RESERVED 0x80090000
//#define PMU_SAFETLIB_PFLASHTST_RESERVED (PMUECCEDC_SAFETLIB_TVT_RESERVED+0x100)

#define PFLASH1_ERASEDRV 0x8000A000
#define  CPU0_PSR_ERASEDRV 0x70101000

#define CPU0_PMI_PSPR 0x70100100


// 100 bytes of DSPR is reserved for sbst_tc0_dspr
#define CPU0_DMI_DSPR 0x70000100



#define __QUOTE_ME2(x) #x
#define __QUOTE_ME(x) __QUOTE_ME2(x)
#define CPULSL_FILE __QUOTE_ME(__CPU__.lsl)

#include CPULSL_FILE

// Define default external memory
// These memory definitions can be replaced by the Debug Target Configuration Wizard

#ifndef HAS_ON_CHIP_FLASH       // On-chip flash is located at 0x[8a]0000000
memory xrom_a (tag="External ROM",tag="dtc")
{
        mau = 8;
        size = 1M;
        type = rom;
         map     cached (dest=bus:sri, dest_offset=0x80000000,           size=1M);
         map not_cached (dest=bus:sri, dest_offset=0xa0000000, reserved, size=1M);
}
#endif







section_layout mpe:tc0:linear
{
  group  (ordered, run_addr=0x80000000)
  {
    select ".rodata.BMD_HDR_CONST_FAR_UNSPECIFIED";
  }
}


section_layout mpe:tc0:linear
{
  group FLSLOADER ( ordered,copy, run_addr = 0x70101000 )
  {
    select ".text.erase_driver";
  }
}
section_layout mpe:tc0:linear
{
  group CPU0_PRIVATE_CODE (ordered, run_addr=PMU_PFLASH0)
  {   
      select ".text.CPU0.Private*";
//      select ".text*"; 
      select ".text.default";    
     
  }
  

}

section_layout mpe:tc0:linear
{
	group RESERVE_CHECKDATA_ROM (ordered, run_addr=0x8001C000)
	{
	     reserved "reserved_checkdata_rom" (alloc_allowed = absolute,size = 0x4000);
	} 
}





section_layout mpe:tc0:linear
{
  group FLSCHECHDATA ( ordered, run_addr = 0x8001C000 )
  {
     select ".rodata.FlsCheckData";
  }

		group FBL_APP_CPB_FBL (ordered, run_addr=0x8001C100)
		{
			section "fbl_cpb_data" ( size = 4, attributes = rx, fill = 0x00)
			{
				select ".rodata.boot_cpb_data";
			}
		}			
  
	 group VSSAPICODE ( ordered, run_addr = 0x80030000 ) 
    {
        select ".text.vss_api_code*";
        select ".rodata.vss_api_code*";
        select ".vss_api_can_ctrl*"; 
    }
  


	group VSSAPIDATAROM_run ( ordered, run_addr = 0x7001A000 )
	{
	    select ".vss_api_data_ROM";
	}
	
	
	group VSSAPIDATAROM_LOAD ( ordered, load_addr = 0x80040000 )
	{
	    select ".vss_api_data_ROM";
	}
	


  group VSSCFGDATACODE ( ordered, run_addr = 0x80045000 )
  {
  	 //select ".data.EP33L_IPD_MCU_V2*";
     select ".rodata.vss_cfg_data_code";
  }
  group VSSAPITABLE ( ordered, run_addr = 0x80048000 )
  {
     select ".rodata.vss_api_table";
  } 
  
  
  group Version ( ordered, run_addr = 0x80020000 )
  {
     select ".rodata.vss_cfg_data_codeF180";
  } 
  
	
	//group VSSAPIDATARAM ( ordered, run_addr = 0x70020000 )
	//{
	//    select ".data.vssvar.*";
	//    select ".bss.vssvar.*";
	//}
}
section_layout mpe:tc0:linear
{
  group DIDFLASHDATA ( ordered, run_addr = 0x8000FF00 )
  {
     select ".rodata.DidCfg";
  }
}
section_layout mpe:tc0:linear
{
	
}


