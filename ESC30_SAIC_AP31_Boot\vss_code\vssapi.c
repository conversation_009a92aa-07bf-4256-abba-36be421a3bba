#include "vsskeym.h"
#include "vssapi.h"
#include "sm2.h"
#include "sm3.h"
#include "sm4.h"
#include "errid.h"
#include "sha.h"
#include "aes.h"
#include "cmac.h"
#include "mizar_ecc.h"
#include "cert.h"
#include "errid.h"
#include "vssconf.h"
#include "kzuc.h"
#include "vsscommon.h"

extern vss_uint8 g_vssRandSeed[32];
extern vss_uint8 g_vssLastRand;
extern flash_io_cb *g_vssIocb;
extern wdt_rst_cb* g_vssWdtcb;

/* 绠楁硶鍒濆鍖栧悗鐨勫叏灞�鍙傛暟: 1-鍥介檯锛�2-鍥藉瘑*/
extern volatile vss_uint8 g_cVssAlg; 
extern volatile vss_uint8 g_cVssEnv; 


#pragma section code "vss_api_code" 

extern const vss_uint8 vssapi_sm2_rand[32];
extern const vss_uint8 vssapi_sm2_rand1[32];
extern const vss_uint8 vssapi_SESS_KEY[16];
extern const vss_char8 VSS_VERSION[16];
extern const vss_char8 vssapi_KEY[4];
extern const vss_char8 vssapi_SERVER[7];
extern const vss_char8 vssapi_CLIENT[7];

vss_uint32 VssCryptoInit(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb)
{
	vss_uint32 ret = 0;
	
	if (type != 0 && type != 1)
		return ERR_PARAMETER;

	g_vssIocb = flashCb;
	g_vssWdtcb = wdtCb;

	if (g_vssIocb == VSS_NULL)
		return 0;

	ret = GetAlg();
	ret = GetEnvironment();
	
	return ret;
}

vss_uint32 VssGenerateKeyByCode(vss_uint32 len, vss_uint8* code,vss_char8* szKeyIdList,vss_uint8 AutoSetWroteFlag)
{
	vss_uint32 ret = 0;
	vss_uint8 wrote_flag = 0;
	
	if (szKeyIdList == VSS_NULL || code == VSS_NULL)
		return ERR_PARAMETER;

	if(len != 32)
		return ERR_LEN_INVALID;

	ret = GetWroteFLag(&wrote_flag);
	if(ret)
		return ret;
	
	if(wrote_flag == 1)
	{
		return ERR_KEY_EXIST;
	}

	ret = GenSymmKey(code, szKeyIdList);
	if(ret == 0)
	{
		if(AutoSetWroteFlag)
		{
			ret = SetWroteFlag(1);
		}
	}

	return ret;
}

vss_uint32 VssSetKeyActive(vss_uint32 keyId, vss_uint32 valid)
{
	vss_uint32 ret = 0;
	
	if(keyId < COMM_KEY || keyId >= RESV_KEY)
		return ERR_INDEX_INVALID;

	if (valid != 0 && valid != 1)
		return ERR_PARAMETER;

	ret = SetKeyActive(keyId, valid);
	
	return ret;
}

vss_uint32 VssECCGenKey(vss_uint8 *szX, vss_uint8 *szY, vss_uint8 *szSK)
{
#if (defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))
	vss_uint32 ret = 0;
	
	if(szX == VSS_NULL || szY == VSS_NULL || szSK == VSS_NULL)
		return ERR_PARAMETER;

	ret = ecc_gen_key(szSK, szX, szY);
	if (ret)
	{
		return ERR_CALC_FAIL;
	}
	
	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssSM2GenKey(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK)
{
#if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U))
	vss_uint32 ret = 0;
	sm2_point pubkey;
	
	if(szX == VSS_NULL || szY == VSS_NULL || szSK == VSS_NULL)
		return ERR_PARAMETER;
	
	pubkey.x = (vss_uint32*)szX;
	pubkey.y = (vss_uint32*)szY;
	
	ret = sm2_genkey(&pubkey, (vss_uint32 *)szSK, (vss_uint32*)vssapi_sm2_rand);
	
	if(ret != RET_SM2_GENKEY_SUCCESS)
	{
		return ERR_CALC_FAIL;
	}

	return 0;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssECCGenKeyIndex(vss_uint8 *szX, vss_uint8 *szY)
{
#if (defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))
	vss_uint32 ret = 0;

	if(g_cVssAlg != ALG_GJ)
		return ERR_SYSTEM_INIT;

	ret = GenAsymmKey(szX, szY);
	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssSM2GenKeyIndex(vss_uint8 *szX, vss_uint8 *szY)
{
#if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U))
	vss_uint32 ret = 0;

	if(g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;

	ret = GenAsymmKey(szX, szY);
	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssImportSessKey(vss_uint32 nKeyLen, vss_uint8* szKey)
{
	vss_uint32 ret = 0;
	vss_uint8 sav[32];
	vss_uint32 savLen = 0;

	if(szKey == VSS_NULL)
		return ERR_PARAMETER;

	if(nKeyLen != 16)
		return ERR_KEYLEN_INVALID;

	VssExportSessKey(SESS_KEY_CUR, &savLen, sav);
	ret = SetSessKey(SESS_KEY_CUR,  nKeyLen, szKey);
	if(ret)
	{
		SetSessKey(SESS_KEY_CUR,  savLen, sav);
		ret = ERR_WRITE_FLASH;
	}else{
		if(savLen > 0)
		{
			ret = SetSessKey(SESS_KEY_SAV,  savLen, sav);
		}
	}

	return ret;
}

vss_uint32 VssExportSessKey(vss_uint32 index, vss_uint32* nKeyLen,vss_uint8* szKey)
{
	if(index >= SESS_KEY_NUM)
		return ERR_INDEX_INVALID;
	
	return GetSessKey(index, nKeyLen, szKey);
}

vss_uint32 VssExportAtKey(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey)
{
	vss_uint32 ret = 0;
	vss_uint8 flag = 0;

	if (nKeyLen == VSS_NULL || szKey == VSS_NULL)
		return ERR_PARAMETER;
	
	if(index >= SECOC_PIN)
		return ERR_INDEX_INVALID;

	ret = GetWroteFLag(&flag);
	if(ret || flag == 0)
		return ERR_GEN_KEY;
	
	ret = GetSymmKey(index, nKeyLen, szKey);
	if(ret)
	{
		ret = ERR_KEY_NOTFOUND;
	}
	
	return ret;
}

vss_uint32 VssExportAtPin(vss_uint32* nPinLen, vss_uint8* szPin)
{
	vss_uint32 ret = 0;
	vss_uint8 flag = 0;
	
	if (nPinLen == VSS_NULL || szPin == VSS_NULL)
		return ERR_PARAMETER;

	ret = GetWroteFLag(&flag);
	if(ret || flag == 0)
		return ERR_GEN_KEY;
	
	ret = GetSymmKey(SECOC_PIN, nPinLen, szPin);
	if(ret)
	{
		ret = ERR_KEY_NOTFOUND;
	}
	
	return ret;
}

vss_uint32 VssExportKey(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey)
{
	vss_uint32 ret = 0;
	vss_uint8 flag = 0;

	if (nKeyLen == VSS_NULL || szKey == VSS_NULL)
		return ERR_PARAMETER;

	if(index < RESV_KEY || index >= SYMM_KEY_NUM)
		return ERR_INDEX_INVALID;

	ret = GetWroteFLag(&flag);
	if(ret || flag == 0)
		return ERR_GEN_KEY;
		
	ret = GetSymmKey(index, nKeyLen, szKey);
	if(ret)
	{
		ret = ERR_KEY_NOTFOUND;
	}
	
	return ret;
}


vss_uint32 VssGetAlgFlag(vss_uint32* nAlgFlag)
{
	vss_uint32 ret = 0;

	if(nAlgFlag == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	ret = GetAlg();
	if(ret)
		return ret;
	
	*nAlgFlag = g_cVssAlg;
	return ret;
}

vss_uint32 VssKeyCodeFeedback(vss_uint8* szFeedback)
{
	vss_uint32 ret = 0;
	vss_uint8 flag;

	if(szFeedback == VSS_NULL)
	{
		return ERR_PARAMETER;
	}	
	
	ret = GetWroteFLag(&flag);
	if(ret)
	{
		return ret;
	}

	if(flag)
	{
		mem_set8(szFeedback, 0, 32);
	}
	else
	{
		mem_set8(szFeedback, 0xFF, 32);
	}
	
	return 0;
}

vss_uint32 VssCertImport(vss_uint32 nCertType, vss_uint32 nLen, vss_uint8* szCert)
{
	vss_uint32 ret = 0;
	vss_uint32 index = 0;

	if(szCert == VSS_NULL || nLen > USER_CERT_SIZE)
	{
		return ERR_PARAMETER;
	}

	if(nCertType != CERT_TYPE_ROOT && nCertType != CERT_TYPE_USR)
	{
		return ERR_PARAMETER;
	}
	
	ret = SaveCert(nCertType, nLen, szCert);
	return ret;
}

vss_uint32 VssCertExport(vss_uint32 nCertType, vss_uint32* nCertLen, vss_uint8* szCert, vss_uint8* info)
{
	vss_uint32 ret = 0;
	vss_uint32 index = 0;
	vss_uint8* temp;

	if(szCert == VSS_NULL || nCertLen == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	if(nCertType != CERT_TYPE_ROOT && nCertType != CERT_TYPE_USR)
	{
		return ERR_PARAMETER;
	}

	temp = info;

	ret = LoadCert(nCertType, nCertLen, szCert);
	if(ret == 0 && temp != VSS_NULL)
	{
		ret = VssGetCertInfo(szCert, *nCertLen, temp);
	}
	
	return ret;
}

vss_uint32 VssGenRandom(vss_uint32 len, vss_uint8* out)
{
	vss_uint32 i = 0;	

	if(out == VSS_NULL)
		return ERR_PARAMETER;
	
	if(len <= 0)
		return ERR_LEN_INVALID;

	for (i = 0; i < len; ++i) 
	{
		out[i] = g_vssRandSeed[g_vssLastRand++%32];
	}

	g_vssRandSeed[(g_vssLastRand+7)%32]++;
	g_vssRandSeed[(g_vssLastRand+13)%32]++;
	g_vssRandSeed[(g_vssLastRand+19)%32]++;
	g_vssRandSeed[(g_vssLastRand+23)%32]++;

	return 0;
}

vss_uint32 VssHash(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen)
{
	THashCtx ctx;
	vss_uint32 ret = 0;

	if(data == VSS_NULL || out == VSS_NULL || pOutLen == VSS_NULL)
		return ERR_PARAMETER;

	if(dataLen == 0)
		return ERR_LEN_INVALID;
	
	ret = VssHashInit(&ctx);
	if (ret)
		return ret;
	
	ret = VssHashUpdate(&ctx, data, dataLen);
	if (ret)
		return ret;
	
	ret = VssHashFinal(&ctx, out, pOutLen);
	if (ret)
		return ret;
	
	return 0;
}

vss_uint32 VssHashInit(THashCtx* ctx)
{
	if(ctx == VSS_NULL)
		return ERR_PARAMETER;
	
	if (g_cVssAlg == ALG_GM)
	{
#if (defined (_ENABLE_MIZAR_SM3_)&&(_ENABLE_MIZAR_SM3_ == 1U))
		sm3_init(&ctx->sm3ctx);
#else
		return ERR_NOT_SUPPORT;
#endif
	}
	else
	{
#if (defined (_ENABLE_MIZAR_SHA256_)&&(_ENABLE_MIZAR_SHA256_ == 1U))
		SHA256_Init(&ctx->shactx);
#else
		return ERR_NOT_SUPPORT;
#endif
	}
	
	return 0;
}

vss_uint32 VssHashUpdate(THashCtx* ctx,vss_uint8* data,vss_uint32 dataLen)
{
	if(ctx == VSS_NULL || data == VSS_NULL)
		return ERR_PARAMETER;
	
	if (g_cVssAlg == ALG_GM)
	{
#if (defined (_ENABLE_MIZAR_SM3_)&&(_ENABLE_MIZAR_SM3_ == 1U))
		sm3_update(&ctx->sm3ctx, data, dataLen);
#else
		return ERR_NOT_SUPPORT;
#endif
	}
	else
	{
#if (defined (_ENABLE_MIZAR_SHA256_)&&(_ENABLE_MIZAR_SHA256_ == 1U))	
		SHA256_Update(&ctx->shactx, data, dataLen);
#else
		return ERR_NOT_SUPPORT;
#endif
	}
	return 0;
}

vss_uint32 VssHashFinal(THashCtx* ctx, vss_uint8* out, vss_uint32* pOutLen)
{
	if(ctx == VSS_NULL || out == VSS_NULL || pOutLen == VSS_NULL)
		return ERR_PARAMETER;
	
	if (g_cVssAlg == ALG_GM)
	{
#if (defined (_ENABLE_MIZAR_SM3_)&&(_ENABLE_MIZAR_SM3_ == 1U))
		sm3_final(out,&ctx->sm3ctx);
#else
		return ERR_NOT_SUPPORT;
#endif
	}
	else
	{
#if (defined (_ENABLE_MIZAR_SHA256_)&&(_ENABLE_MIZAR_SHA256_ == 1U))	
		SHA256_Final(out,&ctx->shactx);
#else
		return ERR_NOT_SUPPORT;
#endif
	}
	*pOutLen = 32;
	return 0;
}

vss_uint32 VssSM2Calc(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey,vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
#if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U))
	vss_uint32 ret = 0;
	vss_uint32 datalen = 0;
	vss_uint8 * pubkey1;
	vss_uint8 * pubkey2;
	vss_uint8 * prikey;
	
	if(szInData == VSS_NULL || szKey == VSS_NULL || szOutData == VSS_NULL || pOutLen == VSS_NULL)
	{
		return ERR_PARAMETER;
	}
	
	if(calcFlag != CALC_ENC && calcFlag != CALC_DEC)
	{
		return ERR_PARAMETER;
	}
	
	if (calcFlag == CALC_ENC) 
	{
		if (nKeyLen != 64)
		{
			return ERR_KEYLEN_INVALID;
		}
		
		if (nInLen > 64 || nInLen == 0)
			return ERR_LEN_INVALID;

		pubkey1 = szKey;
		pubkey2 = pubkey1 + 32;
		ret = sm2_pub_enc(0, (vss_uint32*)pubkey1, (vss_uint32*)pubkey2, (vss_uint8*)szInData, nInLen, szOutData);
		if (ret)
		{
			return ERR_CALC_FAIL;
		}
		datalen = nInLen + 96;
	} else {
		if (nKeyLen != 32)
		{
			return ERR_KEYLEN_INVALID;
		}
		
		if (nInLen > 160 || nInLen <= 96)
			return ERR_LEN_INVALID;

		prikey = szKey;
		ret = sm2_pri_dec(0, (vss_uint32*)prikey, szInData, nInLen, szOutData);
		if (ret)
		{
			return ERR_CALC_FAIL;
		}
		datalen = nInLen - 96;
	}

	*pOutLen = datalen;
	return 0;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssSM2CalcIndex(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
#if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U))
	vss_uint32 ret = 0;
	vss_uint8* key = VSS_NULL;
	vss_uint8 keypair[ASYMM_KEY_LEN];
	vss_uint32 keyLen = 0;

	if(g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;

	ret = LoadAsymmKey(keypair, keypair + 32, keypair + 64);
	if(ret)
		return ERR_KEY_NOTFOUND;

	if(calcFlag == CALC_ENC)
	{
		key = keypair;
		keyLen = 64;
 	}else if(calcFlag == CALC_DEC)
 	{
		key = keypair + 64;
		keyLen = 32;
 	}else
	{
		return ERR_PARAMETER;
	}
	
	return VssSM2Calc(szInData, nInLen, calcFlag, key, keyLen, szOutData, pOutLen);
#else
	return ERR_NOT_SUPPORT;
#endif
}


vss_uint32 VssSM2Sign(vss_uint8* sk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
#if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U))
	vss_uint32 ret = 0;
	vss_uint8 hash[32];
	sm2_sig sigSt;

	if(sk == VSS_NULL || data == VSS_NULL || sig == VSS_NULL)
		return ERR_PARAMETER;
	
	sigSt.r = (vss_uint32 *)sig;
	sigSt.s = sigSt.r + SM2_PARA_WLEN;

	if (hashFlag == HASH_NO_CALC)
	{
		if(dataLen != 32)
			return ERR_LEN_INVALID;
		
		mem_cpy8(hash, data, 32);
	}
	else if(hashFlag == HASH_CALC)
	{
		if(dataLen == 0)
			return ERR_LEN_INVALID;
		
		mizar_sm3(data, dataLen, hash);
	}else{
		return ERR_PARAMETER;
	}
 	
	ret = sm2_gensig(&sigSt, (vss_uint32*)hash, (vss_uint32*)sk, (vss_uint32*)vssapi_sm2_rand1);
	if(ret != RET_SM2_GENSIG_SUCCESS)
	{
 		return ERR_CALC_FAIL;
	}

 	return 0;
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssSM2SignIndex(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
#if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U))
	vss_uint32 ret = 0;
	vss_uint8 keypair[96];

	if(data == VSS_NULL || sig == VSS_NULL)
		return ERR_PARAMETER;

	if(g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;
	
	ret = LoadAsymmKey(keypair, keypair + 32, keypair + 64);
	if (ret)
		return ERR_KEY_NOTFOUND;
	
 	return VssSM2Sign(keypair + 64, data, dataLen, hashFlag,sig);;
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssSM2Verify(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
#if (defined (_ENABLE_MIZAR_SM2_)&&(_ENABLE_MIZAR_SM2_ == 1U))
	vss_uint8 hash[32];
	vss_uint32 ret = 0;
	sm2_sig sigSt;	
	sm2_point pubkey;

	if(pk == VSS_NULL || data == VSS_NULL || sig == VSS_NULL)
		return ERR_PARAMETER;
	
	sigSt.r = (vss_uint32 *)sig;
	sigSt.s = sigSt.r + SM2_PARA_WLEN;
	pubkey.x = (vss_uint32*)pk;
	pubkey.y = pubkey.x + SM2_PARA_WLEN;
	
	if (hashFlag == HASH_NO_CALC)
	{
		if(dataLen != 32)
			return ERR_LEN_INVALID;
		
		mem_cpy8(hash, data, 32);
	}
	else if(hashFlag == HASH_CALC)
	{
		if(dataLen == 0)
			return ERR_LEN_INVALID;
		
		mizar_sm3(data, dataLen, hash);
	}else{
		return ERR_PARAMETER;
	}
	
	ret = sm2_verifysig(&sigSt, (vss_uint32*)hash, &pubkey);	
	if(ret != RET_SM2_VERIFYSIG_SUCCESS)
	{
		return ERR_CALC_FAIL;
	}
	
	return 0;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssSM4Calc(vss_uint8* in, vss_uint32 inLen, vss_uint8 *keyin, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding,vss_uint8* out, vss_uint32* pOutLen)
{
	vss_uint8 key[16];
	vss_uint8 lastBlockPadded[16];
	vss_uint32 nDataLen1 = 0;
	vss_uint32 nDataLen2 = 0;
	vss_uint32 ret = 0;

	if(in == VSS_NULL || keyin == VSS_NULL || out == VSS_NULL || pOutLen == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	if(calcFlag != CALC_ENC && calcFlag != CALC_DEC)
	{
		return ERR_PARAMETER;
	}

	if(padding != PAD_FORCE && padding != PAD_NO_FORCE)
	{
		return ERR_PARAMETER;
	}
	
	if (keyLen != 16)
	{
		return ERR_KEYLEN_INVALID;
	}
	mem_cpy8(key, keyin, keyLen);
	mem_set8(lastBlockPadded, 0, 16);

	if (calcFlag == CALC_ENC) 
	{		
		nDataLen1 = (inLen/16)*16;
		nDataLen2 = inLen%16;
		
		if (nDataLen2 > 0 || padding == PAD_FORCE)
		{
			if (nDataLen2 > 0)
				mem_cpy8(lastBlockPadded, in + nDataLen1, nDataLen2);
			
			nDataLen2 = CryptoPaddingTo16(lastBlockPadded, nDataLen2, padding);
		}
		
		if (nDataLen1 > 0)
		{
			ret = sm4_ecb_encrypt_new(in, out, nDataLen1, key, TT_SM4_ENCRYPT);
			if(ret != 0)
			{
				return ERR_CALC_FAIL;
			}
		}
		if (nDataLen2 > 0)
		{			
			ret = sm4_ecb_encrypt_new(lastBlockPadded, out+nDataLen1, nDataLen2, key, TT_SM4_ENCRYPT);
			if(ret != 0)
			{
				return ERR_CALC_FAIL;
			}
		}
				
		*pOutLen = nDataLen1 + nDataLen2;
	} 
	else
	{			
		ret = sm4_ecb_encrypt_new(in, out, inLen, key, TT_SM4_DECRYPT);
		if(ret != 0)
		{
			return ERR_CALC_FAIL;
		}
		nDataLen1 = inLen;
		
		if (padding == PAD_FORCE)
			nDataLen1 = CryptoUnPadding16(out, inLen);
		
		*pOutLen = nDataLen1;
	}
	
	return 0;
}

vss_uint32 VssSM4CalcIndex(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding,vss_uint8* out, vss_uint32* pOutLen)
{
	vss_uint32 ret = 0;
	vss_uint32 keyLen = 0;
	vss_uint8 key[16];
	
	if (index >= SYMM_KEY_NUM)
		return ERR_INDEX_INVALID;

	ret = GetSymmKey(index, &keyLen, key);
	if(ret)
		return ret;
		
	return VssSM4Calc(in,inLen, key, 16, calcFlag, padding, out, pOutLen);
}


vss_uint32 VssSM4Mac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
	vss_uint8 bySession[16];
	vss_uint8 byTemp[16];
	vss_uint32 mabLen = 0;
	vss_uint32 noutlen = 0;
	vss_uint32 i = 0;
	vss_uint32 j = 0;

	if(in == VSS_NULL || key == VSS_NULL || out == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	if(keyLen != 16)
		return ERR_KEYLEN_INVALID;

	mem_set8(bySession, 0, 16);
	
	mabLen = (inLen/16)*16;	
	for(i=0; i<mabLen; i+=16)
	{
		mem_cpy8(byTemp, in+i, 16);
		
		for(j=0; j<16; ++j)
		{
			bySession[j] ^= byTemp[j];
		}

		VssSM4Calc(bySession, 16, key, keyLen, CALC_ENC, PAD_NO_FORCE, byTemp,&noutlen);
		mem_cpy8(bySession, byTemp, 16);
	}

	mem_set8(byTemp, 0x00, 16);
	if(inLen > mabLen)
		mem_cpy8(byTemp, in+i, inLen-mabLen);
	byTemp[inLen-mabLen] = 0x80;

	for(j=0; j<16; ++j)
	{
		bySession[j] ^= byTemp[j];
	}

	VssSM4Calc(bySession, 16, key, keyLen, CALC_ENC, PAD_NO_FORCE, byTemp,&noutlen);	
	mem_cpy8(out, byTemp, 16);
	return 0;

}

vss_uint32 VssSM4CMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
	vss_uint32 ret = 0;
	
	if(in == VSS_NULL || key == VSS_NULL || out == VSS_NULL)
	{
		return ERR_PARAMETER;
	}	
	
	if (keyLen != 16)
		return ERR_KEYLEN_INVALID;
	
	ret = CMacData(ALG_GM, keyLen, key, in, inLen, out);
	return ret;
}

vss_uint32 VssSM4CMacIndex(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out)
{
	vss_uint32 ret = 0;
	vss_uint32 keyLen = 0;
	vss_uint8 key[16];
	
	if(in == VSS_NULL || out == VSS_NULL)
	{
		return ERR_PARAMETER;
	}	
	
	if (index >= SYMM_KEY_NUM)
		return ERR_INDEX_INVALID;

	ret = GetSymmKey(index, &keyLen, key);
	if(ret)
		return ret;
	
	ret = CMacData(ALG_GM, 16, key, in, inLen, out);
	return ret;

}


vss_uint32 VssECCCalc(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey,vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
#if (defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))
	vss_uint32 datalen = 0;
	vss_uint32 ret = 0;
	vss_uint32 i = 0;
	vss_uint8 buf[128];
	vss_uint8* pTemp;
	vss_uint8* pubkey1;
	vss_uint8* pubkey2;
	vss_uint8* prikey;

	if(szInData == VSS_NULL || szKey == VSS_NULL || szOutData == VSS_NULL || pOutLen == VSS_NULL)
	{
		return ERR_PARAMETER;
	}
	if(calcFlag != CALC_ENC && calcFlag != CALC_DEC)
	{
		return ERR_PARAMETER;
	}
	
	pTemp = VSS_NULL;
	pubkey1 = szKey;
	pubkey2 = pubkey1 + 32;
	prikey = szKey;

	mem_set8(buf, 0, 128);
	if (calcFlag == CALC_ENC) {
		if (nKeyLen != 64)
		{
			return ERR_KEYLEN_INVALID;
		}

		if (nInLen > 64 || nInLen == 0)
		{
			return ERR_LEN_INVALID;
		}


		mem_cpy8(buf, szInData, nInLen);
		buf[nInLen] = 0x80;
		ret = ecc_pub_enc((vss_uint32*)pubkey1, (vss_uint32*)pubkey2, 8, buf, nInLen, buf);
		if (ret)
		{
			return ERR_CALC_FAIL;
		}
		pTemp = szOutData;
		mem_cpy8(pTemp, szKey, 64);
		pTemp += 64;
		mem_cpy8(pTemp, buf, 128);
		datalen = 192;
	} else {
		if (nKeyLen != 32)
		{
			return ERR_KEYLEN_INVALID;
		}

		if (nInLen != 192)
		{
			return ERR_LEN_INVALID;
		}

		ret = ecc_pri_dec((vss_uint32*)prikey, 8, szInData+64, 128, buf);
		if (ret)
		{
			return ERR_CALC_FAIL;
		}

		/* unpadding */
		pTemp = buf + 63;
		datalen = 64;
		for (i = 0; i < 64; i++) {
			if (*pTemp == 0) {
				pTemp--;
				datalen--;
			} else if (*pTemp == 0x80) {
				*pTemp = 0;
				datalen--;
				break;
			} else {
				break;
			}
		}

		mem_cpy8(szOutData, buf, datalen);
	}

	*pOutLen = datalen;
	return 0;
#else 
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssECCCalcIndex(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
#if (defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))
	vss_uint32 ret = 0;
	vss_uint8* key = VSS_NULL;
	vss_uint8 keypair[ASYMM_KEY_LEN];
	vss_uint32 keyLen = 0;

	if(g_cVssAlg != ALG_GJ)
		return ERR_SYSTEM_INIT;
	
	ret = LoadAsymmKey(keypair, keypair + 32, keypair + 64);
	if(ret)
		return ERR_KEY_NOTFOUND;

	if(calcFlag == CALC_ENC)
	{
		key = keypair;
		keyLen = 64;
 	}else if(calcFlag == CALC_DEC)
 	{
		key = keypair + 64;
		keyLen = 32;
 	}else
	{
		return ERR_PARAMETER;
	}
	
	return VssECCCalc(szInData, nInLen, calcFlag, key, keyLen, szOutData, pOutLen);
#else 
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssECCSign(vss_uint8* key, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
#if (defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))
	vss_uint8 hash[32];
	vss_uint32 ret = 0;

	if(key == VSS_NULL || data == VSS_NULL || sig == VSS_NULL)
		return ERR_PARAMETER;
	
	if (hashFlag == HASH_NO_CALC)
	{
		if(dataLen != 32)
			return ERR_LEN_INVALID;
		
		mem_cpy8(hash, data, 32);
	}
	else if(hashFlag == HASH_CALC)
	{
		if(dataLen == 0)
			return ERR_LEN_INVALID;
		
		SHA256(data, dataLen, hash);
	}else{
		return ERR_PARAMETER;
	}
	
	ret = my_ecc_sign(key, hash, sig);
	if(ret)
	{
		return ERR_CALC_FAIL;
	}

	return 0;	
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssECCSignIndex(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
#if (defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))
	vss_uint8 hash[32];
	vss_uint32 ret = 0;
	vss_uint8 keypair[ASYMM_KEY_LEN];
	vss_uint32 keyLen = 0;

	if(g_cVssAlg != ALG_GJ)
		return ERR_SYSTEM_INIT;

	if(data == VSS_NULL || sig == VSS_NULL)
		return ERR_PARAMETER;

	ret = LoadAsymmKey(keypair, keypair + 32, keypair + 64);
	if(ret)
		return ERR_KEY_NOTFOUND;

	return VssECCSign(keypair + 64, data, dataLen, hashFlag, sig);
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssECCVerify(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
#if (defined (_ENABLE_MIZAR_ECC_)&&(_ENABLE_MIZAR_ECC_ == 1U))
	vss_uint32 ret = 0;
	vss_uint8 hash[32];

	if(pk == VSS_NULL || data == VSS_NULL || sig == VSS_NULL)
		return ERR_PARAMETER;
	
	if (hashFlag == HASH_NO_CALC)
	{
		if(dataLen != 32)
			return ERR_LEN_INVALID;
		
		mem_cpy8(hash, data, 32);
	}
	else if(hashFlag == HASH_CALC)
	{
		if(dataLen == 0)
			return ERR_LEN_INVALID;
		
		SHA256(data, dataLen, hash);
	}else{
		return ERR_PARAMETER;
	}
	
	ret = my_ecc_verify(pk, pk+32, hash, sig);
	if(ret)
	{
		return ERR_CALC_FAIL;
	}
	return 0;
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssAESCalc(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding,vss_uint8* out, vss_uint32* pOutLen)
{
#if (defined (_ENABLE_MIZAR_AES_)&&(_ENABLE_MIZAR_AES_ == 1U))	
	vss_uint8 lastBlockPadded[16];
	vss_uint32 nDataLen1 = 0;
	vss_uint32 nDataLen2 = 0;
	vss_uint32 nCalcKeyLen = 0;
	aes_context ctx;

	if(in == VSS_NULL || key == VSS_NULL || out == VSS_NULL || pOutLen == VSS_NULL)
	{
		return ERR_PARAMETER;
	}	

	if(calcFlag != CALC_ENC && calcFlag != CALC_DEC)
	{
		return ERR_PARAMETER;
	}

	if(padding != PAD_FORCE && padding != PAD_NO_FORCE)
	{
		return ERR_PARAMETER;
	}

	nCalcKeyLen = keyLen;
	if (nCalcKeyLen != 16)
	{
		return ERR_KEYLEN_INVALID;
	}
	aes_set_key(&ctx, key);

	if (calcFlag == CALC_ENC)
	{
		nDataLen1 = (inLen/16)*16;
		nDataLen2 = inLen%16;
		
		if (nDataLen2 > 0 || padding == PAD_FORCE)
		{
			if (nDataLen2 > 0)
				mem_cpy8(lastBlockPadded, in + nDataLen1, nDataLen2);
			
			nDataLen2 = CryptoPaddingTo16(lastBlockPadded, nDataLen2, padding);
		}
		
		if (nDataLen1 > 0)
			aes_ecb_encrypt(in, out, nDataLen1, &ctx, TT_AES_ENCRYPT);
		if (nDataLen2 > 0)
		{			
			aes_ecb_encrypt(lastBlockPadded, out+nDataLen1, nDataLen2, &ctx, TT_AES_ENCRYPT);
		}
				
		*pOutLen = nDataLen1 + nDataLen2;		
	} 
	else 
	{
		
		aes_ecb_encrypt(in, out, inLen, &ctx, TT_AES_DECRYPT);
		
		if (padding == PAD_FORCE)
		{
			inLen = CryptoUnPadding16(out, inLen);
		}

		*pOutLen = inLen;

	}
	return 0;
#else 
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssAESCalcIndex(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding,vss_uint8* out, vss_uint32* pOutLen)
{
#if (defined (_ENABLE_MIZAR_AES_)&&(_ENABLE_MIZAR_AES_ == 1U))	
	vss_uint32 ret = 0;
	vss_uint32 keyLen = 0;
	vss_uint8 key[16];
	
	if (index >= SYMM_KEY_NUM)
		return ERR_INDEX_INVALID;

	ret = GetSymmKey(index, &keyLen, key);
	if(ret)
		return ret;
	
	return VssAESCalc(in,inLen, key, 16, calcFlag, padding, out, pOutLen);
#else 
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssAESMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
#if (defined (_ENABLE_MIZAR_AES_)&&(_ENABLE_MIZAR_AES_ == 1U))
	vss_uint8 bySession[16];
	vss_uint8 byTemp[16];
	vss_uint32 mabLen = 0;
	vss_uint32 noutlen =0;
	vss_uint32 i=0;
	vss_uint32 j=0;

	if(in == VSS_NULL || key == VSS_NULL || out == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	if(keyLen != 16)
		return ERR_KEYLEN_INVALID;
	
	mem_set8(bySession, 0, 16);
	mabLen = (inLen/16)*16;	
	for(i=0; i<mabLen; i+=16)
	{
		mem_cpy8(byTemp, in+i, 16);
		
		for(j=0; j<16; ++j)
		{
			bySession[j] ^= byTemp[j];
		}

		VssAESCalc(bySession, 16, key, keyLen, CALC_ENC, PAD_NO_FORCE, byTemp,&noutlen);
		mem_cpy8(bySession, byTemp, 16);
	}

	mem_set8(byTemp, 0x00, 16);
	if(inLen > mabLen)
		mem_cpy8(byTemp, in+i, inLen-mabLen);
	byTemp[inLen-mabLen] = 0x80;

	for(j=0; j<16; ++j)
	{
		bySession[j] ^= byTemp[j];
	}

	VssAESCalc(bySession, 16, key, keyLen, CALC_ENC, PAD_NO_FORCE, byTemp,&noutlen);	
	mem_cpy8(out, byTemp, 16);
	return 0;
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssAESCMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
#if (defined (_ENABLE_MIZAR_AES_)&&(_ENABLE_MIZAR_AES_ == 1U))
	vss_uint32 ret = 0;

	if(in == VSS_NULL || key == VSS_NULL || out == VSS_NULL)
	{
		return ERR_PARAMETER;
	}	
	
	if (keyLen != 16)
		return ERR_KEYLEN_INVALID;
	
	ret = CMacData(ALG_GJ, keyLen, key, in, inLen, out);
	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssAESCMacIndex(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out)
{
#if (defined (_ENABLE_MIZAR_AES_)&&(_ENABLE_MIZAR_AES_ == 1U))
	vss_uint32 ret = 0;
	vss_uint32 keyLen = 0;
	vss_uint8 key[16];
	
	if(in == VSS_NULL || out == VSS_NULL)
	{
		return ERR_PARAMETER;
	}	
	
	if (index >= SYMM_KEY_NUM)
		return ERR_INDEX_INVALID;

	ret = GetSymmKey(index, &keyLen, key);
	if(ret)
		return ret;
	
	ret = CMacData(ALG_GJ, 16, key, in, inLen, out);
	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssHMAC(vss_uint8* key, vss_uint32 keyLen, vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen)
{
	vss_uint32 i = 0;
	vss_uint8 hmac_k0[64];
	vss_uint8 hmac_k0ipad[64];
	vss_uint8 hmac_k0opad[64];
	vss_uint8 hmac_tmp1[32];
	vss_uint32 mdlen = 0;	
	THashCtx ctx;
	vss_uint32 ret = 0;
	
	if(key == VSS_NULL || data == VSS_NULL || out == VSS_NULL || pOutLen == VSS_NULL)
		return ERR_PARAMETER;

	if(keyLen == 0)
		return ERR_KEYLEN_INVALID;

	if(dataLen == 0)
		return ERR_LEN_INVALID;		

	mem_set8(hmac_k0, 0, 64);
	if (keyLen > 64) {
		ret = VssHash(key, keyLen, hmac_k0, &mdlen);
		if (ret)
			return ret;
	} else {
		mem_cpy8((vss_uint8*)hmac_k0, key, keyLen);
	}
	
	for (i = 0; i < 64; i++) {
		hmac_k0ipad[i] = hmac_k0[i] ^ 0x36;
		hmac_k0opad[i] = hmac_k0[i] ^ 0x5C;
	}
	
	ret = VssHashInit(&ctx);
	if (ret) return ret;
	ret = VssHashUpdate(&ctx, hmac_k0ipad, 64);
	if (ret) return ret;
	ret = VssHashUpdate(&ctx, data, dataLen);
	if (ret) return ret;
	ret = VssHashFinal(&ctx, hmac_tmp1, &mdlen);
	if (ret) return ret;

	ret = VssHashInit(&ctx);
	if (ret) return ret;
	ret = VssHashUpdate(&ctx, hmac_k0opad, 64);
	if (ret) return ret;
	ret = VssHashUpdate(&ctx, hmac_tmp1, 32);
	if (ret) return ret;
	ret = VssHashFinal(&ctx, out, pOutLen);
	if (ret) return ret;
	
	return 0;
}

vss_uint32 VssZucSetKey(vss_uint8* key, TZucKey* zuc_key)
{
#if (defined (_ENABLE_MIZAR_ZUC_)&&(_ENABLE_MIZAR_ZUC_ == 1U))
	if (key == VSS_NULL || zuc_key == VSS_NULL)
		return ERR_PARAMETER;

	mem_set8(zuc_key, 0, 8);
	ZucSetKey(key, zuc_key);
	return 0;
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssZucCalc(TZucKey* zuc_key, vss_uint8* in, vss_uint8* out)
{
#if (defined (_ENABLE_MIZAR_ZUC_)&&(_ENABLE_MIZAR_ZUC_ == 1U))
	if (zuc_key == VSS_NULL || in == VSS_NULL || out == VSS_NULL)
		return ERR_PARAMETER;
	
	ZucCalc(zuc_key, in, out);	
	return 0;
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssZucCalcData(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint8* out)
{
#if (defined (_ENABLE_MIZAR_ZUC_)&&(_ENABLE_MIZAR_ZUC_ == 1U))
	TZucKey zuc_key;
	vss_uint32 i;
	
	if (in == VSS_NULL || out == VSS_NULL || key == VSS_NULL)
		return ERR_PARAMETER;

	if(inLen == 0 || inLen%8 > 0)
		return ERR_LEN_INVALID;

	ZucSetKey(key, &zuc_key);
	
	for(i=0; i<inLen; i+=8)
	{
		ZucCalc(&zuc_key, in+i, out+i);
	}
	return 0;
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssGenCertReq(vss_uint8 ecuType, vss_char8* VIN, vss_char8* info, vss_uint8* pk, vss_uint8* sig)
{
#if (defined (_ENABLE_MIZAR_FOTA_)&&(_ENABLE_MIZAR_FOTA_ == 1U))
	vss_uint8 data[101];
	vss_uint32 ret = 0;
	vss_uint32 offset = 0;	
	
	if(g_cVssAlg != ALG_GJ && g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;
	
  	if (VIN == VSS_NULL || info == VSS_NULL || pk == VSS_NULL || sig == VSS_NULL)
	{
		return ERR_PARAMETER;	
	}
	
	ret = VssAsymGenKeyIndex(pk, pk + 32);
	if(ret)
		return ret;

	data[offset++] = ecuType;

	mem_cpy8(data + offset, VIN, 17);
	offset += 17;

	mem_cpy8(data + offset, info, 16);
	offset += 16;
	
	if(g_cVssAlg == ALG_GJ)
	{
		data[offset++] = 0x01;
		data[offset++] = 0x21;
		data[offset++] = 0x02;
	}else{
		data[offset++] = 0x04;
		data[offset++] = 0x11;
		data[offset++] = 0x07;
	}

	mem_cpy8(data + offset, pk, 64);	
	offset += 64;

	ret = VssSignIndex(data, offset, HASH_CALC, sig);
	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssStSymmCalc(vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint32 lastKey, vss_uint8* out, vss_uint32* pOutLen)
{
#if (defined (_ENABLE_MIZAR_FOTA_)&&(_ENABLE_MIZAR_FOTA_ == 1U))
	vss_uint8 key[16];
	vss_uint32 ret = 0;
	vss_uint32 keyLen = 0;
	
	if(g_cVssAlg != ALG_GJ && g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;

	if(in == VSS_NULL || out == VSS_NULL ||pOutLen == VSS_NULL)
	{
		return ERR_PARAMETER;
	}
	
	
	if(lastKey == 0)
	{
		ret = VssExportSessKey(SESS_KEY_CUR, &keyLen, key);
		if(ret)
			return ret;
	}
	else if(lastKey == 1)
	{
		ret = VssExportSessKey(SESS_KEY_SAV, &keyLen, key);
		if(ret)
			return ret;
	}
	else
	{
		return ERR_PARAMETER;
	}

	if(keyLen != 16)
		return ERR_KEYLEN_INVALID;

	switch(g_cVssAlg)
	{
	case ALG_GJ:
		 ret = VssAESCalc(in, inLen, key, keyLen, calcFlag, padding, out, pOutLen);
		 break;
	case ALG_GM:
		 ret = VssSM4Calc(in, inLen, key, keyLen, calcFlag, padding, out, pOutLen);
		 break;
	}

	if(calcFlag == CALC_DEC && padding == PAD_FORCE)
	{
		if(*pOutLen >= inLen)
			return ERR_KEY_INVALID;
	}

	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssGenSessionKey(vss_uint8* M1, vss_uint8* r1, vss_uint8* r2)
{
#if (defined (_ENABLE_MIZAR_FOTA_)&&(_ENABLE_MIZAR_FOTA_ == 1U))
	vss_uint8 temp[128];
	vss_uint8 x[32];
	vss_uint32 xLen = 0;
	vss_uint32 ret = 0;
	
	if(g_cVssAlg != ALG_GJ && g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;
	
	if (M1 == VSS_NULL || r1 == VSS_NULL || r2 == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	mem_cpy8(temp, vssapi_KEY, 3);
	mem_cpy8(temp + 3, r1, 32);
	mem_cpy8(temp + 35, r2, 32);	
	ret = VssHMAC(M1, 32, temp, 67, x, &xLen);
	if(ret == 0)
	{
		ret = VssImportSessKey(16, x);
	}	
	
	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssSignData(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen)
{
#if (defined (_ENABLE_MIZAR_FOTA_)&&(_ENABLE_MIZAR_FOTA_ == 1U))
	vss_uint32 ret = 0;

	if(g_cVssAlg != ALG_GJ && g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;

	if(data == VSS_NULL || out == VSS_NULL || pOutLen == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	ret = VssSignIndex(data, dataLen, HASH_CALC, out);
	if(ret == 0)
	{
		*pOutLen = 64;
	}

	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssCalcFinishHmac(vss_uint8* R1, vss_uint8* R2, vss_uint8* M1, vss_uint8* S1,
	vss_uint8* E1, vss_uint32 E1Len, vss_char8* KeyWord , vss_uint8* siteCert, vss_uint32 siteLen,
	vss_uint8* vehicleCert, vss_uint32 vehicleLen, vss_uint8* hmac)
{
#if (defined (_ENABLE_MIZAR_FOTA_)&&(_ENABLE_MIZAR_FOTA_ == 1U))
	vss_uint8 temp[256];
	vss_uint32 ret = 0;
	vss_uint32 buff_size = 0;
	
	if(g_cVssAlg != ALG_GJ && g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;
	
	if (R1 == VSS_NULL || R2 == VSS_NULL || M1 == VSS_NULL || S1 == VSS_NULL || E1 == VSS_NULL	
		|| KeyWord == VSS_NULL || siteCert == VSS_NULL || vehicleCert == VSS_NULL || hmac == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	if (siteLen != USER_CERT_SIZE || vehicleLen != USER_CERT_SIZE)
	{
		return ERR_LEN_INVALID;
	}

	if (mem_cmp8(KeyWord, vssapi_SERVER, 6) != 0 && mem_cmp8(KeyWord, vssapi_CLIENT, 6) != 0)	
	{
		return ERR_DATA_INVALID;
	}

	mem_cpy8(temp + buff_size, M1, 32);
	buff_size += 32;
	mem_cpy8(temp + buff_size, KeyWord, 6);
	buff_size += 6;
	mem_cpy8(temp + buff_size, R1, 33);
	buff_size += 33;
	mem_cpy8(temp + buff_size, R2, 33);
	buff_size += 33;

	ret = VssHash(temp, buff_size, hmac, &buff_size);
	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssGetCertInfo(vss_uint8* cert, vss_uint32 certLen, vss_uint8* info)
{
#if (defined (_ENABLE_MIZAR_FOTA_)&&(_ENABLE_MIZAR_FOTA_ == 1U))
	vss_uint32 ret = 0;
	CertInfo ci;
	
	if(g_cVssAlg != ALG_GJ && g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;
	
	if (cert == VSS_NULL || info == VSS_NULL)	
	{
		return ERR_PARAMETER;
	}

	mem_set8(info, 0, 16);
	ret = _cert_decode(cert, certLen, &ci);
	if(ret)
	{
		return ret;
	}
	
	switch(ci.cert)
	{
	case CERTTYPE_ROOT:	
	case CERTTYPE_USR:
		mem_cpy8(info, ci.type, 4);
		mem_cpy8(info + 4, ci.expire, 3);
		mem_cpy8(info + 8, ci.serial, 4);
		if(ci.alg == 4)
		{
			info[12] = 0;
		}
		else
		{
			info[12] = 1;
		}
		info[13] = ci.root;
	break;
	default:
		return ERR_CERT_INVALID;
	}

	return 0;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssVerifyCert(vss_uint8* cert, vss_uint32 len)
{
#if (defined (_ENABLE_MIZAR_FOTA_)&&(_ENABLE_MIZAR_FOTA_ == 1U))
	vss_uint8 root[144];
	vss_uint32 rootLen = 0;
	vss_uint32 ret = 0;

	if(g_cVssAlg != ALG_GJ && g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;
	
	if (cert == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	ret = VssCertExport(CERT_TYPE_ROOT, &rootLen, root, VSS_NULL);
	if(ret)
		return ret;

	if(rootLen != ROOT_CERT_SIZE)
		return ERR_LEN_INVALID;

	ret =  _cert_verify(cert, len, root, rootLen);
	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssCertPkEnc(vss_uint8* in, vss_uint32 inLen, vss_uint8 *cert, vss_uint32 certLen, 
	vss_uint8* out, vss_uint32* pOutLen)
{
#if (defined (_ENABLE_MIZAR_FOTA_)&&(_ENABLE_MIZAR_FOTA_ == 1U))
	if(g_cVssAlg != ALG_GJ && g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;
	
	if (in == VSS_NULL || cert == VSS_NULL || out == VSS_NULL || pOutLen == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	if(inLen > 64)
		return ERR_LEN_INVALID;

	return  _cert_encrypt(cert, certLen, in, inLen, out, pOutLen);
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 VssResetSessionKey(void)
{
#if (defined (_ENABLE_MIZAR_FOTA_)&&(_ENABLE_MIZAR_FOTA_ == 1U))
	vss_uint32 ret = 0;

	if(g_cVssAlg != ALG_GJ && g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;
	
	ret = VssImportSessKey(16, (vss_uint8*)vssapi_SESS_KEY);
	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssVerifyCertValid(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow)
{
#if (defined (_ENABLE_MIZAR_FOTA_)&&(_ENABLE_MIZAR_FOTA_ == 1U))
	vss_uint32 ret = 0;
	vss_uint32 year;
	vss_uint32 month;
	vss_uint32 day;
	vss_uint32 cert_time;
 	vss_uint32 local_time;
	CertInfo ci;
	
	if(g_cVssAlg != ALG_GJ && g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;

	if (cert == VSS_NULL || szDataNow == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	ret = VssVerifyCert(cert, len);
	if(ret)
		return ret;

	ret = _cert_decode(cert, len, &ci);
	if(ret)
	{
		return ret;
	}

	year = 2000 + _cert_get_cn_real_value(ci.expire[0]);
	month = _cert_get_cn_real_value(ci.expire[1]);
	day = _cert_get_cn_real_value(ci.expire[2]);
	cert_time = year * 10000 + month * 100 + day;
 	local_time = VssA2I(szDataNow);

	if( local_time <= cert_time)
	{
		ret = 1;
	}
	else
	{
		ret = 0;
	}
	
	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssVerifyToolCert(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow)
{
#if (defined (_ENABLE_MIZAR_FOTA_)&&(_ENABLE_MIZAR_FOTA_ == 1U))
	vss_uint32 ret = 0;
	vss_uint32 year;
	vss_uint32 month;
	vss_uint32 day;
	vss_uint32 cert_time;
 	vss_uint32 local_time;
 	vss_uint8 root[ROOT_CERT_SIZE];
 	vss_uint32 rootLen;
	vss_uint32 certId;
	CertInfo ci;
	
	if(g_cVssAlg != ALG_GJ && g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;

	if (cert == VSS_NULL || szDataNow == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	if (cert == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	if(g_cVssAlg == ALG_GJ)
		certId = 4;
	else
		certId = 5;
	
	ret = LoadCertById(certId, &rootLen, root);
	if(ret)
		return ret;

	if(rootLen != ROOT_CERT_SIZE)
		return ERR_LEN_INVALID;

	ret =  _cert_verify(cert, len, root, rootLen);
	if(ret)
		return ret;

	ret = _cert_decode(cert, len, &ci);
	if(ret)
	{
		return ret;
	}

	year = 2000 + _cert_get_cn_real_value(ci.expire[0]);
	month = _cert_get_cn_real_value(ci.expire[1]);
	day = _cert_get_cn_real_value(ci.expire[2]);
	cert_time = year * 10000 + month * 100 + day;
 	local_time = VssA2I(szDataNow);

	if( local_time <= cert_time)
	{
		ret = 0;
	}
	else
	{
		ret = 1;
	}
	
	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssVerifyEcuSign(vss_char8* szEcuFileName)
{
	return ERR_NOT_SUPPORT;
}

vss_uint32 VssVerifyEcuSignWithHead(vss_char8* szEcuFileName, vss_uint32 headSize, vss_uint32 certOffset, vss_uint32 signOffset)
{
	return ERR_NOT_SUPPORT;
}

vss_uint32 VssCalcEcu(vss_char8* szSrcEcuFile, vss_char8* szDestEcuFile, vss_uint32 calcFlag, vss_uint8* key, vss_uint32 srcOffset)
{
	return ERR_NOT_SUPPORT;
}

vss_uint32 VssCalcEcu64(vss_char8* szSrcEcuFile, vss_char8* szDestEcuFile, vss_uint32 calcFlag, vss_uint8* key, vss_uint32 srcOffset)
{
	return ERR_NOT_SUPPORT;
}

vss_uint32 VssCalcEcuNoLen(vss_char8* szSrcEcuFile, vss_char8* szDestEcuFile, vss_uint32 calcFlag, vss_uint8* key)
{
	return ERR_NOT_SUPPORT;
}


vss_uint32 VssGetChipID(vss_uint8 * szKeyX, vss_uint8 * szKeyY,vss_uint8* szChipCode)
{	
	return ERR_NOT_SUPPORT;
}

vss_uint32 VssUpdateMasterKey(vss_uint8* szMkey,vss_uint8* szMac,vss_uint8* szCV)
{	
	return ERR_NOT_SUPPORT;
}

vss_uint32 VssChipWrite(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data)
{
	vss_uint32 ret = 0;

	if(data == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	if(g_vssIocb == VSS_NULL)
	{
		return ERR_SYSTEM_INIT;
	}
		
	ret = g_vssIocb(FLASH_IO_WRITE, FLASH_EXTDATA_OFFSET+ offset, data, dataLen);
	return ret;
}

vss_uint32 VssChipRead(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data)
{
	vss_uint32 ret = 0;

	if(data == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	if(g_vssIocb == VSS_NULL)
	{
		return ERR_SYSTEM_INIT;
	}
	
	ret = g_vssIocb(FLASH_IO_READ, FLASH_EXTDATA_OFFSET + offset, data, dataLen);
	return ret;
}

vss_uint32 VssAsymGenKey(vss_uint8 *szX, vss_uint8 *szY, vss_uint8 *szSK)
{
	vss_uint32 ret = 0;

	if(g_cVssAlg == ALG_GJ)
	{
		ret = VssECCGenKey(szX, szY, szSK);
	}
	else
	{
		ret = VssSM2GenKey(szX, szY, szSK);

	}

	return ret;
}

vss_uint32 VssAsymGenKeyIndex(vss_uint8 *szX, vss_uint8 *szY)
{
	vss_uint32 ret = 0;
	
	if(g_cVssAlg == ALG_GJ)
	{
		ret = VssECCGenKeyIndex(szX, szY);
	}
	else
	{
		ret = VssSM2GenKeyIndex(szX, szY);
	}
	
	return ret;
}


vss_uint32 VssAsymmCalc(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
	vss_uint32 ret = 0;
	
	if(g_cVssAlg == ALG_GJ)
	{
		ret = VssECCCalc(szInData, nInLen, calcFlag, szKey, nKeyLen, szOutData, pOutLen);
	}
	else
	{
		ret = VssSM2Calc(szInData, nInLen,calcFlag,  szKey,nKeyLen, szOutData, pOutLen);
	}
	
	return ret;
}

vss_uint32 VssAsymmCalcIndex(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
	vss_uint32 ret = 0;
	
	if(g_cVssAlg == ALG_GJ)
	{
		ret = VssECCCalcIndex(szInData, nInLen, calcFlag, szOutData, pOutLen);
	}
	else
	{
		ret = VssSM2CalcIndex(szInData, nInLen, calcFlag, szOutData, pOutLen);
	}
	return ret;
}

vss_uint32 VssSign(vss_uint8* key, vss_uint32 keyLen,vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
	vss_uint32 ret = 0;
	if(keyLen != 32)
	{
		return ERR_KEYLEN_INVALID;
	}

	if(g_cVssAlg == ALG_GJ)
	{
		ret = VssECCSign(key, data,  dataLen, hashFlag,sig);
	}
	else
	{
		ret = VssSM2Sign(key, data,dataLen, hashFlag, sig);
	}
	
	return ret;
}

vss_uint32 VssSignIndex(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
	vss_uint32 ret = 0;

	if(g_cVssAlg == ALG_GJ)
	{
		ret = VssECCSignIndex(data,dataLen,hashFlag,sig);
	}
	else
	{
		ret = VssSM2SignIndex(data,dataLen,hashFlag,sig);
	}
	
	return ret;
}

vss_uint32 VssSignVerify(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
	vss_uint32 ret = 0;

	if(g_cVssAlg == ALG_GJ)
	{
		ret = VssECCVerify(pk, data, dataLen, hashFlag, sig);
	}
	else
	{
		ret = VssSM2Verify(pk, data, dataLen, hashFlag, sig);
	}
	
	return ret;
}

vss_uint32 VssSymmCalc(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
	vss_uint32 ret = 0;

	if(g_cVssAlg == ALG_GJ)
	{
		ret = VssAESCalc( in,  inLen,  key,  keyLen,  calcFlag, padding,  out,  pOutLen);
	}
	else
	{
		ret = VssSM4Calc( in,  inLen,  key,  keyLen,  calcFlag,  padding, out,  pOutLen);
	}
	
	return ret;
}

vss_uint32 VssSymmCalcIndex(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
	vss_uint32 ret = 0;


	if(g_cVssAlg == ALG_GJ)
	{
		ret = VssAESCalcIndex( index,  in,  inLen,  calcFlag,  padding, out,  pOutLen);
	}
	else
	{
		ret = VssSM4CalcIndex( index,  in,  inLen,  calcFlag,  padding,  out,  pOutLen);
	}
	
	return ret;
}


vss_uint32 VssMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
	vss_uint32 ret = 0;
	
	if(g_cVssAlg == ALG_GJ)
	{
		ret = VssAESMac( in,  inLen,  key,  keyLen, out);
	}
	else
	{
		ret = VssSM4Mac( in,  inLen,  key,  keyLen, out);
	}
	
	return ret;
}

vss_uint32 VssCMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
	vss_uint32 ret = 0;

	if(g_cVssAlg == ALG_GJ)
	{
		ret = VssAESCMac( in,  inLen,  key,  keyLen, out);
	}
	else
	{
		ret = VssSM4CMac( in,  inLen,  key,  keyLen, out);
	}

	return ret;
}

vss_uint32 VssCMacIndex(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out)
{
	vss_uint32 ret = 0;

	if(g_cVssAlg == ALG_GJ)
	{
		ret = VssAESCMacIndex( in,  inLen,  index,  out);
	}
	else
	{
		ret = VssSM4CMacIndex( in,  inLen,  index,  out);
	}

	return ret;
}

vss_uint32 VssSetWroteFlag(void)
{
	return SetWroteFlag(1);
}


vss_uint32 VssGetWroteFlag(vss_uint8* wroteFlag)
{
	if(wroteFlag == VSS_NULL)
		return ERR_PARAMETER;
	
	return GetWroteFLag(wroteFlag);
}

vss_uint32 VssUpdateEnvironment(vss_uint32 env)
{
	return SetEnvironment(env);
}

vss_uint32 VssGetEnvironment(vss_uint32* env)
{
	vss_uint32 ret = 0;
	if(env == VSS_NULL)
		return ERR_PARAMETER;
	
	ret = GetEnvironment();
	if(ret)
		return ret;

	*env = g_cVssEnv;
	return 0;
}

vss_uint32 VssGetChipVersion(vss_char8* version)
{
	return ERR_NOT_SUPPORT;
}

vss_uint32 VssGetChipState(vss_uint32* state)
{
	return ERR_NOT_SUPPORT;
}

vss_uint32 VssGetVersion(vss_char8* version)
{
	if(version == VSS_NULL)
		return ERR_PARAMETER;
	
	mem_cpy8(version, VSS_VERSION, 16);
	return 0;
}

vss_uint32 VssSetCfgPath(vss_char8* szPath)
{
	return ERR_NOT_SUPPORT;
}

vss_uint32 VssVerifySignCertValid(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow)
{
#if (defined (_ENABLE_MIZAR_FOTA_)&&(_ENABLE_MIZAR_FOTA_ == 1U))
	vss_uint32 ret = 0;
	vss_uint32 year;
	vss_uint32 month;
	vss_uint32 day;
	vss_uint32 cert_time;
	vss_uint32 local_time;
	vss_uint8 root_idx;
	vss_uint8 root[256];
	vss_uint32 root_len;
	CertInfo ci;
	
	if(g_cVssAlg != ALG_GJ && g_cVssAlg != ALG_GM)
		return ERR_SYSTEM_INIT;

	if (cert == VSS_NULL || szDataNow == VSS_NULL)
	{
		return ERR_PARAMETER;
	}

	ret = _cert_decode(cert, len, &ci);
	if(ret)
	{
		return ret;
	}

	if(g_cVssAlg == ALG_GJ)
	{
		if(ci.root == 0x11 || ci.root == 0x21)
			return ERR_DATA_INVALID;
	}

	if(g_cVssEnv == ENV_P)
	{
		if(ci.root == 0x10 || ci.root == 0x11)
			return ERR_DATA_INVALID;
	}

	if(ci.root == 0x10)			root_idx = 0;
	else if(ci.root == 0x11)	root_idx = 1;
	else if(ci.root == 0x20)	root_idx = 2;
	else if(ci.root == 0x21)	root_idx = 3;
	else if(ci.root == 0x40)	root_idx = 4;
	else if(ci.root == 0x41)	root_idx = 5;
	else						return ERR_DATA_INVALID;
	
	ret = LoadCertById(root_idx, &root_len, root);
	if(ret)
		return ret;
	
	if(root_len != ROOT_CERT_SIZE)
		return ERR_LEN_INVALID;
	
	ret = _cert_verify(cert, len, root, root_len);
	if(ret)
		return ret;

	year = 2000 + _cert_get_cn_real_value(ci.expire[0]);
	month = _cert_get_cn_real_value(ci.expire[1]);
	day = _cert_get_cn_real_value(ci.expire[2]);
	cert_time = year * 10000 + month * 100 + day;
 	local_time = VssA2I(szDataNow);

	if( local_time <= cert_time)
	{
		ret = 1;
	}
	else
	{
		ret = 0;
	}
	
	return ret;
#else
	return ERR_NOT_SUPPORT;
#endif	
}

vss_uint32 VssGetKeyActive(vss_uint32 keyId, vss_uint8* valid)
{
	vss_uint32 ret = 0;

	if(valid == VSS_NULL)
		return ERR_PARAMETER;
	
	if(keyId < COMM_KEY || keyId >= RESV_KEY)
		return ERR_INDEX_INVALID;

	ret = GetKeyActive(keyId, valid);
	
	return ret;
}


#pragma section code restore


