/*******************************************************************************
 * Copyright (C) 2023 Technomous. All rights reserved         *
 ******************************************************************************/
/**
 *  \file
 *      Secure.c
 *  \brief
 *      The secure boot.
 *	\author
 *		Ou Hengyue
 */

#include "Secure.h"
#include "Appl.h"
#include "SecM.h"
#include "FL.h"
#include "VssApiIndirect.h"
#include "eeprom_Cfg.h"

#define HEADER_TYPE_APP 1
#define HEADER_TYPE_CAL 2

static Secure_BypassLicenseType SecureBypassLic;
static Secure_StateTypes SecureState;
static uint8 Secure_HeaderType;
static uint8 SecureDownloadIdx = FL_NUM_LOGICAL_BLOCKS;
static char szDateNow[8];
Secure_SignHeaderType SecureSignHeader;
uint16 SecureSignHeaderIdx = 0;

Secure_IntlSignDigestType SecureIntlSignDigests[5];
Secure_NtlSignDigestType SecureNtlSignDigests[5];
void Secure_Init(void)
{
    // VssCryptoInit(0, 0, NULL, NULL);
    SecureDownloadIdx = FL_NUM_LOGICAL_BLOCKS;
    SecureSignHeaderIdx = 0;
    Secure_HeaderType = 0;
}

Std_ReturnType Secure_SaveSignHeader(uint8 *data, uint16 length, uint8 headerType)
{
    Std_ReturnType ret = SECURE_FAILED;
    uint8 RequestResultPtr = 0;
    Secure_HeaderType = headerType;

    switch (Secure_HeaderType)
    {
    case HEADER_TYPE_APP:
        if (SecureSignHeaderIdx < SECURE_HEADER_LEN)
        {
            if (SecureSignHeaderIdx + length > SECURE_HEADER_LEN)
            {
                length = SECURE_HEADER_LEN - SecureSignHeaderIdx;
            }
            Appl_Memcpy(&SecureSignHeader.datas + SecureSignHeaderIdx, data, length);
            SecureSignHeaderIdx += length;
            ret = SECURE_PASS;
            /* Check if the header downloading is completed */
            if (SecureSignHeaderIdx >= SECURE_HEADER_LEN)
            {
                ret = SECURE_SAVE_FINISH;
                /* Save the file digest to the NVM. */
                Appl_Memcpy(&Ram_File_Digest, &SecureSignHeader.app_parameters.msgDigestIntl, SECURE_HASH_LEN);
                Appl_Memcpy(&Ram_File_Digest[SECURE_HASH_LEN], &SecureSignHeader.app_parameters.msgDigestNtl, SECURE_HASH_LEN);
                // NvM_WriteBlock(NvMConf_NvMBlockDescriptor_CpApBoot_Security_Refresh_File_Degest, NULL);
                // NvM_WriteAll();
                // do
                // {
                //     Appl_UpdateTriggerCondition();
                //     NvM_MainFunction();
                //     Fee_MainFunction();
                //     Fls_17_Dmu_MainFunction();
                //     NvM_GetErrorStatus(NvMConf___MultiBlockRequest, &RequestResultPtr);
                // } while (RequestResultPtr == NVM_REQ_PENDING);
            }
        }
        else
        {
            ret = SECURE_SAVE_OVERFLOW;
        }
        break;
    case HEADER_TYPE_CAL:

        if (SecureSignHeaderIdx < SECURE_CAL_HEADER_LEN)
        {
            if (SecureSignHeaderIdx + length > SECURE_CAL_HEADER_LEN)
            {
                length = SECURE_CAL_HEADER_LEN - SecureSignHeaderIdx;
            }

            Appl_Memcpy(&SecureSignHeader.datas[0] + SecureSignHeaderIdx, data, length);
            
            SecureSignHeaderIdx += length;
            ret = SECURE_PASS;
            /* Check if the header downloading is completed */
            if (SecureSignHeaderIdx >= SECURE_CAL_HEADER_LEN)
            {
                ret = SECURE_SAVE_FINISH;
                /* Save the file digest to the NVM. */
                Appl_Memcpy(&Ram_File_Digest, &SecureSignHeader.cal_parameters.msgDigestIntl, SECURE_HASH_LEN);
                Appl_Memcpy(&Ram_File_Digest[SECURE_HASH_LEN], &SecureSignHeader.cal_parameters.msgDigestNtl, SECURE_HASH_LEN);
            //     NvM_WriteBlock(NvMConf_NvMBlockDescriptor_CpApBoot_Security_Refresh_File_Degest, NULL);
            //     NvM_WriteAll();
            //     do
            //     {
            //         Appl_UpdateTriggerCondition();
            //         NvM_MainFunction();
            //         Fee_MainFunction();
            //         Fls_17_Dmu_MainFunction();
            //         NvM_GetErrorStatus(NvMConf___MultiBlockRequest, &RequestResultPtr);
            //     } while (RequestResultPtr == NVM_REQ_PENDING);
            }
        }
        else
        {
            ret = SECURE_SAVE_OVERFLOW;
        }

        break;
    default:
        break;
    }

    return ret;
}

uint8 Secure_GetDownloadIdx(void)
{
    return SecureDownloadIdx;
}

Std_ReturnType Secure_GetDate(char* date)
{
    uint8 RequestResultPtr = 0;
    Std_ReturnType ret = SECURE_SIGN_VERIF_FAIL;
//    NvM_ReadBlock(EEP_SECURITY_DATE_INFORMATION, Ram_Date_Information);
//    do
//	{
//	   NvM_MainFunction();
//	   Fee_MainFunction();
//	   Fls_17_Dmu_MainFunction();
//	   NvM_GetErrorStatus(EEP_SECURITY_DATE_INFORMATION, &RequestResultPtr);
//	}while(RequestResultPtr == NVM_REQ_PENDING);

    if ((Ram_Date_Information[0] < 255u) && (Ram_Date_Information[1] <= 12) && (Ram_Date_Information[2] <= 31))
    {
        date[0] = '2';
        date[1] = (char)(Ram_Date_Information[0] / 100 + 0x30u);
        date[2] = (char)((Ram_Date_Information[0] % 100) / 10 + 0x30u);
        date[3] = (char)(Ram_Date_Information[0] % 10 + 0x30u);
        date[4] = (char)(Ram_Date_Information[1] / 10 + 0x30u);
        date[5] = (char)(Ram_Date_Information[1] % 10 + 0x30u);
        date[6] = (char)(Ram_Date_Information[2] / 10 + 0x30u);
        date[7] = (char)(Ram_Date_Information[2] % 10 + 0x30u);
        ret = SECURE_OK;
    }
    return ret;
}

//for test key
//unsigned char  RootPublickey[64] =
//{
//		0x9F, 0xB2, 0xCB, 0xA5, 0x65, 0x9D, 0x2E, 0xF8, 0xAA, 0x57, 0x73,
//		0x35, 0x5E, 0xC0, 0xC8, 0x12, 0xC2, 0xE8, 0xB1, 0xC1, 0x03, 0xBE,
//		0x46, 0x18, 0xCF, 0x0F, 0x3D, 0xC0, 0xA3, 0x35, 0x4D, 0x81, 0x94,
//		0x0E, 0x4A, 0xC4, 0xB7, 0x05, 0x70, 0xEA, 0xFA, 0xD7, 0x0E, 0x08,
//		0x22, 0xD4, 0x6B, 0xE1, 0x55, 0x65, 0x9E, 0x2A, 0xD3, 0x7D, 0x27,
//		0x78, 0x29, 0xA3, 0x45, 0x0B, 0x2C, 0x27, 0xF0, 0x33
//};

unsigned char RootPublickey[64] = {
    0x90, 0x7A, 0x89, 0x39, 0x64, 0xEC, 0x2E, 0x88,
    0xFD, 0xB0, 0x60, 0x9E, 0x4B, 0xF0, 0x62, 0xE5,
    0x68, 0xF2, 0xA6, 0x5E, 0x09, 0x15, 0x7A, 0x03,
    0x26, 0x53, 0xBE, 0x99, 0xE2, 0x7C, 0xB9, 0x93,
    0xCD, 0xC9, 0xA4, 0x3B, 0x51, 0x4F, 0x58, 0xD9,
    0x60, 0xA7, 0xF0, 0x65, 0x9A, 0x5C, 0xD3, 0x55,
    0x9A, 0xEA, 0x61, 0xB4, 0xA8, 0xF2, 0xD5, 0x9B,
    0x16, 0x0C, 0x95, 0x53, 0x75, 0x92, 0x5F, 0x1B
};

unsigned char RootPublickeySha256[32] = {
    0x18,0x1C,0x21,0xA1,0xB8,0x72,0xF1,0x51,
    0xC6,0x4D,0xBD,0x18,0x8E,0xC1,0x31,0xBB,
    0xBF,0x03,0x49,0x42,0xEF,0x96,0x07,0x58,
    0xC5,0xA2,0x4D,0xD6,0x3E,0x79,0x63,0x10
};
unsigned char RootPublickeyLen = 64;
uint8 rootdata[100];
uint8 rootCertSigature[64];
Std_ReturnType Secure_SignHeaderChk(uint8* fileIdx)
{
    /*
    1. Chek the module ID.
    2. Check the signature information field.
    3. Check the signature of header.
    4. Return the corresponding error code.*/

    Std_ReturnType errorCode = SECURE_ERRORSEQUENCE;
    Std_ReturnType checkRet;

    switch (Secure_HeaderType)
    {
    case HEADER_TYPE_APP:
        errorCode = SECURE_OK;
        /* Check the module ID */
        if (0xA1u == SecureSignHeader.app_parameters.moduleId[0])
        {
            checkRet = FL_CheckModuleId(SecureSignHeader.app_parameters.moduleId[1], &SecureSignHeader.app_parameters.locInfo, &SecureDownloadIdx);
            *fileIdx = SecureDownloadIdx;
            if (FL_MODULEID_UNMATCH == checkRet)
            {
                errorCode = SECURE_MODULEID_UNMATCH;
            }
            else if (FL_OUT_OF_RANGE == checkRet)
            {
                errorCode = SECURE_OUT_OF_RANGE;
            }
        }
        else
        {
            errorCode = SECURE_MODULEID_UNMATCH;
        }

        if (SECURE_OK == errorCode)
        {
            errorCode = Secure_GetDate(szDateNow);
        }
        if (SECURE_OK == errorCode)
        {
            errorCode = Secure_RootPublickeyHashVerification((uint32)(&RootPublickey[0]), (uint32)RootPublickeyLen);
        }
        /* Check the signature */
        if (SECURE_OK == errorCode)
        {
        	vss_uint32 certLen=164;
           // checkRet = VssVerifySignCertValid((vss_uint8 *)SecureSignHeader.app_parameters.signerInfoIntl, (vss_uint32)164, (vss_char8 *)szDateNow);

        	checkRet=VssECCVerify(RootPublickey ,(vss_uint8 *)SecureSignHeader.app_parameters.signerInfoIntl, certLen - 64, 1, SecureSignHeader.app_parameters.signerInfoIntl + certLen - 64);
           // if (0x01u == checkRet)
            if (0x00u == checkRet)
            {
                uint8 publicKey[SECURE_PUBLIC_KEY_LEN];
             //   Appl_Memcpy(&publicKey, &SecureSignHeader.app_parameters.signerInfoNtl[SECURE_CERT_SUB_INFO_LEN], SECURE_PUBLIC_KEY_LEN);
             //   uint8 signaData[SECURE_HEADER_TO_SIGNA_DATA_LEN];
              //  Appl_Memcpy(&signaData, &SecureSignHeader.datas, SECURE_HEADER_SUB_INFO_LEN);
              //  Appl_Memcpy(&signaData[SECURE_HEADER_SUB_INFO_LEN], &SecureSignHeader.app_parameters.signerInfoNtl, (SECURE_CERT_TOTAL_LEN + SECURE_HASH_LEN));
             //   checkRet = VssSignVerify(&publicKey, &signaData, SECURE_HEADER_TO_SIGNA_DATA_LEN, 1, &SecureSignHeader.app_parameters.signNtl);


                 Appl_Memcpy(&publicKey, &SecureSignHeader.app_parameters.signerInfoIntl[SECURE_CERT_SUB_INFO_LEN], SECURE_PUBLIC_KEY_LEN);
                 uint8 signaData[SECURE_HEADER_TO_SIGNA_DATA_LEN];
                 Appl_Memcpy(&signaData, &SecureSignHeader.datas, SECURE_HEADER_SUB_INFO_LEN);
                 Appl_Memcpy(&signaData[SECURE_HEADER_SUB_INFO_LEN], &SecureSignHeader.app_parameters.signerInfoIntl, (SECURE_CERT_TOTAL_LEN + SECURE_HASH_LEN));
                 checkRet = VssSignVerify(&publicKey, &signaData, SECURE_HEADER_TO_SIGNA_DATA_LEN, 1, &SecureSignHeader.app_parameters.signIntl);

                if (0x00u == checkRet)
                {
                    errorCode = SECURE_OK;
                  //  SecureNtlSignDigests[0].chkResult = 1;
                  //  Appl_Memcpy(&SecureNtlSignDigests[0].digest[0], &Ram_File_Digest[SECURE_HASH_LEN], SECURE_HASH_LEN);


                    SecureIntlSignDigests[0].chkResult = 1;
                    Appl_Memcpy(&SecureIntlSignDigests[0].digest[0], &Ram_File_Digest, SECURE_HASH_LEN);
                }
                else
                {
                    errorCode = SECURE_SIGN_VERIF_FAIL;
                }
            }
            else
            {
                errorCode = SECURE_CERT_VERIF_FAIL;
            }
        }
        break;
    // case HEADER_TYPE_CAL:
    //     errorCode = SECURE_OK;
    //     /* Check the module ID */
    //     if (0xA1u == SecureSignHeader.cal_parameters.moduleId[0])
    //     {
    //         checkRet = FL_CheckModuleId(SecureSignHeader.cal_parameters.moduleId[1], &SecureSignHeader.cal_parameters.locInfo, &SecureDownloadIdx);
    //         *fileIdx = SecureDownloadIdx;
    //         if (FL_MODULEID_UNMATCH == checkRet)
    //         {
    //             errorCode = SECURE_MODULEID_UNMATCH;
    //         }
    //         else if (FL_OUT_OF_RANGE == checkRet)
    //         {
    //             errorCode = SECURE_OUT_OF_RANGE;
    //         }
    //     }
    //     else
    //     {
    //         errorCode = SECURE_MODULEID_UNMATCH;
    //     }

    //     if (SECURE_OK == errorCode)
    //     {
    //         errorCode = Secure_GetDate(szDateNow);
    //     }

    //     /* Check the signature */
    //     if (SECURE_OK == errorCode)
    //     {
    //         checkRet = VssVerifySignCertValid((vss_uint8 *)SecureSignHeader.cal_parameters.signerInfoIntl, (vss_uint32)164, (vss_char8 *)szDateNow);
    //         if (0x01u == checkRet)
    //         {
    //             uint8 publicKey[SECURE_PUBLIC_KEY_LEN];
    //             Appl_Memcpy(&publicKey, &SecureSignHeader.cal_parameters.signerInfoIntl[SECURE_CERT_SUB_INFO_LEN], SECURE_PUBLIC_KEY_LEN);

    //             uint8 signaData[SECURE_CAL_HEADER_TO_SIGNA_DATA_LEN];
    //             Appl_Memcpy(&signaData, &SecureSignHeader.datas, SECURE_CAL_HEADER_SUB_INFO_LEN);
    //             Appl_Memcpy(&signaData[SECURE_CAL_HEADER_SUB_INFO_LEN], &SecureSignHeader.cal_parameters.signerInfoIntl, (SECURE_CERT_TOTAL_LEN + SECURE_HASH_LEN));

    //             checkRet = VssSignVerify(&publicKey[0], &signaData[0], SECURE_CAL_HEADER_TO_SIGNA_DATA_LEN, 1, &SecureSignHeader.cal_parameters.signIntl);
    //             if (0x00u == checkRet)
    //             {
    //                 errorCode = SECURE_OK;
    //                 SecureIntlSignDigests[SecureDownloadIdx].chkResult = 1;
    //                 Appl_Memcpy(&SecureIntlSignDigests[SecureDownloadIdx].digest[0], &Ram_File_Digest, SECURE_HASH_LEN);
    //             }
    //             else
    //             {
    //                 errorCode = SECURE_SIGN_VERIF_FAIL;
    //             }
    //         }
    //         else
    //         {
    //             errorCode = SECURE_CERT_VERIF_FAIL;
    //         }
    //     }
    //     break;
    default:
        break;
    }
    return errorCode;
}

Std_ReturnType Secure_CheckSumForHeader(uint16 * crcValPtr)
{
    /* Try: Calculate the crc16 of header in the positive reponse of 37. */
    Std_ReturnType ret = SECURE_OK;
    SecM_VerifyParamType verifyParam;
	SecM_StatusType crcRet = SECM_OK;
    FL_SegmentListType headerSegmentList;

    headerSegmentList.nrOfSegments = 1;
    headerSegmentList.segmentInfo[0].address = (uint32)&SecureSignHeader.datas[0]; /* The way pointer called TBD */
    // headerSegmentList.segmentInfo[0].length = (HEADER_TYPE_APP == Secure_HeaderType) ? SECURE_HEADER_LEN : SECURE_CAL_HEADER_LEN;
    headerSegmentList.segmentInfo[0].length = SECURE_HEADER_LEN;
    verifyParam.segmentList = &headerSegmentList;

    crcRet=SecM_CRC_Calculate(&verifyParam);
	if(crcRet==SECM_OK)
	{
		*crcValPtr = verifyParam.crcTotle;
	}
	else
	{
		ret = SECURE_FAILED;
	}

	return ret;
}
uint8 computeDigest[32] = {0};
uint32 Hashlength;
Std_ReturnType Secure_HashVerification(uint32 address, uint32 length, uint8 index)
{
    /* To-Do: Calculate the hash of corresponding block. Compare with the file digest */
    /* To-Do: Add the module Id in the beginning */
    Std_ReturnType ret = SECURE_PASS;
    //uint8 computeDigest[32] = {0};
    uint8 headerDigest[32] = {0};
    uint8 RequestResultPtr = 0;

    VssHash((uint8 *)address, length, &computeDigest[0], &Hashlength);

    // NvM_ReadBlock(NvMConf_NvMBlockDescriptor_CpApBoot_Security_Refresh_File_Degest, Ram_File_Digest);
    // do
	// {
	//    NvM_MainFunction();
	//    Fee_MainFunction();
	//    Fls_17_Dmu_MainFunction();
	//    NvM_GetErrorStatus(EEP_SECURITY_DATE_INFORMATION, &RequestResultPtr);
	// }while(RequestResultPtr == NVM_REQ_PENDING);

    for(uint8 i = 0; i < 32; i++)
    {
        if(computeDigest[i] != SecureIntlSignDigests[0].digest[i])
        {
            ret = SECURE_FAILED;
            break;
        }
    }
    return ret;
}
Std_ReturnType Secure_RootPublickeyHashVerification(uint32 address, uint32 length)
{
    /* To-Do: Calculate the hash of corresponding block. Compare with the file digest */
    /* To-Do: Add the module Id in the beginning */
    Std_ReturnType ret = SECURE_PASS;
    //uint8 computeDigest[32] = {0};
    uint8 headerDigest[32] = {0};
    uint8 RequestResultPtr = 0;

    VssHash((uint8 *)address, length, &computeDigest[0], &Hashlength);

    for(uint8 i = 0; i < 32; i++)
    {
        if(computeDigest[i] != RootPublickeySha256[i])
        {
            ret = SECURE_CERT_VERIF_FAIL;
            break;
        }
    }
    return ret;
}
Std_ReturnType Secure_CheckSecureState(void)
{

}
