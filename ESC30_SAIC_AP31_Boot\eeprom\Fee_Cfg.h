/******************************************************************************
**                                                                           **
** Copyright (C) Infineon Technologies (2013)                                **
**                                                                           **
** All rights reserved.                                                      **
**                                                                           **
** This document contains proprietary information belonging to Infineon      **
** Technologies. Passing on and copying of this document, and communication  **
** of its contents is not permitted without prior written authorization.     **
**                                                                           **
*******************************************************************************
**                                                                           **
**  $FILENAME   : Fee_Cfg.h $                                                **
**                                                                           **
**  $CC VERSION : \main\26 $                                                 **
**                                                                           **
**  DATE, TIME: 2021-10-28, 12:21:17                                         **
**                                                                           **
**  GENERATOR : Build b141014-0350                                           **
**                                                                           **
**  AUTHOR    : DL-AUTOSAR-Engineering                                       **
**                                                                           **
**  VENDOR    : Infineon Technologies                                        **
**                                                                           **
**  DESCRIPTION  : FEE configuration generated out of ECU configuration      **
**                   file (Fee.bmd)                                          **
**                                                                           **
**  MAY BE CHANGED BY USER [yes/no]: No                                      **
**                                                                           **
******************************************************************************/

#ifndef  FEE_CFG_H
#define  FEE_CFG_H

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/

/* Typedefs Imported from Memory Abstract Interface */ 
#include "MemIf_Types.h"

/* Callback functions imported from NvM Module */
#include "NvM_Cbk.h"

/* Functions imported from Fls Module */
#include "Fls_17_Pmu.h"

/*******************************************************************************
**                      Private Macro Definitions                             **
*******************************************************************************/

/* FEE AS version information */
#define FEE_AS_VERSION (403)
#define FEE_AR_RELEASE_MAJOR_VERSION  (4U)
#define FEE_AR_RELEASE_MINOR_VERSION  (0U)
#define FEE_AR_RELEASE_REVISION_VERSION  (3U)

/* Vendor specific implementation version information */
#define FEE_SW_MAJOR_VERSION  (2U)
#define FEE_SW_MINOR_VERSION  (6U)
#define FEE_SW_PATCH_VERSION  (0U)

/*******************************************************************************
**                      Global Function Declarations                          **
*******************************************************************************/
                        
/*******************************************************************************
**                    Static configuration parameters                         **
*******************************************************************************/

/* Development error detection enabled/disabled */
#define FEE_DEV_ERROR_DETECT       (STD_OFF)

/* Fee_GetVersionInfo API enabled/disabled */
#define FEE_VERSION_INFO_API       (STD_OFF)

/* Fee_GetCycleCount API enabled/disabled */
#define FEE_GET_CYCLE_COUNT_API    (STD_OFF)

/* Fee_SetMode API enabled/disabled */
#define FEE_SET_MODE_SUPPORTED     (STD_OFF)

/* Fee_17_GetPrevData API enabled/disabled */
#define FEE_GET_PREV_DATA_API      (STD_OFF)

#define FEE_MAX_BYTES_PER_CYCLE      (512U)
/* FEE reaches illegal state upon detection virgin flash */
#define FEE_VIRGIN_FLASH_ILLEGAL_STATE      (STD_ON)

/* Enable/Disable Debug support  */
#define FEE_DEBUG_SUPPORT     (STD_OFF)

/* Erase suspend/resume feature supported in FLS */
#define FEE_FLS_SUPPORTS_ERASE_SUSPEND  (STD_OFF)

/* DFlash WordLine size */
#define FEE_DFLASH_WORDLINE_SIZE     (512U)

#define FEE_CONTINUE          (0U)
#define FEE_STOP_AT_GC        (1U)

#define FEE_UNCFG_BLK_OVERFLOW_HANDLE    (FEE_CONTINUE)

/* Virtual page size, i.e., DF_EEPROM page size */
#define FEE_VIRTUAL_PAGE_SIZE      (8U)

/* Logical block's overhead in bytes */
#define FEE_BLOCK_OVERHEAD         (17U)

/* Logical block's data page overhead in bytes */
#define FEE_PAGE_OVERHEAD          (1U)

/* Maximum blocking (delay) time in ms */
#define FEE_MAXIMUM_BLOCKING_TIME  (10U)

/* Maximum number of configured blocks to be handled */
#define FEE_MAX_BLOCK_COUNT        (102U)

/* Symbolic names of logical blocks */
#ifdef FeeConf_FeeBlockConfiguration_FeeBlockConfiguration 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeBlockConfiguration already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeBlockConfiguration ((uint16)16)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeBlockConfiguration */

#ifdef FeeConf_FeeBlockConfiguration_FeeBlockConfiguration_cons0 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeBlockConfiguration_cons0 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeBlockConfiguration_cons0 ((uint16)17)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeBlockConfiguration_cons0 */

#ifdef FeeConf_FeeBlockConfiguration_FeeBlock_Admin 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeBlock_Admin already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeBlock_Admin ((uint16)32)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeBlock_Admin */

#ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary0 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary0 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary0 ((uint16)48)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary0 */

#ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary1 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary1 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary1 ((uint16)64)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary1 */

#ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary2 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary2 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary2 ((uint16)80)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary2 */

#ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary3 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary3 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary3 ((uint16)96)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary3 */

#ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary4 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary4 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary4 ((uint16)112)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary4 */

#ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary5 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary5 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary5 ((uint16)128)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary5 */

#ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary6 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary6 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary6 ((uint16)144)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary6 */

#ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary7 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary7 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary7 ((uint16)160)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemPrimary7 */

#ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemStatus 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeBlock_DemStatus already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeBlock_DemStatus ((uint16)176)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeBlock_DemStatus */

#ifdef FeeConf_FeeBlockConfiguration_BeforeLastOpenInf 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_BeforeLastOpenInf already defined
#else 
#define FeeConf_FeeBlockConfiguration_BeforeLastOpenInf ((uint16)192)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_BeforeLastOpenInf */

#ifdef FeeConf_FeeBlockConfiguration_LastOpenInf 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_LastOpenInf already defined
#else 
#define FeeConf_FeeBlockConfiguration_LastOpenInf ((uint16)208)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_LastOpenInf */

#ifdef FeeConf_FeeBlockConfiguration_OpenReasonRecord 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_OpenReasonRecord already defined
#else 
#define FeeConf_FeeBlockConfiguration_OpenReasonRecord ((uint16)224)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_OpenReasonRecord */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageLowerThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageLowerThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageLowerThreshold ((uint16)1536)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageLowerThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageHigherThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageHigherThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageHigherThreshold ((uint16)240)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageHigherThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout ((uint16)256)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageHigherThresholdTimeout */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout ((uint16)272)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30OverVoltageLowerThresholdTimeout */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageHigherThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageHigherThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageHigherThreshold ((uint16)288)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageHigherThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold ((uint16)304)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageHigherThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout ((uint16)320)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageLowerThresholdTimeout */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold ((uint16)336)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageLowerThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout ((uint16)352)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageHigherThresholdTimeout */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold ((uint16)368)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_ROverVoltageHigherThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold ((uint16)384)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentLowerThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold ((uint16)400)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentHigherThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout ((uint16)416)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentHigherThresholdTimeout */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout ((uint16)432)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverCurrentLowerThresholdTimeout */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold ((uint16)448)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMMosfetOverTemperatureThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold ((uint16)464)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30OverVoltageThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold ((uint16)480)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30UnderVoltageThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold ((uint16)496)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30_ROverVoltageThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold ((uint16)512)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoveryKL30_RUnderVoltageThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoverytimes 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoverytimes already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoverytimes ((uint16)528)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMrecoverytimes */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SecurityAttemptCounter 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SecurityAttemptCounter already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SecurityAttemptCounter ((uint16)544)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SecurityAttemptCounter */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen ((uint16)560)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMVoltageStabilizationFilterTimeAfterItOpen */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMNormalVoltageMin 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMNormalVoltageMin already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMNormalVoltageMin ((uint16)592)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMNormalVoltageMin */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMNormalVoltageMax 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMNormalVoltageMax already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMNormalVoltageMax ((uint16)608)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMNormalVoltageMax */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMHardwareOpenOverVoltage 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMHardwareOpenOverVoltage already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMHardwareOpenOverVoltage ((uint16)624)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMHardwareOpenOverVoltage */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMOverCurrentHardwareOpenFilterTime 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMOverCurrentHardwareOpenFilterTime already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMOverCurrentHardwareOpenFilterTime ((uint16)640)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMOverCurrentHardwareOpenFilterTime */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMOverCurrentHardwareOpenThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMOverCurrentHardwareOpenThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMOverCurrentHardwareOpenThreshold ((uint16)656)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMOverCurrentHardwareOpenThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMShortCircuitHarewareOpenLoweVoltage 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMShortCircuitHarewareOpenLoweVoltage already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMShortCircuitHarewareOpenLoweVoltage ((uint16)672)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PGMShortCircuitHarewareOpenLoweVoltage */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve19 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve19 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve19 ((uint16)688)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve19 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve20 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve20 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve20 ((uint16)704)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve20 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve21 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve21 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve21 ((uint16)720)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve21 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve22 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve22 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve22 ((uint16)736)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve22 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve23 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve23 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve23 ((uint16)752)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve23 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve24 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve24 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve24 ((uint16)768)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve24 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve25 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve25 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve25 ((uint16)784)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve25 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve26 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve26 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve26 ((uint16)800)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve26 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve27 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve27 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve27 ((uint16)816)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_DIDReserve27 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F110 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F110 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F110 ((uint16)832)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F110 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F111 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F111 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F111 ((uint16)848)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F111 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F112 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F112 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F112 ((uint16)864)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F112 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F113 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F113 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F113 ((uint16)880)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F113 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F114 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F114 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F114 ((uint16)896)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F114 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F115 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F115 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F115 ((uint16)912)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F115 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F116 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F116 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F116 ((uint16)928)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F116 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F117 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F117 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F117 ((uint16)944)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F117 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F118 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F118 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F118 ((uint16)960)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F118 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F119 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F119 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F119 ((uint16)976)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F119 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11A 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11A already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11A ((uint16)992)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11A */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11B 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11B already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11B ((uint16)1008)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11B */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11C 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11C already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11C ((uint16)1024)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11C */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11D 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11D already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11D ((uint16)1040)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11D */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11E 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11E already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11E ((uint16)1056)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11E */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11F 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11F already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11F ((uint16)1072)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_PIF_F11F */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_NIF_FactoryData_F120 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_NIF_FactoryData_F120 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_NIF_FactoryData_F120 ((uint16)1088)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_NIF_FactoryData_F120 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_NIF_CurrentData_F121 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_NIF_CurrentData_F121 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_NIF_CurrentData_F121 ((uint16)1104)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_NIF_CurrentData_F121 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUPartNumber_F187 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUPartNumber_F187 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUPartNumber_F187 ((uint16)1120)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUPartNumber_F187 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SystemSupplierIdentifier_F18A 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SystemSupplierIdentifier_F18A already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SystemSupplierIdentifier_F18A ((uint16)1136)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SystemSupplierIdentifier_F18A */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUManufactureDate_F18B 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUManufactureDate_F18B already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUManufactureDate_F18B ((uint16)1152)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUManufactureDate_F18B */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUSerialNumber_F18C 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUSerialNumber_F18C already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUSerialNumber_F18C ((uint16)1168)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUSerialNumber_F18C */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_VIN_F190 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_VIN_F190 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_VIN_F190 ((uint16)1184)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_VIN_F190 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUHardwareNumber_F191 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUHardwareNumber_F191 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUHardwareNumber_F191 ((uint16)1200)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUHardwareNumber_F191 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192 ((uint16)1216)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ConfigurationTraceabilityField_F198 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ConfigurationTraceabilityField_F198 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ConfigurationTraceabilityField_F198 ((uint16)1232)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ConfigurationTraceabilityField_F198 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUIndexInformation_F1A5 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUIndexInformation_F1A5 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUIndexInformation_F1A5 ((uint16)1248)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUIndexInformation_F1A5 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_VehicleFeatureInformation_F1A8 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_VehicleFeatureInformation_F1A8 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_VehicleFeatureInformation_F1A8 ((uint16)1264)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_VehicleFeatureInformation_F1A8 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_LastPGMOpenInformation_B042 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_LastPGMOpenInformation_B042 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_LastPGMOpenInformation_B042 ((uint16)1280)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_LastPGMOpenInformation_B042 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043 ((uint16)1296)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_BeforeLastPGMOpenInformation_B043 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SwitchOpenReasonRecord_B044 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SwitchOpenReasonRecord_B044 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SwitchOpenReasonRecord_B044 ((uint16)1312)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SwitchOpenReasonRecord_B044 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Softwareintegritystatus_AFFD 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Softwareintegritystatus_AFFD already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Softwareintegritystatus_AFFD ((uint16)1328)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Softwareintegritystatus_AFFD */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE ((uint16)1344)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Softwarecompatibilitystatus_AFFE */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF ((uint16)1360)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Softwarecompatibilitystatus_AFFF */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ReseveforAPP 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ReseveforAPP already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ReseveforAPP ((uint16)1376)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ReseveforAPP */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ReseveforFBL 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ReseveforFBL already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ReseveforFBL ((uint16)1392)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ReseveforFBL */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ST_ResetBlockInfo_T 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ST_ResetBlockInfo_T already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ST_ResetBlockInfo_T ((uint16)1408)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ST_ResetBlockInfo_T */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ST_ResetMngInfo_T 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ST_ResetMngInfo_T already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ST_ResetMngInfo_T ((uint16)1424)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ST_ResetMngInfo_T */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageLowerThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageLowerThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageLowerThreshold ((uint16)1440)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageLowerThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout ((uint16)1456)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageHigherThresholdTimeout */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout ((uint16)1472)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30UnderVoltageLowerThresholdTimeout */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout ((uint16)1488)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageLowerThresholdTimeout */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold ((uint16)1504)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageLowerThreshold */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout ((uint16)1520)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_KL30_RUnderVoltageHigherThresholdTimeout */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9 already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9 ((uint16)208)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9 */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA ((uint16)192)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA */

#ifdef FeeConf_FeeBlockConfiguration_FeeBlock_Aging 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeBlock_Aging already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeBlock_Aging ((uint16)224)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeBlock_Aging */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_OCV_Map 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_OCV_Map already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_OCV_Map ((uint16)1552)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_OCV_Map */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Usg_Md 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Usg_Md already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Usg_Md ((uint16)576)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_Usg_Md */

#ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SecurityLog 
/* to prevent double declaration */
#error FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SecurityLog already defined
#else 
#define FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SecurityLog ((uint16)1568)
#endif /* #ifdef FeeConf_FeeBlockConfiguration_FeeCtApNvM_NvSWC_SecurityLog */


#define FEE_DISABLE_DEM_REPORT   (0U)
#define FEE_ENABLE_DEM_REPORT    (1U)

/* DEM Configurations */
#define FEE_GC_INIT_DEM_REPORT       (FEE_DISABLE_DEM_REPORT)
#define FEE_WRITE_DEM_REPORT         (FEE_DISABLE_DEM_REPORT)
#define FEE_READ_DEM_REPORT          (FEE_DISABLE_DEM_REPORT)
#define FEE_GC_WRITE_DEM_REPORT      (FEE_DISABLE_DEM_REPORT)
#define FEE_GC_READ_DEM_REPORT       (FEE_DISABLE_DEM_REPORT)
#define FEE_GC_ERASE_DEM_REPORT      (FEE_DISABLE_DEM_REPORT)
#define FEE_INVALIDATE_DEM_REPORT    (FEE_DISABLE_DEM_REPORT)
#define FEE_WRITE_CYCLES_DEM_REPORT  (FEE_DISABLE_DEM_REPORT)
#define FEE_GC_TRIG_DEM_REPORT       (FEE_DISABLE_DEM_REPORT)
#define FEE_UNCFG_BLK_DEM_REPORT     (FEE_DISABLE_DEM_REPORT)
#define FEE_DEM_ENABLED              (STD_OFF)

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/

#define FEE_CONFIG_PTR      (Fee_CfgPtr)

/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/

#endif /* #ifndef FEE_CFG_H */
