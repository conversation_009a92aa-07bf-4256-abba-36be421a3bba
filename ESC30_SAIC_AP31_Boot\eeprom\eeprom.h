/*
 * eeprom.h
 *
 *  Created on: 2019-9-16
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#ifndef EEPROM_H_
#define EEPROM_H_
#include "Platform_Types.h"

#define EEPROM_TYPE    1
#define FLASH_TYPE     2
#define FUNC_TYPE      3
#define DEFAULT_TYPE   4
#define UNUSED         0

#define NVM_JOB_STATUS_IDLE              0x00000000
#define NVM_JOB_STATUS_READ_BUZY         0x00000001
#define NVM_JOB_STATUS_READ_FAIL         0x00000002
#define NVM_JOB_STATUS_WRITE_FAIL        0x00000004

#define NVM_JOB_STATUS_WRTIE_F121        0x00000010
#define NVM_JOB_STATUS_WRTIE_F1AA        0x00000020
#define NVM_JOB_STATUS_WRTIE_F187        0x00000040
#define NVM_JOB_STATUS_WRTIE_F110        0x00000080
#define NVM_JOB_STATUS_WRTIE_PIF         0x00000100

#define NVM_JOB_STATUS_WRTIE_F121_FAIL   0x00000200
#define NVM_JOB_STATUS_WRTIE_F1AA_FAIL   0x00000400
#define NVM_JOB_STATUS_WRTIE_F187_FAIL   0x00000800
#define NVM_JOB_STATUS_WRTIE_F110_FAIL   0x00001000
#define NVM_JOB_STATUS_WRTIE_PIF_FAIL    0x00002000

#define NVM_JOB_STATUS_WRTIE_SECERR       	0x00004000
#define NVM_JOB_STATUS_WRTIE_SECERR_FAIL   	0x00008000
#define NVM_JOB_STATUS_WRTIE_SECLOG       	0x00010000
#define NVM_JOB_STATUS_WRTIE_SECLOG_FAIL   	0x00020000

#define NVM_JOB_STATUS_BUSY_MASK         0x000141F1

typedef uint16 (*Eep_WriteFct)( uint8 index,uint8 * data);
typedef uint16 (*Eep_ReadFct)( uint8 index,uint8 * data);

typedef struct {
	const uint16 u16t_did;
	uint8 u8t_datatype;
	uint32 u32t_addr;
	uint8 *u8t_buffer;
	const uint16 len;
	const Eep_WriteFct writeeeprom;
	const Eep_ReadFct  readeeprom;
}ST_NVM_DID_TYPE;


extern uint32 NvM_JobFinished_Flag;

extern uint16 EEP_ReadDID(uint8 *readData,uint16 did);
extern uint16 EEP_WriteDID(uint8 *writeData,uint16 datalength);
extern uint8  EEP_GetLastSecurityAttemptResult(void);
extern void EEP_Init(void);
extern void EEP_MainFunction(void);
extern uint16 EEP_SetSecErrFlag(uint8* data);
extern uint16 EEP_SaveSecLog(void);
extern uint16 EEP_ReadCurrent_session(uint8 *readData);
#endif /* EEPROM_H_ */
