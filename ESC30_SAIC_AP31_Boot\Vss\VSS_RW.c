#include "NvM.h"
#include "NvM_Cfg.h"

#include "Os.h"

extern uint8 VSS_SecurityKey_PIM[576];
uint8* bufstart=(uint8*)0x80045000;
extern unsigned char vss_cfg_data[1088];

//*********************************VSS cb*****************************//
//**rwflag 0 read, 1 write
//**offset[in]
//**buf[in] data need to be written
//**size_buff[in] the length of data
//**return value 0 sucess,0x1c offset over the scopewrite failure,0x1D failure

uint32 flash_cb(uint32 rwflag, uint32 offset, uint8* buf, uint32 size_buff)
{
    uint16 blockid1;                                          //eeprom block
    uint8 RequestResultPtr;
    // blockid1 = NvMConf_NvMBlockDescriptor_VSS_SecurityKey_PIM;//get eeprom block
    uint32 returnvalue = 0;                                       //return value
 uint8 test = vss_cfg_data[0];
    //************ read PFLASH data***************
    if (offset<1088)
    {
        uint32 K=0;
        for(K=0;K<size_buff;K++)
        {
            buf[K]=bufstart[K+offset];                        //read pflash data
        }
        return 0;
    }
    //*******************read/write eeprom data *********
    else if ((offset >= 1088) && (offset < 1664)) // confirm the offset scope锛�1088 to 锛�1088+576锛夛級
    {
        uint32 i, j;
        j = offset - 1088;
        if (rwflag == 0) // read eeprom data
        {
            // NvM_ReadBlock(NvMConf_NvMBlockDescriptor_VSS_SecurityKey_PIM, VSS_SecurityKey_PIM);
            // do
            // {
            //     NvM_MainFunction();
            //     Fee_MainFunction();
            //     Fls_17_Dmu_MainFunction();
            //     NvM_GetErrorStatus(NvMConf_NvMBlockDescriptor_VSS_SecurityKey_PIM, &RequestResultPtr);
            // } while (RequestResultPtr == NVM_REQ_PENDING);
            
            for (i = 0; i < size_buff; i++)
            {
                buf[i] = VSS_SecurityKey_PIM[i + j];
            }
            return 0;
        }
        else // write into eeprom
        {
                for (i=0; i<size_buff; i++)
                {

                    VSS_SecurityKey_PIM[i+j] = buf[i];
                }
            	// returnvalue = NvM_SetRamBlockStatus(blockid1, TRUE);
               // returnvalue= NvM_WriteBlock(blockid1, &VSS_SecurityKey_PIM);
                if (returnvalue==0)
                {
                    return 0;    //sucess
                }
                else
                {
                    return 0x1D;//write failure
                }
        }
    }
    else
    {
		return 0x1C;                                         //offset over the scopewrite failure
    }
}


