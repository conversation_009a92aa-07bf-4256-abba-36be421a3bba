/******************************************************************************
**                                                                           **
** Copyright (C) Infineon Technologies (2013)                                **
**                                                                           **
** All rights reserved.                                                      **
**                                                                           **
** This document contains proprietary information belonging to Infineon      **
** Technologies. Passing on and copying of this document, and communication  **
** of its contents is not permitted without prior written authorization.     **
**                                                                           **
*******************************************************************************
**                                                                           **
**  $FILENAME   : Fee_PBCfg.c $                                              **
**                                                                           **
**  $CC VERSION : \main\18 $                                                 **
**                                                                           **
**  DATE, TIME: 2021-10-28, 12:21:17                                         **
**                                                                           **
**  GENERATOR : Build b141014-0350                                           **
**                                                                           **
**  AUTHOR    : DL-AUTOSAR-Engineering                                       **
**                                                                           **
**  VENDOR    : Infineon Technologies                                        **
**                                                                           **
**  DESCRIPTION  : FEE configuration generated out of ECU configuration      **
**                   file (Fee.bmd)                                          **
**                                                                           **
**  MAY BE CHANGED BY USER [yes/no]: No                                      **
**                                                                           **
******************************************************************************/

/*******************************************************************************
**                      Includes                                              **
*******************************************************************************/

/* Include Fee Module Header File */
#include "Fee.h"

/*******************************************************************************
**                      Imported Compiler Switch Checks                       **
*******************************************************************************/

/*******************************************************************************
**                      Private Macro Definitions                             **
*******************************************************************************/

/*******************************************************************************
**                      Private Type Definitions                              **
*******************************************************************************/

/*******************************************************************************
**                      Private Function Declarations                         **
*******************************************************************************/

/*******************************************************************************
**                      Global Function Declarations                          **
*******************************************************************************/

/* Illegal State Notification Function */
extern void FeeIllegalStateNotification(void);

/*******************************************************************************
**                      Global Constant Definitions                           **
*******************************************************************************/

/* Allocate space for state data variables in RAM */
#define FEE_START_SEC_VAR_FAST_UNSPECIFIED
/*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
 allowed only for MemMap.h*/
//#include "MemMap.h"

/* Fee State Variable structure */
static Fee_StateDataType Fee_StateVar;

#define FEE_STOP_SEC_VAR_FAST_UNSPECIFIED
/*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
 allowed only for MemMap.h*/
//#include "MemMap.h"


/* User configured logical block initialization structure */
#define FEE_START_SEC_CONST_UNSPECIFIED
/*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
 allowed only for MemMap.h*/
//#include "MemMap.h"

static const Fee_BlockType Fee_BlockConfig[] =
{
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    16U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    17U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    32U, /* Block number */
    12U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    48U, /* Block number */
    50U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    64U, /* Block number */
    50U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    80U, /* Block number */
    50U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    96U, /* Block number */
    50U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    112U, /* Block number */
    50U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    128U, /* Block number */
    50U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    144U, /* Block number */
    50U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    160U, /* Block number */
    50U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    176U, /* Block number */
    24U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    192U, /* Block number */
    17U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    208U, /* Block number */
    17U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    224U, /* Block number */
    16U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1536U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    240U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    256U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    272U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    288U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    304U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    320U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    336U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    352U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    368U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    384U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    400U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    416U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    432U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    448U, /* Block number */
    3U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    464U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    480U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    496U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    512U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    528U, /* Block number */
    3U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    544U, /* Block number */
    3U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    560U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    592U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    608U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    624U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    640U, /* Block number */
    3U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    656U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    672U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    688U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    704U, /* Block number */
    6U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    720U, /* Block number */
    6U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    736U, /* Block number */
    6U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    752U, /* Block number */
    6U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    768U, /* Block number */
    6U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    784U, /* Block number */
    6U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    800U, /* Block number */
    6U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    816U, /* Block number */
    6U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    832U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    848U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    864U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    880U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    896U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    912U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    928U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    944U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    960U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    976U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    992U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1008U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1024U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1040U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1056U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1072U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1088U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1104U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1120U, /* Block number */
    7U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1136U, /* Block number */
    7U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1152U, /* Block number */
    5U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1168U, /* Block number */
    18U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1184U, /* Block number */
    19U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1200U, /* Block number */
    7U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1216U, /* Block number */
    12U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1232U, /* Block number */
    13U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1248U, /* Block number */
    5U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1264U, /* Block number */
    22U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1280U, /* Block number */
    17U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1296U, /* Block number */
    17U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1312U, /* Block number */
    16U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1328U, /* Block number */
    3U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1344U, /* Block number */
    3U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1360U, /* Block number */
    3U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1376U, /* Block number */
    66U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1392U, /* Block number */
    66U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1408U, /* Block number */
    10U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1424U, /* Block number */
    10U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1440U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1456U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1472U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1488U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1504U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1520U, /* Block number */
    4U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    208U, /* Block number */
    7U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    192U, /* Block number */
    7U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    224U, /* Block number */
    14U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1552U, /* Block number */
    112U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    576U, /* Block number */
    3U /* Fee Block Size */
  },
  {
    0U, /* Block Cycle Count */
    (uint8)FEE_NORMAL_DATA,     /* Block type is Normal */
    1568U, /* Block number */
    42U /* Fee Block Size */
  }
};

/* Fee Global initialization structure. */
const Fee_ConfigType Fee_ConfigRoot[] =
{
  {
    /* Fee State Data Structure */
    &Fee_StateVar,
    /* Pointer to logical block configurations */
    &Fee_BlockConfig[0],
    /* Fee Job end notification API */
    &NvM_JobEndNotification,
    /* Fee Job error notification API */
    &NvM_JobErrorNotification,
    /* Fee threshold value */
    2288U,
    /* Number of blocks configured */
    102U,

    {
      /* Ignore the unconfigured blocks */
      FEE_UNCONFIG_BLOCK_IGNORE,
      /* Restart Garbage Collection during initialization */
      FEE_GC_RESTART_INIT,
      /* Erase Suspend feature is disabled */
      FEE_ERASE_SUSPEND_DISABLED,
      /* Reserved */
      0U
    },

    /* Fee Illegal State notification */
    &FeeIllegalStateNotification,
    /* Erase All feature is enabled */
    (boolean)TRUE
  }
};

#define FEE_STOP_SEC_CONST_UNSPECIFIED
/*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
 allowed only for MemMap.h*/
//#include "MemMap.h"

#define FEE_START_SEC_SPL_VAR_32BIT
/*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
 allowed only for MemMap.h*/
//#include "MemMap.h"
const Fee_ConfigType * Fee_CfgPtr = &Fee_ConfigRoot[0];
#define FEE_STOP_SEC_SPL_VAR_32BIT
/*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
 allowed only for MemMap.h*/
//#include "MemMap.h"

