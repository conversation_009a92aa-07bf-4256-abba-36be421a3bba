{"backtrace": 1, "backtraceGraph": {"commands": ["add_custom_target"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 136, "parent": 0}]}, "id": "tasking_info::@6890427a1f51a3e7e1df", "name": "tasking_info", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "isGenerated": true, "path": "build/CMakeFiles/tasking_info", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/tasking_info.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}