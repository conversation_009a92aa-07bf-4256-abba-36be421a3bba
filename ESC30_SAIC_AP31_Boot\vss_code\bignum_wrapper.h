#ifndef _VSS_BIGNUM_WRAPPER_H_
#define _VSS_BIGNUM_WRAPPER_H_

#include "vsstype.h"
#include "vssconf.h"
#define RET_CALC_FUNCTION_ID_ERROR       (('R'<<24)|('F'<<16)|('I'<<8)|('E'))
#define RET_CALC_OPERAND_LENGTH_ERROR    (('R'<<24)|('O'<<16)|('L'<<8)|('E'))

#define RET_CALC_ADD_CARRY		1
#define RET_CALC_ADD_NOCARRY	0
#define RET_CALC_SUB_BORROW		1
#define RET_CALC_SUB_NOBORROW	0

#define RET_CALC_EQUAL			0
#define RET_CALC_BIGGER			1
#define RET_CALC_SMALLER		2

#define RET_CALC_IMPLEMENT_ERROR			(('R' << 24) | ('C' << 16) | ('M' << 8) | ('E'))
#define RET_CALC_IMPLEMENT_SUCCESS       	(('R' << 24) | ('C' << 16) | ('M' << 8) | ('S'))
#define RET_CALC_GCD_NOT_ONE             	(('R' << 24) | ('G' << 16) | ('N' << 8) | ('O'))

#define CALC_POLL  0
#define CALC_IDLE  1

/*void mc_clrint(void);
void mc_init(vss_uint32 *rand);
vss_uint32 mc_version(void);*/

vss_uint32 calc_add(vss_uint32* result, vss_uint32* a, vss_uint32 a_wlen, vss_uint32* b, vss_uint32 b_wlen);
vss_uint32 calc_sub(vss_uint32* result, vss_uint32* a, vss_uint32 a_wlen, vss_uint32* b, vss_uint32 b_wlen);
vss_uint32 calc_modadd(vss_uint32* result, vss_uint32* a, vss_uint32* b, vss_uint32* p, vss_uint32 wlen);
vss_uint32 calc_modsub(vss_uint32* result, vss_uint32* a, vss_uint32* b, vss_uint32* p, vss_uint32 wlen);
vss_uint32 calc_div(vss_uint32* result_q, vss_uint32* result_r, vss_uint32* a, vss_uint32 a_wlen, vss_uint32* b, vss_uint32 b_wlen);
vss_uint32 calc_mul(vss_uint32* result, vss_uint32* a, vss_uint32 a_wlen, vss_uint32* b, vss_uint32 b_wlen);
vss_uint32 calc_modmul(vss_uint32* result, vss_uint32* a, vss_uint32* b, vss_uint32* p, vss_uint32 wlen);
vss_uint32 calc_modexp(vss_uint32* result, vss_uint32* a, vss_uint32 a_wlen, vss_uint32* b, vss_uint32 b_wlen, vss_uint32* p);
vss_uint32 calc_cmp(vss_uint32* a, vss_uint32* b, vss_uint32 wlen);
vss_uint32 calc_lsr(vss_uint32* result, vss_uint32* a, vss_uint32 wlen, vss_uint32 bitlen);
vss_uint32 calc_bitlen(vss_uint32* a, vss_uint32 wlen);
vss_uint32 calc_mod(vss_uint32* result, vss_uint32* a, vss_uint32 a_wlen, vss_uint32* b, vss_uint32 b_wlen);
vss_uint32 calc_modinv(vss_uint32 *result, vss_uint32 *a, vss_uint32 a_wlen, vss_uint32 *b, vss_uint32 b_wlen);
vss_uint32 calc_modmul4sm2(vss_uint32* result, vss_uint32* a, vss_uint32* b, vss_uint32* p, vss_uint32 wlen);
vss_uint32 calc_modmul4nist256(vss_uint32* result, vss_uint32* a, vss_uint32* b, vss_uint32* p, vss_uint32 wlen);
#endif
