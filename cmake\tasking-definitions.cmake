# Tasking TriCore 编译器定义文件
# 基于ESC30_SAIC_AP31_Boot项目Debug目录的makefile分析

# 核心编译器定义
add_compile_definitions(
    # Tasking编译器标识
    _TASKING_C_TRICORE_=1
    
    # CPU架构定义
    __CPU__=tc23x
    __TRICORE__
    
    # AUTOSAR/OSEK相关定义
    STATUSTYPEDEFINED
    
    # 编译器特性
    __TASKING__
    __VERSION__=42020  # v4.2r2
    
    # 语言特性
    __STDC__=1
    __STDC_VERSION__=199901L  # C99
    
    # TriCore特有定义
    __TRICORE_NAME__=0x2300  # TC23x
    __CORE_TC23X__
    
    # 内存模型
    __NEAR_THRESHOLD__=0
    __A0_THRESHOLD__=0
    __A1_THRESHOLD__=0
    
    # 优化相关
    __OPTIMIZE__=2
    __TRADEOFF__=4
    
    # 调试信息
    __DEBUG__
    __SOURCE__
)

# 编译器内置函数和类型
add_compile_definitions(
    # 内置类型
    __SIZEOF_INT__=4
    __SIZEOF_LONG__=4
    __SIZEOF_POINTER__=4
    __SIZEOF_FLOAT__=4
    __SIZEOF_DOUBLE__=8
    
    # 字节序
    __LITTLE_ENDIAN__
    __BYTE_ORDER__=1234
    
    # 对齐
    __ALIGNOF_INT__=4
    __ALIGNOF_POINTER__=4
    
    # 位域
    __BITFIELD_MSB_FIRST__=0
)

# TriCore架构特有定义
add_compile_definitions(
    # 寄存器相关
    __TRICORE_HAVE_CSFR__
    __TRICORE_HAVE_ENDINIT__
    
    # 中断相关
    __TRICORE_HAVE_ICR__
    __TRICORE_HAVE_BIV__
    
    # 内存保护
    __TRICORE_HAVE_MPU__
    
    # 浮点单元
    __TRICORE_HAVE_FPU__
    
    # 缓存
    __TRICORE_HAVE_PCACHE__
    __TRICORE_HAVE_DCACHE__
)

# MCAL和AUTOSAR相关定义
add_compile_definitions(
    # AUTOSAR版本
    AUTOSAR_VERSION_403
    
    # MCAL模块启用
    ADC_ENABLED
    CAN_ENABLED
    DIO_ENABLED
    DMA_ENABLED
    GTM_ENABLED
    MCU_ENABLED
    PORT_ENABLED
    SPI_ENABLED
    UART_ENABLED
    WDG_ENABLED
    
    # 开发错误检测
    DET_ENABLED
    
    # 版本信息检查
    VERSION_INFO_API_ENABLED
)

# Bootloader特有定义
add_compile_definitions(
    # Bootloader类型
    PRIMARY_BOOTLOADER
    PBL_VERSION=103
    
    # Flash相关
    FLASH_DRIVER_ENABLED
    EXTERNAL_FLASH_ENABLED
    
    # 通信协议
    UDS_ENABLED
    CAN_TP_ENABLED
    
    # 安全相关
    SECURE_BOOT_ENABLED
    CRC_CHECK_ENABLED
    
    # 诊断相关
    DIAGNOSTIC_ENABLED
    DID_F186_ENABLED
)

# 内存布局定义
add_compile_definitions(
    # Flash分区
    PBL_START_ADDRESS=0x80000020
    PBL_SIZE=0x0000FFE0
    SBL_VALID_FLAG_ADDRESS=0x80010000
    SBL_CRC_ADDRESS=0x80014000
    SBL_START_ADDRESS=0x80018200
    APP_START_ADDRESS=0xA0080000
    
    # RAM分区
    FL_BOOT_MODE_ADDRESS=0x70101FF0
    CPU0_PMI_PSPR=0x70100100
    CPU0_DMI_DSPR=0x70000100
    
    # 启动模式定义
    BOOT_MODE_PBL=0x6A
    BOOT_MODE_SBL=0x2A
    BOOT_MODE_SBL2=0x3A
    BOOT_MODE_NORMAL=0xFF
)

# 编译器优化和警告控制
add_compile_options(
    # 模拟Tasking编译器的优化选项
    $<$<COMPILE_LANGUAGE:C>:-O2>
    
    # 模拟对齐选项
    $<$<COMPILE_LANGUAGE:C>:-malign-functions=4>
    
    # 模拟语言选项
    $<$<COMPILE_LANGUAGE:C>:-std=c99>
    
    # 警告控制
    $<$<COMPILE_LANGUAGE:C>:-Wall>
    $<$<COMPILE_LANGUAGE:C>:-Wextra>
    $<$<COMPILE_LANGUAGE:C>:-Wno-unused-parameter>
    $<$<COMPILE_LANGUAGE:C>:-Wno-unused-variable>
)

# 链接器选项模拟
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,--gc-sections")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,--print-memory-usage")

# 输出格式定义
set(OUTPUT_NAME "AP31_ESC_PBL")
set(OUTPUT_FORMATS "elf;hex;map")

message(STATUS "Tasking TriCore definitions loaded")
message(STATUS "Target: ${OUTPUT_NAME}")
message(STATUS "CPU: TC23x TriCore")
message(STATUS "Bootloader: Primary Bootloader v1.03")
