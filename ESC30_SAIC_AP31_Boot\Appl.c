/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <Appl.c>
 *  @brief      <App Loader>
 *  
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *  
 *  <AUTHOR>
 *  @date       <2013-09-13>
 */
/*============================================================================*/

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/* <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20121227    Gary       Initial version
 *
 *  V1.1    20130913    ccl        update
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/
#include "Appl.h"
#include "Mcu.h"
#include "SecM.h"
#include "CanIf.h"
#include "CanTp.h"
#include "Dcm.h"
#include "Fls.h"
#include "Secure.h"
#include "stdio.h"
#include "Dio.h"
#include "Dio_Cfg.h"
#include "Port.h"
#include "Can_17_MCanP.h"
#include "eeprom_Cfg.h"
#include "Dcm_Cfg.h"
#include "wdg.h"
/* for test */
/*ȫ�ֱ���*/
uint32 Appl_cnt_test10=0;
uint32 curr10mscounter=0;
//uint32 cnt_test50=0;
//uint8  AfterRun_Flag=1;
//uint16 AfterRun_Count=0;
//uint8  Recover_Flag=0;
/*�ⲿȫ�ֱ���*/
/*=======[I N T E R N A L   D A T A]==========================================*/
/** timer counter for ECU shut down */
#if (FL_SLEEP_TIMER > 0)
STATIC uint32 ApplShutDownTimer;
#endif

/** timer delay when ECU start up */
#if (FL_MODE_STAY_TIME > 0)
STATIC uint32 ApplBootStayTimer;
#endif

uint8 pt_datBuf[8] = {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00};

/*=======[I N T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
STATIC void Appl_InitNvmInfo(void);

#if (FL_SLEEP_TIMER > 0)
STATIC void Appl_EcuShutdownTimer(void);
#endif

#if (FL_MODE_STAY_TIME > 0)
STATIC void Appl_BootStayTimer(void);
#endif

STATIC void Appl_GotoAppSW(void);
STATIC void Appl_DownloadFlashdriver(void);
extern uint32 u32_seed_tmp;
uint16 lastcounter = 0;


/*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/

/******************************************************************************/
/**
 * @brief               <jump to application software>
 * 
 * <jump to application software> .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/

void Appl_JumpApp(void)
{
//    Wdg_Stop();

//	Can_17_MCanP_DeInit();
//	Mcal_DisableAllInterrupts();
//	Irq_ClearAllInterruptFlags();
//	Mcu_DeInit();
//	Wdg_Stop();
    // __asm("ja (0xA0050000)" );
      __asm("ja (0xA0080000)" );
}

/******************************************************************************/
/**
 * @brief               <ECU initialize>
 * 
 * <This routine shall be called by the bootcode contained in the flashloader 
 *  to initialize common hardware.> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Non Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>    
 */
/******************************************************************************/
FL_ResultType Appl_EcuStartup(void)
{
    /* stay in bootloader and initialize */
    (void)Appl_FlStartup();


    Appl_InitNvmInfo();
//    EEP_Init();
    Secure_Init();
    if(0x01 == FL_GetSecErrFlag())
    {
    	Dcm_StartSecurityTimer((uint32)DCM_SECURITY_TIME);
    }
    /* check if bootloader program is requested in app */
    if (FL_EXT_PROG_REQUEST_RECEIVED == *(uint8 *)FL_BOOT_MODE)
    // if ((FL_EXT_PROG_REQUEST_RECEIVED == *(uint8 *)FL_BOOT_MODE) || (FL_EXT_PROG_REQUEST82_RECEIVED == *(uint8 *)FL_BOOT_MODE))
    {
        /* clear bootloader request flash and app update flag */
        *(uint8 *)FL_BOOT_MODE = 0x00;
        *(uint8 *)FL_APPL_UPDATE = 0x00;
        // if(FL_EXT_PROG_REQUEST_RECEIVED == *(uint8 *)FL_BOOT_MODE)
        {
            /* DCM go programming mode and response programming session service */
            Dcm_ProgramInit(DCM_SESSION_PROGRAMMING);
        }
        // else 
        // {
        //     /* DCM go programming mode and response programming session service */
        //     Dcm_ProgramInit((DCM_SESSION_PROGRAMMING | 0x80));
        // }
    }
    else if (FL_EXT_PROG_REQUEST82_RECEIVED == *(uint8 *)FL_BOOT_MODE)
    {
        *(uint8 *)FL_BOOT_MODE = 0x00;
        *(uint8 *)FL_APPL_UPDATE = 0x00;
        /* DCM go programming mode and response programming session service */
        Dcm_ProgramInit((DCM_SESSION_PROGRAMMING | 0x80));

    }
    else 
    {
        if (FL_BOOT_DEFAULT_FROM_PROG == *(uint8 *)FL_BOOT_DEFAULT)
        {
            *(uint8 *)FL_BOOT_DEFAULT = 0x00;
            *(uint8 *)FL_APPL_UPDATE = 0x00;
            /* DCM go default mode and response */
            Dcm_ProgramInit(DCM_SESSION_DEFAULT);
        }
        /* check if application software is consistency */
        if (FL_OK == Appl_CheckValidation())
        {
#if (FL_MODE_STAY_TIME > 0)
            ApplBootStayTimer = FL_MODE_STAY_TIME / GPT_PERIOD_TIME;
#else
            Appl_GotoAppSW();
#endif
        }
    }

    return FL_OK;
}

/******************************************************************************/
/**
 * @brief               <flashloader initialize>
 * 
 * <This routine shall be called by the flashloader to initialize the software 
 *  environment needed for ECU reprogramming (after the decision to switch to 
 *  flashloader mode).> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Non Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>    
 */
/******************************************************************************/
FL_ResultType Appl_FlStartup(void)
{
#if(FLS_USED == STD_ON)
	//Appl_DownloadFlashdriver();	/* release FlashDrv to RAM */
#endif
    FL_InitState();

#if (FL_SLEEP_TIMER > 0)
    ApplShutDownTimer = FL_SLEEP_TIMER / GPT_PERIOD_TIME;
#endif
    
#if (FL_MODE_STAY_TIME > 0)
    ApplBootStayTimer = 0;
#endif
    
    return FL_OK;
}
    
#if (FL_SLEEP_TIMER > 0)
/******************************************************************************/
/**
 * @brief               <ECU goto sleep mode>
 * 
 * <This routine shall be called by the flashloader to shut down the ECU (put 
 *  the ECU to sleep).> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Non Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Appl_EcuShutdown (void)
{
    /* do nothing */
    /* reserved for user, application related */
    return;
}
#endif

/******************************************************************************/
/**
 * @brief               <ECU reset>
 * 
 * <This routine shall be called by the flashloader to reset the ECU upon 
 *  request from the external programming device.> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Non Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
#include "Wdg.h"
void Appl_EcuReset(void)
{
    /* clear flash driver in RAM */
    Appl_Memset((uint8 *)FL_DEV_BASE_ADDRESS, 0x00u, FL_DEV_SIZE);
//    Wdg_SystemReset();
    Mcu_PerformReset();
//    Appl_JumpApp();
    /* watch dog reset */
    return;
}

/*2019-08-15 ���ڷ�ֹ��λ��д���DID����û�д�EEPROM*/
//void Appl_CheckEcuResetMain(void)
//{
//
//    /* clear flash driver in RAM */
//    Appl_Memset((uint8 *)FL_DEV_BASE_ADDRESS, 0x00u, FL_DEV_SIZE);
//
//    /* watch dog reset */
//    Wdg_SystemReset();
//
//    return;
//}
/******************************************************************************/
/**
 * @brief               <check application software consistency>
 * 
 * <This routine shall be called by the flashloader to check whether the 
 *  individual parts (logical blocks) that make up the application software of 
 *  an ECU are consistent with each other.> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Non Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <FL_ResultType>    
 */
/******************************************************************************/
FL_ResultType Appl_CheckValidation(void)
{
    FL_ResultType ret = FL_OK;
    uint8 blockIndex = 0;
    

     /*�궨�����APP������Ч��������ת*/
	if (FALSE == FL_NvmInfo.isAppFblCpb) 
	{
		ret = FL_FAILED;
	}
	else if (FALSE == FL_NvmInfo.isAppCalCpb)
	{
		ret = FL_FAILED;
	}

    return ret;
}

/******************************************************************************/
/**
 * @brief               <trigger watch dog>
 * 
 * <This routine shall be called by functions of the flashloader runtime 
 *  environment and the security module at least every 500 milliseconds.> .
 * Service ID   :       <NONE>
 * Sync/Async   :       <Synchronous>
 * Reentrancy           <Non Reentrant>
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/


void Appl_UpdateTriggerCondition(void)
{//1ms
	if(GetTicks10ms()==TRUE)
	{
		u32_seed_tmp++;
		CanTp_MainFunction();
		Dcm_MainFunction();
		Can_17_MCanP_MainFunction_Write();
	}

    return;
}

void Appl_UpdateTriggerConditionImmediate(uint8 count)
{
	uint8 excute_count=count;
	while(excute_count>0)
	{
		u32_seed_tmp++;
		CanTp_MainFunction();
		//Dcm_MainFunction();
		Can_17_MCanP_MainFunction_Write();
		excute_count--;
	}

    return;
}
/******************************************************************************/
/**
 * @brief               <memory copy>
 * 
 * <MISRA C 2004 forbid to use memcpy() lib,only used  to copy data buffer of 
 *  indirect address.> .
 * @param[in]           <source (IN), length (IN)>
 * @param[out]          <dest (OUT)>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Appl_Memcpy(uint8 * dest,
    const uint8 *source,
    uint32 length)
{
    while (length > 0)
    {
        if ((dest != NULL_PTR )&& (source != NULL_PTR))
        {            
            *dest = *source;
            dest ++;
            source ++;
        }
        else
        {
            break;
        }

        length --;
    }

    return;
}

/******************************************************************************/
/**
 * @brief               <memory set>
 * 
 * <MISRA C 2004 forbid to use memset() lib,only used  to set data buffer of 
 *  indirect address.> .
 * @param[in]           <source (IN), length (IN)>
 * @param[out]          <dest (OUT)>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void Appl_Memset(uint8 * dest,
    const uint8 source,
    uint32 length)
{
    while (length > 0)
    {
        if (dest != NULL_PTR )
        {
            *dest = source;
            dest++;
        }
        else
        {
            break;
        }
        
        length--;
    }
    
    return;
}

/******************************************************************************/
/**
 * @brief               <get nvm infomation>
 * 
 * <get infomation from EEPROM about bootloader infomation(e.g.fingerprint)> .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
STATIC void Appl_InitNvmInfo(void)
{
    uint8 blockIndex = 0;
    
    /* CRC32 parameter */
    SecM_CRCParamType crcParam;

    /* get block infomation from flash */
    FlashReadMemory((uint8 *)&FL_NvmInfo, FL_NVM_INFO_ADDRESS,sizeof(FL_NvmInfoType));

    /* compute CRC for NVM infomation */
    crcParam.crcState = SECM_CRC_INIT;
    crcParam.crcSourceBuffer = (const uint8 *)&FL_NvmInfo;
    crcParam.crcByteCount = sizeof(FL_NvmInfoType) - 6;
    (void)SecM_ComputeCRC(&crcParam);
    crcParam.crcState = SECM_CRC_COMPUTE;
    (void)SecM_ComputeCRC(&crcParam);
    crcParam.crcState = SECM_CRC_FINALIZE;
    (void)SecM_ComputeCRC(&crcParam);
    
    /* compare CRC for NVM infomation */
    if (crcParam.currentCRC != FL_NvmInfo.infoChecksum)
    {
        /* if there is no infomation in NVM,then initialize the infomation */
        for (blockIndex = 0; blockIndex < FL_NUM_LOGICAL_BLOCKS ; blockIndex++)
        {
            FL_NvmInfo.blockInfo[blockIndex].blkChecksum = 0x00uL;
            FL_NvmInfo.blockInfo[blockIndex].blkProgAttempt = 0x00u;
            FL_NvmInfo.blockInfo[blockIndex].blkValid = FALSE;
            Appl_Memset(FL_NvmInfo.blockInfo[blockIndex].fingerPrint,
                0x00u,
                FL_FINGER_PRINT_LENGTH);
        }
        FL_NvmInfo.isAppCalCpb=TRUE;
        FL_NvmInfo.isAppFblCpb=FALSE;
        FL_NvmInfo.infoChecksum = 0x00u;
    }
    lastcounter =  FL_NvmInfo.blockInfo[APPBLOCKINDEX].blkProgAttempt;
    return;
}

#if (FL_SLEEP_TIMER > 0)
/******************************************************************************/
/**
 * @brief               <time count for ECU shut down>
 * 
 * <time count for ECU shut down> .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
STATIC void Appl_EcuShutdownTimer(void)
{
    /* 
     ** if diagnostic leave default session mode or security unlocked, stop
     ** sleep timer count.
     */
    if ((ApplShutDownTimer > 0) &&
        (DCM_SECURITY_LOCKED == Dcm_GetSecurityLevel()) &&
        (DCM_SESSION_DEFAULT == Dcm_GetSessionMode()))
    {
        ApplShutDownTimer --;
        if (0x00u == ApplShutDownTimer)
        {
            /* set ECU to sleep state */
            Appl_EcuShutdown();
        }
    }
    else
    {
        ApplShutDownTimer = 0x00u;
    }

    return;
}
#endif

#if (FL_MODE_STAY_TIME > 0)
/******************************************************************************/
/**
 * @brief               <time count for ECU stay in boot>
 * 
 * <time count for ECU stay in boot> .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
STATIC void Appl_BootStayTimer(void)
{
    /* 
     ** if diagnostic leave default session mode or security unlocked, stop
     ** boot stay timer count.
     */
    if ((ApplBootStayTimer > 0) &&
        (DCM_SECURITY_LOCKED == Dcm_GetSecurityLevel()) &&
        (DCM_SESSION_DEFAULT == Dcm_GetSessionMode()))
    {

        ApplBootStayTimer--;
        if (0x00u == ApplBootStayTimer)
        {
            Appl_GotoAppSW();
        }
    }
    else
    {
        ApplBootStayTimer = 0x00u;
    }

    return;
}
#endif

/******************************************************************************/
/**
 * @brief               <go to application software>
 * 
 * <go to application software> .
 * @param[in]           <NONE>
 * @param[out]          <NONE>
 * @param[in/out]       <NONE>
 * @return              <NONE>    
 */
/******************************************************************************/
void STM_Deinit(void)
{
	/* Access to Endinit-protected registers is permitted */
	_safety_endinit_clear();

	SCU_CCUCON1.U = 0x2211U | (0x1U << 30);
	STM0_ISCR.U = 0x0U;
	STM0_CMP0.U = 0x0U;
	STM0_CMCON.U = 0x0U;
	STM0_ICR.U = 0x0U;

	_safety_endinit_set();

    return;
}
STATIC void Appl_GotoAppSW(void)
{
    /* de-initialize hardware module */
    //Can_Deinit();
    //    Gpt_Deinit();
   // Mcu_Deinit();
//	Can_Deinit();
	Can_17_MCanP_DeInit();
	STM_Deinit();
//	  Gtm_DeInit();
    /* jump to APP */
    Appl_JumpApp();
    
    return;
}

#if(FLS_USED == STD_ON)
/*==========================================================================*/

/*
	FUNCTION NAME:		Appl_DownloadFlashdriver
	COMPONENT:			Flashloader
	SCOPE:				<APPLICATION/COMPONENT/PACKAGE>
	DESCRIPTION:
		<This Function download flash driver to the ram>
*/
/*==========================================================================*/
extern void Fls_lLoadAccessCode(uint8 JobRequest);
STATIC void Appl_DownloadFlashdriver(void)
{
	int i;
	uint8* t_pu1_RamAddr;


	t_pu1_RamAddr = (uint8*)(FlASH_DRIVER_RAM_START_ADDR);

	for (i = 0; i < FLASH_ERASE_SIZE;  i++)
	{
		*(t_pu1_RamAddr + i) = u8_FlashEraseDrvBin[i];
	}

	for (i = 0; i < FLASH_WRITE_SIZE;  i++)
	{
		*(t_pu1_RamAddr + i+FLASH_ERASE_SIZE+WRITE_OFFSET) = u8_FlashWriteDrvBin[i];
	}
}
#endif





/* 1ms */
void  Appl_Period_10ms(void)
{
	u32_seed_tmp++;
	Appl_cnt_test10++;
	CanTp_MainFunction();
	Dcm_MainFunction();
	#if (FL_SLEEP_TIMER > 0)
			Appl_EcuShutdownTimer();
	#endif

	#if (FL_MODE_STAY_TIME > 0)
			Appl_BootStayTimer();
	#endif
//    EEP_SaveSecLog();
}
//1ms
uint8 GetTicks10ms(void)
{
	static uint32 PreStmTimer=0;
	uint32 currStmTimer=0;
	uint8 rtn=TRUE;
	currStmTimer=Stm0_GetSysTickTime();
	if(currStmTimer>PreStmTimer)
	{
		if((currStmTimer-PreStmTimer)>=TIMER_10MS_COUNTER)
		{
			rtn=TRUE;
			curr10mscounter++;
			PreStmTimer=currStmTimer;
		}
		else
		{
			rtn=FALSE;
		}
	}
	else
	{
		if(((0xFFFFFFFF-PreStmTimer)+currStmTimer)>=TIMER_10MS_COUNTER)
		{
			rtn=TRUE;
			curr10mscounter++;
		    PreStmTimer=currStmTimer;
		}
		else
		{
			rtn=FALSE;
		}
	}
	return rtn;
}
void Sl_WaitTimer_us(uint32 us)
{
	uint32 start_time;
	uint32 now_time;
	start_time = Stm0_GetSysTickTime();

	while(1){
		now_time = Stm0_GetSysTickTime();

		if(now_time - start_time > us){
			break;
		}
	}
}
