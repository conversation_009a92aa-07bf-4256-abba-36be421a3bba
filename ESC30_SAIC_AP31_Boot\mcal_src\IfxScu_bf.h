/**
 * \file IfxScu_bf.h
 * \brief
 * \copyright Copyright (c) 2016 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC23XADAS_UM_V1.1.R1
 * Specification: tc23xadas_um_sfrs_MCSFR.xml (Revision: UM_V1.1)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Scu_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Scu
 * 
 */
#ifndef IFXSCU_BF_H
#define IFXSCU_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Scu_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN0 */
#define IFX_SCU_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN0 */
#define IFX_SCU_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN0 */
#define IFX_SCU_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN10 */
#define IFX_SCU_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN10 */
#define IFX_SCU_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN10 */
#define IFX_SCU_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN11 */
#define IFX_SCU_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN11 */
#define IFX_SCU_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN11 */
#define IFX_SCU_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN12 */
#define IFX_SCU_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN12 */
#define IFX_SCU_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN12 */
#define IFX_SCU_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN13 */
#define IFX_SCU_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN13 */
#define IFX_SCU_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN13 */
#define IFX_SCU_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN14 */
#define IFX_SCU_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN14 */
#define IFX_SCU_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN14 */
#define IFX_SCU_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN15 */
#define IFX_SCU_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN15 */
#define IFX_SCU_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN15 */
#define IFX_SCU_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN16 */
#define IFX_SCU_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN16 */
#define IFX_SCU_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN16 */
#define IFX_SCU_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN17 */
#define IFX_SCU_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN17 */
#define IFX_SCU_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN17 */
#define IFX_SCU_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN18 */
#define IFX_SCU_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN18 */
#define IFX_SCU_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN18 */
#define IFX_SCU_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN19 */
#define IFX_SCU_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN19 */
#define IFX_SCU_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN19 */
#define IFX_SCU_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN1 */
#define IFX_SCU_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN1 */
#define IFX_SCU_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN1 */
#define IFX_SCU_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN20 */
#define IFX_SCU_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN20 */
#define IFX_SCU_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN20 */
#define IFX_SCU_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN21 */
#define IFX_SCU_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN21 */
#define IFX_SCU_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN21 */
#define IFX_SCU_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN22 */
#define IFX_SCU_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN22 */
#define IFX_SCU_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN22 */
#define IFX_SCU_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN23 */
#define IFX_SCU_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN23 */
#define IFX_SCU_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN23 */
#define IFX_SCU_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN24 */
#define IFX_SCU_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN24 */
#define IFX_SCU_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN24 */
#define IFX_SCU_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN25 */
#define IFX_SCU_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN25 */
#define IFX_SCU_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN25 */
#define IFX_SCU_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN26 */
#define IFX_SCU_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN26 */
#define IFX_SCU_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN26 */
#define IFX_SCU_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN27 */
#define IFX_SCU_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN27 */
#define IFX_SCU_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN27 */
#define IFX_SCU_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN28 */
#define IFX_SCU_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN28 */
#define IFX_SCU_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN28 */
#define IFX_SCU_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN29 */
#define IFX_SCU_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN29 */
#define IFX_SCU_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN29 */
#define IFX_SCU_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN2 */
#define IFX_SCU_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN2 */
#define IFX_SCU_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN2 */
#define IFX_SCU_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN30 */
#define IFX_SCU_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN30 */
#define IFX_SCU_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN30 */
#define IFX_SCU_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN31 */
#define IFX_SCU_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN31 */
#define IFX_SCU_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN31 */
#define IFX_SCU_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN3 */
#define IFX_SCU_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN3 */
#define IFX_SCU_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN3 */
#define IFX_SCU_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN4 */
#define IFX_SCU_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN4 */
#define IFX_SCU_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN4 */
#define IFX_SCU_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN5 */
#define IFX_SCU_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN5 */
#define IFX_SCU_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN5 */
#define IFX_SCU_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN6 */
#define IFX_SCU_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN6 */
#define IFX_SCU_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN6 */
#define IFX_SCU_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN7 */
#define IFX_SCU_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN7 */
#define IFX_SCU_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN7 */
#define IFX_SCU_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN8 */
#define IFX_SCU_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN8 */
#define IFX_SCU_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN8 */
#define IFX_SCU_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_SCU_ACCEN0_Bits.EN9 */
#define IFX_SCU_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_SCU_ACCEN0_Bits.EN9 */
#define IFX_SCU_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ACCEN0_Bits.EN9 */
#define IFX_SCU_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_SCU_ARSTDIS_Bits.STM0DIS */
#define IFX_SCU_ARSTDIS_STM0DIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_ARSTDIS_Bits.STM0DIS */
#define IFX_SCU_ARSTDIS_STM0DIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ARSTDIS_Bits.STM0DIS */
#define IFX_SCU_ARSTDIS_STM0DIS_OFF (0u)

/** \brief  Length for Ifx_SCU_ARSTDIS_Bits.STM1DIS */
#define IFX_SCU_ARSTDIS_STM1DIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_ARSTDIS_Bits.STM1DIS */
#define IFX_SCU_ARSTDIS_STM1DIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ARSTDIS_Bits.STM1DIS */
#define IFX_SCU_ARSTDIS_STM1DIS_OFF (1u)

/** \brief  Length for Ifx_SCU_ARSTDIS_Bits.STM2DIS */
#define IFX_SCU_ARSTDIS_STM2DIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_ARSTDIS_Bits.STM2DIS */
#define IFX_SCU_ARSTDIS_STM2DIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ARSTDIS_Bits.STM2DIS */
#define IFX_SCU_ARSTDIS_STM2DIS_OFF (2u)

/** \brief  Length for Ifx_SCU_CCUCON0_Bits.BAUD2DIV */
#define IFX_SCU_CCUCON0_BAUD2DIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_CCUCON0_Bits.BAUD2DIV */
#define IFX_SCU_CCUCON0_BAUD2DIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CCUCON0_Bits.BAUD2DIV */
#define IFX_SCU_CCUCON0_BAUD2DIV_OFF (4u)

/** \brief  Length for Ifx_SCU_CCUCON0_Bits.CLKSEL */
#define IFX_SCU_CCUCON0_CLKSEL_LEN (2u)

/** \brief  Mask for Ifx_SCU_CCUCON0_Bits.CLKSEL */
#define IFX_SCU_CCUCON0_CLKSEL_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_CCUCON0_Bits.CLKSEL */
#define IFX_SCU_CCUCON0_CLKSEL_OFF (28u)

/** \brief  Length for Ifx_SCU_CCUCON0_Bits.FSI2DIV */
#define IFX_SCU_CCUCON0_FSI2DIV_LEN (2u)

/** \brief  Mask for Ifx_SCU_CCUCON0_Bits.FSI2DIV */
#define IFX_SCU_CCUCON0_FSI2DIV_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_CCUCON0_Bits.FSI2DIV */
#define IFX_SCU_CCUCON0_FSI2DIV_OFF (20u)

/** \brief  Length for Ifx_SCU_CCUCON0_Bits.FSIDIV */
#define IFX_SCU_CCUCON0_FSIDIV_LEN (2u)

/** \brief  Mask for Ifx_SCU_CCUCON0_Bits.FSIDIV */
#define IFX_SCU_CCUCON0_FSIDIV_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_CCUCON0_Bits.FSIDIV */
#define IFX_SCU_CCUCON0_FSIDIV_OFF (24u)

/** \brief  Length for Ifx_SCU_CCUCON0_Bits.LCK */
#define IFX_SCU_CCUCON0_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_CCUCON0_Bits.LCK */
#define IFX_SCU_CCUCON0_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CCUCON0_Bits.LCK */
#define IFX_SCU_CCUCON0_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_CCUCON0_Bits.LPDIV */
#define IFX_SCU_CCUCON0_LPDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_CCUCON0_Bits.LPDIV */
#define IFX_SCU_CCUCON0_LPDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CCUCON0_Bits.LPDIV */
#define IFX_SCU_CCUCON0_LPDIV_OFF (12u)

/** \brief  Length for Ifx_SCU_CCUCON0_Bits.SPBDIV */
#define IFX_SCU_CCUCON0_SPBDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_CCUCON0_Bits.SPBDIV */
#define IFX_SCU_CCUCON0_SPBDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CCUCON0_Bits.SPBDIV */
#define IFX_SCU_CCUCON0_SPBDIV_OFF (16u)

/** \brief  Length for Ifx_SCU_CCUCON0_Bits.SRIDIV */
#define IFX_SCU_CCUCON0_SRIDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_CCUCON0_Bits.SRIDIV */
#define IFX_SCU_CCUCON0_SRIDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CCUCON0_Bits.SRIDIV */
#define IFX_SCU_CCUCON0_SRIDIV_OFF (8u)

/** \brief  Length for Ifx_SCU_CCUCON0_Bits.UP */
#define IFX_SCU_CCUCON0_UP_LEN (1u)

/** \brief  Mask for Ifx_SCU_CCUCON0_Bits.UP */
#define IFX_SCU_CCUCON0_UP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CCUCON0_Bits.UP */
#define IFX_SCU_CCUCON0_UP_OFF (30u)

/** \brief  Length for Ifx_SCU_CCUCON1_Bits.ASCLINFDIV */
#define IFX_SCU_CCUCON1_ASCLINFDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_CCUCON1_Bits.ASCLINFDIV */
#define IFX_SCU_CCUCON1_ASCLINFDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CCUCON1_Bits.ASCLINFDIV */
#define IFX_SCU_CCUCON1_ASCLINFDIV_OFF (20u)

/** \brief  Length for Ifx_SCU_CCUCON1_Bits.ASCLINSDIV */
#define IFX_SCU_CCUCON1_ASCLINSDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_CCUCON1_Bits.ASCLINSDIV */
#define IFX_SCU_CCUCON1_ASCLINSDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CCUCON1_Bits.ASCLINSDIV */
#define IFX_SCU_CCUCON1_ASCLINSDIV_OFF (24u)

/** \brief  Length for Ifx_SCU_CCUCON1_Bits.CANDIV */
#define IFX_SCU_CCUCON1_CANDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_CCUCON1_Bits.CANDIV */
#define IFX_SCU_CCUCON1_CANDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CCUCON1_Bits.CANDIV */
#define IFX_SCU_CCUCON1_CANDIV_OFF (0u)

/** \brief  Length for Ifx_SCU_CCUCON1_Bits.ERAYDIV */
#define IFX_SCU_CCUCON1_ERAYDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_CCUCON1_Bits.ERAYDIV */
#define IFX_SCU_CCUCON1_ERAYDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CCUCON1_Bits.ERAYDIV */
#define IFX_SCU_CCUCON1_ERAYDIV_OFF (4u)

/** \brief  Length for Ifx_SCU_CCUCON1_Bits.ETHDIV */
#define IFX_SCU_CCUCON1_ETHDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_CCUCON1_Bits.ETHDIV */
#define IFX_SCU_CCUCON1_ETHDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CCUCON1_Bits.ETHDIV */
#define IFX_SCU_CCUCON1_ETHDIV_OFF (16u)

/** \brief  Length for Ifx_SCU_CCUCON1_Bits.GTMDIV */
#define IFX_SCU_CCUCON1_GTMDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_CCUCON1_Bits.GTMDIV */
#define IFX_SCU_CCUCON1_GTMDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CCUCON1_Bits.GTMDIV */
#define IFX_SCU_CCUCON1_GTMDIV_OFF (12u)

/** \brief  Length for Ifx_SCU_CCUCON1_Bits.INSEL */
#define IFX_SCU_CCUCON1_INSEL_LEN (2u)

/** \brief  Mask for Ifx_SCU_CCUCON1_Bits.INSEL */
#define IFX_SCU_CCUCON1_INSEL_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_CCUCON1_Bits.INSEL */
#define IFX_SCU_CCUCON1_INSEL_OFF (28u)

/** \brief  Length for Ifx_SCU_CCUCON1_Bits.LCK */
#define IFX_SCU_CCUCON1_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_CCUCON1_Bits.LCK */
#define IFX_SCU_CCUCON1_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CCUCON1_Bits.LCK */
#define IFX_SCU_CCUCON1_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_CCUCON1_Bits.STMDIV */
#define IFX_SCU_CCUCON1_STMDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_CCUCON1_Bits.STMDIV */
#define IFX_SCU_CCUCON1_STMDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CCUCON1_Bits.STMDIV */
#define IFX_SCU_CCUCON1_STMDIV_OFF (8u)

/** \brief  Length for Ifx_SCU_CCUCON1_Bits.UP */
#define IFX_SCU_CCUCON1_UP_LEN (1u)

/** \brief  Mask for Ifx_SCU_CCUCON1_Bits.UP */
#define IFX_SCU_CCUCON1_UP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CCUCON1_Bits.UP */
#define IFX_SCU_CCUCON1_UP_OFF (30u)

/** \brief  Length for Ifx_SCU_CCUCON2_Bits.BBBDIV */
#define IFX_SCU_CCUCON2_BBBDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_CCUCON2_Bits.BBBDIV */
#define IFX_SCU_CCUCON2_BBBDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CCUCON2_Bits.BBBDIV */
#define IFX_SCU_CCUCON2_BBBDIV_OFF (0u)

/** \brief  Length for Ifx_SCU_CCUCON2_Bits.LCK */
#define IFX_SCU_CCUCON2_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_CCUCON2_Bits.LCK */
#define IFX_SCU_CCUCON2_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CCUCON2_Bits.LCK */
#define IFX_SCU_CCUCON2_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_CCUCON2_Bits.UP */
#define IFX_SCU_CCUCON2_UP_LEN (1u)

/** \brief  Mask for Ifx_SCU_CCUCON2_Bits.UP */
#define IFX_SCU_CCUCON2_UP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CCUCON2_Bits.UP */
#define IFX_SCU_CCUCON2_UP_OFF (30u)

/** \brief  Length for Ifx_SCU_CCUCON3_Bits.LCK */
#define IFX_SCU_CCUCON3_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_CCUCON3_Bits.LCK */
#define IFX_SCU_CCUCON3_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CCUCON3_Bits.LCK */
#define IFX_SCU_CCUCON3_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_CCUCON3_Bits.PLLDIV */
#define IFX_SCU_CCUCON3_PLLDIV_LEN (6u)

/** \brief  Mask for Ifx_SCU_CCUCON3_Bits.PLLDIV */
#define IFX_SCU_CCUCON3_PLLDIV_MSK (0x3fu)

/** \brief  Offset for Ifx_SCU_CCUCON3_Bits.PLLDIV */
#define IFX_SCU_CCUCON3_PLLDIV_OFF (0u)

/** \brief  Length for Ifx_SCU_CCUCON3_Bits.PLLERAYDIV */
#define IFX_SCU_CCUCON3_PLLERAYDIV_LEN (6u)

/** \brief  Mask for Ifx_SCU_CCUCON3_Bits.PLLERAYDIV */
#define IFX_SCU_CCUCON3_PLLERAYDIV_MSK (0x3fu)

/** \brief  Offset for Ifx_SCU_CCUCON3_Bits.PLLERAYDIV */
#define IFX_SCU_CCUCON3_PLLERAYDIV_OFF (8u)

/** \brief  Length for Ifx_SCU_CCUCON3_Bits.PLLERAYSEL */
#define IFX_SCU_CCUCON3_PLLERAYSEL_LEN (2u)

/** \brief  Mask for Ifx_SCU_CCUCON3_Bits.PLLERAYSEL */
#define IFX_SCU_CCUCON3_PLLERAYSEL_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_CCUCON3_Bits.PLLERAYSEL */
#define IFX_SCU_CCUCON3_PLLERAYSEL_OFF (14u)

/** \brief  Length for Ifx_SCU_CCUCON3_Bits.PLLSEL */
#define IFX_SCU_CCUCON3_PLLSEL_LEN (2u)

/** \brief  Mask for Ifx_SCU_CCUCON3_Bits.PLLSEL */
#define IFX_SCU_CCUCON3_PLLSEL_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_CCUCON3_Bits.PLLSEL */
#define IFX_SCU_CCUCON3_PLLSEL_OFF (6u)

/** \brief  Length for Ifx_SCU_CCUCON3_Bits.SRIDIV */
#define IFX_SCU_CCUCON3_SRIDIV_LEN (6u)

/** \brief  Mask for Ifx_SCU_CCUCON3_Bits.SRIDIV */
#define IFX_SCU_CCUCON3_SRIDIV_MSK (0x3fu)

/** \brief  Offset for Ifx_SCU_CCUCON3_Bits.SRIDIV */
#define IFX_SCU_CCUCON3_SRIDIV_OFF (16u)

/** \brief  Length for Ifx_SCU_CCUCON3_Bits.SRISEL */
#define IFX_SCU_CCUCON3_SRISEL_LEN (2u)

/** \brief  Mask for Ifx_SCU_CCUCON3_Bits.SRISEL */
#define IFX_SCU_CCUCON3_SRISEL_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_CCUCON3_Bits.SRISEL */
#define IFX_SCU_CCUCON3_SRISEL_OFF (22u)

/** \brief  Length for Ifx_SCU_CCUCON3_Bits.UP */
#define IFX_SCU_CCUCON3_UP_LEN (1u)

/** \brief  Mask for Ifx_SCU_CCUCON3_Bits.UP */
#define IFX_SCU_CCUCON3_UP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CCUCON3_Bits.UP */
#define IFX_SCU_CCUCON3_UP_OFF (30u)

/** \brief  Length for Ifx_SCU_CCUCON4_Bits.GTMDIV */
#define IFX_SCU_CCUCON4_GTMDIV_LEN (6u)

/** \brief  Mask for Ifx_SCU_CCUCON4_Bits.GTMDIV */
#define IFX_SCU_CCUCON4_GTMDIV_MSK (0x3fu)

/** \brief  Offset for Ifx_SCU_CCUCON4_Bits.GTMDIV */
#define IFX_SCU_CCUCON4_GTMDIV_OFF (8u)

/** \brief  Length for Ifx_SCU_CCUCON4_Bits.GTMSEL */
#define IFX_SCU_CCUCON4_GTMSEL_LEN (2u)

/** \brief  Mask for Ifx_SCU_CCUCON4_Bits.GTMSEL */
#define IFX_SCU_CCUCON4_GTMSEL_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_CCUCON4_Bits.GTMSEL */
#define IFX_SCU_CCUCON4_GTMSEL_OFF (14u)

/** \brief  Length for Ifx_SCU_CCUCON4_Bits.LCK */
#define IFX_SCU_CCUCON4_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_CCUCON4_Bits.LCK */
#define IFX_SCU_CCUCON4_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CCUCON4_Bits.LCK */
#define IFX_SCU_CCUCON4_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_CCUCON4_Bits.SPBDIV */
#define IFX_SCU_CCUCON4_SPBDIV_LEN (6u)

/** \brief  Mask for Ifx_SCU_CCUCON4_Bits.SPBDIV */
#define IFX_SCU_CCUCON4_SPBDIV_MSK (0x3fu)

/** \brief  Offset for Ifx_SCU_CCUCON4_Bits.SPBDIV */
#define IFX_SCU_CCUCON4_SPBDIV_OFF (0u)

/** \brief  Length for Ifx_SCU_CCUCON4_Bits.SPBSEL */
#define IFX_SCU_CCUCON4_SPBSEL_LEN (2u)

/** \brief  Mask for Ifx_SCU_CCUCON4_Bits.SPBSEL */
#define IFX_SCU_CCUCON4_SPBSEL_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_CCUCON4_Bits.SPBSEL */
#define IFX_SCU_CCUCON4_SPBSEL_OFF (6u)

/** \brief  Length for Ifx_SCU_CCUCON4_Bits.STMDIV */
#define IFX_SCU_CCUCON4_STMDIV_LEN (6u)

/** \brief  Mask for Ifx_SCU_CCUCON4_Bits.STMDIV */
#define IFX_SCU_CCUCON4_STMDIV_MSK (0x3fu)

/** \brief  Offset for Ifx_SCU_CCUCON4_Bits.STMDIV */
#define IFX_SCU_CCUCON4_STMDIV_OFF (16u)

/** \brief  Length for Ifx_SCU_CCUCON4_Bits.STMSEL */
#define IFX_SCU_CCUCON4_STMSEL_LEN (2u)

/** \brief  Mask for Ifx_SCU_CCUCON4_Bits.STMSEL */
#define IFX_SCU_CCUCON4_STMSEL_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_CCUCON4_Bits.STMSEL */
#define IFX_SCU_CCUCON4_STMSEL_OFF (22u)

/** \brief  Length for Ifx_SCU_CCUCON4_Bits.UP */
#define IFX_SCU_CCUCON4_UP_LEN (1u)

/** \brief  Mask for Ifx_SCU_CCUCON4_Bits.UP */
#define IFX_SCU_CCUCON4_UP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CCUCON4_Bits.UP */
#define IFX_SCU_CCUCON4_UP_OFF (30u)

/** \brief  Length for Ifx_SCU_CCUCON5_Bits.LCK */
#define IFX_SCU_CCUCON5_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_CCUCON5_Bits.LCK */
#define IFX_SCU_CCUCON5_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CCUCON5_Bits.LCK */
#define IFX_SCU_CCUCON5_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_CCUCON5_Bits.MAXDIV */
#define IFX_SCU_CCUCON5_MAXDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_CCUCON5_Bits.MAXDIV */
#define IFX_SCU_CCUCON5_MAXDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CCUCON5_Bits.MAXDIV */
#define IFX_SCU_CCUCON5_MAXDIV_OFF (0u)

/** \brief  Length for Ifx_SCU_CCUCON5_Bits.UP */
#define IFX_SCU_CCUCON5_UP_LEN (1u)

/** \brief  Mask for Ifx_SCU_CCUCON5_Bits.UP */
#define IFX_SCU_CCUCON5_UP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CCUCON5_Bits.UP */
#define IFX_SCU_CCUCON5_UP_OFF (30u)

/** \brief  Length for Ifx_SCU_CCUCON6_Bits.CPU0DIV */
#define IFX_SCU_CCUCON6_CPU0DIV_LEN (6u)

/** \brief  Mask for Ifx_SCU_CCUCON6_Bits.CPU0DIV */
#define IFX_SCU_CCUCON6_CPU0DIV_MSK (0x3fu)

/** \brief  Offset for Ifx_SCU_CCUCON6_Bits.CPU0DIV */
#define IFX_SCU_CCUCON6_CPU0DIV_OFF (0u)

/** \brief  Length for Ifx_SCU_CHIPID_Bits.CHID */
#define IFX_SCU_CHIPID_CHID_LEN (8u)

/** \brief  Mask for Ifx_SCU_CHIPID_Bits.CHID */
#define IFX_SCU_CHIPID_CHID_MSK (0xffu)

/** \brief  Offset for Ifx_SCU_CHIPID_Bits.CHID */
#define IFX_SCU_CHIPID_CHID_OFF (8u)

/** \brief  Length for Ifx_SCU_CHIPID_Bits.CHREV */
#define IFX_SCU_CHIPID_CHREV_LEN (6u)

/** \brief  Mask for Ifx_SCU_CHIPID_Bits.CHREV */
#define IFX_SCU_CHIPID_CHREV_MSK (0x3fu)

/** \brief  Offset for Ifx_SCU_CHIPID_Bits.CHREV */
#define IFX_SCU_CHIPID_CHREV_OFF (0u)

/** \brief  Length for Ifx_SCU_CHIPID_Bits.CHTEC */
#define IFX_SCU_CHIPID_CHTEC_LEN (2u)

/** \brief  Mask for Ifx_SCU_CHIPID_Bits.CHTEC */
#define IFX_SCU_CHIPID_CHTEC_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_CHIPID_Bits.CHTEC */
#define IFX_SCU_CHIPID_CHTEC_OFF (6u)

/** \brief  Length for Ifx_SCU_CHIPID_Bits.EEA */
#define IFX_SCU_CHIPID_EEA_LEN (1u)

/** \brief  Mask for Ifx_SCU_CHIPID_Bits.EEA */
#define IFX_SCU_CHIPID_EEA_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CHIPID_Bits.EEA */
#define IFX_SCU_CHIPID_EEA_OFF (16u)

/** \brief  Length for Ifx_SCU_CHIPID_Bits.FSIZE */
#define IFX_SCU_CHIPID_FSIZE_LEN (4u)

/** \brief  Mask for Ifx_SCU_CHIPID_Bits.FSIZE */
#define IFX_SCU_CHIPID_FSIZE_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_CHIPID_Bits.FSIZE */
#define IFX_SCU_CHIPID_FSIZE_OFF (24u)

/** \brief  Length for Ifx_SCU_CHIPID_Bits.SEC */
#define IFX_SCU_CHIPID_SEC_LEN (1u)

/** \brief  Mask for Ifx_SCU_CHIPID_Bits.SEC */
#define IFX_SCU_CHIPID_SEC_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_CHIPID_Bits.SEC */
#define IFX_SCU_CHIPID_SEC_OFF (30u)

/** \brief  Length for Ifx_SCU_CHIPID_Bits.SP */
#define IFX_SCU_CHIPID_SP_LEN (2u)

/** \brief  Mask for Ifx_SCU_CHIPID_Bits.SP */
#define IFX_SCU_CHIPID_SP_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_CHIPID_Bits.SP */
#define IFX_SCU_CHIPID_SP_OFF (28u)

/** \brief  Length for Ifx_SCU_CHIPID_Bits.UCODE */
#define IFX_SCU_CHIPID_UCODE_LEN (7u)

/** \brief  Mask for Ifx_SCU_CHIPID_Bits.UCODE */
#define IFX_SCU_CHIPID_UCODE_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_CHIPID_Bits.UCODE */
#define IFX_SCU_CHIPID_UCODE_OFF (17u)

/** \brief  Length for Ifx_SCU_DTSCON_Bits.CAL */
#define IFX_SCU_DTSCON_CAL_LEN (22u)

/** \brief  Mask for Ifx_SCU_DTSCON_Bits.CAL */
#define IFX_SCU_DTSCON_CAL_MSK (0x3fffffu)

/** \brief  Offset for Ifx_SCU_DTSCON_Bits.CAL */
#define IFX_SCU_DTSCON_CAL_OFF (4u)

/** \brief  Length for Ifx_SCU_DTSCON_Bits.PWD */
#define IFX_SCU_DTSCON_PWD_LEN (1u)

/** \brief  Mask for Ifx_SCU_DTSCON_Bits.PWD */
#define IFX_SCU_DTSCON_PWD_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_DTSCON_Bits.PWD */
#define IFX_SCU_DTSCON_PWD_OFF (0u)

/** \brief  Length for Ifx_SCU_DTSCON_Bits.SLCK */
#define IFX_SCU_DTSCON_SLCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_DTSCON_Bits.SLCK */
#define IFX_SCU_DTSCON_SLCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_DTSCON_Bits.SLCK */
#define IFX_SCU_DTSCON_SLCK_OFF (31u)

/** \brief  Length for Ifx_SCU_DTSCON_Bits.START */
#define IFX_SCU_DTSCON_START_LEN (1u)

/** \brief  Mask for Ifx_SCU_DTSCON_Bits.START */
#define IFX_SCU_DTSCON_START_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_DTSCON_Bits.START */
#define IFX_SCU_DTSCON_START_OFF (1u)

/** \brief  Length for Ifx_SCU_DTSLIM_Bits.LLU */
#define IFX_SCU_DTSLIM_LLU_LEN (1u)

/** \brief  Mask for Ifx_SCU_DTSLIM_Bits.LLU */
#define IFX_SCU_DTSLIM_LLU_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_DTSLIM_Bits.LLU */
#define IFX_SCU_DTSLIM_LLU_OFF (15u)

/** \brief  Length for Ifx_SCU_DTSLIM_Bits.LOWER */
#define IFX_SCU_DTSLIM_LOWER_LEN (10u)

/** \brief  Mask for Ifx_SCU_DTSLIM_Bits.LOWER */
#define IFX_SCU_DTSLIM_LOWER_MSK (0x3ffu)

/** \brief  Offset for Ifx_SCU_DTSLIM_Bits.LOWER */
#define IFX_SCU_DTSLIM_LOWER_OFF (0u)

/** \brief  Length for Ifx_SCU_DTSLIM_Bits.SLCK */
#define IFX_SCU_DTSLIM_SLCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_DTSLIM_Bits.SLCK */
#define IFX_SCU_DTSLIM_SLCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_DTSLIM_Bits.SLCK */
#define IFX_SCU_DTSLIM_SLCK_OFF (30u)

/** \brief  Length for Ifx_SCU_DTSLIM_Bits.UOF */
#define IFX_SCU_DTSLIM_UOF_LEN (1u)

/** \brief  Mask for Ifx_SCU_DTSLIM_Bits.UOF */
#define IFX_SCU_DTSLIM_UOF_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_DTSLIM_Bits.UOF */
#define IFX_SCU_DTSLIM_UOF_OFF (31u)

/** \brief  Length for Ifx_SCU_DTSLIM_Bits.UPPER */
#define IFX_SCU_DTSLIM_UPPER_LEN (10u)

/** \brief  Mask for Ifx_SCU_DTSLIM_Bits.UPPER */
#define IFX_SCU_DTSLIM_UPPER_MSK (0x3ffu)

/** \brief  Offset for Ifx_SCU_DTSLIM_Bits.UPPER */
#define IFX_SCU_DTSLIM_UPPER_OFF (16u)

/** \brief  Length for Ifx_SCU_DTSSTAT_Bits.BUSY */
#define IFX_SCU_DTSSTAT_BUSY_LEN (1u)

/** \brief  Mask for Ifx_SCU_DTSSTAT_Bits.BUSY */
#define IFX_SCU_DTSSTAT_BUSY_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_DTSSTAT_Bits.BUSY */
#define IFX_SCU_DTSSTAT_BUSY_OFF (15u)

/** \brief  Length for Ifx_SCU_DTSSTAT_Bits.RDY */
#define IFX_SCU_DTSSTAT_RDY_LEN (1u)

/** \brief  Mask for Ifx_SCU_DTSSTAT_Bits.RDY */
#define IFX_SCU_DTSSTAT_RDY_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_DTSSTAT_Bits.RDY */
#define IFX_SCU_DTSSTAT_RDY_OFF (14u)

/** \brief  Length for Ifx_SCU_DTSSTAT_Bits.RESULT */
#define IFX_SCU_DTSSTAT_RESULT_LEN (10u)

/** \brief  Mask for Ifx_SCU_DTSSTAT_Bits.RESULT */
#define IFX_SCU_DTSSTAT_RESULT_MSK (0x3ffu)

/** \brief  Offset for Ifx_SCU_DTSSTAT_Bits.RESULT */
#define IFX_SCU_DTSSTAT_RESULT_OFF (0u)

/** \brief  Length for Ifx_SCU_EICR_Bits.EIEN0 */
#define IFX_SCU_EICR_EIEN0_LEN (1u)

/** \brief  Mask for Ifx_SCU_EICR_Bits.EIEN0 */
#define IFX_SCU_EICR_EIEN0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EICR_Bits.EIEN0 */
#define IFX_SCU_EICR_EIEN0_OFF (11u)

/** \brief  Length for Ifx_SCU_EICR_Bits.EIEN1 */
#define IFX_SCU_EICR_EIEN1_LEN (1u)

/** \brief  Mask for Ifx_SCU_EICR_Bits.EIEN1 */
#define IFX_SCU_EICR_EIEN1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EICR_Bits.EIEN1 */
#define IFX_SCU_EICR_EIEN1_OFF (27u)

/** \brief  Length for Ifx_SCU_EICR_Bits.EXIS0 */
#define IFX_SCU_EICR_EXIS0_LEN (3u)

/** \brief  Mask for Ifx_SCU_EICR_Bits.EXIS0 */
#define IFX_SCU_EICR_EXIS0_MSK (0x7u)

/** \brief  Offset for Ifx_SCU_EICR_Bits.EXIS0 */
#define IFX_SCU_EICR_EXIS0_OFF (4u)

/** \brief  Length for Ifx_SCU_EICR_Bits.EXIS1 */
#define IFX_SCU_EICR_EXIS1_LEN (3u)

/** \brief  Mask for Ifx_SCU_EICR_Bits.EXIS1 */
#define IFX_SCU_EICR_EXIS1_MSK (0x7u)

/** \brief  Offset for Ifx_SCU_EICR_Bits.EXIS1 */
#define IFX_SCU_EICR_EXIS1_OFF (20u)

/** \brief  Length for Ifx_SCU_EICR_Bits.FEN0 */
#define IFX_SCU_EICR_FEN0_LEN (1u)

/** \brief  Mask for Ifx_SCU_EICR_Bits.FEN0 */
#define IFX_SCU_EICR_FEN0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EICR_Bits.FEN0 */
#define IFX_SCU_EICR_FEN0_OFF (8u)

/** \brief  Length for Ifx_SCU_EICR_Bits.FEN1 */
#define IFX_SCU_EICR_FEN1_LEN (1u)

/** \brief  Mask for Ifx_SCU_EICR_Bits.FEN1 */
#define IFX_SCU_EICR_FEN1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EICR_Bits.FEN1 */
#define IFX_SCU_EICR_FEN1_OFF (24u)

/** \brief  Length for Ifx_SCU_EICR_Bits.INP0 */
#define IFX_SCU_EICR_INP0_LEN (3u)

/** \brief  Mask for Ifx_SCU_EICR_Bits.INP0 */
#define IFX_SCU_EICR_INP0_MSK (0x7u)

/** \brief  Offset for Ifx_SCU_EICR_Bits.INP0 */
#define IFX_SCU_EICR_INP0_OFF (12u)

/** \brief  Length for Ifx_SCU_EICR_Bits.INP1 */
#define IFX_SCU_EICR_INP1_LEN (3u)

/** \brief  Mask for Ifx_SCU_EICR_Bits.INP1 */
#define IFX_SCU_EICR_INP1_MSK (0x7u)

/** \brief  Offset for Ifx_SCU_EICR_Bits.INP1 */
#define IFX_SCU_EICR_INP1_OFF (28u)

/** \brief  Length for Ifx_SCU_EICR_Bits.LDEN0 */
#define IFX_SCU_EICR_LDEN0_LEN (1u)

/** \brief  Mask for Ifx_SCU_EICR_Bits.LDEN0 */
#define IFX_SCU_EICR_LDEN0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EICR_Bits.LDEN0 */
#define IFX_SCU_EICR_LDEN0_OFF (10u)

/** \brief  Length for Ifx_SCU_EICR_Bits.LDEN1 */
#define IFX_SCU_EICR_LDEN1_LEN (1u)

/** \brief  Mask for Ifx_SCU_EICR_Bits.LDEN1 */
#define IFX_SCU_EICR_LDEN1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EICR_Bits.LDEN1 */
#define IFX_SCU_EICR_LDEN1_OFF (26u)

/** \brief  Length for Ifx_SCU_EICR_Bits.REN0 */
#define IFX_SCU_EICR_REN0_LEN (1u)

/** \brief  Mask for Ifx_SCU_EICR_Bits.REN0 */
#define IFX_SCU_EICR_REN0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EICR_Bits.REN0 */
#define IFX_SCU_EICR_REN0_OFF (9u)

/** \brief  Length for Ifx_SCU_EICR_Bits.REN1 */
#define IFX_SCU_EICR_REN1_LEN (1u)

/** \brief  Mask for Ifx_SCU_EICR_Bits.REN1 */
#define IFX_SCU_EICR_REN1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EICR_Bits.REN1 */
#define IFX_SCU_EICR_REN1_OFF (25u)

/** \brief  Length for Ifx_SCU_EIFR_Bits.INTF0 */
#define IFX_SCU_EIFR_INTF0_LEN (1u)

/** \brief  Mask for Ifx_SCU_EIFR_Bits.INTF0 */
#define IFX_SCU_EIFR_INTF0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EIFR_Bits.INTF0 */
#define IFX_SCU_EIFR_INTF0_OFF (0u)

/** \brief  Length for Ifx_SCU_EIFR_Bits.INTF1 */
#define IFX_SCU_EIFR_INTF1_LEN (1u)

/** \brief  Mask for Ifx_SCU_EIFR_Bits.INTF1 */
#define IFX_SCU_EIFR_INTF1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EIFR_Bits.INTF1 */
#define IFX_SCU_EIFR_INTF1_OFF (1u)

/** \brief  Length for Ifx_SCU_EIFR_Bits.INTF2 */
#define IFX_SCU_EIFR_INTF2_LEN (1u)

/** \brief  Mask for Ifx_SCU_EIFR_Bits.INTF2 */
#define IFX_SCU_EIFR_INTF2_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EIFR_Bits.INTF2 */
#define IFX_SCU_EIFR_INTF2_OFF (2u)

/** \brief  Length for Ifx_SCU_EIFR_Bits.INTF3 */
#define IFX_SCU_EIFR_INTF3_LEN (1u)

/** \brief  Mask for Ifx_SCU_EIFR_Bits.INTF3 */
#define IFX_SCU_EIFR_INTF3_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EIFR_Bits.INTF3 */
#define IFX_SCU_EIFR_INTF3_OFF (3u)

/** \brief  Length for Ifx_SCU_EIFR_Bits.INTF4 */
#define IFX_SCU_EIFR_INTF4_LEN (1u)

/** \brief  Mask for Ifx_SCU_EIFR_Bits.INTF4 */
#define IFX_SCU_EIFR_INTF4_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EIFR_Bits.INTF4 */
#define IFX_SCU_EIFR_INTF4_OFF (4u)

/** \brief  Length for Ifx_SCU_EIFR_Bits.INTF5 */
#define IFX_SCU_EIFR_INTF5_LEN (1u)

/** \brief  Mask for Ifx_SCU_EIFR_Bits.INTF5 */
#define IFX_SCU_EIFR_INTF5_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EIFR_Bits.INTF5 */
#define IFX_SCU_EIFR_INTF5_OFF (5u)

/** \brief  Length for Ifx_SCU_EIFR_Bits.INTF6 */
#define IFX_SCU_EIFR_INTF6_LEN (1u)

/** \brief  Mask for Ifx_SCU_EIFR_Bits.INTF6 */
#define IFX_SCU_EIFR_INTF6_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EIFR_Bits.INTF6 */
#define IFX_SCU_EIFR_INTF6_OFF (6u)

/** \brief  Length for Ifx_SCU_EIFR_Bits.INTF7 */
#define IFX_SCU_EIFR_INTF7_LEN (1u)

/** \brief  Mask for Ifx_SCU_EIFR_Bits.INTF7 */
#define IFX_SCU_EIFR_INTF7_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EIFR_Bits.INTF7 */
#define IFX_SCU_EIFR_INTF7_OFF (7u)

/** \brief  Length for Ifx_SCU_EMSR_Bits.EMSF */
#define IFX_SCU_EMSR_EMSF_LEN (1u)

/** \brief  Mask for Ifx_SCU_EMSR_Bits.EMSF */
#define IFX_SCU_EMSR_EMSF_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EMSR_Bits.EMSF */
#define IFX_SCU_EMSR_EMSF_OFF (16u)

/** \brief  Length for Ifx_SCU_EMSR_Bits.EMSFM */
#define IFX_SCU_EMSR_EMSFM_LEN (2u)

/** \brief  Mask for Ifx_SCU_EMSR_Bits.EMSFM */
#define IFX_SCU_EMSR_EMSFM_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_EMSR_Bits.EMSFM */
#define IFX_SCU_EMSR_EMSFM_OFF (24u)

/** \brief  Length for Ifx_SCU_EMSR_Bits.ENON */
#define IFX_SCU_EMSR_ENON_LEN (1u)

/** \brief  Mask for Ifx_SCU_EMSR_Bits.ENON */
#define IFX_SCU_EMSR_ENON_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EMSR_Bits.ENON */
#define IFX_SCU_EMSR_ENON_OFF (2u)

/** \brief  Length for Ifx_SCU_EMSR_Bits.MODE */
#define IFX_SCU_EMSR_MODE_LEN (1u)

/** \brief  Mask for Ifx_SCU_EMSR_Bits.MODE */
#define IFX_SCU_EMSR_MODE_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EMSR_Bits.MODE */
#define IFX_SCU_EMSR_MODE_OFF (1u)

/** \brief  Length for Ifx_SCU_EMSR_Bits.POL */
#define IFX_SCU_EMSR_POL_LEN (1u)

/** \brief  Mask for Ifx_SCU_EMSR_Bits.POL */
#define IFX_SCU_EMSR_POL_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EMSR_Bits.POL */
#define IFX_SCU_EMSR_POL_OFF (0u)

/** \brief  Length for Ifx_SCU_EMSR_Bits.PSEL */
#define IFX_SCU_EMSR_PSEL_LEN (1u)

/** \brief  Mask for Ifx_SCU_EMSR_Bits.PSEL */
#define IFX_SCU_EMSR_PSEL_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EMSR_Bits.PSEL */
#define IFX_SCU_EMSR_PSEL_OFF (3u)

/** \brief  Length for Ifx_SCU_EMSR_Bits.SEMSF */
#define IFX_SCU_EMSR_SEMSF_LEN (1u)

/** \brief  Mask for Ifx_SCU_EMSR_Bits.SEMSF */
#define IFX_SCU_EMSR_SEMSF_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EMSR_Bits.SEMSF */
#define IFX_SCU_EMSR_SEMSF_OFF (17u)

/** \brief  Length for Ifx_SCU_EMSR_Bits.SEMSFM */
#define IFX_SCU_EMSR_SEMSFM_LEN (2u)

/** \brief  Mask for Ifx_SCU_EMSR_Bits.SEMSFM */
#define IFX_SCU_EMSR_SEMSFM_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_EMSR_Bits.SEMSFM */
#define IFX_SCU_EMSR_SEMSFM_OFF (26u)

/** \brief  Length for Ifx_SCU_ESRCFG_Bits.EDCON */
#define IFX_SCU_ESRCFG_EDCON_LEN (2u)

/** \brief  Mask for Ifx_SCU_ESRCFG_Bits.EDCON */
#define IFX_SCU_ESRCFG_EDCON_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_ESRCFG_Bits.EDCON */
#define IFX_SCU_ESRCFG_EDCON_OFF (7u)

/** \brief  Length for Ifx_SCU_ESROCFG_Bits.ARC */
#define IFX_SCU_ESROCFG_ARC_LEN (1u)

/** \brief  Mask for Ifx_SCU_ESROCFG_Bits.ARC */
#define IFX_SCU_ESROCFG_ARC_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ESROCFG_Bits.ARC */
#define IFX_SCU_ESROCFG_ARC_OFF (1u)

/** \brief  Length for Ifx_SCU_ESROCFG_Bits.ARI */
#define IFX_SCU_ESROCFG_ARI_LEN (1u)

/** \brief  Mask for Ifx_SCU_ESROCFG_Bits.ARI */
#define IFX_SCU_ESROCFG_ARI_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_ESROCFG_Bits.ARI */
#define IFX_SCU_ESROCFG_ARI_OFF (0u)

/** \brief  Length for Ifx_SCU_EVR13CON_Bits.BPEVR13OFF */
#define IFX_SCU_EVR13CON_BPEVR13OFF_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVR13CON_Bits.BPEVR13OFF */
#define IFX_SCU_EVR13CON_BPEVR13OFF_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVR13CON_Bits.BPEVR13OFF */
#define IFX_SCU_EVR13CON_BPEVR13OFF_OFF (29u)

/** \brief  Length for Ifx_SCU_EVR13CON_Bits.EVR13OFF */
#define IFX_SCU_EVR13CON_EVR13OFF_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVR13CON_Bits.EVR13OFF */
#define IFX_SCU_EVR13CON_EVR13OFF_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVR13CON_Bits.EVR13OFF */
#define IFX_SCU_EVR13CON_EVR13OFF_OFF (28u)

/** \brief  Length for Ifx_SCU_EVR13CON_Bits.LCK */
#define IFX_SCU_EVR13CON_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVR13CON_Bits.LCK */
#define IFX_SCU_EVR13CON_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVR13CON_Bits.LCK */
#define IFX_SCU_EVR13CON_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_EVRADCSTAT_Bits.ADC13V */
#define IFX_SCU_EVRADCSTAT_ADC13V_LEN (8u)

/** \brief  Mask for Ifx_SCU_EVRADCSTAT_Bits.ADC13V */
#define IFX_SCU_EVRADCSTAT_ADC13V_MSK (0xffu)

/** \brief  Offset for Ifx_SCU_EVRADCSTAT_Bits.ADC13V */
#define IFX_SCU_EVRADCSTAT_ADC13V_OFF (0u)

/** \brief  Length for Ifx_SCU_EVRADCSTAT_Bits.ADCSWDV */
#define IFX_SCU_EVRADCSTAT_ADCSWDV_LEN (8u)

/** \brief  Mask for Ifx_SCU_EVRADCSTAT_Bits.ADCSWDV */
#define IFX_SCU_EVRADCSTAT_ADCSWDV_MSK (0xffu)

/** \brief  Offset for Ifx_SCU_EVRADCSTAT_Bits.ADCSWDV */
#define IFX_SCU_EVRADCSTAT_ADCSWDV_OFF (16u)

/** \brief  Length for Ifx_SCU_EVRADCSTAT_Bits.VAL */
#define IFX_SCU_EVRADCSTAT_VAL_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRADCSTAT_Bits.VAL */
#define IFX_SCU_EVRADCSTAT_VAL_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRADCSTAT_Bits.VAL */
#define IFX_SCU_EVRADCSTAT_VAL_OFF (31u)

/** \brief  Length for Ifx_SCU_EVRMONCTRL_Bits.EVR13OVMOD */
#define IFX_SCU_EVRMONCTRL_EVR13OVMOD_LEN (2u)

/** \brief  Mask for Ifx_SCU_EVRMONCTRL_Bits.EVR13OVMOD */
#define IFX_SCU_EVRMONCTRL_EVR13OVMOD_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_EVRMONCTRL_Bits.EVR13OVMOD */
#define IFX_SCU_EVRMONCTRL_EVR13OVMOD_OFF (0u)

/** \brief  Length for Ifx_SCU_EVRMONCTRL_Bits.EVR13UVMOD */
#define IFX_SCU_EVRMONCTRL_EVR13UVMOD_LEN (2u)

/** \brief  Mask for Ifx_SCU_EVRMONCTRL_Bits.EVR13UVMOD */
#define IFX_SCU_EVRMONCTRL_EVR13UVMOD_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_EVRMONCTRL_Bits.EVR13UVMOD */
#define IFX_SCU_EVRMONCTRL_EVR13UVMOD_OFF (4u)

/** \brief  Length for Ifx_SCU_EVRMONCTRL_Bits.SLCK */
#define IFX_SCU_EVRMONCTRL_SLCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRMONCTRL_Bits.SLCK */
#define IFX_SCU_EVRMONCTRL_SLCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRMONCTRL_Bits.SLCK */
#define IFX_SCU_EVRMONCTRL_SLCK_OFF (30u)

/** \brief  Length for Ifx_SCU_EVRMONCTRL_Bits.SWDOVMOD */
#define IFX_SCU_EVRMONCTRL_SWDOVMOD_LEN (2u)

/** \brief  Mask for Ifx_SCU_EVRMONCTRL_Bits.SWDOVMOD */
#define IFX_SCU_EVRMONCTRL_SWDOVMOD_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_EVRMONCTRL_Bits.SWDOVMOD */
#define IFX_SCU_EVRMONCTRL_SWDOVMOD_OFF (16u)

/** \brief  Length for Ifx_SCU_EVRMONCTRL_Bits.SWDUVMOD */
#define IFX_SCU_EVRMONCTRL_SWDUVMOD_LEN (2u)

/** \brief  Mask for Ifx_SCU_EVRMONCTRL_Bits.SWDUVMOD */
#define IFX_SCU_EVRMONCTRL_SWDUVMOD_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_EVRMONCTRL_Bits.SWDUVMOD */
#define IFX_SCU_EVRMONCTRL_SWDUVMOD_OFF (20u)

/** \brief  Length for Ifx_SCU_EVROVMON_Bits.EVR13OVVAL */
#define IFX_SCU_EVROVMON_EVR13OVVAL_LEN (8u)

/** \brief  Mask for Ifx_SCU_EVROVMON_Bits.EVR13OVVAL */
#define IFX_SCU_EVROVMON_EVR13OVVAL_MSK (0xffu)

/** \brief  Offset for Ifx_SCU_EVROVMON_Bits.EVR13OVVAL */
#define IFX_SCU_EVROVMON_EVR13OVVAL_OFF (0u)

/** \brief  Length for Ifx_SCU_EVROVMON_Bits.LCK */
#define IFX_SCU_EVROVMON_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVROVMON_Bits.LCK */
#define IFX_SCU_EVROVMON_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVROVMON_Bits.LCK */
#define IFX_SCU_EVROVMON_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_EVROVMON_Bits.SLCK */
#define IFX_SCU_EVROVMON_SLCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVROVMON_Bits.SLCK */
#define IFX_SCU_EVROVMON_SLCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVROVMON_Bits.SLCK */
#define IFX_SCU_EVROVMON_SLCK_OFF (30u)

/** \brief  Length for Ifx_SCU_EVROVMON_Bits.SWDOVVAL */
#define IFX_SCU_EVROVMON_SWDOVVAL_LEN (8u)

/** \brief  Mask for Ifx_SCU_EVROVMON_Bits.SWDOVVAL */
#define IFX_SCU_EVROVMON_SWDOVVAL_MSK (0xffu)

/** \brief  Offset for Ifx_SCU_EVROVMON_Bits.SWDOVVAL */
#define IFX_SCU_EVROVMON_SWDOVVAL_OFF (16u)

/** \brief  Length for Ifx_SCU_EVRRSTCON_Bits.BPRSTSWDOFF */
#define IFX_SCU_EVRRSTCON_BPRSTSWDOFF_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRRSTCON_Bits.BPRSTSWDOFF */
#define IFX_SCU_EVRRSTCON_BPRSTSWDOFF_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRRSTCON_Bits.BPRSTSWDOFF */
#define IFX_SCU_EVRRSTCON_BPRSTSWDOFF_OFF (29u)

/** \brief  Length for Ifx_SCU_EVRRSTCON_Bits.LCK */
#define IFX_SCU_EVRRSTCON_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRRSTCON_Bits.LCK */
#define IFX_SCU_EVRRSTCON_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRRSTCON_Bits.LCK */
#define IFX_SCU_EVRRSTCON_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_EVRRSTCON_Bits.RSTSWDOFF */
#define IFX_SCU_EVRRSTCON_RSTSWDOFF_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRRSTCON_Bits.RSTSWDOFF */
#define IFX_SCU_EVRRSTCON_RSTSWDOFF_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRRSTCON_Bits.RSTSWDOFF */
#define IFX_SCU_EVRRSTCON_RSTSWDOFF_OFF (28u)

/** \brief  Length for Ifx_SCU_EVRRSTCON_Bits.SLCK */
#define IFX_SCU_EVRRSTCON_SLCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRRSTCON_Bits.SLCK */
#define IFX_SCU_EVRRSTCON_SLCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRRSTCON_Bits.SLCK */
#define IFX_SCU_EVRRSTCON_SLCK_OFF (30u)

/** \brief  Length for Ifx_SCU_EVRSDCOEFF2_Bits.LCK */
#define IFX_SCU_EVRSDCOEFF2_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSDCOEFF2_Bits.LCK */
#define IFX_SCU_EVRSDCOEFF2_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSDCOEFF2_Bits.LCK */
#define IFX_SCU_EVRSDCOEFF2_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_EVRSDCOEFF2_Bits.SD33I */
#define IFX_SCU_EVRSDCOEFF2_SD33I_LEN (4u)

/** \brief  Mask for Ifx_SCU_EVRSDCOEFF2_Bits.SD33I */
#define IFX_SCU_EVRSDCOEFF2_SD33I_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_EVRSDCOEFF2_Bits.SD33I */
#define IFX_SCU_EVRSDCOEFF2_SD33I_OFF (8u)

/** \brief  Length for Ifx_SCU_EVRSDCOEFF2_Bits.SD33P */
#define IFX_SCU_EVRSDCOEFF2_SD33P_LEN (4u)

/** \brief  Mask for Ifx_SCU_EVRSDCOEFF2_Bits.SD33P */
#define IFX_SCU_EVRSDCOEFF2_SD33P_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_EVRSDCOEFF2_Bits.SD33P */
#define IFX_SCU_EVRSDCOEFF2_SD33P_OFF (0u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL1_Bits.LCK */
#define IFX_SCU_EVRSDCTRL1_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL1_Bits.LCK */
#define IFX_SCU_EVRSDCTRL1_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL1_Bits.LCK */
#define IFX_SCU_EVRSDCTRL1_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL1_Bits.SDFREQSPRD */
#define IFX_SCU_EVRSDCTRL1_SDFREQSPRD_LEN (4u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL1_Bits.SDFREQSPRD */
#define IFX_SCU_EVRSDCTRL1_SDFREQSPRD_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL1_Bits.SDFREQSPRD */
#define IFX_SCU_EVRSDCTRL1_SDFREQSPRD_OFF (0u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL1_Bits.SDSTEP */
#define IFX_SCU_EVRSDCTRL1_SDSTEP_LEN (4u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL1_Bits.SDSTEP */
#define IFX_SCU_EVRSDCTRL1_SDSTEP_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL1_Bits.SDSTEP */
#define IFX_SCU_EVRSDCTRL1_SDSTEP_OFF (24u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL1_Bits.SYNCDIV */
#define IFX_SCU_EVRSDCTRL1_SYNCDIV_LEN (3u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL1_Bits.SYNCDIV */
#define IFX_SCU_EVRSDCTRL1_SYNCDIV_MSK (0x7u)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL1_Bits.SYNCDIV */
#define IFX_SCU_EVRSDCTRL1_SYNCDIV_OFF (28u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL1_Bits.TOFF */
#define IFX_SCU_EVRSDCTRL1_TOFF_LEN (8u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL1_Bits.TOFF */
#define IFX_SCU_EVRSDCTRL1_TOFF_MSK (0xffu)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL1_Bits.TOFF */
#define IFX_SCU_EVRSDCTRL1_TOFF_OFF (16u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL1_Bits.TON */
#define IFX_SCU_EVRSDCTRL1_TON_LEN (8u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL1_Bits.TON */
#define IFX_SCU_EVRSDCTRL1_TON_MSK (0xffu)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL1_Bits.TON */
#define IFX_SCU_EVRSDCTRL1_TON_OFF (8u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL2_Bits.ADCLPF */
#define IFX_SCU_EVRSDCTRL2_ADCLPF_LEN (2u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL2_Bits.ADCLPF */
#define IFX_SCU_EVRSDCTRL2_ADCLPF_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL2_Bits.ADCLPF */
#define IFX_SCU_EVRSDCTRL2_ADCLPF_OFF (20u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL2_Bits.ADCLSB */
#define IFX_SCU_EVRSDCTRL2_ADCLSB_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL2_Bits.ADCLSB */
#define IFX_SCU_EVRSDCTRL2_ADCLSB_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL2_Bits.ADCLSB */
#define IFX_SCU_EVRSDCTRL2_ADCLSB_OFF (22u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL2_Bits.ADCMODE */
#define IFX_SCU_EVRSDCTRL2_ADCMODE_LEN (4u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL2_Bits.ADCMODE */
#define IFX_SCU_EVRSDCTRL2_ADCMODE_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL2_Bits.ADCMODE */
#define IFX_SCU_EVRSDCTRL2_ADCMODE_OFF (16u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL2_Bits.LCK */
#define IFX_SCU_EVRSDCTRL2_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL2_Bits.LCK */
#define IFX_SCU_EVRSDCTRL2_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL2_Bits.LCK */
#define IFX_SCU_EVRSDCTRL2_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL2_Bits.NS */
#define IFX_SCU_EVRSDCTRL2_NS_LEN (2u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL2_Bits.NS */
#define IFX_SCU_EVRSDCTRL2_NS_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL2_Bits.NS */
#define IFX_SCU_EVRSDCTRL2_NS_OFF (12u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL2_Bits.OL */
#define IFX_SCU_EVRSDCTRL2_OL_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL2_Bits.OL */
#define IFX_SCU_EVRSDCTRL2_OL_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL2_Bits.OL */
#define IFX_SCU_EVRSDCTRL2_OL_OFF (14u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL2_Bits.PIAD */
#define IFX_SCU_EVRSDCTRL2_PIAD_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL2_Bits.PIAD */
#define IFX_SCU_EVRSDCTRL2_PIAD_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL2_Bits.PIAD */
#define IFX_SCU_EVRSDCTRL2_PIAD_OFF (15u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL2_Bits.SDLUT */
#define IFX_SCU_EVRSDCTRL2_SDLUT_LEN (6u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL2_Bits.SDLUT */
#define IFX_SCU_EVRSDCTRL2_SDLUT_MSK (0x3fu)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL2_Bits.SDLUT */
#define IFX_SCU_EVRSDCTRL2_SDLUT_OFF (24u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL2_Bits.STBS */
#define IFX_SCU_EVRSDCTRL2_STBS_LEN (2u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL2_Bits.STBS */
#define IFX_SCU_EVRSDCTRL2_STBS_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL2_Bits.STBS */
#define IFX_SCU_EVRSDCTRL2_STBS_OFF (8u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL2_Bits.STSP */
#define IFX_SCU_EVRSDCTRL2_STSP_LEN (2u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL2_Bits.STSP */
#define IFX_SCU_EVRSDCTRL2_STSP_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL2_Bits.STSP */
#define IFX_SCU_EVRSDCTRL2_STSP_OFF (10u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL3_Bits.LCK */
#define IFX_SCU_EVRSDCTRL3_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL3_Bits.LCK */
#define IFX_SCU_EVRSDCTRL3_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL3_Bits.LCK */
#define IFX_SCU_EVRSDCTRL3_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL3_Bits.MODHIGH */
#define IFX_SCU_EVRSDCTRL3_MODHIGH_LEN (7u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL3_Bits.MODHIGH */
#define IFX_SCU_EVRSDCTRL3_MODHIGH_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL3_Bits.MODHIGH */
#define IFX_SCU_EVRSDCTRL3_MODHIGH_OFF (24u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL3_Bits.MODLOW */
#define IFX_SCU_EVRSDCTRL3_MODLOW_LEN (7u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL3_Bits.MODLOW */
#define IFX_SCU_EVRSDCTRL3_MODLOW_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL3_Bits.MODLOW */
#define IFX_SCU_EVRSDCTRL3_MODLOW_OFF (8u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL3_Bits.MODMAN */
#define IFX_SCU_EVRSDCTRL3_MODMAN_LEN (2u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL3_Bits.MODMAN */
#define IFX_SCU_EVRSDCTRL3_MODMAN_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL3_Bits.MODMAN */
#define IFX_SCU_EVRSDCTRL3_MODMAN_OFF (22u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL3_Bits.MODSEL */
#define IFX_SCU_EVRSDCTRL3_MODSEL_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL3_Bits.MODSEL */
#define IFX_SCU_EVRSDCTRL3_MODSEL_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL3_Bits.MODSEL */
#define IFX_SCU_EVRSDCTRL3_MODSEL_OFF (7u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL3_Bits.SDOLCON */
#define IFX_SCU_EVRSDCTRL3_SDOLCON_LEN (7u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL3_Bits.SDOLCON */
#define IFX_SCU_EVRSDCTRL3_SDOLCON_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL3_Bits.SDOLCON */
#define IFX_SCU_EVRSDCTRL3_SDOLCON_OFF (0u)

/** \brief  Length for Ifx_SCU_EVRSDCTRL3_Bits.SDVOKLVL */
#define IFX_SCU_EVRSDCTRL3_SDVOKLVL_LEN (6u)

/** \brief  Mask for Ifx_SCU_EVRSDCTRL3_Bits.SDVOKLVL */
#define IFX_SCU_EVRSDCTRL3_SDVOKLVL_MSK (0x3fu)

/** \brief  Offset for Ifx_SCU_EVRSDCTRL3_Bits.SDVOKLVL */
#define IFX_SCU_EVRSDCTRL3_SDVOKLVL_OFF (16u)

/** \brief  Length for Ifx_SCU_EVRSTAT_Bits.BGPROK */
#define IFX_SCU_EVRSTAT_BGPROK_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSTAT_Bits.BGPROK */
#define IFX_SCU_EVRSTAT_BGPROK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSTAT_Bits.BGPROK */
#define IFX_SCU_EVRSTAT_BGPROK_OFF (10u)

/** \brief  Length for Ifx_SCU_EVRSTAT_Bits.EVR13 */
#define IFX_SCU_EVRSTAT_EVR13_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSTAT_Bits.EVR13 */
#define IFX_SCU_EVRSTAT_EVR13_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSTAT_Bits.EVR13 */
#define IFX_SCU_EVRSTAT_EVR13_OFF (0u)

/** \brief  Length for Ifx_SCU_EVRSTAT_Bits.OV13 */
#define IFX_SCU_EVRSTAT_OV13_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSTAT_Bits.OV13 */
#define IFX_SCU_EVRSTAT_OV13_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSTAT_Bits.OV13 */
#define IFX_SCU_EVRSTAT_OV13_OFF (1u)

/** \brief  Length for Ifx_SCU_EVRSTAT_Bits.OVSWD */
#define IFX_SCU_EVRSTAT_OVSWD_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSTAT_Bits.OVSWD */
#define IFX_SCU_EVRSTAT_OVSWD_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSTAT_Bits.OVSWD */
#define IFX_SCU_EVRSTAT_OVSWD_OFF (4u)

/** \brief  Length for Ifx_SCU_EVRSTAT_Bits.SCMOD */
#define IFX_SCU_EVRSTAT_SCMOD_LEN (2u)

/** \brief  Mask for Ifx_SCU_EVRSTAT_Bits.SCMOD */
#define IFX_SCU_EVRSTAT_SCMOD_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_EVRSTAT_Bits.SCMOD */
#define IFX_SCU_EVRSTAT_SCMOD_OFF (12u)

/** \brief  Length for Ifx_SCU_EVRSTAT_Bits.UV13 */
#define IFX_SCU_EVRSTAT_UV13_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSTAT_Bits.UV13 */
#define IFX_SCU_EVRSTAT_UV13_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSTAT_Bits.UV13 */
#define IFX_SCU_EVRSTAT_UV13_OFF (5u)

/** \brief  Length for Ifx_SCU_EVRSTAT_Bits.UVSWD */
#define IFX_SCU_EVRSTAT_UVSWD_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRSTAT_Bits.UVSWD */
#define IFX_SCU_EVRSTAT_UVSWD_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRSTAT_Bits.UVSWD */
#define IFX_SCU_EVRSTAT_UVSWD_OFF (7u)

/** \brief  Length for Ifx_SCU_EVRUVMON_Bits.EVR13UVVAL */
#define IFX_SCU_EVRUVMON_EVR13UVVAL_LEN (8u)

/** \brief  Mask for Ifx_SCU_EVRUVMON_Bits.EVR13UVVAL */
#define IFX_SCU_EVRUVMON_EVR13UVVAL_MSK (0xffu)

/** \brief  Offset for Ifx_SCU_EVRUVMON_Bits.EVR13UVVAL */
#define IFX_SCU_EVRUVMON_EVR13UVVAL_OFF (0u)

/** \brief  Length for Ifx_SCU_EVRUVMON_Bits.LCK */
#define IFX_SCU_EVRUVMON_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRUVMON_Bits.LCK */
#define IFX_SCU_EVRUVMON_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRUVMON_Bits.LCK */
#define IFX_SCU_EVRUVMON_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_EVRUVMON_Bits.SLCK */
#define IFX_SCU_EVRUVMON_SLCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_EVRUVMON_Bits.SLCK */
#define IFX_SCU_EVRUVMON_SLCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EVRUVMON_Bits.SLCK */
#define IFX_SCU_EVRUVMON_SLCK_OFF (30u)

/** \brief  Length for Ifx_SCU_EVRUVMON_Bits.SWDUVVAL */
#define IFX_SCU_EVRUVMON_SWDUVVAL_LEN (8u)

/** \brief  Mask for Ifx_SCU_EVRUVMON_Bits.SWDUVVAL */
#define IFX_SCU_EVRUVMON_SWDUVVAL_MSK (0xffu)

/** \brief  Offset for Ifx_SCU_EVRUVMON_Bits.SWDUVVAL */
#define IFX_SCU_EVRUVMON_SWDUVVAL_OFF (16u)

/** \brief  Length for Ifx_SCU_EXTCON_Bits.DIV1 */
#define IFX_SCU_EXTCON_DIV1_LEN (8u)

/** \brief  Mask for Ifx_SCU_EXTCON_Bits.DIV1 */
#define IFX_SCU_EXTCON_DIV1_MSK (0xffu)

/** \brief  Offset for Ifx_SCU_EXTCON_Bits.DIV1 */
#define IFX_SCU_EXTCON_DIV1_OFF (24u)

/** \brief  Length for Ifx_SCU_EXTCON_Bits.EN0 */
#define IFX_SCU_EXTCON_EN0_LEN (1u)

/** \brief  Mask for Ifx_SCU_EXTCON_Bits.EN0 */
#define IFX_SCU_EXTCON_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EXTCON_Bits.EN0 */
#define IFX_SCU_EXTCON_EN0_OFF (0u)

/** \brief  Length for Ifx_SCU_EXTCON_Bits.EN1 */
#define IFX_SCU_EXTCON_EN1_LEN (1u)

/** \brief  Mask for Ifx_SCU_EXTCON_Bits.EN1 */
#define IFX_SCU_EXTCON_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EXTCON_Bits.EN1 */
#define IFX_SCU_EXTCON_EN1_OFF (16u)

/** \brief  Length for Ifx_SCU_EXTCON_Bits.NSEL */
#define IFX_SCU_EXTCON_NSEL_LEN (1u)

/** \brief  Mask for Ifx_SCU_EXTCON_Bits.NSEL */
#define IFX_SCU_EXTCON_NSEL_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_EXTCON_Bits.NSEL */
#define IFX_SCU_EXTCON_NSEL_OFF (17u)

/** \brief  Length for Ifx_SCU_EXTCON_Bits.SEL0 */
#define IFX_SCU_EXTCON_SEL0_LEN (4u)

/** \brief  Mask for Ifx_SCU_EXTCON_Bits.SEL0 */
#define IFX_SCU_EXTCON_SEL0_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_EXTCON_Bits.SEL0 */
#define IFX_SCU_EXTCON_SEL0_OFF (2u)

/** \brief  Length for Ifx_SCU_EXTCON_Bits.SEL1 */
#define IFX_SCU_EXTCON_SEL1_LEN (4u)

/** \brief  Mask for Ifx_SCU_EXTCON_Bits.SEL1 */
#define IFX_SCU_EXTCON_SEL1_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_EXTCON_Bits.SEL1 */
#define IFX_SCU_EXTCON_SEL1_OFF (18u)

/** \brief  Length for Ifx_SCU_FDR_Bits.DISCLK */
#define IFX_SCU_FDR_DISCLK_LEN (1u)

/** \brief  Mask for Ifx_SCU_FDR_Bits.DISCLK */
#define IFX_SCU_FDR_DISCLK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FDR_Bits.DISCLK */
#define IFX_SCU_FDR_DISCLK_OFF (31u)

/** \brief  Length for Ifx_SCU_FDR_Bits.DM */
#define IFX_SCU_FDR_DM_LEN (2u)

/** \brief  Mask for Ifx_SCU_FDR_Bits.DM */
#define IFX_SCU_FDR_DM_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_FDR_Bits.DM */
#define IFX_SCU_FDR_DM_OFF (14u)

/** \brief  Length for Ifx_SCU_FDR_Bits.RESULT */
#define IFX_SCU_FDR_RESULT_LEN (10u)

/** \brief  Mask for Ifx_SCU_FDR_Bits.RESULT */
#define IFX_SCU_FDR_RESULT_MSK (0x3ffu)

/** \brief  Offset for Ifx_SCU_FDR_Bits.RESULT */
#define IFX_SCU_FDR_RESULT_OFF (16u)

/** \brief  Length for Ifx_SCU_FDR_Bits.STEP */
#define IFX_SCU_FDR_STEP_LEN (10u)

/** \brief  Mask for Ifx_SCU_FDR_Bits.STEP */
#define IFX_SCU_FDR_STEP_MSK (0x3ffu)

/** \brief  Offset for Ifx_SCU_FDR_Bits.STEP */
#define IFX_SCU_FDR_STEP_OFF (0u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FC0 */
#define IFX_SCU_FMR_FC0_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FC0 */
#define IFX_SCU_FMR_FC0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FC0 */
#define IFX_SCU_FMR_FC0_OFF (16u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FC1 */
#define IFX_SCU_FMR_FC1_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FC1 */
#define IFX_SCU_FMR_FC1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FC1 */
#define IFX_SCU_FMR_FC1_OFF (17u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FC2 */
#define IFX_SCU_FMR_FC2_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FC2 */
#define IFX_SCU_FMR_FC2_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FC2 */
#define IFX_SCU_FMR_FC2_OFF (18u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FC3 */
#define IFX_SCU_FMR_FC3_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FC3 */
#define IFX_SCU_FMR_FC3_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FC3 */
#define IFX_SCU_FMR_FC3_OFF (19u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FC4 */
#define IFX_SCU_FMR_FC4_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FC4 */
#define IFX_SCU_FMR_FC4_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FC4 */
#define IFX_SCU_FMR_FC4_OFF (20u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FC5 */
#define IFX_SCU_FMR_FC5_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FC5 */
#define IFX_SCU_FMR_FC5_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FC5 */
#define IFX_SCU_FMR_FC5_OFF (21u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FC6 */
#define IFX_SCU_FMR_FC6_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FC6 */
#define IFX_SCU_FMR_FC6_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FC6 */
#define IFX_SCU_FMR_FC6_OFF (22u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FC7 */
#define IFX_SCU_FMR_FC7_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FC7 */
#define IFX_SCU_FMR_FC7_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FC7 */
#define IFX_SCU_FMR_FC7_OFF (23u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FS0 */
#define IFX_SCU_FMR_FS0_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FS0 */
#define IFX_SCU_FMR_FS0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FS0 */
#define IFX_SCU_FMR_FS0_OFF (0u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FS1 */
#define IFX_SCU_FMR_FS1_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FS1 */
#define IFX_SCU_FMR_FS1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FS1 */
#define IFX_SCU_FMR_FS1_OFF (1u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FS2 */
#define IFX_SCU_FMR_FS2_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FS2 */
#define IFX_SCU_FMR_FS2_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FS2 */
#define IFX_SCU_FMR_FS2_OFF (2u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FS3 */
#define IFX_SCU_FMR_FS3_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FS3 */
#define IFX_SCU_FMR_FS3_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FS3 */
#define IFX_SCU_FMR_FS3_OFF (3u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FS4 */
#define IFX_SCU_FMR_FS4_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FS4 */
#define IFX_SCU_FMR_FS4_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FS4 */
#define IFX_SCU_FMR_FS4_OFF (4u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FS5 */
#define IFX_SCU_FMR_FS5_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FS5 */
#define IFX_SCU_FMR_FS5_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FS5 */
#define IFX_SCU_FMR_FS5_OFF (5u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FS6 */
#define IFX_SCU_FMR_FS6_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FS6 */
#define IFX_SCU_FMR_FS6_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FS6 */
#define IFX_SCU_FMR_FS6_OFF (6u)

/** \brief  Length for Ifx_SCU_FMR_Bits.FS7 */
#define IFX_SCU_FMR_FS7_LEN (1u)

/** \brief  Mask for Ifx_SCU_FMR_Bits.FS7 */
#define IFX_SCU_FMR_FS7_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_FMR_Bits.FS7 */
#define IFX_SCU_FMR_FS7_OFF (7u)

/** \brief  Length for Ifx_SCU_ID_Bits.MODNUMBER */
#define IFX_SCU_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_SCU_ID_Bits.MODNUMBER */
#define IFX_SCU_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_SCU_ID_Bits.MODNUMBER */
#define IFX_SCU_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_SCU_ID_Bits.MODREV */
#define IFX_SCU_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_SCU_ID_Bits.MODREV */
#define IFX_SCU_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_SCU_ID_Bits.MODREV */
#define IFX_SCU_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_SCU_ID_Bits.MODTYPE */
#define IFX_SCU_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_SCU_ID_Bits.MODTYPE */
#define IFX_SCU_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_SCU_ID_Bits.MODTYPE */
#define IFX_SCU_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.GEEN0 */
#define IFX_SCU_IGCR_GEEN0_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.GEEN0 */
#define IFX_SCU_IGCR_GEEN0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.GEEN0 */
#define IFX_SCU_IGCR_GEEN0_OFF (13u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.GEEN1 */
#define IFX_SCU_IGCR_GEEN1_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.GEEN1 */
#define IFX_SCU_IGCR_GEEN1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.GEEN1 */
#define IFX_SCU_IGCR_GEEN1_OFF (29u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IGP0 */
#define IFX_SCU_IGCR_IGP0_LEN (2u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IGP0 */
#define IFX_SCU_IGCR_IGP0_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IGP0 */
#define IFX_SCU_IGCR_IGP0_OFF (14u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IGP1 */
#define IFX_SCU_IGCR_IGP1_LEN (2u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IGP1 */
#define IFX_SCU_IGCR_IGP1_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IGP1 */
#define IFX_SCU_IGCR_IGP1_OFF (30u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN00 */
#define IFX_SCU_IGCR_IPEN00_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN00 */
#define IFX_SCU_IGCR_IPEN00_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN00 */
#define IFX_SCU_IGCR_IPEN00_OFF (0u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN01 */
#define IFX_SCU_IGCR_IPEN01_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN01 */
#define IFX_SCU_IGCR_IPEN01_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN01 */
#define IFX_SCU_IGCR_IPEN01_OFF (1u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN02 */
#define IFX_SCU_IGCR_IPEN02_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN02 */
#define IFX_SCU_IGCR_IPEN02_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN02 */
#define IFX_SCU_IGCR_IPEN02_OFF (2u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN03 */
#define IFX_SCU_IGCR_IPEN03_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN03 */
#define IFX_SCU_IGCR_IPEN03_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN03 */
#define IFX_SCU_IGCR_IPEN03_OFF (3u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN04 */
#define IFX_SCU_IGCR_IPEN04_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN04 */
#define IFX_SCU_IGCR_IPEN04_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN04 */
#define IFX_SCU_IGCR_IPEN04_OFF (4u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN05 */
#define IFX_SCU_IGCR_IPEN05_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN05 */
#define IFX_SCU_IGCR_IPEN05_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN05 */
#define IFX_SCU_IGCR_IPEN05_OFF (5u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN06 */
#define IFX_SCU_IGCR_IPEN06_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN06 */
#define IFX_SCU_IGCR_IPEN06_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN06 */
#define IFX_SCU_IGCR_IPEN06_OFF (6u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN07 */
#define IFX_SCU_IGCR_IPEN07_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN07 */
#define IFX_SCU_IGCR_IPEN07_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN07 */
#define IFX_SCU_IGCR_IPEN07_OFF (7u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN10 */
#define IFX_SCU_IGCR_IPEN10_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN10 */
#define IFX_SCU_IGCR_IPEN10_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN10 */
#define IFX_SCU_IGCR_IPEN10_OFF (16u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN11 */
#define IFX_SCU_IGCR_IPEN11_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN11 */
#define IFX_SCU_IGCR_IPEN11_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN11 */
#define IFX_SCU_IGCR_IPEN11_OFF (17u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN12 */
#define IFX_SCU_IGCR_IPEN12_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN12 */
#define IFX_SCU_IGCR_IPEN12_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN12 */
#define IFX_SCU_IGCR_IPEN12_OFF (18u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN13 */
#define IFX_SCU_IGCR_IPEN13_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN13 */
#define IFX_SCU_IGCR_IPEN13_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN13 */
#define IFX_SCU_IGCR_IPEN13_OFF (19u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN14 */
#define IFX_SCU_IGCR_IPEN14_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN14 */
#define IFX_SCU_IGCR_IPEN14_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN14 */
#define IFX_SCU_IGCR_IPEN14_OFF (20u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN15 */
#define IFX_SCU_IGCR_IPEN15_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN15 */
#define IFX_SCU_IGCR_IPEN15_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN15 */
#define IFX_SCU_IGCR_IPEN15_OFF (21u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN16 */
#define IFX_SCU_IGCR_IPEN16_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN16 */
#define IFX_SCU_IGCR_IPEN16_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN16 */
#define IFX_SCU_IGCR_IPEN16_OFF (22u)

/** \brief  Length for Ifx_SCU_IGCR_Bits.IPEN17 */
#define IFX_SCU_IGCR_IPEN17_LEN (1u)

/** \brief  Mask for Ifx_SCU_IGCR_Bits.IPEN17 */
#define IFX_SCU_IGCR_IPEN17_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IGCR_Bits.IPEN17 */
#define IFX_SCU_IGCR_IPEN17_OFF (23u)

/** \brief  Length for Ifx_SCU_IN_Bits.P0 */
#define IFX_SCU_IN_P0_LEN (1u)

/** \brief  Mask for Ifx_SCU_IN_Bits.P0 */
#define IFX_SCU_IN_P0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IN_Bits.P0 */
#define IFX_SCU_IN_P0_OFF (0u)

/** \brief  Length for Ifx_SCU_IN_Bits.P1 */
#define IFX_SCU_IN_P1_LEN (1u)

/** \brief  Mask for Ifx_SCU_IN_Bits.P1 */
#define IFX_SCU_IN_P1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_IN_Bits.P1 */
#define IFX_SCU_IN_P1_OFF (1u)

/** \brief  Length for Ifx_SCU_IOCR_Bits.PC0 */
#define IFX_SCU_IOCR_PC0_LEN (4u)

/** \brief  Mask for Ifx_SCU_IOCR_Bits.PC0 */
#define IFX_SCU_IOCR_PC0_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_IOCR_Bits.PC0 */
#define IFX_SCU_IOCR_PC0_OFF (4u)

/** \brief  Length for Ifx_SCU_IOCR_Bits.PC1 */
#define IFX_SCU_IOCR_PC1_LEN (4u)

/** \brief  Mask for Ifx_SCU_IOCR_Bits.PC1 */
#define IFX_SCU_IOCR_PC1_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_IOCR_Bits.PC1 */
#define IFX_SCU_IOCR_PC1_OFF (12u)

/** \brief  Length for Ifx_SCU_LBISTCTRL0_Bits.LBISTREQ */
#define IFX_SCU_LBISTCTRL0_LBISTREQ_LEN (1u)

/** \brief  Mask for Ifx_SCU_LBISTCTRL0_Bits.LBISTREQ */
#define IFX_SCU_LBISTCTRL0_LBISTREQ_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_LBISTCTRL0_Bits.LBISTREQ */
#define IFX_SCU_LBISTCTRL0_LBISTREQ_OFF (0u)

/** \brief  Length for Ifx_SCU_LBISTCTRL0_Bits.LBISTREQP */
#define IFX_SCU_LBISTCTRL0_LBISTREQP_LEN (1u)

/** \brief  Mask for Ifx_SCU_LBISTCTRL0_Bits.LBISTREQP */
#define IFX_SCU_LBISTCTRL0_LBISTREQP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_LBISTCTRL0_Bits.LBISTREQP */
#define IFX_SCU_LBISTCTRL0_LBISTREQP_OFF (1u)

/** \brief  Length for Ifx_SCU_LBISTCTRL0_Bits.PATTERNS */
#define IFX_SCU_LBISTCTRL0_PATTERNS_LEN (14u)

/** \brief  Mask for Ifx_SCU_LBISTCTRL0_Bits.PATTERNS */
#define IFX_SCU_LBISTCTRL0_PATTERNS_MSK (0x3fffu)

/** \brief  Offset for Ifx_SCU_LBISTCTRL0_Bits.PATTERNS */
#define IFX_SCU_LBISTCTRL0_PATTERNS_OFF (2u)

/** \brief  Length for Ifx_SCU_LBISTCTRL1_Bits.BODY */
#define IFX_SCU_LBISTCTRL1_BODY_LEN (1u)

/** \brief  Mask for Ifx_SCU_LBISTCTRL1_Bits.BODY */
#define IFX_SCU_LBISTCTRL1_BODY_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_LBISTCTRL1_Bits.BODY */
#define IFX_SCU_LBISTCTRL1_BODY_OFF (27u)

/** \brief  Length for Ifx_SCU_LBISTCTRL1_Bits.LBISTFREQU */
#define IFX_SCU_LBISTCTRL1_LBISTFREQU_LEN (4u)

/** \brief  Mask for Ifx_SCU_LBISTCTRL1_Bits.LBISTFREQU */
#define IFX_SCU_LBISTCTRL1_LBISTFREQU_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_LBISTCTRL1_Bits.LBISTFREQU */
#define IFX_SCU_LBISTCTRL1_LBISTFREQU_OFF (28u)

/** \brief  Length for Ifx_SCU_LBISTCTRL1_Bits.SEED */
#define IFX_SCU_LBISTCTRL1_SEED_LEN (23u)

/** \brief  Mask for Ifx_SCU_LBISTCTRL1_Bits.SEED */
#define IFX_SCU_LBISTCTRL1_SEED_MSK (0x7fffffu)

/** \brief  Offset for Ifx_SCU_LBISTCTRL1_Bits.SEED */
#define IFX_SCU_LBISTCTRL1_SEED_OFF (0u)

/** \brief  Length for Ifx_SCU_LBISTCTRL1_Bits.SPLITSH */
#define IFX_SCU_LBISTCTRL1_SPLITSH_LEN (3u)

/** \brief  Mask for Ifx_SCU_LBISTCTRL1_Bits.SPLITSH */
#define IFX_SCU_LBISTCTRL1_SPLITSH_MSK (0x7u)

/** \brief  Offset for Ifx_SCU_LBISTCTRL1_Bits.SPLITSH */
#define IFX_SCU_LBISTCTRL1_SPLITSH_OFF (24u)

/** \brief  Length for Ifx_SCU_LBISTCTRL2_Bits.LBISTDONE */
#define IFX_SCU_LBISTCTRL2_LBISTDONE_LEN (1u)

/** \brief  Mask for Ifx_SCU_LBISTCTRL2_Bits.LBISTDONE */
#define IFX_SCU_LBISTCTRL2_LBISTDONE_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_LBISTCTRL2_Bits.LBISTDONE */
#define IFX_SCU_LBISTCTRL2_LBISTDONE_OFF (31u)

/** \brief  Length for Ifx_SCU_LBISTCTRL2_Bits.SIGNATURE */
#define IFX_SCU_LBISTCTRL2_SIGNATURE_LEN (24u)

/** \brief  Mask for Ifx_SCU_LBISTCTRL2_Bits.SIGNATURE */
#define IFX_SCU_LBISTCTRL2_SIGNATURE_MSK (0xffffffu)

/** \brief  Offset for Ifx_SCU_LBISTCTRL2_Bits.SIGNATURE */
#define IFX_SCU_LBISTCTRL2_SIGNATURE_OFF (0u)

/** \brief  Length for Ifx_SCU_LCLCON0_Bits.LS */
#define IFX_SCU_LCLCON0_LS_LEN (1u)

/** \brief  Mask for Ifx_SCU_LCLCON0_Bits.LS */
#define IFX_SCU_LCLCON0_LS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_LCLCON0_Bits.LS */
#define IFX_SCU_LCLCON0_LS_OFF (16u)

/** \brief  Length for Ifx_SCU_LCLCON0_Bits.LSEN */
#define IFX_SCU_LCLCON0_LSEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_LCLCON0_Bits.LSEN */
#define IFX_SCU_LCLCON0_LSEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_LCLCON0_Bits.LSEN */
#define IFX_SCU_LCLCON0_LSEN_OFF (31u)

/** \brief  Length for Ifx_SCU_LCLTEST_Bits.LCLT0 */
#define IFX_SCU_LCLTEST_LCLT0_LEN (1u)

/** \brief  Mask for Ifx_SCU_LCLTEST_Bits.LCLT0 */
#define IFX_SCU_LCLTEST_LCLT0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_LCLTEST_Bits.LCLT0 */
#define IFX_SCU_LCLTEST_LCLT0_OFF (0u)

/** \brief  Length for Ifx_SCU_LCLTEST_Bits.LCLT1 */
#define IFX_SCU_LCLTEST_LCLT1_LEN (1u)

/** \brief  Mask for Ifx_SCU_LCLTEST_Bits.LCLT1 */
#define IFX_SCU_LCLTEST_LCLT1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_LCLTEST_Bits.LCLT1 */
#define IFX_SCU_LCLTEST_LCLT1_OFF (1u)

/** \brief  Length for Ifx_SCU_MANID_Bits.DEPT */
#define IFX_SCU_MANID_DEPT_LEN (5u)

/** \brief  Mask for Ifx_SCU_MANID_Bits.DEPT */
#define IFX_SCU_MANID_DEPT_MSK (0x1fu)

/** \brief  Offset for Ifx_SCU_MANID_Bits.DEPT */
#define IFX_SCU_MANID_DEPT_OFF (0u)

/** \brief  Length for Ifx_SCU_MANID_Bits.MANUF */
#define IFX_SCU_MANID_MANUF_LEN (11u)

/** \brief  Mask for Ifx_SCU_MANID_Bits.MANUF */
#define IFX_SCU_MANID_MANUF_MSK (0x7ffu)

/** \brief  Offset for Ifx_SCU_MANID_Bits.MANUF */
#define IFX_SCU_MANID_MANUF_OFF (5u)

/** \brief  Length for Ifx_SCU_OMR_Bits.PCL0 */
#define IFX_SCU_OMR_PCL0_LEN (1u)

/** \brief  Mask for Ifx_SCU_OMR_Bits.PCL0 */
#define IFX_SCU_OMR_PCL0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OMR_Bits.PCL0 */
#define IFX_SCU_OMR_PCL0_OFF (16u)

/** \brief  Length for Ifx_SCU_OMR_Bits.PCL1 */
#define IFX_SCU_OMR_PCL1_LEN (1u)

/** \brief  Mask for Ifx_SCU_OMR_Bits.PCL1 */
#define IFX_SCU_OMR_PCL1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OMR_Bits.PCL1 */
#define IFX_SCU_OMR_PCL1_OFF (17u)

/** \brief  Length for Ifx_SCU_OMR_Bits.PS0 */
#define IFX_SCU_OMR_PS0_LEN (1u)

/** \brief  Mask for Ifx_SCU_OMR_Bits.PS0 */
#define IFX_SCU_OMR_PS0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OMR_Bits.PS0 */
#define IFX_SCU_OMR_PS0_OFF (0u)

/** \brief  Length for Ifx_SCU_OMR_Bits.PS1 */
#define IFX_SCU_OMR_PS1_LEN (1u)

/** \brief  Mask for Ifx_SCU_OMR_Bits.PS1 */
#define IFX_SCU_OMR_PS1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OMR_Bits.PS1 */
#define IFX_SCU_OMR_PS1_OFF (1u)

/** \brief  Length for Ifx_SCU_OSCCON_Bits.APREN */
#define IFX_SCU_OSCCON_APREN_LEN (1u)

/** \brief  Mask for Ifx_SCU_OSCCON_Bits.APREN */
#define IFX_SCU_OSCCON_APREN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OSCCON_Bits.APREN */
#define IFX_SCU_OSCCON_APREN_OFF (23u)

/** \brief  Length for Ifx_SCU_OSCCON_Bits.GAINSEL */
#define IFX_SCU_OSCCON_GAINSEL_LEN (2u)

/** \brief  Mask for Ifx_SCU_OSCCON_Bits.GAINSEL */
#define IFX_SCU_OSCCON_GAINSEL_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_OSCCON_Bits.GAINSEL */
#define IFX_SCU_OSCCON_GAINSEL_OFF (3u)

/** \brief  Length for Ifx_SCU_OSCCON_Bits.MODE */
#define IFX_SCU_OSCCON_MODE_LEN (2u)

/** \brief  Mask for Ifx_SCU_OSCCON_Bits.MODE */
#define IFX_SCU_OSCCON_MODE_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_OSCCON_Bits.MODE */
#define IFX_SCU_OSCCON_MODE_OFF (5u)

/** \brief  Length for Ifx_SCU_OSCCON_Bits.OSCRES */
#define IFX_SCU_OSCCON_OSCRES_LEN (1u)

/** \brief  Mask for Ifx_SCU_OSCCON_Bits.OSCRES */
#define IFX_SCU_OSCCON_OSCRES_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OSCCON_Bits.OSCRES */
#define IFX_SCU_OSCCON_OSCRES_OFF (2u)

/** \brief  Length for Ifx_SCU_OSCCON_Bits.OSCVAL */
#define IFX_SCU_OSCCON_OSCVAL_LEN (5u)

/** \brief  Mask for Ifx_SCU_OSCCON_Bits.OSCVAL */
#define IFX_SCU_OSCCON_OSCVAL_MSK (0x1fu)

/** \brief  Offset for Ifx_SCU_OSCCON_Bits.OSCVAL */
#define IFX_SCU_OSCCON_OSCVAL_OFF (16u)

/** \brief  Length for Ifx_SCU_OSCCON_Bits.PLLHV */
#define IFX_SCU_OSCCON_PLLHV_LEN (1u)

/** \brief  Mask for Ifx_SCU_OSCCON_Bits.PLLHV */
#define IFX_SCU_OSCCON_PLLHV_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OSCCON_Bits.PLLHV */
#define IFX_SCU_OSCCON_PLLHV_OFF (8u)

/** \brief  Length for Ifx_SCU_OSCCON_Bits.PLLLV */
#define IFX_SCU_OSCCON_PLLLV_LEN (1u)

/** \brief  Mask for Ifx_SCU_OSCCON_Bits.PLLLV */
#define IFX_SCU_OSCCON_PLLLV_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OSCCON_Bits.PLLLV */
#define IFX_SCU_OSCCON_PLLLV_OFF (1u)

/** \brief  Length for Ifx_SCU_OSCCON_Bits.SHBY */
#define IFX_SCU_OSCCON_SHBY_LEN (1u)

/** \brief  Mask for Ifx_SCU_OSCCON_Bits.SHBY */
#define IFX_SCU_OSCCON_SHBY_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OSCCON_Bits.SHBY */
#define IFX_SCU_OSCCON_SHBY_OFF (7u)

/** \brief  Length for Ifx_SCU_OSCCON_Bits.X1D */
#define IFX_SCU_OSCCON_X1D_LEN (1u)

/** \brief  Mask for Ifx_SCU_OSCCON_Bits.X1D */
#define IFX_SCU_OSCCON_X1D_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OSCCON_Bits.X1D */
#define IFX_SCU_OSCCON_X1D_OFF (10u)

/** \brief  Length for Ifx_SCU_OSCCON_Bits.X1DEN */
#define IFX_SCU_OSCCON_X1DEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_OSCCON_Bits.X1DEN */
#define IFX_SCU_OSCCON_X1DEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OSCCON_Bits.X1DEN */
#define IFX_SCU_OSCCON_X1DEN_OFF (11u)

/** \brief  Length for Ifx_SCU_OUT_Bits.P0 */
#define IFX_SCU_OUT_P0_LEN (1u)

/** \brief  Mask for Ifx_SCU_OUT_Bits.P0 */
#define IFX_SCU_OUT_P0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OUT_Bits.P0 */
#define IFX_SCU_OUT_P0_OFF (0u)

/** \brief  Length for Ifx_SCU_OUT_Bits.P1 */
#define IFX_SCU_OUT_P1_LEN (1u)

/** \brief  Mask for Ifx_SCU_OUT_Bits.P1 */
#define IFX_SCU_OUT_P1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OUT_Bits.P1 */
#define IFX_SCU_OUT_P1_OFF (1u)

/** \brief  Length for Ifx_SCU_OVCCON_Bits.CSEL0 */
#define IFX_SCU_OVCCON_CSEL0_LEN (1u)

/** \brief  Mask for Ifx_SCU_OVCCON_Bits.CSEL0 */
#define IFX_SCU_OVCCON_CSEL0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OVCCON_Bits.CSEL0 */
#define IFX_SCU_OVCCON_CSEL0_OFF (0u)

/** \brief  Length for Ifx_SCU_OVCCON_Bits.CSEL1 */
#define IFX_SCU_OVCCON_CSEL1_LEN (1u)

/** \brief  Mask for Ifx_SCU_OVCCON_Bits.CSEL1 */
#define IFX_SCU_OVCCON_CSEL1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OVCCON_Bits.CSEL1 */
#define IFX_SCU_OVCCON_CSEL1_OFF (1u)

/** \brief  Length for Ifx_SCU_OVCCON_Bits.CSEL2 */
#define IFX_SCU_OVCCON_CSEL2_LEN (1u)

/** \brief  Mask for Ifx_SCU_OVCCON_Bits.CSEL2 */
#define IFX_SCU_OVCCON_CSEL2_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OVCCON_Bits.CSEL2 */
#define IFX_SCU_OVCCON_CSEL2_OFF (2u)

/** \brief  Length for Ifx_SCU_OVCCON_Bits.DCINVAL */
#define IFX_SCU_OVCCON_DCINVAL_LEN (1u)

/** \brief  Mask for Ifx_SCU_OVCCON_Bits.DCINVAL */
#define IFX_SCU_OVCCON_DCINVAL_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OVCCON_Bits.DCINVAL */
#define IFX_SCU_OVCCON_DCINVAL_OFF (18u)

/** \brief  Length for Ifx_SCU_OVCCON_Bits.OVCONF */
#define IFX_SCU_OVCCON_OVCONF_LEN (1u)

/** \brief  Mask for Ifx_SCU_OVCCON_Bits.OVCONF */
#define IFX_SCU_OVCCON_OVCONF_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OVCCON_Bits.OVCONF */
#define IFX_SCU_OVCCON_OVCONF_OFF (24u)

/** \brief  Length for Ifx_SCU_OVCCON_Bits.OVSTP */
#define IFX_SCU_OVCCON_OVSTP_LEN (1u)

/** \brief  Mask for Ifx_SCU_OVCCON_Bits.OVSTP */
#define IFX_SCU_OVCCON_OVSTP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OVCCON_Bits.OVSTP */
#define IFX_SCU_OVCCON_OVSTP_OFF (17u)

/** \brief  Length for Ifx_SCU_OVCCON_Bits.OVSTRT */
#define IFX_SCU_OVCCON_OVSTRT_LEN (1u)

/** \brief  Mask for Ifx_SCU_OVCCON_Bits.OVSTRT */
#define IFX_SCU_OVCCON_OVSTRT_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OVCCON_Bits.OVSTRT */
#define IFX_SCU_OVCCON_OVSTRT_OFF (16u)

/** \brief  Length for Ifx_SCU_OVCCON_Bits.POVCONF */
#define IFX_SCU_OVCCON_POVCONF_LEN (1u)

/** \brief  Mask for Ifx_SCU_OVCCON_Bits.POVCONF */
#define IFX_SCU_OVCCON_POVCONF_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OVCCON_Bits.POVCONF */
#define IFX_SCU_OVCCON_POVCONF_OFF (25u)

/** \brief  Length for Ifx_SCU_OVCENABLE_Bits.OVEN0 */
#define IFX_SCU_OVCENABLE_OVEN0_LEN (1u)

/** \brief  Mask for Ifx_SCU_OVCENABLE_Bits.OVEN0 */
#define IFX_SCU_OVCENABLE_OVEN0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OVCENABLE_Bits.OVEN0 */
#define IFX_SCU_OVCENABLE_OVEN0_OFF (0u)

/** \brief  Length for Ifx_SCU_OVCENABLE_Bits.OVEN1 */
#define IFX_SCU_OVCENABLE_OVEN1_LEN (1u)

/** \brief  Mask for Ifx_SCU_OVCENABLE_Bits.OVEN1 */
#define IFX_SCU_OVCENABLE_OVEN1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OVCENABLE_Bits.OVEN1 */
#define IFX_SCU_OVCENABLE_OVEN1_OFF (1u)

/** \brief  Length for Ifx_SCU_OVCENABLE_Bits.OVEN2 */
#define IFX_SCU_OVCENABLE_OVEN2_LEN (1u)

/** \brief  Mask for Ifx_SCU_OVCENABLE_Bits.OVEN2 */
#define IFX_SCU_OVCENABLE_OVEN2_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_OVCENABLE_Bits.OVEN2 */
#define IFX_SCU_OVCENABLE_OVEN2_OFF (2u)

/** \brief  Length for Ifx_SCU_PDISC_Bits.PDIS0 */
#define IFX_SCU_PDISC_PDIS0_LEN (1u)

/** \brief  Mask for Ifx_SCU_PDISC_Bits.PDIS0 */
#define IFX_SCU_PDISC_PDIS0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PDISC_Bits.PDIS0 */
#define IFX_SCU_PDISC_PDIS0_OFF (0u)

/** \brief  Length for Ifx_SCU_PDISC_Bits.PDIS1 */
#define IFX_SCU_PDISC_PDIS1_LEN (1u)

/** \brief  Mask for Ifx_SCU_PDISC_Bits.PDIS1 */
#define IFX_SCU_PDISC_PDIS1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PDISC_Bits.PDIS1 */
#define IFX_SCU_PDISC_PDIS1_OFF (1u)

/** \brief  Length for Ifx_SCU_PDR_Bits.PD0 */
#define IFX_SCU_PDR_PD0_LEN (3u)

/** \brief  Mask for Ifx_SCU_PDR_Bits.PD0 */
#define IFX_SCU_PDR_PD0_MSK (0x7u)

/** \brief  Offset for Ifx_SCU_PDR_Bits.PD0 */
#define IFX_SCU_PDR_PD0_OFF (0u)

/** \brief  Length for Ifx_SCU_PDR_Bits.PD1 */
#define IFX_SCU_PDR_PD1_LEN (3u)

/** \brief  Mask for Ifx_SCU_PDR_Bits.PD1 */
#define IFX_SCU_PDR_PD1_MSK (0x7u)

/** \brief  Offset for Ifx_SCU_PDR_Bits.PD1 */
#define IFX_SCU_PDR_PD1_OFF (4u)

/** \brief  Length for Ifx_SCU_PDR_Bits.PL0 */
#define IFX_SCU_PDR_PL0_LEN (1u)

/** \brief  Mask for Ifx_SCU_PDR_Bits.PL0 */
#define IFX_SCU_PDR_PL0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PDR_Bits.PL0 */
#define IFX_SCU_PDR_PL0_OFF (3u)

/** \brief  Length for Ifx_SCU_PDR_Bits.PL1 */
#define IFX_SCU_PDR_PL1_LEN (1u)

/** \brief  Mask for Ifx_SCU_PDR_Bits.PL1 */
#define IFX_SCU_PDR_PL1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PDR_Bits.PL1 */
#define IFX_SCU_PDR_PL1_OFF (7u)

/** \brief  Length for Ifx_SCU_PDRR_Bits.PDR0 */
#define IFX_SCU_PDRR_PDR0_LEN (1u)

/** \brief  Mask for Ifx_SCU_PDRR_Bits.PDR0 */
#define IFX_SCU_PDRR_PDR0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PDRR_Bits.PDR0 */
#define IFX_SCU_PDRR_PDR0_OFF (0u)

/** \brief  Length for Ifx_SCU_PDRR_Bits.PDR1 */
#define IFX_SCU_PDRR_PDR1_LEN (1u)

/** \brief  Mask for Ifx_SCU_PDRR_Bits.PDR1 */
#define IFX_SCU_PDRR_PDR1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PDRR_Bits.PDR1 */
#define IFX_SCU_PDRR_PDR1_OFF (1u)

/** \brief  Length for Ifx_SCU_PDRR_Bits.PDR2 */
#define IFX_SCU_PDRR_PDR2_LEN (1u)

/** \brief  Mask for Ifx_SCU_PDRR_Bits.PDR2 */
#define IFX_SCU_PDRR_PDR2_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PDRR_Bits.PDR2 */
#define IFX_SCU_PDRR_PDR2_OFF (2u)

/** \brief  Length for Ifx_SCU_PDRR_Bits.PDR3 */
#define IFX_SCU_PDRR_PDR3_LEN (1u)

/** \brief  Mask for Ifx_SCU_PDRR_Bits.PDR3 */
#define IFX_SCU_PDRR_PDR3_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PDRR_Bits.PDR3 */
#define IFX_SCU_PDRR_PDR3_OFF (3u)

/** \brief  Length for Ifx_SCU_PDRR_Bits.PDR4 */
#define IFX_SCU_PDRR_PDR4_LEN (1u)

/** \brief  Mask for Ifx_SCU_PDRR_Bits.PDR4 */
#define IFX_SCU_PDRR_PDR4_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PDRR_Bits.PDR4 */
#define IFX_SCU_PDRR_PDR4_OFF (4u)

/** \brief  Length for Ifx_SCU_PDRR_Bits.PDR5 */
#define IFX_SCU_PDRR_PDR5_LEN (1u)

/** \brief  Mask for Ifx_SCU_PDRR_Bits.PDR5 */
#define IFX_SCU_PDRR_PDR5_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PDRR_Bits.PDR5 */
#define IFX_SCU_PDRR_PDR5_OFF (5u)

/** \brief  Length for Ifx_SCU_PDRR_Bits.PDR6 */
#define IFX_SCU_PDRR_PDR6_LEN (1u)

/** \brief  Mask for Ifx_SCU_PDRR_Bits.PDR6 */
#define IFX_SCU_PDRR_PDR6_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PDRR_Bits.PDR6 */
#define IFX_SCU_PDRR_PDR6_OFF (6u)

/** \brief  Length for Ifx_SCU_PDRR_Bits.PDR7 */
#define IFX_SCU_PDRR_PDR7_LEN (1u)

/** \brief  Mask for Ifx_SCU_PDRR_Bits.PDR7 */
#define IFX_SCU_PDRR_PDR7_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PDRR_Bits.PDR7 */
#define IFX_SCU_PDRR_PDR7_OFF (7u)

/** \brief  Length for Ifx_SCU_PLLCON0_Bits.CLRFINDIS */
#define IFX_SCU_PLLCON0_CLRFINDIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLCON0_Bits.CLRFINDIS */
#define IFX_SCU_PLLCON0_CLRFINDIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLCON0_Bits.CLRFINDIS */
#define IFX_SCU_PLLCON0_CLRFINDIS_OFF (5u)

/** \brief  Length for Ifx_SCU_PLLCON0_Bits.MODEN */
#define IFX_SCU_PLLCON0_MODEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLCON0_Bits.MODEN */
#define IFX_SCU_PLLCON0_MODEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLCON0_Bits.MODEN */
#define IFX_SCU_PLLCON0_MODEN_OFF (2u)

/** \brief  Length for Ifx_SCU_PLLCON0_Bits.NDIV */
#define IFX_SCU_PLLCON0_NDIV_LEN (7u)

/** \brief  Mask for Ifx_SCU_PLLCON0_Bits.NDIV */
#define IFX_SCU_PLLCON0_NDIV_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_PLLCON0_Bits.NDIV */
#define IFX_SCU_PLLCON0_NDIV_OFF (9u)

/** \brief  Length for Ifx_SCU_PLLCON0_Bits.OSCDISCDIS */
#define IFX_SCU_PLLCON0_OSCDISCDIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLCON0_Bits.OSCDISCDIS */
#define IFX_SCU_PLLCON0_OSCDISCDIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLCON0_Bits.OSCDISCDIS */
#define IFX_SCU_PLLCON0_OSCDISCDIS_OFF (6u)

/** \brief  Length for Ifx_SCU_PLLCON0_Bits.PDIV */
#define IFX_SCU_PLLCON0_PDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_PLLCON0_Bits.PDIV */
#define IFX_SCU_PLLCON0_PDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_PLLCON0_Bits.PDIV */
#define IFX_SCU_PLLCON0_PDIV_OFF (24u)

/** \brief  Length for Ifx_SCU_PLLCON0_Bits.PLLPWD */
#define IFX_SCU_PLLCON0_PLLPWD_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLCON0_Bits.PLLPWD */
#define IFX_SCU_PLLCON0_PLLPWD_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLCON0_Bits.PLLPWD */
#define IFX_SCU_PLLCON0_PLLPWD_OFF (16u)

/** \brief  Length for Ifx_SCU_PLLCON0_Bits.RESLD */
#define IFX_SCU_PLLCON0_RESLD_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLCON0_Bits.RESLD */
#define IFX_SCU_PLLCON0_RESLD_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLCON0_Bits.RESLD */
#define IFX_SCU_PLLCON0_RESLD_OFF (18u)

/** \brief  Length for Ifx_SCU_PLLCON0_Bits.SETFINDIS */
#define IFX_SCU_PLLCON0_SETFINDIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLCON0_Bits.SETFINDIS */
#define IFX_SCU_PLLCON0_SETFINDIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLCON0_Bits.SETFINDIS */
#define IFX_SCU_PLLCON0_SETFINDIS_OFF (4u)

/** \brief  Length for Ifx_SCU_PLLCON0_Bits.VCOBYP */
#define IFX_SCU_PLLCON0_VCOBYP_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLCON0_Bits.VCOBYP */
#define IFX_SCU_PLLCON0_VCOBYP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLCON0_Bits.VCOBYP */
#define IFX_SCU_PLLCON0_VCOBYP_OFF (0u)

/** \brief  Length for Ifx_SCU_PLLCON0_Bits.VCOPWD */
#define IFX_SCU_PLLCON0_VCOPWD_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLCON0_Bits.VCOPWD */
#define IFX_SCU_PLLCON0_VCOPWD_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLCON0_Bits.VCOPWD */
#define IFX_SCU_PLLCON0_VCOPWD_OFF (1u)

/** \brief  Length for Ifx_SCU_PLLCON1_Bits.K1DIV */
#define IFX_SCU_PLLCON1_K1DIV_LEN (7u)

/** \brief  Mask for Ifx_SCU_PLLCON1_Bits.K1DIV */
#define IFX_SCU_PLLCON1_K1DIV_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_PLLCON1_Bits.K1DIV */
#define IFX_SCU_PLLCON1_K1DIV_OFF (16u)

/** \brief  Length for Ifx_SCU_PLLCON1_Bits.K2DIV */
#define IFX_SCU_PLLCON1_K2DIV_LEN (7u)

/** \brief  Mask for Ifx_SCU_PLLCON1_Bits.K2DIV */
#define IFX_SCU_PLLCON1_K2DIV_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_PLLCON1_Bits.K2DIV */
#define IFX_SCU_PLLCON1_K2DIV_OFF (0u)

/** \brief  Length for Ifx_SCU_PLLCON1_Bits.K3DIV */
#define IFX_SCU_PLLCON1_K3DIV_LEN (7u)

/** \brief  Mask for Ifx_SCU_PLLCON1_Bits.K3DIV */
#define IFX_SCU_PLLCON1_K3DIV_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_PLLCON1_Bits.K3DIV */
#define IFX_SCU_PLLCON1_K3DIV_OFF (8u)

/** \brief  Length for Ifx_SCU_PLLCON2_Bits.MODCFG */
#define IFX_SCU_PLLCON2_MODCFG_LEN (16u)

/** \brief  Mask for Ifx_SCU_PLLCON2_Bits.MODCFG */
#define IFX_SCU_PLLCON2_MODCFG_MSK (0xffffu)

/** \brief  Offset for Ifx_SCU_PLLCON2_Bits.MODCFG */
#define IFX_SCU_PLLCON2_MODCFG_OFF (0u)

/** \brief  Length for Ifx_SCU_PLLERAYCON0_Bits.CLRFINDIS */
#define IFX_SCU_PLLERAYCON0_CLRFINDIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLERAYCON0_Bits.CLRFINDIS */
#define IFX_SCU_PLLERAYCON0_CLRFINDIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLERAYCON0_Bits.CLRFINDIS */
#define IFX_SCU_PLLERAYCON0_CLRFINDIS_OFF (5u)

/** \brief  Length for Ifx_SCU_PLLERAYCON0_Bits.NDIV */
#define IFX_SCU_PLLERAYCON0_NDIV_LEN (5u)

/** \brief  Mask for Ifx_SCU_PLLERAYCON0_Bits.NDIV */
#define IFX_SCU_PLLERAYCON0_NDIV_MSK (0x1fu)

/** \brief  Offset for Ifx_SCU_PLLERAYCON0_Bits.NDIV */
#define IFX_SCU_PLLERAYCON0_NDIV_OFF (9u)

/** \brief  Length for Ifx_SCU_PLLERAYCON0_Bits.OSCDISCDIS */
#define IFX_SCU_PLLERAYCON0_OSCDISCDIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLERAYCON0_Bits.OSCDISCDIS */
#define IFX_SCU_PLLERAYCON0_OSCDISCDIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLERAYCON0_Bits.OSCDISCDIS */
#define IFX_SCU_PLLERAYCON0_OSCDISCDIS_OFF (6u)

/** \brief  Length for Ifx_SCU_PLLERAYCON0_Bits.PDIV */
#define IFX_SCU_PLLERAYCON0_PDIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_PLLERAYCON0_Bits.PDIV */
#define IFX_SCU_PLLERAYCON0_PDIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_PLLERAYCON0_Bits.PDIV */
#define IFX_SCU_PLLERAYCON0_PDIV_OFF (24u)

/** \brief  Length for Ifx_SCU_PLLERAYCON0_Bits.PLLPWD */
#define IFX_SCU_PLLERAYCON0_PLLPWD_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLERAYCON0_Bits.PLLPWD */
#define IFX_SCU_PLLERAYCON0_PLLPWD_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLERAYCON0_Bits.PLLPWD */
#define IFX_SCU_PLLERAYCON0_PLLPWD_OFF (16u)

/** \brief  Length for Ifx_SCU_PLLERAYCON0_Bits.RESLD */
#define IFX_SCU_PLLERAYCON0_RESLD_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLERAYCON0_Bits.RESLD */
#define IFX_SCU_PLLERAYCON0_RESLD_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLERAYCON0_Bits.RESLD */
#define IFX_SCU_PLLERAYCON0_RESLD_OFF (18u)

/** \brief  Length for Ifx_SCU_PLLERAYCON0_Bits.SETFINDIS */
#define IFX_SCU_PLLERAYCON0_SETFINDIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLERAYCON0_Bits.SETFINDIS */
#define IFX_SCU_PLLERAYCON0_SETFINDIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLERAYCON0_Bits.SETFINDIS */
#define IFX_SCU_PLLERAYCON0_SETFINDIS_OFF (4u)

/** \brief  Length for Ifx_SCU_PLLERAYCON0_Bits.VCOBYP */
#define IFX_SCU_PLLERAYCON0_VCOBYP_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLERAYCON0_Bits.VCOBYP */
#define IFX_SCU_PLLERAYCON0_VCOBYP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLERAYCON0_Bits.VCOBYP */
#define IFX_SCU_PLLERAYCON0_VCOBYP_OFF (0u)

/** \brief  Length for Ifx_SCU_PLLERAYCON0_Bits.VCOPWD */
#define IFX_SCU_PLLERAYCON0_VCOPWD_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLERAYCON0_Bits.VCOPWD */
#define IFX_SCU_PLLERAYCON0_VCOPWD_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLERAYCON0_Bits.VCOPWD */
#define IFX_SCU_PLLERAYCON0_VCOPWD_OFF (1u)

/** \brief  Length for Ifx_SCU_PLLERAYCON1_Bits.K1DIV */
#define IFX_SCU_PLLERAYCON1_K1DIV_LEN (7u)

/** \brief  Mask for Ifx_SCU_PLLERAYCON1_Bits.K1DIV */
#define IFX_SCU_PLLERAYCON1_K1DIV_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_PLLERAYCON1_Bits.K1DIV */
#define IFX_SCU_PLLERAYCON1_K1DIV_OFF (16u)

/** \brief  Length for Ifx_SCU_PLLERAYCON1_Bits.K2DIV */
#define IFX_SCU_PLLERAYCON1_K2DIV_LEN (7u)

/** \brief  Mask for Ifx_SCU_PLLERAYCON1_Bits.K2DIV */
#define IFX_SCU_PLLERAYCON1_K2DIV_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_PLLERAYCON1_Bits.K2DIV */
#define IFX_SCU_PLLERAYCON1_K2DIV_OFF (0u)

/** \brief  Length for Ifx_SCU_PLLERAYCON1_Bits.K3DIV */
#define IFX_SCU_PLLERAYCON1_K3DIV_LEN (4u)

/** \brief  Mask for Ifx_SCU_PLLERAYCON1_Bits.K3DIV */
#define IFX_SCU_PLLERAYCON1_K3DIV_MSK (0xfu)

/** \brief  Offset for Ifx_SCU_PLLERAYCON1_Bits.K3DIV */
#define IFX_SCU_PLLERAYCON1_K3DIV_OFF (8u)

/** \brief  Length for Ifx_SCU_PLLERAYSTAT_Bits.FINDIS */
#define IFX_SCU_PLLERAYSTAT_FINDIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLERAYSTAT_Bits.FINDIS */
#define IFX_SCU_PLLERAYSTAT_FINDIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLERAYSTAT_Bits.FINDIS */
#define IFX_SCU_PLLERAYSTAT_FINDIS_OFF (3u)

/** \brief  Length for Ifx_SCU_PLLERAYSTAT_Bits.K1RDY */
#define IFX_SCU_PLLERAYSTAT_K1RDY_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLERAYSTAT_Bits.K1RDY */
#define IFX_SCU_PLLERAYSTAT_K1RDY_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLERAYSTAT_Bits.K1RDY */
#define IFX_SCU_PLLERAYSTAT_K1RDY_OFF (4u)

/** \brief  Length for Ifx_SCU_PLLERAYSTAT_Bits.K2RDY */
#define IFX_SCU_PLLERAYSTAT_K2RDY_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLERAYSTAT_Bits.K2RDY */
#define IFX_SCU_PLLERAYSTAT_K2RDY_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLERAYSTAT_Bits.K2RDY */
#define IFX_SCU_PLLERAYSTAT_K2RDY_OFF (5u)

/** \brief  Length for Ifx_SCU_PLLERAYSTAT_Bits.PWDSTAT */
#define IFX_SCU_PLLERAYSTAT_PWDSTAT_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLERAYSTAT_Bits.PWDSTAT */
#define IFX_SCU_PLLERAYSTAT_PWDSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLERAYSTAT_Bits.PWDSTAT */
#define IFX_SCU_PLLERAYSTAT_PWDSTAT_OFF (1u)

/** \brief  Length for Ifx_SCU_PLLERAYSTAT_Bits.VCOBYST */
#define IFX_SCU_PLLERAYSTAT_VCOBYST_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLERAYSTAT_Bits.VCOBYST */
#define IFX_SCU_PLLERAYSTAT_VCOBYST_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLERAYSTAT_Bits.VCOBYST */
#define IFX_SCU_PLLERAYSTAT_VCOBYST_OFF (0u)

/** \brief  Length for Ifx_SCU_PLLERAYSTAT_Bits.VCOLOCK */
#define IFX_SCU_PLLERAYSTAT_VCOLOCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLERAYSTAT_Bits.VCOLOCK */
#define IFX_SCU_PLLERAYSTAT_VCOLOCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLERAYSTAT_Bits.VCOLOCK */
#define IFX_SCU_PLLERAYSTAT_VCOLOCK_OFF (2u)

/** \brief  Length for Ifx_SCU_PLLSTAT_Bits.FINDIS */
#define IFX_SCU_PLLSTAT_FINDIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLSTAT_Bits.FINDIS */
#define IFX_SCU_PLLSTAT_FINDIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLSTAT_Bits.FINDIS */
#define IFX_SCU_PLLSTAT_FINDIS_OFF (3u)

/** \brief  Length for Ifx_SCU_PLLSTAT_Bits.K1RDY */
#define IFX_SCU_PLLSTAT_K1RDY_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLSTAT_Bits.K1RDY */
#define IFX_SCU_PLLSTAT_K1RDY_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLSTAT_Bits.K1RDY */
#define IFX_SCU_PLLSTAT_K1RDY_OFF (4u)

/** \brief  Length for Ifx_SCU_PLLSTAT_Bits.K2RDY */
#define IFX_SCU_PLLSTAT_K2RDY_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLSTAT_Bits.K2RDY */
#define IFX_SCU_PLLSTAT_K2RDY_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLSTAT_Bits.K2RDY */
#define IFX_SCU_PLLSTAT_K2RDY_OFF (5u)

/** \brief  Length for Ifx_SCU_PLLSTAT_Bits.MODRUN */
#define IFX_SCU_PLLSTAT_MODRUN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLSTAT_Bits.MODRUN */
#define IFX_SCU_PLLSTAT_MODRUN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLSTAT_Bits.MODRUN */
#define IFX_SCU_PLLSTAT_MODRUN_OFF (7u)

/** \brief  Length for Ifx_SCU_PLLSTAT_Bits.VCOBYST */
#define IFX_SCU_PLLSTAT_VCOBYST_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLSTAT_Bits.VCOBYST */
#define IFX_SCU_PLLSTAT_VCOBYST_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLSTAT_Bits.VCOBYST */
#define IFX_SCU_PLLSTAT_VCOBYST_OFF (0u)

/** \brief  Length for Ifx_SCU_PLLSTAT_Bits.VCOLOCK */
#define IFX_SCU_PLLSTAT_VCOLOCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_PLLSTAT_Bits.VCOLOCK */
#define IFX_SCU_PLLSTAT_VCOLOCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PLLSTAT_Bits.VCOLOCK */
#define IFX_SCU_PLLSTAT_VCOLOCK_OFF (2u)

/** \brief  Length for Ifx_SCU_PMCSR_Bits.PMST */
#define IFX_SCU_PMCSR_PMST_LEN (3u)

/** \brief  Mask for Ifx_SCU_PMCSR_Bits.PMST */
#define IFX_SCU_PMCSR_PMST_MSK (0x7u)

/** \brief  Offset for Ifx_SCU_PMCSR_Bits.PMST */
#define IFX_SCU_PMCSR_PMST_OFF (8u)

/** \brief  Length for Ifx_SCU_PMCSR_Bits.REQSLP */
#define IFX_SCU_PMCSR_REQSLP_LEN (2u)

/** \brief  Mask for Ifx_SCU_PMCSR_Bits.REQSLP */
#define IFX_SCU_PMCSR_REQSLP_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_PMCSR_Bits.REQSLP */
#define IFX_SCU_PMCSR_REQSLP_OFF (0u)

/** \brief  Length for Ifx_SCU_PMCSR_Bits.SMUSLP */
#define IFX_SCU_PMCSR_SMUSLP_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMCSR_Bits.SMUSLP */
#define IFX_SCU_PMCSR_SMUSLP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMCSR_Bits.SMUSLP */
#define IFX_SCU_PMCSR_SMUSLP_OFF (2u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.DCDCSYNC */
#define IFX_SCU_PMSWCR0_DCDCSYNC_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.DCDCSYNC */
#define IFX_SCU_PMSWCR0_DCDCSYNC_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.DCDCSYNC */
#define IFX_SCU_PMSWCR0_DCDCSYNC_OFF (25u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.ESR0DFEN */
#define IFX_SCU_PMSWCR0_ESR0DFEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.ESR0DFEN */
#define IFX_SCU_PMSWCR0_ESR0DFEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.ESR0DFEN */
#define IFX_SCU_PMSWCR0_ESR0DFEN_OFF (4u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.ESR0EDCON */
#define IFX_SCU_PMSWCR0_ESR0EDCON_LEN (2u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.ESR0EDCON */
#define IFX_SCU_PMSWCR0_ESR0EDCON_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.ESR0EDCON */
#define IFX_SCU_PMSWCR0_ESR0EDCON_OFF (5u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.ESR0TRIST */
#define IFX_SCU_PMSWCR0_ESR0TRIST_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.ESR0TRIST */
#define IFX_SCU_PMSWCR0_ESR0TRIST_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.ESR0TRIST */
#define IFX_SCU_PMSWCR0_ESR0TRIST_OFF (29u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.ESR1DFEN */
#define IFX_SCU_PMSWCR0_ESR1DFEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.ESR1DFEN */
#define IFX_SCU_PMSWCR0_ESR1DFEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.ESR1DFEN */
#define IFX_SCU_PMSWCR0_ESR1DFEN_OFF (7u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.ESR1EDCON */
#define IFX_SCU_PMSWCR0_ESR1EDCON_LEN (2u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.ESR1EDCON */
#define IFX_SCU_PMSWCR0_ESR1EDCON_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.ESR1EDCON */
#define IFX_SCU_PMSWCR0_ESR1EDCON_OFF (8u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.ESR1WKEN */
#define IFX_SCU_PMSWCR0_ESR1WKEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.ESR1WKEN */
#define IFX_SCU_PMSWCR0_ESR1WKEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.ESR1WKEN */
#define IFX_SCU_PMSWCR0_ESR1WKEN_OFF (1u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.LCK */
#define IFX_SCU_PMSWCR0_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.LCK */
#define IFX_SCU_PMSWCR0_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.LCK */
#define IFX_SCU_PMSWCR0_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.PINADFEN */
#define IFX_SCU_PMSWCR0_PINADFEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.PINADFEN */
#define IFX_SCU_PMSWCR0_PINADFEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.PINADFEN */
#define IFX_SCU_PMSWCR0_PINADFEN_OFF (10u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.PINAEDCON */
#define IFX_SCU_PMSWCR0_PINAEDCON_LEN (2u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.PINAEDCON */
#define IFX_SCU_PMSWCR0_PINAEDCON_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.PINAEDCON */
#define IFX_SCU_PMSWCR0_PINAEDCON_OFF (11u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.PINAWKEN */
#define IFX_SCU_PMSWCR0_PINAWKEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.PINAWKEN */
#define IFX_SCU_PMSWCR0_PINAWKEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.PINAWKEN */
#define IFX_SCU_PMSWCR0_PINAWKEN_OFF (2u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.PINBDFEN */
#define IFX_SCU_PMSWCR0_PINBDFEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.PINBDFEN */
#define IFX_SCU_PMSWCR0_PINBDFEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.PINBDFEN */
#define IFX_SCU_PMSWCR0_PINBDFEN_OFF (13u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.PINBEDCON */
#define IFX_SCU_PMSWCR0_PINBEDCON_LEN (2u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.PINBEDCON */
#define IFX_SCU_PMSWCR0_PINBEDCON_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.PINBEDCON */
#define IFX_SCU_PMSWCR0_PINBEDCON_OFF (14u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.PINBWKEN */
#define IFX_SCU_PMSWCR0_PINBWKEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.PINBWKEN */
#define IFX_SCU_PMSWCR0_PINBWKEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.PINBWKEN */
#define IFX_SCU_PMSWCR0_PINBWKEN_OFF (3u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.PORSTDF */
#define IFX_SCU_PMSWCR0_PORSTDF_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.PORSTDF */
#define IFX_SCU_PMSWCR0_PORSTDF_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.PORSTDF */
#define IFX_SCU_PMSWCR0_PORSTDF_OFF (23u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.STBYRAMSEL */
#define IFX_SCU_PMSWCR0_STBYRAMSEL_LEN (2u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.STBYRAMSEL */
#define IFX_SCU_PMSWCR0_STBYRAMSEL_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.STBYRAMSEL */
#define IFX_SCU_PMSWCR0_STBYRAMSEL_OFF (17u)

/** \brief  Length for Ifx_SCU_PMSWCR0_Bits.WUTWKEN */
#define IFX_SCU_PMSWCR0_WUTWKEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR0_Bits.WUTWKEN */
#define IFX_SCU_PMSWCR0_WUTWKEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR0_Bits.WUTWKEN */
#define IFX_SCU_PMSWCR0_WUTWKEN_OFF (20u)

/** \brief  Length for Ifx_SCU_PMSWCR1_Bits.IRADIS */
#define IFX_SCU_PMSWCR1_IRADIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR1_Bits.IRADIS */
#define IFX_SCU_PMSWCR1_IRADIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR1_Bits.IRADIS */
#define IFX_SCU_PMSWCR1_IRADIS_OFF (12u)

/** \brief  Length for Ifx_SCU_PMSWCR1_Bits.STBYEV */
#define IFX_SCU_PMSWCR1_STBYEV_LEN (3u)

/** \brief  Mask for Ifx_SCU_PMSWCR1_Bits.STBYEV */
#define IFX_SCU_PMSWCR1_STBYEV_MSK (0x7u)

/** \brief  Offset for Ifx_SCU_PMSWCR1_Bits.STBYEV */
#define IFX_SCU_PMSWCR1_STBYEV_OFF (28u)

/** \brief  Length for Ifx_SCU_PMSWCR1_Bits.STBYEVEN */
#define IFX_SCU_PMSWCR1_STBYEVEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR1_Bits.STBYEVEN */
#define IFX_SCU_PMSWCR1_STBYEVEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR1_Bits.STBYEVEN */
#define IFX_SCU_PMSWCR1_STBYEVEN_OFF (27u)

/** \brief  Length for Ifx_SCU_PMSWCR3_Bits.LCK */
#define IFX_SCU_PMSWCR3_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR3_Bits.LCK */
#define IFX_SCU_PMSWCR3_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR3_Bits.LCK */
#define IFX_SCU_PMSWCR3_LCK_OFF (31u)

/** \brief  Length for Ifx_SCU_PMSWCR3_Bits.WUTDIV */
#define IFX_SCU_PMSWCR3_WUTDIV_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR3_Bits.WUTDIV */
#define IFX_SCU_PMSWCR3_WUTDIV_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR3_Bits.WUTDIV */
#define IFX_SCU_PMSWCR3_WUTDIV_OFF (28u)

/** \brief  Length for Ifx_SCU_PMSWCR3_Bits.WUTEN */
#define IFX_SCU_PMSWCR3_WUTEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR3_Bits.WUTEN */
#define IFX_SCU_PMSWCR3_WUTEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR3_Bits.WUTEN */
#define IFX_SCU_PMSWCR3_WUTEN_OFF (29u)

/** \brief  Length for Ifx_SCU_PMSWCR3_Bits.WUTMODE */
#define IFX_SCU_PMSWCR3_WUTMODE_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWCR3_Bits.WUTMODE */
#define IFX_SCU_PMSWCR3_WUTMODE_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWCR3_Bits.WUTMODE */
#define IFX_SCU_PMSWCR3_WUTMODE_OFF (30u)

/** \brief  Length for Ifx_SCU_PMSWCR3_Bits.WUTREL */
#define IFX_SCU_PMSWCR3_WUTREL_LEN (24u)

/** \brief  Mask for Ifx_SCU_PMSWCR3_Bits.WUTREL */
#define IFX_SCU_PMSWCR3_WUTREL_MSK (0xffffffu)

/** \brief  Offset for Ifx_SCU_PMSWCR3_Bits.WUTREL */
#define IFX_SCU_PMSWCR3_WUTREL_OFF (0u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.ESR0TRIST */
#define IFX_SCU_PMSWSTAT_ESR0TRIST_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.ESR0TRIST */
#define IFX_SCU_PMSWSTAT_ESR0TRIST_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.ESR0TRIST */
#define IFX_SCU_PMSWSTAT_ESR0TRIST_OFF (27u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.ESR1OVRUN */
#define IFX_SCU_PMSWSTAT_ESR1OVRUN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.ESR1OVRUN */
#define IFX_SCU_PMSWSTAT_ESR1OVRUN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.ESR1OVRUN */
#define IFX_SCU_PMSWSTAT_ESR1OVRUN_OFF (3u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.ESR1WKEN */
#define IFX_SCU_PMSWSTAT_ESR1WKEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.ESR1WKEN */
#define IFX_SCU_PMSWSTAT_ESR1WKEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.ESR1WKEN */
#define IFX_SCU_PMSWSTAT_ESR1WKEN_OFF (20u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.ESR1WKP */
#define IFX_SCU_PMSWSTAT_ESR1WKP_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.ESR1WKP */
#define IFX_SCU_PMSWSTAT_ESR1WKP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.ESR1WKP */
#define IFX_SCU_PMSWSTAT_ESR1WKP_OFF (2u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.HWCFGEVR */
#define IFX_SCU_PMSWSTAT_HWCFGEVR_LEN (3u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.HWCFGEVR */
#define IFX_SCU_PMSWSTAT_HWCFGEVR_MSK (0x7u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.HWCFGEVR */
#define IFX_SCU_PMSWSTAT_HWCFGEVR_OFF (10u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.PINAOVRUN */
#define IFX_SCU_PMSWSTAT_PINAOVRUN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.PINAOVRUN */
#define IFX_SCU_PMSWSTAT_PINAOVRUN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.PINAOVRUN */
#define IFX_SCU_PMSWSTAT_PINAOVRUN_OFF (5u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.PINAWKEN */
#define IFX_SCU_PMSWSTAT_PINAWKEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.PINAWKEN */
#define IFX_SCU_PMSWSTAT_PINAWKEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.PINAWKEN */
#define IFX_SCU_PMSWSTAT_PINAWKEN_OFF (21u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.PINAWKP */
#define IFX_SCU_PMSWSTAT_PINAWKP_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.PINAWKP */
#define IFX_SCU_PMSWSTAT_PINAWKP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.PINAWKP */
#define IFX_SCU_PMSWSTAT_PINAWKP_OFF (4u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.PINBOVRUN */
#define IFX_SCU_PMSWSTAT_PINBOVRUN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.PINBOVRUN */
#define IFX_SCU_PMSWSTAT_PINBOVRUN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.PINBOVRUN */
#define IFX_SCU_PMSWSTAT_PINBOVRUN_OFF (7u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.PINBWKEN */
#define IFX_SCU_PMSWSTAT_PINBWKEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.PINBWKEN */
#define IFX_SCU_PMSWSTAT_PINBWKEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.PINBWKEN */
#define IFX_SCU_PMSWSTAT_PINBWKEN_OFF (22u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.PINBWKP */
#define IFX_SCU_PMSWSTAT_PINBWKP_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.PINBWKP */
#define IFX_SCU_PMSWSTAT_PINBWKP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.PINBWKP */
#define IFX_SCU_PMSWSTAT_PINBWKP_OFF (6u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.PORSTDF */
#define IFX_SCU_PMSWSTAT_PORSTDF_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.PORSTDF */
#define IFX_SCU_PMSWSTAT_PORSTDF_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.PORSTDF */
#define IFX_SCU_PMSWSTAT_PORSTDF_OFF (9u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.STBYRAM */
#define IFX_SCU_PMSWSTAT_STBYRAM_LEN (2u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.STBYRAM */
#define IFX_SCU_PMSWSTAT_STBYRAM_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.STBYRAM */
#define IFX_SCU_PMSWSTAT_STBYRAM_OFF (13u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.WUTEN */
#define IFX_SCU_PMSWSTAT_WUTEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.WUTEN */
#define IFX_SCU_PMSWSTAT_WUTEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.WUTEN */
#define IFX_SCU_PMSWSTAT_WUTEN_OFF (29u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.WUTMODE */
#define IFX_SCU_PMSWSTAT_WUTMODE_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.WUTMODE */
#define IFX_SCU_PMSWSTAT_WUTMODE_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.WUTMODE */
#define IFX_SCU_PMSWSTAT_WUTMODE_OFF (30u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.WUTOVRUN */
#define IFX_SCU_PMSWSTAT_WUTOVRUN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.WUTOVRUN */
#define IFX_SCU_PMSWSTAT_WUTOVRUN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.WUTOVRUN */
#define IFX_SCU_PMSWSTAT_WUTOVRUN_OFF (17u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.WUTRUN */
#define IFX_SCU_PMSWSTAT_WUTRUN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.WUTRUN */
#define IFX_SCU_PMSWSTAT_WUTRUN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.WUTRUN */
#define IFX_SCU_PMSWSTAT_WUTRUN_OFF (31u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.WUTWKEN */
#define IFX_SCU_PMSWSTAT_WUTWKEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.WUTWKEN */
#define IFX_SCU_PMSWSTAT_WUTWKEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.WUTWKEN */
#define IFX_SCU_PMSWSTAT_WUTWKEN_OFF (19u)

/** \brief  Length for Ifx_SCU_PMSWSTAT_Bits.WUTWKP */
#define IFX_SCU_PMSWSTAT_WUTWKP_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTAT_Bits.WUTWKP */
#define IFX_SCU_PMSWSTAT_WUTWKP_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTAT_Bits.WUTWKP */
#define IFX_SCU_PMSWSTAT_WUTWKP_OFF (16u)

/** \brief  Length for Ifx_SCU_PMSWSTATCLR_Bits.ESR1OVRUNCLR */
#define IFX_SCU_PMSWSTATCLR_ESR1OVRUNCLR_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTATCLR_Bits.ESR1OVRUNCLR */
#define IFX_SCU_PMSWSTATCLR_ESR1OVRUNCLR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTATCLR_Bits.ESR1OVRUNCLR */
#define IFX_SCU_PMSWSTATCLR_ESR1OVRUNCLR_OFF (3u)

/** \brief  Length for Ifx_SCU_PMSWSTATCLR_Bits.ESR1WKPCLR */
#define IFX_SCU_PMSWSTATCLR_ESR1WKPCLR_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTATCLR_Bits.ESR1WKPCLR */
#define IFX_SCU_PMSWSTATCLR_ESR1WKPCLR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTATCLR_Bits.ESR1WKPCLR */
#define IFX_SCU_PMSWSTATCLR_ESR1WKPCLR_OFF (2u)

/** \brief  Length for Ifx_SCU_PMSWSTATCLR_Bits.PINAOVRUNCLR */
#define IFX_SCU_PMSWSTATCLR_PINAOVRUNCLR_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTATCLR_Bits.PINAOVRUNCLR */
#define IFX_SCU_PMSWSTATCLR_PINAOVRUNCLR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTATCLR_Bits.PINAOVRUNCLR */
#define IFX_SCU_PMSWSTATCLR_PINAOVRUNCLR_OFF (5u)

/** \brief  Length for Ifx_SCU_PMSWSTATCLR_Bits.PINAWKPCLR */
#define IFX_SCU_PMSWSTATCLR_PINAWKPCLR_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTATCLR_Bits.PINAWKPCLR */
#define IFX_SCU_PMSWSTATCLR_PINAWKPCLR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTATCLR_Bits.PINAWKPCLR */
#define IFX_SCU_PMSWSTATCLR_PINAWKPCLR_OFF (4u)

/** \brief  Length for Ifx_SCU_PMSWSTATCLR_Bits.PINBOVRUNCLR */
#define IFX_SCU_PMSWSTATCLR_PINBOVRUNCLR_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTATCLR_Bits.PINBOVRUNCLR */
#define IFX_SCU_PMSWSTATCLR_PINBOVRUNCLR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTATCLR_Bits.PINBOVRUNCLR */
#define IFX_SCU_PMSWSTATCLR_PINBOVRUNCLR_OFF (7u)

/** \brief  Length for Ifx_SCU_PMSWSTATCLR_Bits.PINBWKPCLR */
#define IFX_SCU_PMSWSTATCLR_PINBWKPCLR_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTATCLR_Bits.PINBWKPCLR */
#define IFX_SCU_PMSWSTATCLR_PINBWKPCLR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTATCLR_Bits.PINBWKPCLR */
#define IFX_SCU_PMSWSTATCLR_PINBWKPCLR_OFF (6u)

/** \brief  Length for Ifx_SCU_PMSWSTATCLR_Bits.WUTOVRUNCLR */
#define IFX_SCU_PMSWSTATCLR_WUTOVRUNCLR_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTATCLR_Bits.WUTOVRUNCLR */
#define IFX_SCU_PMSWSTATCLR_WUTOVRUNCLR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTATCLR_Bits.WUTOVRUNCLR */
#define IFX_SCU_PMSWSTATCLR_WUTOVRUNCLR_OFF (17u)

/** \brief  Length for Ifx_SCU_PMSWSTATCLR_Bits.WUTWKPCLR */
#define IFX_SCU_PMSWSTATCLR_WUTWKPCLR_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWSTATCLR_Bits.WUTWKPCLR */
#define IFX_SCU_PMSWSTATCLR_WUTWKPCLR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWSTATCLR_Bits.WUTWKPCLR */
#define IFX_SCU_PMSWSTATCLR_WUTWKPCLR_OFF (16u)

/** \brief  Length for Ifx_SCU_PMSWUTCNT_Bits.VAL */
#define IFX_SCU_PMSWUTCNT_VAL_LEN (1u)

/** \brief  Mask for Ifx_SCU_PMSWUTCNT_Bits.VAL */
#define IFX_SCU_PMSWUTCNT_VAL_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_PMSWUTCNT_Bits.VAL */
#define IFX_SCU_PMSWUTCNT_VAL_OFF (31u)

/** \brief  Length for Ifx_SCU_PMSWUTCNT_Bits.WUTCNT */
#define IFX_SCU_PMSWUTCNT_WUTCNT_LEN (24u)

/** \brief  Mask for Ifx_SCU_PMSWUTCNT_Bits.WUTCNT */
#define IFX_SCU_PMSWUTCNT_WUTCNT_MSK (0xffffffu)

/** \brief  Offset for Ifx_SCU_PMSWUTCNT_Bits.WUTCNT */
#define IFX_SCU_PMSWUTCNT_WUTCNT_OFF (0u)

/** \brief  Length for Ifx_SCU_RSTCON2_Bits.CLRC */
#define IFX_SCU_RSTCON2_CLRC_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTCON2_Bits.CLRC */
#define IFX_SCU_RSTCON2_CLRC_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTCON2_Bits.CLRC */
#define IFX_SCU_RSTCON2_CLRC_OFF (1u)

/** \brief  Length for Ifx_SCU_RSTCON2_Bits.CSS0 */
#define IFX_SCU_RSTCON2_CSS0_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTCON2_Bits.CSS0 */
#define IFX_SCU_RSTCON2_CSS0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTCON2_Bits.CSS0 */
#define IFX_SCU_RSTCON2_CSS0_OFF (12u)

/** \brief  Length for Ifx_SCU_RSTCON2_Bits.CSS1 */
#define IFX_SCU_RSTCON2_CSS1_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTCON2_Bits.CSS1 */
#define IFX_SCU_RSTCON2_CSS1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTCON2_Bits.CSS1 */
#define IFX_SCU_RSTCON2_CSS1_OFF (13u)

/** \brief  Length for Ifx_SCU_RSTCON2_Bits.CSS2 */
#define IFX_SCU_RSTCON2_CSS2_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTCON2_Bits.CSS2 */
#define IFX_SCU_RSTCON2_CSS2_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTCON2_Bits.CSS2 */
#define IFX_SCU_RSTCON2_CSS2_OFF (14u)

/** \brief  Length for Ifx_SCU_RSTCON2_Bits.USRINFO */
#define IFX_SCU_RSTCON2_USRINFO_LEN (16u)

/** \brief  Mask for Ifx_SCU_RSTCON2_Bits.USRINFO */
#define IFX_SCU_RSTCON2_USRINFO_MSK (0xffffu)

/** \brief  Offset for Ifx_SCU_RSTCON2_Bits.USRINFO */
#define IFX_SCU_RSTCON2_USRINFO_OFF (16u)

/** \brief  Length for Ifx_SCU_RSTCON_Bits.ESR0 */
#define IFX_SCU_RSTCON_ESR0_LEN (2u)

/** \brief  Mask for Ifx_SCU_RSTCON_Bits.ESR0 */
#define IFX_SCU_RSTCON_ESR0_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_RSTCON_Bits.ESR0 */
#define IFX_SCU_RSTCON_ESR0_OFF (0u)

/** \brief  Length for Ifx_SCU_RSTCON_Bits.ESR1 */
#define IFX_SCU_RSTCON_ESR1_LEN (2u)

/** \brief  Mask for Ifx_SCU_RSTCON_Bits.ESR1 */
#define IFX_SCU_RSTCON_ESR1_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_RSTCON_Bits.ESR1 */
#define IFX_SCU_RSTCON_ESR1_OFF (2u)

/** \brief  Length for Ifx_SCU_RSTCON_Bits.SMU */
#define IFX_SCU_RSTCON_SMU_LEN (2u)

/** \brief  Mask for Ifx_SCU_RSTCON_Bits.SMU */
#define IFX_SCU_RSTCON_SMU_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_RSTCON_Bits.SMU */
#define IFX_SCU_RSTCON_SMU_OFF (6u)

/** \brief  Length for Ifx_SCU_RSTCON_Bits.STM0 */
#define IFX_SCU_RSTCON_STM0_LEN (2u)

/** \brief  Mask for Ifx_SCU_RSTCON_Bits.STM0 */
#define IFX_SCU_RSTCON_STM0_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_RSTCON_Bits.STM0 */
#define IFX_SCU_RSTCON_STM0_OFF (10u)

/** \brief  Length for Ifx_SCU_RSTCON_Bits.STM1 */
#define IFX_SCU_RSTCON_STM1_LEN (2u)

/** \brief  Mask for Ifx_SCU_RSTCON_Bits.STM1 */
#define IFX_SCU_RSTCON_STM1_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_RSTCON_Bits.STM1 */
#define IFX_SCU_RSTCON_STM1_OFF (12u)

/** \brief  Length for Ifx_SCU_RSTCON_Bits.STM2 */
#define IFX_SCU_RSTCON_STM2_LEN (2u)

/** \brief  Mask for Ifx_SCU_RSTCON_Bits.STM2 */
#define IFX_SCU_RSTCON_STM2_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_RSTCON_Bits.STM2 */
#define IFX_SCU_RSTCON_STM2_OFF (14u)

/** \brief  Length for Ifx_SCU_RSTCON_Bits.SW */
#define IFX_SCU_RSTCON_SW_LEN (2u)

/** \brief  Mask for Ifx_SCU_RSTCON_Bits.SW */
#define IFX_SCU_RSTCON_SW_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_RSTCON_Bits.SW */
#define IFX_SCU_RSTCON_SW_OFF (8u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.CB0 */
#define IFX_SCU_RSTSTAT_CB0_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.CB0 */
#define IFX_SCU_RSTSTAT_CB0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.CB0 */
#define IFX_SCU_RSTSTAT_CB0_OFF (18u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.CB1 */
#define IFX_SCU_RSTSTAT_CB1_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.CB1 */
#define IFX_SCU_RSTSTAT_CB1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.CB1 */
#define IFX_SCU_RSTSTAT_CB1_OFF (19u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.CB3 */
#define IFX_SCU_RSTSTAT_CB3_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.CB3 */
#define IFX_SCU_RSTSTAT_CB3_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.CB3 */
#define IFX_SCU_RSTSTAT_CB3_OFF (20u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.ESR0 */
#define IFX_SCU_RSTSTAT_ESR0_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.ESR0 */
#define IFX_SCU_RSTSTAT_ESR0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.ESR0 */
#define IFX_SCU_RSTSTAT_ESR0_OFF (0u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.ESR1 */
#define IFX_SCU_RSTSTAT_ESR1_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.ESR1 */
#define IFX_SCU_RSTSTAT_ESR1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.ESR1 */
#define IFX_SCU_RSTSTAT_ESR1_OFF (1u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.EVR13 */
#define IFX_SCU_RSTSTAT_EVR13_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.EVR13 */
#define IFX_SCU_RSTSTAT_EVR13_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.EVR13 */
#define IFX_SCU_RSTSTAT_EVR13_OFF (23u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.EVR33 */
#define IFX_SCU_RSTSTAT_EVR33_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.EVR33 */
#define IFX_SCU_RSTSTAT_EVR33_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.EVR33 */
#define IFX_SCU_RSTSTAT_EVR33_OFF (24u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.PORST */
#define IFX_SCU_RSTSTAT_PORST_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.PORST */
#define IFX_SCU_RSTSTAT_PORST_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.PORST */
#define IFX_SCU_RSTSTAT_PORST_OFF (16u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.SMU */
#define IFX_SCU_RSTSTAT_SMU_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.SMU */
#define IFX_SCU_RSTSTAT_SMU_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.SMU */
#define IFX_SCU_RSTSTAT_SMU_OFF (3u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.STBYR */
#define IFX_SCU_RSTSTAT_STBYR_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.STBYR */
#define IFX_SCU_RSTSTAT_STBYR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.STBYR */
#define IFX_SCU_RSTSTAT_STBYR_OFF (28u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.STM0 */
#define IFX_SCU_RSTSTAT_STM0_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.STM0 */
#define IFX_SCU_RSTSTAT_STM0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.STM0 */
#define IFX_SCU_RSTSTAT_STM0_OFF (5u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.STM1 */
#define IFX_SCU_RSTSTAT_STM1_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.STM1 */
#define IFX_SCU_RSTSTAT_STM1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.STM1 */
#define IFX_SCU_RSTSTAT_STM1_OFF (6u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.STM2 */
#define IFX_SCU_RSTSTAT_STM2_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.STM2 */
#define IFX_SCU_RSTSTAT_STM2_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.STM2 */
#define IFX_SCU_RSTSTAT_STM2_OFF (7u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.SW */
#define IFX_SCU_RSTSTAT_SW_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.SW */
#define IFX_SCU_RSTSTAT_SW_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.SW */
#define IFX_SCU_RSTSTAT_SW_OFF (4u)

/** \brief  Length for Ifx_SCU_RSTSTAT_Bits.SWD */
#define IFX_SCU_RSTSTAT_SWD_LEN (1u)

/** \brief  Mask for Ifx_SCU_RSTSTAT_Bits.SWD */
#define IFX_SCU_RSTSTAT_SWD_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_RSTSTAT_Bits.SWD */
#define IFX_SCU_RSTSTAT_SWD_OFF (25u)

/** \brief  Length for Ifx_SCU_SAFECON_Bits.HBT */
#define IFX_SCU_SAFECON_HBT_LEN (1u)

/** \brief  Mask for Ifx_SCU_SAFECON_Bits.HBT */
#define IFX_SCU_SAFECON_HBT_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_SAFECON_Bits.HBT */
#define IFX_SCU_SAFECON_HBT_OFF (0u)

/** \brief  Length for Ifx_SCU_STSTAT_Bits.FCBAE */
#define IFX_SCU_STSTAT_FCBAE_LEN (1u)

/** \brief  Mask for Ifx_SCU_STSTAT_Bits.FCBAE */
#define IFX_SCU_STSTAT_FCBAE_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_STSTAT_Bits.FCBAE */
#define IFX_SCU_STSTAT_FCBAE_OFF (16u)

/** \brief  Length for Ifx_SCU_STSTAT_Bits.FTM */
#define IFX_SCU_STSTAT_FTM_LEN (7u)

/** \brief  Mask for Ifx_SCU_STSTAT_Bits.FTM */
#define IFX_SCU_STSTAT_FTM_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_STSTAT_Bits.FTM */
#define IFX_SCU_STSTAT_FTM_OFF (8u)

/** \brief  Length for Ifx_SCU_STSTAT_Bits.HWCFG */
#define IFX_SCU_STSTAT_HWCFG_LEN (8u)

/** \brief  Mask for Ifx_SCU_STSTAT_Bits.HWCFG */
#define IFX_SCU_STSTAT_HWCFG_MSK (0xffu)

/** \brief  Offset for Ifx_SCU_STSTAT_Bits.HWCFG */
#define IFX_SCU_STSTAT_HWCFG_OFF (0u)

/** \brief  Length for Ifx_SCU_STSTAT_Bits.LUDIS */
#define IFX_SCU_STSTAT_LUDIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_STSTAT_Bits.LUDIS */
#define IFX_SCU_STSTAT_LUDIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_STSTAT_Bits.LUDIS */
#define IFX_SCU_STSTAT_LUDIS_OFF (17u)

/** \brief  Length for Ifx_SCU_STSTAT_Bits.MODE */
#define IFX_SCU_STSTAT_MODE_LEN (1u)

/** \brief  Mask for Ifx_SCU_STSTAT_Bits.MODE */
#define IFX_SCU_STSTAT_MODE_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_STSTAT_Bits.MODE */
#define IFX_SCU_STSTAT_MODE_OFF (15u)

/** \brief  Length for Ifx_SCU_STSTAT_Bits.RAMINT */
#define IFX_SCU_STSTAT_RAMINT_LEN (1u)

/** \brief  Mask for Ifx_SCU_STSTAT_Bits.RAMINT */
#define IFX_SCU_STSTAT_RAMINT_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_STSTAT_Bits.RAMINT */
#define IFX_SCU_STSTAT_RAMINT_OFF (24u)

/** \brief  Length for Ifx_SCU_STSTAT_Bits.SPDEN */
#define IFX_SCU_STSTAT_SPDEN_LEN (1u)

/** \brief  Mask for Ifx_SCU_STSTAT_Bits.SPDEN */
#define IFX_SCU_STSTAT_SPDEN_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_STSTAT_Bits.SPDEN */
#define IFX_SCU_STSTAT_SPDEN_OFF (20u)

/** \brief  Length for Ifx_SCU_STSTAT_Bits.TRSTL */
#define IFX_SCU_STSTAT_TRSTL_LEN (1u)

/** \brief  Mask for Ifx_SCU_STSTAT_Bits.TRSTL */
#define IFX_SCU_STSTAT_TRSTL_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_STSTAT_Bits.TRSTL */
#define IFX_SCU_STSTAT_TRSTL_OFF (19u)

/** \brief  Length for Ifx_SCU_SWRSTCON_Bits.SWRSTREQ */
#define IFX_SCU_SWRSTCON_SWRSTREQ_LEN (1u)

/** \brief  Mask for Ifx_SCU_SWRSTCON_Bits.SWRSTREQ */
#define IFX_SCU_SWRSTCON_SWRSTREQ_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_SWRSTCON_Bits.SWRSTREQ */
#define IFX_SCU_SWRSTCON_SWRSTREQ_OFF (1u)

/** \brief  Length for Ifx_SCU_SYSCON_Bits.CCTRIG0 */
#define IFX_SCU_SYSCON_CCTRIG0_LEN (1u)

/** \brief  Mask for Ifx_SCU_SYSCON_Bits.CCTRIG0 */
#define IFX_SCU_SYSCON_CCTRIG0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_SYSCON_Bits.CCTRIG0 */
#define IFX_SCU_SYSCON_CCTRIG0_OFF (0u)

/** \brief  Length for Ifx_SCU_SYSCON_Bits.DATM */
#define IFX_SCU_SYSCON_DATM_LEN (1u)

/** \brief  Mask for Ifx_SCU_SYSCON_Bits.DATM */
#define IFX_SCU_SYSCON_DATM_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_SYSCON_Bits.DATM */
#define IFX_SCU_SYSCON_DATM_OFF (8u)

/** \brief  Length for Ifx_SCU_SYSCON_Bits.RAMINTM */
#define IFX_SCU_SYSCON_RAMINTM_LEN (2u)

/** \brief  Mask for Ifx_SCU_SYSCON_Bits.RAMINTM */
#define IFX_SCU_SYSCON_RAMINTM_MSK (0x3u)

/** \brief  Offset for Ifx_SCU_SYSCON_Bits.RAMINTM */
#define IFX_SCU_SYSCON_RAMINTM_OFF (2u)

/** \brief  Length for Ifx_SCU_SYSCON_Bits.SETLUDIS */
#define IFX_SCU_SYSCON_SETLUDIS_LEN (1u)

/** \brief  Mask for Ifx_SCU_SYSCON_Bits.SETLUDIS */
#define IFX_SCU_SYSCON_SETLUDIS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_SYSCON_Bits.SETLUDIS */
#define IFX_SCU_SYSCON_SETLUDIS_OFF (4u)

/** \brief  Length for Ifx_SCU_TRAPCLR_Bits.ESR0T */
#define IFX_SCU_TRAPCLR_ESR0T_LEN (1u)

/** \brief  Mask for Ifx_SCU_TRAPCLR_Bits.ESR0T */
#define IFX_SCU_TRAPCLR_ESR0T_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_TRAPCLR_Bits.ESR0T */
#define IFX_SCU_TRAPCLR_ESR0T_OFF (0u)

/** \brief  Length for Ifx_SCU_TRAPCLR_Bits.ESR1T */
#define IFX_SCU_TRAPCLR_ESR1T_LEN (1u)

/** \brief  Mask for Ifx_SCU_TRAPCLR_Bits.ESR1T */
#define IFX_SCU_TRAPCLR_ESR1T_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_TRAPCLR_Bits.ESR1T */
#define IFX_SCU_TRAPCLR_ESR1T_OFF (1u)

/** \brief  Length for Ifx_SCU_TRAPCLR_Bits.SMUT */
#define IFX_SCU_TRAPCLR_SMUT_LEN (1u)

/** \brief  Mask for Ifx_SCU_TRAPCLR_Bits.SMUT */
#define IFX_SCU_TRAPCLR_SMUT_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_TRAPCLR_Bits.SMUT */
#define IFX_SCU_TRAPCLR_SMUT_OFF (3u)

/** \brief  Length for Ifx_SCU_TRAPDIS_Bits.ESR0T */
#define IFX_SCU_TRAPDIS_ESR0T_LEN (1u)

/** \brief  Mask for Ifx_SCU_TRAPDIS_Bits.ESR0T */
#define IFX_SCU_TRAPDIS_ESR0T_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_TRAPDIS_Bits.ESR0T */
#define IFX_SCU_TRAPDIS_ESR0T_OFF (0u)

/** \brief  Length for Ifx_SCU_TRAPDIS_Bits.ESR1T */
#define IFX_SCU_TRAPDIS_ESR1T_LEN (1u)

/** \brief  Mask for Ifx_SCU_TRAPDIS_Bits.ESR1T */
#define IFX_SCU_TRAPDIS_ESR1T_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_TRAPDIS_Bits.ESR1T */
#define IFX_SCU_TRAPDIS_ESR1T_OFF (1u)

/** \brief  Length for Ifx_SCU_TRAPDIS_Bits.SMUT */
#define IFX_SCU_TRAPDIS_SMUT_LEN (1u)

/** \brief  Mask for Ifx_SCU_TRAPDIS_Bits.SMUT */
#define IFX_SCU_TRAPDIS_SMUT_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_TRAPDIS_Bits.SMUT */
#define IFX_SCU_TRAPDIS_SMUT_OFF (3u)

/** \brief  Length for Ifx_SCU_TRAPSET_Bits.ESR0T */
#define IFX_SCU_TRAPSET_ESR0T_LEN (1u)

/** \brief  Mask for Ifx_SCU_TRAPSET_Bits.ESR0T */
#define IFX_SCU_TRAPSET_ESR0T_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_TRAPSET_Bits.ESR0T */
#define IFX_SCU_TRAPSET_ESR0T_OFF (0u)

/** \brief  Length for Ifx_SCU_TRAPSET_Bits.ESR1T */
#define IFX_SCU_TRAPSET_ESR1T_LEN (1u)

/** \brief  Mask for Ifx_SCU_TRAPSET_Bits.ESR1T */
#define IFX_SCU_TRAPSET_ESR1T_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_TRAPSET_Bits.ESR1T */
#define IFX_SCU_TRAPSET_ESR1T_OFF (1u)

/** \brief  Length for Ifx_SCU_TRAPSET_Bits.SMUT */
#define IFX_SCU_TRAPSET_SMUT_LEN (1u)

/** \brief  Mask for Ifx_SCU_TRAPSET_Bits.SMUT */
#define IFX_SCU_TRAPSET_SMUT_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_TRAPSET_Bits.SMUT */
#define IFX_SCU_TRAPSET_SMUT_OFF (3u)

/** \brief  Length for Ifx_SCU_TRAPSTAT_Bits.ESR0T */
#define IFX_SCU_TRAPSTAT_ESR0T_LEN (1u)

/** \brief  Mask for Ifx_SCU_TRAPSTAT_Bits.ESR0T */
#define IFX_SCU_TRAPSTAT_ESR0T_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_TRAPSTAT_Bits.ESR0T */
#define IFX_SCU_TRAPSTAT_ESR0T_OFF (0u)

/** \brief  Length for Ifx_SCU_TRAPSTAT_Bits.ESR1T */
#define IFX_SCU_TRAPSTAT_ESR1T_LEN (1u)

/** \brief  Mask for Ifx_SCU_TRAPSTAT_Bits.ESR1T */
#define IFX_SCU_TRAPSTAT_ESR1T_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_TRAPSTAT_Bits.ESR1T */
#define IFX_SCU_TRAPSTAT_ESR1T_OFF (1u)

/** \brief  Length for Ifx_SCU_TRAPSTAT_Bits.SMUT */
#define IFX_SCU_TRAPSTAT_SMUT_LEN (1u)

/** \brief  Mask for Ifx_SCU_TRAPSTAT_Bits.SMUT */
#define IFX_SCU_TRAPSTAT_SMUT_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_TRAPSTAT_Bits.SMUT */
#define IFX_SCU_TRAPSTAT_SMUT_OFF (3u)

/** \brief  Length for Ifx_SCU_WDTCPU_CON0_Bits.ENDINIT */
#define IFX_SCU_WDTCPU_CON0_ENDINIT_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_CON0_Bits.ENDINIT */
#define IFX_SCU_WDTCPU_CON0_ENDINIT_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_CON0_Bits.ENDINIT */
#define IFX_SCU_WDTCPU_CON0_ENDINIT_OFF (0u)

/** \brief  Length for Ifx_SCU_WDTCPU_CON0_Bits.LCK */
#define IFX_SCU_WDTCPU_CON0_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_CON0_Bits.LCK */
#define IFX_SCU_WDTCPU_CON0_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_CON0_Bits.LCK */
#define IFX_SCU_WDTCPU_CON0_LCK_OFF (1u)

/** \brief  Length for Ifx_SCU_WDTCPU_CON0_Bits.PW */
#define IFX_SCU_WDTCPU_CON0_PW_LEN (14u)

/** \brief  Mask for Ifx_SCU_WDTCPU_CON0_Bits.PW */
#define IFX_SCU_WDTCPU_CON0_PW_MSK (0x3fffu)

/** \brief  Offset for Ifx_SCU_WDTCPU_CON0_Bits.PW */
#define IFX_SCU_WDTCPU_CON0_PW_OFF (2u)

/** \brief  Length for Ifx_SCU_WDTCPU_CON0_Bits.REL */
#define IFX_SCU_WDTCPU_CON0_REL_LEN (16u)

/** \brief  Mask for Ifx_SCU_WDTCPU_CON0_Bits.REL */
#define IFX_SCU_WDTCPU_CON0_REL_MSK (0xffffu)

/** \brief  Offset for Ifx_SCU_WDTCPU_CON0_Bits.REL */
#define IFX_SCU_WDTCPU_CON0_REL_OFF (16u)

/** \brief  Length for Ifx_SCU_WDTCPU_CON1_Bits.DR */
#define IFX_SCU_WDTCPU_CON1_DR_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_CON1_Bits.DR */
#define IFX_SCU_WDTCPU_CON1_DR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_CON1_Bits.DR */
#define IFX_SCU_WDTCPU_CON1_DR_OFF (3u)

/** \brief  Length for Ifx_SCU_WDTCPU_CON1_Bits.IR0 */
#define IFX_SCU_WDTCPU_CON1_IR0_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_CON1_Bits.IR0 */
#define IFX_SCU_WDTCPU_CON1_IR0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_CON1_Bits.IR0 */
#define IFX_SCU_WDTCPU_CON1_IR0_OFF (2u)

/** \brief  Length for Ifx_SCU_WDTCPU_CON1_Bits.IR1 */
#define IFX_SCU_WDTCPU_CON1_IR1_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_CON1_Bits.IR1 */
#define IFX_SCU_WDTCPU_CON1_IR1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_CON1_Bits.IR1 */
#define IFX_SCU_WDTCPU_CON1_IR1_OFF (5u)

/** \brief  Length for Ifx_SCU_WDTCPU_CON1_Bits.PAR */
#define IFX_SCU_WDTCPU_CON1_PAR_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_CON1_Bits.PAR */
#define IFX_SCU_WDTCPU_CON1_PAR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_CON1_Bits.PAR */
#define IFX_SCU_WDTCPU_CON1_PAR_OFF (7u)

/** \brief  Length for Ifx_SCU_WDTCPU_CON1_Bits.TCR */
#define IFX_SCU_WDTCPU_CON1_TCR_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_CON1_Bits.TCR */
#define IFX_SCU_WDTCPU_CON1_TCR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_CON1_Bits.TCR */
#define IFX_SCU_WDTCPU_CON1_TCR_OFF (8u)

/** \brief  Length for Ifx_SCU_WDTCPU_CON1_Bits.TCTR */
#define IFX_SCU_WDTCPU_CON1_TCTR_LEN (7u)

/** \brief  Mask for Ifx_SCU_WDTCPU_CON1_Bits.TCTR */
#define IFX_SCU_WDTCPU_CON1_TCTR_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_WDTCPU_CON1_Bits.TCTR */
#define IFX_SCU_WDTCPU_CON1_TCTR_OFF (9u)

/** \brief  Length for Ifx_SCU_WDTCPU_CON1_Bits.UR */
#define IFX_SCU_WDTCPU_CON1_UR_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_CON1_Bits.UR */
#define IFX_SCU_WDTCPU_CON1_UR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_CON1_Bits.UR */
#define IFX_SCU_WDTCPU_CON1_UR_OFF (6u)

/** \brief  Length for Ifx_SCU_WDTCPU_SR_Bits.AE */
#define IFX_SCU_WDTCPU_SR_AE_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_SR_Bits.AE */
#define IFX_SCU_WDTCPU_SR_AE_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_SR_Bits.AE */
#define IFX_SCU_WDTCPU_SR_AE_OFF (0u)

/** \brief  Length for Ifx_SCU_WDTCPU_SR_Bits.DS */
#define IFX_SCU_WDTCPU_SR_DS_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_SR_Bits.DS */
#define IFX_SCU_WDTCPU_SR_DS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_SR_Bits.DS */
#define IFX_SCU_WDTCPU_SR_DS_OFF (3u)

/** \brief  Length for Ifx_SCU_WDTCPU_SR_Bits.IS0 */
#define IFX_SCU_WDTCPU_SR_IS0_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_SR_Bits.IS0 */
#define IFX_SCU_WDTCPU_SR_IS0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_SR_Bits.IS0 */
#define IFX_SCU_WDTCPU_SR_IS0_OFF (2u)

/** \brief  Length for Ifx_SCU_WDTCPU_SR_Bits.IS1 */
#define IFX_SCU_WDTCPU_SR_IS1_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_SR_Bits.IS1 */
#define IFX_SCU_WDTCPU_SR_IS1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_SR_Bits.IS1 */
#define IFX_SCU_WDTCPU_SR_IS1_OFF (5u)

/** \brief  Length for Ifx_SCU_WDTCPU_SR_Bits.OE */
#define IFX_SCU_WDTCPU_SR_OE_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_SR_Bits.OE */
#define IFX_SCU_WDTCPU_SR_OE_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_SR_Bits.OE */
#define IFX_SCU_WDTCPU_SR_OE_OFF (1u)

/** \brief  Length for Ifx_SCU_WDTCPU_SR_Bits.PAS */
#define IFX_SCU_WDTCPU_SR_PAS_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_SR_Bits.PAS */
#define IFX_SCU_WDTCPU_SR_PAS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_SR_Bits.PAS */
#define IFX_SCU_WDTCPU_SR_PAS_OFF (7u)

/** \brief  Length for Ifx_SCU_WDTCPU_SR_Bits.TCS */
#define IFX_SCU_WDTCPU_SR_TCS_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_SR_Bits.TCS */
#define IFX_SCU_WDTCPU_SR_TCS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_SR_Bits.TCS */
#define IFX_SCU_WDTCPU_SR_TCS_OFF (8u)

/** \brief  Length for Ifx_SCU_WDTCPU_SR_Bits.TCT */
#define IFX_SCU_WDTCPU_SR_TCT_LEN (7u)

/** \brief  Mask for Ifx_SCU_WDTCPU_SR_Bits.TCT */
#define IFX_SCU_WDTCPU_SR_TCT_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_WDTCPU_SR_Bits.TCT */
#define IFX_SCU_WDTCPU_SR_TCT_OFF (9u)

/** \brief  Length for Ifx_SCU_WDTCPU_SR_Bits.TIM */
#define IFX_SCU_WDTCPU_SR_TIM_LEN (16u)

/** \brief  Mask for Ifx_SCU_WDTCPU_SR_Bits.TIM */
#define IFX_SCU_WDTCPU_SR_TIM_MSK (0xffffu)

/** \brief  Offset for Ifx_SCU_WDTCPU_SR_Bits.TIM */
#define IFX_SCU_WDTCPU_SR_TIM_OFF (16u)

/** \brief  Length for Ifx_SCU_WDTCPU_SR_Bits.TO */
#define IFX_SCU_WDTCPU_SR_TO_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_SR_Bits.TO */
#define IFX_SCU_WDTCPU_SR_TO_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_SR_Bits.TO */
#define IFX_SCU_WDTCPU_SR_TO_OFF (4u)

/** \brief  Length for Ifx_SCU_WDTCPU_SR_Bits.US */
#define IFX_SCU_WDTCPU_SR_US_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTCPU_SR_Bits.US */
#define IFX_SCU_WDTCPU_SR_US_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTCPU_SR_Bits.US */
#define IFX_SCU_WDTCPU_SR_US_OFF (6u)

/** \brief  Length for Ifx_SCU_WDTS_CON0_Bits.ENDINIT */
#define IFX_SCU_WDTS_CON0_ENDINIT_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_CON0_Bits.ENDINIT */
#define IFX_SCU_WDTS_CON0_ENDINIT_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_CON0_Bits.ENDINIT */
#define IFX_SCU_WDTS_CON0_ENDINIT_OFF (0u)

/** \brief  Length for Ifx_SCU_WDTS_CON0_Bits.LCK */
#define IFX_SCU_WDTS_CON0_LCK_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_CON0_Bits.LCK */
#define IFX_SCU_WDTS_CON0_LCK_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_CON0_Bits.LCK */
#define IFX_SCU_WDTS_CON0_LCK_OFF (1u)

/** \brief  Length for Ifx_SCU_WDTS_CON0_Bits.PW */
#define IFX_SCU_WDTS_CON0_PW_LEN (14u)

/** \brief  Mask for Ifx_SCU_WDTS_CON0_Bits.PW */
#define IFX_SCU_WDTS_CON0_PW_MSK (0x3fffu)

/** \brief  Offset for Ifx_SCU_WDTS_CON0_Bits.PW */
#define IFX_SCU_WDTS_CON0_PW_OFF (2u)

/** \brief  Length for Ifx_SCU_WDTS_CON0_Bits.REL */
#define IFX_SCU_WDTS_CON0_REL_LEN (16u)

/** \brief  Mask for Ifx_SCU_WDTS_CON0_Bits.REL */
#define IFX_SCU_WDTS_CON0_REL_MSK (0xffffu)

/** \brief  Offset for Ifx_SCU_WDTS_CON0_Bits.REL */
#define IFX_SCU_WDTS_CON0_REL_OFF (16u)

/** \brief  Length for Ifx_SCU_WDTS_CON1_Bits.CLRIRF */
#define IFX_SCU_WDTS_CON1_CLRIRF_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_CON1_Bits.CLRIRF */
#define IFX_SCU_WDTS_CON1_CLRIRF_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_CON1_Bits.CLRIRF */
#define IFX_SCU_WDTS_CON1_CLRIRF_OFF (0u)

/** \brief  Length for Ifx_SCU_WDTS_CON1_Bits.DR */
#define IFX_SCU_WDTS_CON1_DR_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_CON1_Bits.DR */
#define IFX_SCU_WDTS_CON1_DR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_CON1_Bits.DR */
#define IFX_SCU_WDTS_CON1_DR_OFF (3u)

/** \brief  Length for Ifx_SCU_WDTS_CON1_Bits.IR0 */
#define IFX_SCU_WDTS_CON1_IR0_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_CON1_Bits.IR0 */
#define IFX_SCU_WDTS_CON1_IR0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_CON1_Bits.IR0 */
#define IFX_SCU_WDTS_CON1_IR0_OFF (2u)

/** \brief  Length for Ifx_SCU_WDTS_CON1_Bits.IR1 */
#define IFX_SCU_WDTS_CON1_IR1_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_CON1_Bits.IR1 */
#define IFX_SCU_WDTS_CON1_IR1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_CON1_Bits.IR1 */
#define IFX_SCU_WDTS_CON1_IR1_OFF (5u)

/** \brief  Length for Ifx_SCU_WDTS_CON1_Bits.PAR */
#define IFX_SCU_WDTS_CON1_PAR_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_CON1_Bits.PAR */
#define IFX_SCU_WDTS_CON1_PAR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_CON1_Bits.PAR */
#define IFX_SCU_WDTS_CON1_PAR_OFF (7u)

/** \brief  Length for Ifx_SCU_WDTS_CON1_Bits.TCR */
#define IFX_SCU_WDTS_CON1_TCR_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_CON1_Bits.TCR */
#define IFX_SCU_WDTS_CON1_TCR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_CON1_Bits.TCR */
#define IFX_SCU_WDTS_CON1_TCR_OFF (8u)

/** \brief  Length for Ifx_SCU_WDTS_CON1_Bits.TCTR */
#define IFX_SCU_WDTS_CON1_TCTR_LEN (7u)

/** \brief  Mask for Ifx_SCU_WDTS_CON1_Bits.TCTR */
#define IFX_SCU_WDTS_CON1_TCTR_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_WDTS_CON1_Bits.TCTR */
#define IFX_SCU_WDTS_CON1_TCTR_OFF (9u)

/** \brief  Length for Ifx_SCU_WDTS_CON1_Bits.UR */
#define IFX_SCU_WDTS_CON1_UR_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_CON1_Bits.UR */
#define IFX_SCU_WDTS_CON1_UR_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_CON1_Bits.UR */
#define IFX_SCU_WDTS_CON1_UR_OFF (6u)

/** \brief  Length for Ifx_SCU_WDTS_SR_Bits.AE */
#define IFX_SCU_WDTS_SR_AE_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_SR_Bits.AE */
#define IFX_SCU_WDTS_SR_AE_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_SR_Bits.AE */
#define IFX_SCU_WDTS_SR_AE_OFF (0u)

/** \brief  Length for Ifx_SCU_WDTS_SR_Bits.DS */
#define IFX_SCU_WDTS_SR_DS_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_SR_Bits.DS */
#define IFX_SCU_WDTS_SR_DS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_SR_Bits.DS */
#define IFX_SCU_WDTS_SR_DS_OFF (3u)

/** \brief  Length for Ifx_SCU_WDTS_SR_Bits.IS0 */
#define IFX_SCU_WDTS_SR_IS0_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_SR_Bits.IS0 */
#define IFX_SCU_WDTS_SR_IS0_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_SR_Bits.IS0 */
#define IFX_SCU_WDTS_SR_IS0_OFF (2u)

/** \brief  Length for Ifx_SCU_WDTS_SR_Bits.IS1 */
#define IFX_SCU_WDTS_SR_IS1_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_SR_Bits.IS1 */
#define IFX_SCU_WDTS_SR_IS1_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_SR_Bits.IS1 */
#define IFX_SCU_WDTS_SR_IS1_OFF (5u)

/** \brief  Length for Ifx_SCU_WDTS_SR_Bits.OE */
#define IFX_SCU_WDTS_SR_OE_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_SR_Bits.OE */
#define IFX_SCU_WDTS_SR_OE_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_SR_Bits.OE */
#define IFX_SCU_WDTS_SR_OE_OFF (1u)

/** \brief  Length for Ifx_SCU_WDTS_SR_Bits.PAS */
#define IFX_SCU_WDTS_SR_PAS_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_SR_Bits.PAS */
#define IFX_SCU_WDTS_SR_PAS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_SR_Bits.PAS */
#define IFX_SCU_WDTS_SR_PAS_OFF (7u)

/** \brief  Length for Ifx_SCU_WDTS_SR_Bits.TCS */
#define IFX_SCU_WDTS_SR_TCS_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_SR_Bits.TCS */
#define IFX_SCU_WDTS_SR_TCS_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_SR_Bits.TCS */
#define IFX_SCU_WDTS_SR_TCS_OFF (8u)

/** \brief  Length for Ifx_SCU_WDTS_SR_Bits.TCT */
#define IFX_SCU_WDTS_SR_TCT_LEN (7u)

/** \brief  Mask for Ifx_SCU_WDTS_SR_Bits.TCT */
#define IFX_SCU_WDTS_SR_TCT_MSK (0x7fu)

/** \brief  Offset for Ifx_SCU_WDTS_SR_Bits.TCT */
#define IFX_SCU_WDTS_SR_TCT_OFF (9u)

/** \brief  Length for Ifx_SCU_WDTS_SR_Bits.TIM */
#define IFX_SCU_WDTS_SR_TIM_LEN (16u)

/** \brief  Mask for Ifx_SCU_WDTS_SR_Bits.TIM */
#define IFX_SCU_WDTS_SR_TIM_MSK (0xffffu)

/** \brief  Offset for Ifx_SCU_WDTS_SR_Bits.TIM */
#define IFX_SCU_WDTS_SR_TIM_OFF (16u)

/** \brief  Length for Ifx_SCU_WDTS_SR_Bits.TO */
#define IFX_SCU_WDTS_SR_TO_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_SR_Bits.TO */
#define IFX_SCU_WDTS_SR_TO_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_SR_Bits.TO */
#define IFX_SCU_WDTS_SR_TO_OFF (4u)

/** \brief  Length for Ifx_SCU_WDTS_SR_Bits.US */
#define IFX_SCU_WDTS_SR_US_LEN (1u)

/** \brief  Mask for Ifx_SCU_WDTS_SR_Bits.US */
#define IFX_SCU_WDTS_SR_US_MSK (0x1u)

/** \brief  Offset for Ifx_SCU_WDTS_SR_Bits.US */
#define IFX_SCU_WDTS_SR_US_OFF (6u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXSCU_BF_H */
