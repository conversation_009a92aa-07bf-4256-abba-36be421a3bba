/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 * @file 	CanIf.h
 * @brief
 *
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *
 * <AUTHOR>
 * @date 	2013-5-22
 * 
 */
/*============================================================================*/

#ifndef CANIF_H_
#define CANIF_H_

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20130522    WBN       Initial version
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/
#include "Can_GeneralTypes.h"
#include "Std_Types.h"
#include "ComStack_Types.h"
#include "Can_17_MCanP.h"
#ifdef __cplusplus
extern "C" {
#endif  /* __cplusplus */
#define Can_Write Can_17_MCanP_Write


/*=======[E X T E R N A L   D A T A]==========================================*/

typedef enum
{
	UDS_CANIF_CS_UNINIT= 0,
	UDS_CANIF_CS_STOPPED,
	UDS_CANIF_CS_STARTED,
	UDS_CANIF_CS_SLEEP
}UDS_CanIf_ControllerModeType;

typedef struct
{
    const PduIdType PduId;
    const PduIdType TpPduId;
    const Can_IdType CanId;
    const uint8 ObjectId;
    const void (*TxConf)(PduIdType TpPduId);
}CanIf_TxChannelType;

typedef struct
{
    const PduIdType PduId;
    const PduIdType TpPduId;
    const Can_IdType CanId;
    const uint8 ObjectId;
    const void (*RxInd)(PduIdType TpPduId, const PduInfoType * const CanTp_objRxPtr);
}CanIf_RxChannelType;

extern void Uds_CanIf_Init(void);

extern Std_ReturnType Uds_CanIf_Transmit(PduIdType Id, const PduInfoType* PduInfoPtr);

extern void Uds_CanIf_TxConfirmation(PduIdType CanTxPduId);

extern void Uds_CanIf_RxIndication(uint8 Hrh,Can_IdType CanId,uint8 CanDlc,uint8 *CanSduPtr);

extern void Uds_CanIf_ControllerBusOff(uint8 Controller);

extern void Uds_CanIf_MainFunction(void);

extern void CanIf_Init(void);

extern void g_v_CanIfControllerBusOff (uint8 ControllerId);


#ifdef __cplusplus
}
#endif  /* __cplusplus */

#endif /* CANIF_H_ */

/*=======[E N D   O F   F I L E]==============================================*/
