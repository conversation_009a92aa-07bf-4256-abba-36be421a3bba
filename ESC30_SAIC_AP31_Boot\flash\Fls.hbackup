/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  
 *  @file       <Flash.h>
 *  @brief      <Macros,Types defines and function decalrations for Flash Driver 
 *              Module>
 *  
 *  <AUTHOR> Chen>
 *  @date       <2012-09-09>
 */
/*============================================================================*/
#ifndef FLASH_H
#define FLASH_H

/*=======[I N C L U D E S]====================================================*/

#include "Std_Types.h"
#include "Fls_Cfg.h"

/*=======[M A C R O S]========================================================*/
/** result values of flash driver routines */
#define kFlashOk	0x00u
#define kFlashFailed	0x01u
#define FLASH_DRIVER_SIZE 0xccc


/*=======[T Y P E   D E F I N I T I O N S]====================================*/
/** flash driver major version number */
typedef uint8 tMajorVersion;

/** flash driver minor version number */
typedef uint8 tMinorVersion;

/** bugfix / patchlevel number */
typedef uint8 tBugfixVersion;

/** result of flash driver routines */
typedef uint8 tFlashResult; 

/** logical address */
typedef uint32 tFlashAddress; 

/** length (in bytes) */
typedef uint32 tFlashLength;

/** ambiguous data */
typedef uint8 tFlashData; 

/** watchdog trigger routine */
typedef void (*tWDTriggerFct)(void); 

/** initialization: input parameters */
typedef struct 
{		
    /* flash driver patch level version */
    tBugfixVersion patchLevel;
    
    /* flash driver minor version number */
    tMajorVersion minorNumber;
    
    /* flash driver major version number */
    tMinorVersion majorNumber;
    
    /* reserved for future use, set to 0x00 for now */
    uint8 reserved1; 
    
    /* retrun value / error code: output parameters */
    tFlashResult errorCode;
    
    /* reserved for future use, set to 0x0000 for now */
    uint16 reserved2; 
    
    /* erase / write: input parameters */
    /* logical target address */
    tFlashAddress address;
    
    /* lenght information (in bytes) */
    tFlashLength length; 
    
    /* pointer to data buffer */
    const tFlashData *data;
    
    /* pointer to watchdog trigger routine */
    tWDTriggerFct wdTriggerFct; 
    
} tFlashParam;

/** prototype of flash driver routine */
typedef void (*tFlashFct)(tFlashParam* flashParam); 

/** flash infomation table */
typedef struct 
{	
    /* Motorola Star12 */
    const uint8 mcuType;

    /* some mask number */
    const uint8 maskType;

    /* byte reserved for future use */
    const uint8 reserve;

    /* interface version number */
    const uint8 interface;

    /* flash initialize function */
    const tFlashFct flashInitFct;

    /* flash de-initialize function */
    const tFlashFct flashDeInitFct;

    /* flash erase function */
    const tFlashFct flashEraseFct;

    /* flash program function */
    const tFlashFct flashWriteFct;
    
} tFlash_InfoType;


/*=======[E X T E R N A L   D A T A]==========================================*/
//#pragma CONST_SEG FLASH_HEADER
extern const tFlash_InfoType* BLFlash_InfoPtr;

//#pragma CONST_SEG DEFAULT

/*=======[E X T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
//#pragma CODE_SEG FLASH_DRV
extern void tFlash_Init(tFlashParam* flashParam);
extern void tFlash_Deinit (tFlashParam* flashParam);
extern void tFlash_Erase(tFlashParam* flashParam);
extern void tFlash_Write (tFlashParam* flashParam);
extern void FlashReadMemory(uint8* DataBuf,uint32 Addr, uint32 Length);
extern void Fls_Memset(uint8 * dest,const uint8 source,uint32 length);
extern void Fls_Memcpy(uint8 * dest,const uint8 *source,uint32 length);
extern uint8 FlashReadByte(uint32 globalAddr);

//#pragma CODE_SEG DEFAULT

#endif/* endof FLASH_H */

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20120909    Gary       Initial version
 */
/*=======[E N D   O F   F I L E]==============================================*/

