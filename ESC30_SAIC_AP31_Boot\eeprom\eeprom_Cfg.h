/*
 * eeprom_Cfg.h
 *
 *  Created on: 2020-11-5
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#ifndef EEPROM_CFG_H_
#define EEPROM_CFG_H_
#include "Platform_Types.h"
#include "Did_Cfg.h"
#include "FL.h"
#include "eeprom.h"
#include "NvM_Cfg.h"



#define EEP_DIDF1AA_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_ECUProgrammingProcessFileNumber_F1AA
#define EEP_DIDF1A9_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_ECUConfigurationFileNumber_F1A9
#define EEP_DIDF198_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_ConfigurationTraceabilityField_F198
#define EEP_DIDF120_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_NIF_FactoryData_F120
#define EEP_DIDF121_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_NIF_CurrentData_F121
#define EEP_DIDF187_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_ECUPartNumber_F187
#define EEP_DIDF191_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_ECUHardwareNumber_F191
#define EEP_DIDF18B_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_ECUManufactureDate_F18B
#define EEP_DIDF1A8_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_VehicleFeatureInformation_F1A8
#define EEP_DIDF192_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_SupplierECUHardwareReferenceNumbe_F192
#define EEP_DIDF18C_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_ECUSerialNumber_F18C
#define EEP_DIDF110_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F110
#define EEP_DIDF111_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F111
#define EEP_DIDF112_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F112
#define EEP_DIDF113_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F113
#define EEP_DIDF114_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F114
#define EEP_DIDF115_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F115
#define EEP_DIDF116_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F116
#define EEP_DIDF117_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F117
#define EEP_DIDF118_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F118
#define EEP_DIDF119_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F119
#define EEP_DIDF11A_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F11A
#define EEP_DIDF11B_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F11B
#define EEP_DIDF11C_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F11C
#define EEP_DIDF11D_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F11D
#define EEP_DIDF11E_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F11E
#define EEP_DIDF11F_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_PIF_F11F
#define EEP_DIDF190_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_VIN_F190
#define EEP_SecErrFlag_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_ReseveforFBL
#define EEP_SecurityLog_BLOCK_ID NvMConf_NvMBlockDescriptor_CtApNvM_NvSWC_SecurityLog

extern uint8 NvM_DID_PIF_CNT[4];/*PIF counter*/
extern uint8 NvM_DID_F110[16];/*PIFPointerToCurrentBlock*/
extern uint8 NvM_DID_F111[16];/*PIFPointerToCurrentBlock1*/
extern uint8 NvM_DID_F112[16];/*PIFPointerToCurrentBlock2*/
extern uint8 NvM_DID_F113[16];/*PIFPointerToCurrentBlock3*/
extern uint8 NvM_DID_F114[16];/*PIFPointerToCurrentBlock4*/
extern uint8 NvM_DID_F115[16];/*PIFPointerToCurrentBlock5*/
extern uint8 NvM_DID_F116[16];/*PIFPointerToCurrentBlock6*/
extern uint8 NvM_DID_F117[16];/*PIFPointerToCurrentBlock7*/
extern uint8 NvM_DID_F118[16];/*PIFPointerToCurrentBlock8*/
extern uint8 NvM_DID_F119[16];/*PIFPointerToCurrentBlock9*/
extern uint8 NvM_DID_F11A[16];/*PIFPointerToCurrentBlock10*/
extern uint8 NvM_DID_F11B[16];/*PIFPointerToCurrentBlock11*/
extern uint8 NvM_DID_F11C[16];/*PIFPointerToCurrentBlock12*/
extern uint8 NvM_DID_F11D[16];/*PIFPointerToCurrentBlock13*/
extern uint8 NvM_DID_F11E[16];/*PIFPointerToCurrentBlock14*/
extern uint8 NvM_DID_F11F[16];/*PIFPointerToCurrentBlock15*/

extern uint8 NvM_DID_F187[5];/*ECUPartNumber*/
extern uint8 NvM_DID_F191[5];/*ECUHardwareNumber*/
extern uint8 NvM_DID_F18B[3];/*ECUManufactureDate*/
extern uint8 NvM_DID_F1A8[20];/*VehicleFeatureInformation*/
extern uint8 NvM_DID_F192[10];/*SupplierECUHardwareReferenceNumber*/
extern uint8 NvM_DID_F18C[16];/*ECUSerialNumber*/
extern uint8 NvM_DID_F190[17];/*VIN*/

extern uint8 NvM_DID_F120[16];
extern uint8 NvM_DID_F121[16];
extern uint8 NvM_DID_F198[11];
extern uint8 NvM_DID_F1A5[3];
extern uint8 NvM_DID_F1A9[5];
extern uint8 NvM_DID_F1AA[5];
extern uint8 NvM_DID_F18A[5];

extern uint8 VSS_SecurityKey_PIM[576];

extern uint8 Ram_File_Digest[64];
extern uint8 Ram_Date_Information[3];

extern uint8 NvM_SECERR_DATA[64];
extern uint8 NvM_SECLOG_DATA[40];

extern volatile const ST_NVM_DID_TYPE Nvm_Did_Cfg[];
extern volatile const uint8 Nvm_Did_Cfg_Size;
extern uint16 EEP_GetPIF_F110(uint8 f110index,uint8* data);
extern uint16 EEP_SetPIF_F110(uint8 index,uint8* data);
extern uint16 EEP_GetSWVerification_F100(uint8 f100index,uint8* data);
extern uint16 EEP_GetECUProgramState_F1A3(uint8 f1a3index,uint8* data);
extern uint16 EEP_GetSoftwareValidFlag_AFFF(uint8 afffindex,uint8* data);
extern uint16 EEP_GetSoftwareIntegrityStatus_AFFD(uint8 affdindex,uint8* data);
extern uint16 EEP_GetSoftwareCompatibilityStatus_AFFE(uint8 affeindex,uint8* data);
extern uint16 EEP_GetProgrammingCounter_AFFC(uint8 affcindex,uint8* data);
#endif /* EEPROM_CFG_H_ */
