/*
 * eeprom.c
 *
 *  Created on: 2019-9-16
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#include "Platform_Types.h"
#include "Did_Cfg.h"
#include "FL.h"
#include "eeprom_Cfg.h"
#include "Fls.h"
#include "Fls_17_Pmu.h"
#include "Fee.h"
#include "NvM.h"
#define EEPROM_OK   FL_OK
#define EEPROM_NOK  FL_FAILED
#define EEPROM_OUTOFRANGE FL_ERR_SEQUENCE
#define EEPROM_WRITE_MORE_TIME FL_WRITEMOREONETIME

#define LOG_BUFFER_SIZE 40

#define ENTRY_SIZE 8

#define MAX_ENTRIES 5

static uint8 log_buffer[LOG_BUFFER_SIZE];

uint32 NvM_JobFinished_Flag=NVM_JOB_STATUS_IDLE;
uint16 test_int=0;
uint8 SecurityErrorFlag = 0;
uint8 blsDIDF187WriteOnlyOnceFlag = 0;
uint8 ReadCurrent_session=2;
extern uint8 appblkIntDefault;
extern uint8 appblkCpbDefault;
extern uint8 FailReason;
extern uint8 SecurityLog_trigger;
static void EEP_DID_Init(void)
{
	uint16 i,cnt;
	 for(i=0;i<Nvm_Did_Cfg_Size;i++)
     {
		 /*if did exsist in flash*/
	     if(Nvm_Did_Cfg[i].u8t_datatype==FLASH_TYPE)
	     {
	    	 FlashReadMemory(Nvm_Did_Cfg[i].u8t_buffer,Nvm_Did_Cfg[i].u32t_addr, Nvm_Did_Cfg[i].len);

	     }
	     else if(Nvm_Did_Cfg[i].u8t_datatype==EEPROM_TYPE)
	     {
	    	 cnt=0;
	    	 NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_BUZY;
	    	 NvM_ReadBlock((uint16)Nvm_Did_Cfg[i].u32t_addr,NULL);
	    	 while((cnt<=20)&&(NVM_JOB_STATUS_READ_BUZY==NvM_JobFinished_Flag))
	    	 {
	    		 cnt++;
	    		 test_int=i;
	    		 EEP_MainFunction();
	    	 }

	     }
     }
	 NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_BUZY;
	 NvM_ReadBlock(EEP_SecErrFlag_BLOCK_ID,NULL);
	 while((cnt<=20)&&(NVM_JOB_STATUS_READ_BUZY==NvM_JobFinished_Flag))
	 {
		 cnt++;
		 EEP_MainFunction();
	 }
	 SecurityErrorFlag = NvM_SECERR_DATA[0];
}

void EEP_Init(void)
{
	uint8 RequestResultPtr = 0;
	Fls_17_Pmu_Init(&Fls_17_Pmu_ConfigRoot[0]);
	Fee_Init();
	NvM_Init();
	EEP_DID_Init();

}

void EEP_MainFunction(void)
{    
	if(0!=(NvM_JobFinished_Flag&NVM_JOB_STATUS_BUSY_MASK))
	{
		Fls_17_Pmu_MainFunction();
		Fee_MainFunction();
		NvM_MainFunction();
	}

}

uint16 EEP_ReadDID(uint8 *readData,uint16 did)
{
	uint16 length,i,j;
	for(i=0;i<Nvm_Did_Cfg_Size;i++)
	{
	   if(Nvm_Did_Cfg[i].u16t_did==did)
	   {
		   break;
	   }
	}

	if(i>=Nvm_Did_Cfg_Size)
	{
	   return 0;
	}
	else
	{
	   length=Nvm_Did_Cfg[i].len;
	   if(NULL!=Nvm_Did_Cfg[i].readeeprom)
	   {
		   Nvm_Did_Cfg[i].readeeprom((uint8)i,readData);
	   }
	   else
	   {
		   for(j=0;j<length;j++)
		   {
			   readData[j]=Nvm_Did_Cfg[i].u8t_buffer[j];
		   }
	   }
	   return length;
	}
}
//extern Dcm_RunTimeType dcmRunTime;
uint16 EEP_ReadCurrent_session(uint8 *readData)
{
	uint16 length=1;
	*readData = ReadCurrent_session;
	if(*readData==0x04)
	{
		*readData=3;
	}
	return length;
}
uint16 EEP_WriteDID(uint8 *writeData,uint16 datalength)
{
	uint16 length,i,j,did;
	// static boolean blsDIDF187WriteOnlyOnceFlag = FALSE;

	did=(((uint16)writeData[0])<<8)+(uint16)writeData[1];
	for(i=0;i<Nvm_Did_Cfg_Size;i++)
	{
		if(Nvm_Did_Cfg[i].u16t_did==did)
		{
		   break;
		}
	}

	if(i>=Nvm_Did_Cfg_Size)
	{
		return EEPROM_OUTOFRANGE;
	}
	else
	{
		if(FLASH_TYPE==Nvm_Did_Cfg[i].u8t_datatype)
		{
			return EEPROM_NOK;
		}
		else
		{
			length=Nvm_Did_Cfg[i].len;
			if(datalength>length)
			{
				return EEPROM_NOK;
			}
			else
			{
				if(NULL!=Nvm_Did_Cfg[i].writeeeprom)
				{
					Nvm_Did_Cfg[i].writeeeprom((uint8)i,writeData+2);
				}
				else
				{
					if(0xF187==Nvm_Did_Cfg[i].u16t_did)
					{
						if( 03 == blsDIDF187WriteOnlyOnceFlag )
						{
							NvM_WriteBlock((uint16)Nvm_Did_Cfg[i].u32t_addr,NULL);
							NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_F187;
							blsDIDF187WriteOnlyOnceFlag = 0;
						}
						else
						{
							return EEPROM_WRITE_MORE_TIME;
						}
					}
					else if(0xF1AA==Nvm_Did_Cfg[i].u16t_did)
					{
						NvM_WriteBlock((uint16)Nvm_Did_Cfg[i].u32t_addr,NULL);
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_F1AA;
					}
					else if(0xF121==Nvm_Did_Cfg[i].u16t_did)
					{
						NvM_WriteBlock((uint16)Nvm_Did_Cfg[i].u32t_addr,NULL);
						NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_F121;
					}
					else
					{}
					for(j=0;j<length;j++)
					{
						Nvm_Did_Cfg[i].u8t_buffer[j]=writeData[j+2];
					}
				}
				return EEPROM_OK;
			}
		}
	}
}

uint8 EEP_GetLastSecurityAttemptResult(void)
{

	return EEPROM_OK;
}
/**/
uint16 EEP_GetPIF_F110(uint8 f110index,uint8* data)
{

	uint32 writecounter=(((uint32)Nvm_Did_Cfg[f110index].u8t_buffer[0])<<24)+(((uint32)Nvm_Did_Cfg[f110index].u8t_buffer[1])<<16)+(((uint32)Nvm_Did_Cfg[f110index].u8t_buffer[2])<<8)+(uint32)Nvm_Did_Cfg[f110index].u8t_buffer[3];
	uint16 did;
	uint8 defaultvalue=0x00;
	uint16 i,j,length;
	/*first turn from F111 to F11F,then always from F112 to F11F*/
	if(writecounter==0)
	{
		for(i=0;i<16;i++)
		{
			data[i]=defaultvalue;
		}
	}
	else
	{
		if(writecounter<16)
		{
			did=0xF110+(uint16)writecounter;
		}
		else
		{
			did=0xF112+((writecounter-16)%14);
		}

		/*find the did to read from*/
		for(i=0;i<Nvm_Did_Cfg_Size;i++)
		{
		   if(Nvm_Did_Cfg[i].u16t_did==did)
		   {
			   break;
		   }
		}

		/*did not found*/
	   if(i>=Nvm_Did_Cfg_Size) return EEPROM_NOK;

	   /*did found,read data*/
	   length=Nvm_Did_Cfg[i].len;
	   for(j=0;j<length;j++)
	   {
		   data[j] = Nvm_Did_Cfg[i].u8t_buffer[j];
	   }
	}
	return EEPROM_OK;
}

uint16 EEP_SetPIF_F110(uint8 f110index,uint8* data)
{
	uint8 pifcntindex=f110index;/*EEP_PIFCNT_BLOCK_ID¶ÔÓ¦µÄindex*/
	uint32 writecounter=(((uint32)Nvm_Did_Cfg[pifcntindex].u8t_buffer[0])<<24)+(((uint32)Nvm_Did_Cfg[pifcntindex].u8t_buffer[1])<<16)+(((uint32)Nvm_Did_Cfg[pifcntindex].u8t_buffer[2])<<8)+(uint32)Nvm_Did_Cfg[pifcntindex].u8t_buffer[3];
	uint16 did;
	uint8 defaultvalue=0xFF;
	uint16 i,j,length;
	/*first turn from F111 to F11F,then always from F112 to F11F*/

	/*increase the counter saved in F110,then store back to f110*/
	writecounter++;
	Nvm_Did_Cfg[pifcntindex].u8t_buffer[0]=(writecounter&0xFF000000)>>24;
	Nvm_Did_Cfg[pifcntindex].u8t_buffer[1]=(writecounter&0x00FF0000)>>16;
	Nvm_Did_Cfg[pifcntindex].u8t_buffer[2]=(writecounter&0x0000FF00)>>8;
	Nvm_Did_Cfg[pifcntindex].u8t_buffer[3]=writecounter&0x000000FF;
	/*store nvm if exsist*/
	NvM_WriteBlock((uint16)Nvm_Did_Cfg[pifcntindex].u32t_addr,NULL);
	NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_F110;
		if(writecounter<16)
		{
			did=0xF110+(uint16)writecounter;
		}
		else
		{
			did=0xF112+((writecounter-16)%14);
		}

		/*find the did to write to*/
		for(i=0;i<Nvm_Did_Cfg_Size;i++)
		{
		   if(Nvm_Did_Cfg[i].u16t_did==did)
		   {
			   break;
		   }
		}

		/*did not found*/
	   if(i>=Nvm_Did_Cfg_Size) return EEPROM_NOK;

	   /*did found,read data*/
	   length=Nvm_Did_Cfg[i].len;
	   for(j=0;j<length;j++)
	   {
		   Nvm_Did_Cfg[i].u8t_buffer[j] = data[j];
	   }
	   /*store nvm if exsist*/
	   NvM_WriteBlock((uint16)Nvm_Did_Cfg[i].u32t_addr,NULL);

	   NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_PIF;
	return EEPROM_OK;
}

uint16 EEP_GetSWVerification_F100(uint8 f100index,uint8* data)
{
    uint8 i;
	FL_CheckSWVerification(data);

	for(i=0;i<Nvm_Did_Cfg[f100index].len;i++)
	{
		Nvm_Did_Cfg[f100index].u8t_buffer[i]=data[i];
	}
	return EEPROM_OK;
}
/*byte0 bit7 is app present                       reference to integrity bit0
 *byte0 bit6 is cal present                       reference to integrity bit2
 *byte1 bit7 is hardware/app compatible           not definite yet by 2020-12-5
 *byte1 bit6 is cal/app compatible                not definite yet by 2020-12-5
 *byte1 bit5 is eyeq/app compatible               not definite yet by 2020-12-5
 *byte2 bit2 is NVRAM Memory fail                 not definite yet by 2020-12-5
 * */
uint16 EEP_GetECUProgramState_F1A3(uint8 f1a3index,uint8* data)
{
    uint8 i;
    uint8 isPresent=0;
    uint8 isCompatible=0;
    uint8 isMemoryFail=0;

    isPresent=FL_CheckProgramIntegrity();
    data[0]=0;
    data[0]|=((isPresent&APP_INTEGRITY_ERR)<<7);
    data[0]|=((isPresent&CAL_INTEGRITY_ERR)<<5);
    data[1]=0;
    FL_CheckProgramDependencies(&isCompatible);
    data[1]|=((isPresent&CAL_APP_CPB_ERR)<<5);
    data[2]=0;
	for(i=0;i<Nvm_Did_Cfg[f1a3index].len;i++)
	{
		Nvm_Did_Cfg[f1a3index].u8t_buffer[i]=data[i];
	}
	return EEPROM_OK;
}

uint16 EEP_GetSoftwareValidFlag_AFFF(uint8 afffindex,uint8* data)
{
	uint8 isCompatible=0;
	FL_CheckProgramDependencies(&isCompatible);
	data[0]=(isCompatible>0? 0:1);
	return EEPROM_OK;
}

uint16 EEP_GetSoftwareIntegrityStatus_AFFD(uint8 affdindex,uint8* data)
{
	uint8 isPresent=0;
	
	isPresent=FL_CheckProgramIntegrity();
	// if( 0x00u != isPresent )
	if(appblkIntDefault)
	{
		isPresent = 0xFFu;
	}
	data[0]=isPresent;
	
	return EEPROM_OK;
}

uint16 EEP_GetSoftwareCompatibilityStatus_AFFE(uint8 affeindex,uint8* data)
{
	uint8 isCompatible=0;
	
	FL_CheckProgramDependencies(&isCompatible);
	// if( 0x00u != isCompatible )
	if(appblkCpbDefault)
	{
		isCompatible = 0xFFu;
	}
	data[0]=isCompatible;
	
	return EEPROM_OK;
}
uint16 EEP_GetProgrammingCounter_AFFC(uint8 affcindex,uint8* data)
{
	uint16 counter=0;
	counter=FL_CheckProgramCounter();
	data[0]=(uint8)(counter>>8);
	data[1]=(uint8)(counter&0xFF);
	return EEPROM_OK;
}

uint16 EEP_SetSecErrFlag(uint8* data)
{
	NvM_SECERR_DATA[0] = *data;
	NvM_WriteBlock(EEP_SecErrFlag_BLOCK_ID,NULL);
	NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_SECERR;
 	return EEPROM_OK;
}


void log_failure(uint8 year, uint8 month, uint8 day,uint8 hour, uint8 minute, uint8 second,uint8 reason)
{
	uint8 cnt = 0;
	uint8 new_entry[ENTRY_SIZE] = {year, month, day, hour, minute, second, reason, 0x00 };//0x00->Reserved

	NvM_JobFinished_Flag=NVM_JOB_STATUS_READ_BUZY;
	NvM_ReadBlock(EEP_SecurityLog_BLOCK_ID,NULL);
	while((cnt<=20)&&(NVM_JOB_STATUS_READ_BUZY==NvM_JobFinished_Flag))
	{
		cnt++;
		EEP_MainFunction();
	}

	memcpy(log_buffer, NvM_SECLOG_DATA, LOG_BUFFER_SIZE);

	memmove(log_buffer + ENTRY_SIZE, log_buffer, LOG_BUFFER_SIZE - ENTRY_SIZE);

	memcpy(log_buffer, new_entry, ENTRY_SIZE);

}

uint16 EEP_SaveSecLog(void)
{
	uint8 year =0;
	uint8 month =0;
	uint8 day = 0;
	uint8 hour = 0; 
	uint8 minute = 0;
	uint8 second=0;
	uint8 reason =0;

	if(0x1==SecurityLog_trigger)
	{   
		log_failure(year,month,day,hour,minute,second,FailReason); 
		memcpy(NvM_SECLOG_DATA, log_buffer, LOG_BUFFER_SIZE);

		NvM_WriteBlock(EEP_SecurityLog_BLOCK_ID,NULL);
		NvM_JobFinished_Flag|=NVM_JOB_STATUS_WRTIE_SECLOG;
		SecurityLog_trigger =0;
	
	}
 	return EEPROM_OK;
}
