/*
******************************************************************************
* Copyright (C), 2019, DIAS Automotive Electronic Systems Co; Ltd.
* File Name:    ICM_Bootloader_Version.h
* Author:       方红庆    Version: *******    Date: 2019-10-22
* Description:  Version  for bootLoader software
*
* Others:       other description
*
* Processor:    TC234
* Compiler:     TASKING v4.2r2
*
****************************** Revision History ******************************
* Date           Version       Name          Description
* 2019-09-23    *******        方红庆                             初始版本
* 2019-10-22    *******        方红庆                             将中断全部去掉，CAN采用轮询的方式
*                                              CAN2下载
*                                              复位采用跳转代替
*                                              跳转地址：0xA0050000
*                                              一个可刷写块：0xA0040000  长度：0x180000
*2020-05-25     *******        方红庆                     跳转前加入CAN和MCU的反初始化以及关中断的内容
*2020-10-20     *******        方红庆                     2个可刷写块：0x80028000~0x80037FFF 0x80038000~0x801FFFFF 
                                                                        1个预留块：0x01000000~0x05000003
*2020-10-27     *******        方红庆                      CAN2下载改为CAN1下载
*2020-10-30     *******        方红庆                     为使用SPI读外部FLASH功能，将软件移到ICM_Bringup平台上面来
*                                            校验数据的存储地址由之前的0xA0010000移到0xA0018000  版本号存储地址为0xA000FF00
*2020-11-24     *******        方红庆                      增加EYEQ的刷新，现在可刷新段为
*                                                      CAL：80028000-80037FFF(校验后的hex文件)
*                                                      APP：80038000-801FFFFF(校验后的hex文件)
*                                                      EYEQ:00000000-03FFFFFF(校验前的bin文件，此工具对bin文件校验，加载后的数据放在01000000-05000003)
*                                                      擦除地址为实际输入文件涵盖的地址
*                                                      流程为：10->22->27->   31(擦除)->36(可重复)->37  ->31(完整性校验)->31(兼容性校验)->2E->11(复位)
*                                                                             [31->36->37循环来刷新段]
*2020-12-09    0.0.6.0        方红庆                                                         增加兼容性校验
*2020-12-09                                              对DID读取，对在FLASH数据的值进行了修改和测试
*2020-12-09                                              删除了不必要的冗余代码
*2020-12-09                                              诊断SV51刷新规范中否定回复NRC进行了修改
*2020-12-14                                              修改01 27 回复NRC=0x12进行修改
*2020-12-25    0.0.7.0        方红庆                     将该版本软件释放给ADAS
*2021-01-24    0.0.8.0        方红庆                      增加了NVM等模块，将物流数据和NVM关联
*2021-01-25    0.0.8.1        方红庆                      将10 01先复位再回复改为先回复再复位
*2021-03-27    0.0.8.1        方红庆                     使用MIFA FBL的base,这样CRC32改为CRC16，刷新流程中去掉31 擦除的步骤，使用默认值方式填写所有的物流数据，可刷新段为[0xA0028000,0xA01FFFFF]
*2021-03-29    0.0.9.0        方红庆                     将硬件号F191和软件号F1A0对换
*2021-05-29    0.1.0.0        方红庆                     增加NVM
*2021-05-31    0.1.1.0        方红庆                     更改F1AA写不进去的问题，用于第一次产线下发
*2021-06-22    0.1.1.1        方红庆                     解决功能寻址22服务不回复，F187可多次写入的问题
*2021-07-22    0.1.1.2        xiaogang                  同步AS32-PGM1.12_Arch2.5_V2.5.10.21_CherryPick9_20210721版NvM、关所有中断避免跳转至App误进入未配置的中断入口函数导致系统复位
*2021-08-05    0.1.1.3        xiaogang            		修复$2705&06任意key都可解锁成功问题，修复DID-F187永久只能写一次问题(应为在刷新会话下支持写一次)
*******************************************************************************/

#ifndef ICM_BOOTLOADER_VERSION_H_
#define ICM_BOOTLOADER_VERSION_H_

/*---------------------------------------------------------------------------*/
/* BOOT软件版本号                                                        */
/*---------------------------------------------------------------------------*/
#define U8_BOOT_NAME_CHAR1   'P'      /* 项目名称 字母1         */
#define U8_BOOT_NAME_CHAR2   'G'      /* 项目名称 字母2         */
#define U8_BOOT_NAME_CHAR3   'M'      /* 项目名称 字母3         */

#define U8_YEAR_20        0x20      /* 软件释放年份的前两位             */
#define U8_YEAR_19        0x25      /* 软件释放年份的后两位             */
#define U8_MONTH          0x04      /* 软件释放的月份             */
#define U8_DAY            0x23      /* 软件释放的日期            */

#define U8_BOOT_MAIN_VERSION        0      /* 主版本号        (0~255)               */
#define U8_BOOT_MILESTONE_VERSION   0      /* milestone版本号 (0~255)         */
#define U8_BOOT_DEVELOP_PHA_VERSION 1      /* 开发阶段版本号  (0~255)             */
#define U8_BOOT_CURRENT_VERSION     2      /* 开发当中的版本号(0~255)            */

/*---------------------------------------------------------------------------*/
/* 项目相关信息                                                        */
/*---------------------------------------------------------------------------*/
#define U8_PRJ_NAME_CHAR1   'S'      /* 项目名称 字母1         */
#define U8_PRJ_NAME_CHAR2   'V'      /* 项目名称 字母2         */
#define U8_PRJ_NAME_CHAR3   '5'      /* 项目名称 字母3         */
#define U8_PRJ_NAME_CHAR4   '1'      /* 项目名称 字母3         */

/*---------------------------------------------------------------------------*/
/* BOOT与APP兼容信息*/
/*---------------------------------------------------------------------------*/
#define U8_FBL_APP_CPB_BYTE1 0x03
#define U8_FBL_APP_CPB_BYTE2 0x06
#define U8_FBL_APP_CPB_BYTE3 0xA1

/*---------------------------------------------------------------------------*/
/* CAL与APP兼容信息*/
/*---------------------------------------------------------------------------*/
#define U8_CAL_APP_CPB_BYTE1 0x03
#define U8_CAL_APP_CPB_BYTE2 0x06
#define U8_CAL_APP_CPB_BYTE3 0xC1

/*---------------------------------------------------------------------------*/
/* EYEQ与APP兼容信息*/
/*---------------------------------------------------------------------------*/
#define U8_EYEQ_APP_CPB_BYTE1 0x03
#define U8_EYEQ_APP_CPB_BYTE2 0x06
#define U8_EYEQ_APP_CPB_BYTE3 0xE1
/*---------------------------------------------------------------------------*/
/* 兼容信息存储地址*/
/*---------------------------------------------------------------------------*/

#define FBL_APP_CPB_FBL_ADDRESS 0xA001C100
#define FBL_APP_CPB_APP_ADDRESS 0xA0038070

//#define CAL_APP_CPB_CAL_ADDRESS 0xA0028020
//#define CAL_APP_CPB_APP_ADDRESS 0xA0038070
//
//#define EYEQ_APP_CPB_APP_ADDRESS 0xA0038080
#endif /* ICM_BOOTLOADER_VERSION_H_ */
