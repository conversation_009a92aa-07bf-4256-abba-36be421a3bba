#ifndef _VSS_TYPE_H_
#define _VSS_TYPE_H_
#ifdef __cplusplus
extern "C" {
#endif
#define VSS_NULL 			0

typedef unsigned char		vss_uint8;
typedef signed char			vss_sint8;
typedef char				vss_char8;
typedef unsigned short		vss_uint16;
typedef signed short		vss_sint16;
typedef unsigned long		vss_uint32;
typedef signed long			vss_sint32;
typedef long				vss_slong;
typedef unsigned char       vss_bool;
typedef unsigned long long	vss_uint64;
typedef unsigned long		vss_ulong;


typedef vss_uint8	 		BYTE; 
typedef vss_uint32			WORD; 

typedef vss_ulong			vss_size;
typedef vss_uint32			SM3_WORD_T;

typedef struct  {
	vss_uint32		m_size;	
	vss_uint8		remain[128];
	vss_uint32		r_len;		
	SM3_WORD_T		iv[8];	
} SM3_CTX_T;

typedef struct {
	BYTE		data[64];
	WORD		datalen;
	vss_uint64	bitlen;
	WORD		state[8];
} SHA256_CTX;

typedef struct
{
	vss_uint32	k[2];
} TZucKey;

typedef union
{
	SHA256_CTX	shactx;
	SM3_CTX_T	sm3ctx;
}THashCtx;

enum{
	FLASH_IO_READ = 0,
	FLASH_IO_WRITE,
};
typedef vss_uint32 flash_io_cb(vss_uint32 rwflag, vss_uint32 offset, vss_uint8 *buf, vss_uint32 size);

typedef vss_uint32 wdt_rst_cb(void);

typedef struct {
	vss_uint32 alg;
	vss_uint32 sock;
	vss_uint32 seq_no;
	vss_uint32 is_chip;
	vss_uint8 sk[32];
	vss_uint8 cert[256];
	vss_uint8 root[256];
	vss_uint8 session_key[16];
} FTLS_CTX;


typedef vss_uint32 someip_io_cb(vss_uint32 rwflag, vss_uint8 *buf, vss_uint32 size, vss_uint32 timeout);

#ifdef __cplusplus
}
#endif
#endif
