
#ifndef _ERRID_H_
#define _ERRID_H_

#define SUCCESS					0x00 
#define ERR_MSG_LEN				0x01
#define ERR_MSG_CRC				0x02
#define ERR_ACCESS_DENY		0x03
#define ERR_PARAMETER			0x04
#define ERR_KEY_NOTFOUND		0x05
#define ERR_KEY_INVALID			0x06
#define ERR_INVALID_INS			0x07
#define ERR_CALC_FAIL			0x08
#define ERR_FLASH_CHECK		0x09
#define ERR_SIG_INVALID			0x0A
#define ERR_DATA_INVALID		0x0B
#define ERR_EXT_AUTH			0x0C
#define ERR_ALG_INVALID			0x0D
#define ERR_CERT_NOTFOUND		0x0E
#define ERR_INDEX_INVALID		0x0F
#define ERR_READ_OTP			0x10
#define ERR_WRITE_OTP			0x11
#define ERR_LEN_INVALID			0x12
#define ERR_TYPE_INVALID		0x13
#define ERR_VERSION_INVALID	0x14
#define ERR_P1_INVALID			0x15
#define ERR_P2_INVALID			0x16
#define ERR_GEN_RND			0x17
#define ERR_NOT_SUPPORT		0x18
#define ERR_KEYLEN_INVALID		0x19
#define ERR_CERT_INVALID		0x1A
#define ERR_SYSTEM_INIT   		0x1B
#define ERR_READ_FLASH			0x1C
#define ERR_WRITE_FLASH		0x1D
#define ERR_CERT_INDEX			0x1E
#define ERR_READ_CFG			0x1F
#define ERR_FTLS_VERIFY			0x20
#define ERR_KEY_EXIST			0x21
#define ERR_DATA_VERIFY		0x22
#define ERR_GEN_KEY				0x23

#define ERR_NET_TIMEOUT		0xE3
#define ERR_NET_SEND			0xE2
#define ERR_NET_RECV			0xE4

#endif

