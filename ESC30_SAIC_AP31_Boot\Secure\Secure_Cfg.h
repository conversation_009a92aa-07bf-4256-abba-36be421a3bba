/*******************************************************************************
 * Copyright (C) 2023 Technomous. All rights reserved         *
 ******************************************************************************/
/**
 *  \file
 *      Secure_Cfg.h
 *  \brief
 *      Config the secure boot.
 *	\author
 *		Ou Hengyue
 */

#ifndef SECURE_CFG_H
#define SECURE_CFG_H
/*****************************************************************************
*   Include Files
*****************************************************************************/

/*****************************************************************************
*   Global Define Definitions
*****************************************************************************/
#define SECURE_APP_BLOCK_NUM  1
#define SECURE_CAL_BLOCK_NUM  1

#define SECURE_PUBLIC_KEY_LEN 64
#define SECURE_SIGNA_LEN  64
#define SECURE_HASH_LEN  32

#define SECURE_CERT_TOTAL_LEN 164
#define SECURE_CERT_SIGNA_LEN 64
#define SECURE_CERT_SUB_INFO_LEN (SECURE_CERT_TOTAL_LEN - SECURE_PUBLIC_KEY_LEN - SECURE_CERT_SIGNA_LEN)

#define SECURE_HEADER_LEN (526 + 8 * SECURE_APP_BLOCK_NUM) 
#define SECURE_CAL_HEADER_LEN (524 + 8 * SECURE_CAL_BLOCK_NUM) 

#define SECURE_APP_LOCINFO_BYTES (2 + 8 * SECURE_APP_BLOCK_NUM)
#define SECURE_HEADER_SUB_INFO_LEN (2 + 2 + SECURE_APP_LOCINFO_BYTES)

#define SECURE_CAL_LOCINFO_BYTES (2 + 8 * SECURE_CAL_BLOCK_NUM)
#define SECURE_CAL_HEADER_SUB_INFO_LEN (2 + SECURE_CAL_LOCINFO_BYTES)

#define SECURE_HEADER_TO_SIGNA_DATA_LEN (SECURE_HEADER_SUB_INFO_LEN + SECURE_CERT_TOTAL_LEN + SECURE_HASH_LEN)
#define SECURE_CAL_HEADER_TO_SIGNA_DATA_LEN (SECURE_CAL_HEADER_SUB_INFO_LEN + SECURE_CERT_TOTAL_LEN + SECURE_HASH_LEN)

#define SECURE_BYPASSLIC_LEN 458
// #define SECURE_MODULEID_UNMATCH 0x95u
// #define SECURE_CERT_VERIF_FAIL  0x96u
// #define SECURE_SIGN_VERIF_FAIL  0x97u
// #define SECURE_VERSION_TOOLOW   0x98u
// #define SECURE_OUT_OF_RANGE     0x99u

/*****************************************************************************
*   Global Type Definitions
*****************************************************************************/


/*****************************************************************************
*   Global Macros Definitions
*****************************************************************************/

/*****************************************************************************
*   Global Data Declarations
*****************************************************************************/

/*****************************************************************************
*   Global Function Declarations
*****************************************************************************/

#endif/* endif SECURE_CFG_H */