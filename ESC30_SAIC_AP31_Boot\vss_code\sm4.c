#include "sm4.h"
#include "vsscommon.h"


#pragma section code "vss_api_code" 

extern const vss_uint8 sm4_SboxTable[16][16];
/* System parameter */
extern const vss_ulong sm4_FK[4];
/* fixed parameter */
extern const vss_ulong sm4_CK[32];

/*
 * 32-bit integer manipulation macros (big endian)
 */
vss_ulong GET_ULONG_BE(vss_uint8* b, int i)
{	
	vss_ulong n;

	n = ((vss_ulong)*(b+i) << 24 ) | 
	((vss_ulong)*(b+i+1) << 16 ) | 
	((vss_ulong)*(b+i+2) << 8 )| 
	((vss_ulong)*(b+i+3));	
	return n;
}

void PUT_ULONG_BE(vss_ulong n, vss_uint8* b, int i)                         
{
	b[i]       = (vss_uint8) ( (n) >> 24 );
	b[i + 1] = (vss_uint8) ( (n) >> 16 );
	b[i + 2] = (vss_uint8) ( (n) >>  8 );
	b[i + 3] = (vss_uint8) ( (n)       );
}

/*
 *rotate shift left marco definition
 *
 */
#define  SHL(x,n) (((x) & 0xFFFFFFFFUL) << n)
#define ROTL(x,n) (SHL((x),n) | ((x) >> (32 - n)))

void SWAP(vss_ulong* a, vss_ulong* b) 
{ 
	vss_ulong t = *a;
	*a = *b; 
	*b = t;
}

/*
 * Expanded SM4 S-boxes
  Sbox table: 8bits input convert to 8 bits output*/
 



/*
 * private function:
 * look up in sm4_SboxTable and get the related value.
 * args:    [in] inch: 0x00~0xFF (8 bits unsigned value).
 */
static vss_uint8 sm4Sbox(vss_uint8 inch)
{
    const vss_uint8 *pTable = (const vss_uint8 *)sm4_SboxTable;
    vss_uint8 retVal = pTable[inch];
    
    return retVal;
}

/*
 * private F(Lt) function:
 * "T algorithm" == "L algorithm" + "t algorithm".
 * args:    [in] a: a is a 32 bits unsigned value;
 * return: c: c is calculated with line algorithm "L" and nonline algorithm "t"
 */
static vss_ulong sm4Lt(vss_ulong ka)
{
	vss_ulong bb = 0;
	vss_ulong c = 0;
	vss_uint8 a[4];
	vss_uint8 b[4];
	
	PUT_ULONG_BE(ka,a,0);
	b[0] = sm4Sbox(a[0]);
	b[1] = sm4Sbox(a[1]);
	b[2] = sm4Sbox(a[2]);
	b[3] = sm4Sbox(a[3]);
	bb = GET_ULONG_BE(b,0);
	c =bb^(ROTL(bb, 2))^(ROTL(bb, 10))^(ROTL(bb, 18))^(ROTL(bb, 24));
	
    return c;
}

/*
 * private F function:
 * Calculating and getting encryption/decryption contents.
 * args:    [in] x0: original contents;
 * args:    [in] x1: original contents;
 * args:    [in] x2: original contents;
 * args:    [in] x3: original contents;
 * args:    [in] rk: encryption/decryption key;
 * return the contents of encryption/decryption contents.
 */
static vss_ulong sm4F(vss_ulong x0, vss_ulong x1, vss_ulong x2, vss_ulong x3, vss_ulong rk)
{
    return (x0^sm4Lt(x1^x2^x3^rk));
}


/* private function:
 * Calculating round encryption key.
 * args:    [in] a: a is a 32 bits unsigned value;
 * return: sk[i]: i{0,1,2,3,...31}.
 */
static vss_ulong sm4CalciRK(vss_ulong ka)
{
	vss_ulong bb = 0;
	vss_ulong rk = 0;
	vss_uint8 a[4];
	vss_uint8 b[4];

	PUT_ULONG_BE(ka,a,0);
	b[0] = sm4Sbox(a[0]);
	b[1] = sm4Sbox(a[1]);
	b[2] = sm4Sbox(a[2]);
	b[3] = sm4Sbox(a[3]);
	bb = GET_ULONG_BE(b,0);
	rk = bb^(ROTL(bb, 13))^(ROTL(bb, 23));
	return rk;
}

static void sm4_setkey( vss_ulong SK[32], vss_uint8 key[16] )
{
	vss_ulong MK[4];
	vss_ulong k[36];
	vss_ulong i = 0;
	
	MK[0] = GET_ULONG_BE(key, 0 );
	MK[1] = GET_ULONG_BE(key, 4 );
	MK[2] = GET_ULONG_BE(key, 8 );
	MK[3] = GET_ULONG_BE(key, 12 );
	k[0] = MK[0]^sm4_FK[0];
	k[1] = MK[1]^sm4_FK[1];
	k[2] = MK[2]^sm4_FK[2];
	k[3] = MK[3]^sm4_FK[3];
	for(; i<32; i++)
	{
		k[i+4] = k[i] ^ (sm4CalciRK(k[i+1]^k[i+2]^k[i+3]^sm4_CK[i]));
		SK[i] = k[i+4];
	}
}

/*
 * SM4 standard one round processing
 *
 */
static void sm4_one_round( vss_ulong sm4_sk[32],
                    vss_uint8 input[16],
                    vss_uint8 output[16] )
{
	vss_ulong i = 0;
	vss_ulong ulbuf[36];

	mem_set8(ulbuf, 0, 36 * 4);
	ulbuf[0] = GET_ULONG_BE(input, 0 );
	ulbuf[1] = GET_ULONG_BE(input, 4 );
	ulbuf[2] = GET_ULONG_BE(input, 8 );
	ulbuf[3] = GET_ULONG_BE(input, 12 );
	while(i<32)
	{
		ulbuf[i+4] = sm4F(ulbuf[i], ulbuf[i+1], ulbuf[i+2], ulbuf[i+3], sm4_sk[i]);
		i++;
	}
	PUT_ULONG_BE(ulbuf[35],output,0);
	PUT_ULONG_BE(ulbuf[34],output,4);
	PUT_ULONG_BE(ulbuf[33],output,8);
	PUT_ULONG_BE(ulbuf[32],output,12);
}

/*
 * SM4 key schedule (128-bit, encryption)
 */
 void sm4_setkey_enc( sm4_context *ctx, vss_uint8 key[16] )
{
	ctx->mode = TT_SM4_ENCRYPT;
	sm4_setkey( ctx->sk, key );
}

/*
 * SM4 key schedule (128-bit, decryption)
 */
 void sm4_setkey_dec( sm4_context *ctx, vss_uint8 key[16] )
{
	vss_uint32 i;
	ctx->mode = TT_SM4_ENCRYPT;
	sm4_setkey( ctx->sk, key );
	for( i = 0; i < 16; i ++ )
	{
		SWAP( &ctx->sk[ i ], &ctx->sk[ 31-i] );
	}
}


/*
 * SM4-ECB block encryption/decryption
 */

 void sm4_crypt_ecb( sm4_context *ctx,
				   vss_uint32 mode,
				   vss_uint32 length,
				   vss_uint8 *input,
                   vss_uint8 *output)
{

	if(length%16 != 0)
	{
		return;
	}


	while( length > 0 )
	{
		sm4_one_round( ctx->sk, input, output );
		input  += 16;
		output += 16;
		length -= 16;
	}
}
#if 0
/*
 * SM4-CBC buffer encryption/decryption
 */
 void sm4_crypt_cbc( sm4_context *ctx,
                    vss_uint32 mode,
                    vss_uint32 length,
                    vss_uint8 iv[16],
                    vss_uint8 *input,
                    vss_uint8 *output )
{
	vss_uint32 i;
	vss_uint8 temp[16];

	if((input == VSS_NULL)||(output == VSS_NULL))
	{
		return;
	}	

	if(length%16 != 0)
	{
		return;
	}

	if( mode == TT_SM4_ENCRYPT )
	{
		while( length > 0 )
		{
			for( i = 0; i < 16; i++ )
				output[i] =  input[i] ^ iv[i] ;

			sm4_one_round( ctx->sk, output, output );
			mem_cpy8( iv, output, 16 );

			input  += 16;
			output += 16;
			length -= 16;
		}
	}
	else /* SM4_DECRYPT */
	{
		while( length > 0 )
		{
			mem_cpy8( temp, input, 16 );
			sm4_one_round( ctx->sk, input, output );

			for( i = 0; i < 16; i++ )
				output[i] =  output[i] ^ iv[i] ;

			mem_cpy8( iv, temp, 16 );

			input  += 16;
			output += 16;
			length -= 16;
		}
	}
}
#endif
vss_uint32 sm4_ecb_encrypt_new(vss_uint8 *in, vss_uint8 *out, vss_uint32 length,  vss_uint8 *key,vss_uint32 enc)
{
	sm4_context ctx;
	
	if (enc == TT_SM4_ENCRYPT)
	{
		sm4_setkey_enc(&ctx,key);
	}
	else
	{
		sm4_setkey_dec(&ctx,key);
	}

	
	if (length % 16 != 0)
	{
		return 1;
	}
	sm4_crypt_ecb(&ctx,enc,length,in,out);
	return 0;
}
#if 0
vss_uint32 sm4_cbc_encrypt_new(vss_uint8 *iv, vss_uint8 *in, vss_uint8 *out, vss_uint32 length,  vss_uint8 *key,vss_uint32 enc)
{
	sm4_context ctx;
	
	if (enc == TT_SM4_ENCRYPT)
	{
		sm4_setkey_enc(&ctx,key);
	}
	else
	{
		sm4_setkey_dec(&ctx,key);
	}
	
	if (length % 16 != 0)
	{
		return 1;
	}
	
	sm4_crypt_cbc(&ctx,enc,length,iv,in,out);
	return 0;
}
#endif


#pragma section code restore


