#include "errid.h"
#include "vsskeym.h"
#include "vsstype.h"
#include "vssconf.h"
#include "vssapi.h"
#include "vsscommon.h"
#include "cert.h"

extern flash_io_cb *g_vssIocb;
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
extern vss_uint8 g_vssAsymmKey[ASYMM_KEY_LEN];
extern vss_uint8 g_vssRootCert[ROOT_CERT_SIZE];
extern vss_uint8 g_vssUserCert[USER_CERT_SIZE];
extern vss_uint8 g_vssSymmKey[SYMM_KEY_NUM][SYMM_CIPHER_LEN];
extern vss_uint8 g_vssSessKey[SESS_KEY_NUM][SYMM_CIPHER_LEN];
#endif
extern vss_uint8 g_cVssAlg;
extern vss_uint8 g_cVssEnv;
extern const vss_uint8 vsskeym_KEK[16];
extern const vss_uint8 vsskeym_SECOC[16];
extern const vss_uint8 vss_ZERO[32];


#pragma section code "vss_api_code" 

enum{
	KEY_FLAG_USED = 1,
	KEY_FLAG_EXPORT = 2,
	KEY_FLAG_VALID = 4,
	KEY_FLAG_CHIP = 8,
};

#define FLASH_IO_RETRY_COUNT	3

typedef struct
{
	/* 8 bytes key attribute */
	vss_uint8 cFlag;
	vss_uint8 cKeyLen;
	vss_uint8 cIndex;
	vss_uint8 cAlg;
	vss_uint8 cv[3];
	vss_uint8 cRFU;   
	/* 24 bytes key value */
	vss_uint8 value[SYMM_KEY_LEN];
} TSymmKeyItem;

typedef struct
{
	/* 8 bytes key attribute */
	vss_uint8 cUseFlag;
	vss_uint8 cIndex;
	vss_uint8 cAlg; /*1-ECC; 2-SM2*/
	vss_uint8 cXLen;
	vss_uint8 cYLen;
	vss_uint8 cSKLen;
	vss_uint8 cRFU7;
	vss_uint8 cRFU8;
	/* 96 bytes key value */
	vss_uint8 value[ASYMM_KEY_LEN];
	/* 8 bytes key mac */
	vss_uint8 mac[ASYMM_KEY_MAC];
} TAsymmKeyItem;

vss_uint32 getEnvIndex(vss_uint32 certType)
{
	if(certType == CERT_TYPE_USR)
		return SYS_CERT_NUM;
	if(g_cVssAlg == ALG_GJ && g_cVssEnv == ENV_QA)
		return 0;

	if(g_cVssAlg == ALG_GM && g_cVssEnv == ENV_QA)
		return 1;

	if(g_cVssAlg == ALG_GJ && g_cVssEnv == ENV_PP)
		return 0;

	if(g_cVssAlg == ALG_GM && g_cVssEnv == ENV_PP)
		return 1;

	if(g_cVssAlg == ALG_GJ && g_cVssEnv == ENV_P)
		return 2;

	if(g_cVssAlg == ALG_GM && g_cVssEnv == ENV_P)
		return 3;
	return ERR_SYSTEM_INIT;
}

vss_uint32 LoadCert(vss_uint32 certType, vss_uint32* certlen, vss_uint8* certData)
{
	vss_uint32 certId = 0;
	
	if (certData == VSS_NULL || certlen == VSS_NULL)
		return ERR_PARAMETER;
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
	if(certType == CERT_TYPE_ROOT && mem_cmp8(g_vssRootCert, vss_ZERO, 16))
	{
		mem_cpy8(certData, g_vssRootCert, ROOT_CERT_SIZE);
		*certlen = ROOT_CERT_SIZE;
		return 0;
	}

	if(certType == CERT_TYPE_USR && mem_cmp8(g_vssUserCert, vss_ZERO, 16))
	{
		mem_cpy8(certData, g_vssUserCert, USER_CERT_SIZE);
		*certlen = USER_CERT_SIZE;
		return 0;
	}
#endif
	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;

	certId = getEnvIndex(certType);
	if(certId == ERR_SYSTEM_INIT)
		return ERR_SYSTEM_INIT;
	return LoadCertById(certId, certlen, certData);
}

vss_uint32 LoadCertById(vss_uint32 certId, vss_uint32* certlen, vss_uint8* certData)
{
	vss_uint32 ret = 0;
	vss_uint8 buf[256];
	vss_uint8 cert[256];
	vss_uint8 mac[16];
	vss_uint32 offset = 0;
	vss_uint32 noutlen = 0;
	vss_uint32 readlen = 0;

	if(certId < SYS_CERT_NUM)
	{
		offset = FLASH_ROOTCERT_OFFSET + certId * ROOT_CIPHER_LEN;
		readlen = ROOT_CIPHER_LEN;
	}else{
		offset = FLASH_USERCERT_OFFSET;
		readlen = USER_CIPHER_LEN;
	}

	ret = g_vssIocb(FLASH_IO_READ, offset, buf, readlen);
	if (ret)
	{
		return ERR_READ_FLASH;
	}

	if(mem_cmp8(buf, vss_ZERO, 16) == 0)
		return ERR_CERT_NOTFOUND;
	
	ret = VssSM4Mac(buf, readlen - 16, (vss_uint8*)vsskeym_KEK, 16, mac);
	if(ret || mem_cmp8(buf + readlen - 16, mac, 16))
		return ERR_DATA_VERIFY;

	ret = VssSM4Calc(buf, readlen - 16, (vss_uint8*)vsskeym_KEK, 16, CALC_DEC, PAD_NO_FORCE,  cert, &noutlen);
	if(ret)
		return ERR_CERT_INVALID;
	
	if(certId < SYS_CERT_NUM)
	{
		if(cert[0] != CERTTYPE_ROOT)
			return ERR_CERT_INVALID;
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
		mem_cpy8(g_vssRootCert, cert, ROOT_CERT_SIZE);
#endif
		mem_cpy8(certData, cert, ROOT_CERT_SIZE);
		*certlen = ROOT_CERT_SIZE;
	}else if(certId == SYS_CERT_NUM)
	{
		if(cert[0] != CERTTYPE_USR)
			return ERR_CERT_INVALID;
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
		mem_cpy8(g_vssUserCert, cert, USER_CERT_SIZE);
#endif
		mem_cpy8(certData, cert, USER_CERT_SIZE);
		*certlen = USER_CERT_SIZE;
	}
	
	return ret;
}

vss_uint32 SaveCert(vss_uint32 certType, vss_uint32 certlen, vss_uint8* certData)
{
	vss_uint32 ret = 0;
	vss_uint8 buf[256];
	vss_uint32 certId = 0;
	vss_uint32 offset = 0;
	vss_uint32 noutlen =0;
	vss_uint32 readlen = 0;
	
	if (certData == VSS_NULL)
		return ERR_PARAMETER;	

	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;

	certId = getEnvIndex(certType);
	if(certId == ERR_SYSTEM_INIT)
		return ERR_SYSTEM_INIT;

	switch(certType)
	{
		case CERT_TYPE_ROOT:
			offset = FLASH_ROOTCERT_OFFSET + certId * ROOT_CIPHER_LEN;
			readlen = ROOT_CIPHER_LEN;
			break;
		case CERT_TYPE_USR:
			offset = FLASH_USERCERT_OFFSET;
			readlen = USER_CIPHER_LEN;
			break;
		default:
			return ERR_PARAMETER;
	}

	if(certlen != (readlen - 16))
	{
		return ERR_LEN_INVALID;
	}
	
	ret = VssSM4Calc((vss_uint8*)certData, certlen, (vss_uint8*)vsskeym_KEK, 16, CALC_ENC, PAD_NO_FORCE, buf, &noutlen);
	if (ret)
	{
		return ret;
	}
	
	ret = VssSM4Mac(buf, certlen, (vss_uint8*)vsskeym_KEK, 16, buf + certlen);
	if (ret)
	{
		return ret;
	}
	
	ret = g_vssIocb(FLASH_IO_WRITE, offset, buf, readlen);
	if (ret)
	{
		return ERR_WRITE_FLASH;
	}

#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))	
	if(certType == CERT_TYPE_ROOT)
	{
		mem_cpy8(g_vssRootCert, certData, ROOT_CERT_SIZE);
	}else if(certType == CERT_TYPE_USR)
	{
		mem_cpy8(g_vssUserCert, certData, USER_CERT_SIZE);
	}
#endif	
	return 0;	
}

vss_uint32 GenSymmKey(vss_uint8* code, vss_char8* keyList)
{
	vss_uint8 vss_div[16];
	vss_uint8 sn[8];
	vss_uint8 key[16];
	vss_uint8 i = 0; 
	vss_uint8 k = 0; 
	vss_uint8 flag = 0; 
	vss_uint32 ret = 0;
	vss_uint8 mk[16];
	vss_uint8 keyatb[4];
	vss_uint8 cfgData[FLASH_CFG_DATA];
	vss_uint8 backup[SYMM_KEY_NUM * SYMM_CIPHER_LEN];
	vss_uint32 noutlen=0;
	vss_uint32 key_off=0;

	if(code == VSS_NULL || keyList == VSS_NULL)
		return ERR_PARAMETER;

	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;
	
	ret = LoadCFGData(cfgData);
	if (ret)
	{
		return ret;
	}

	ret = g_vssIocb(FLASH_IO_READ, FLASH_KEYDATA_OFFSET, backup, SYMM_KEY_NUM * SYMM_CIPHER_LEN);
	if (ret)
	{
		return ERR_READ_FLASH;
	}

	mem_set8(sn, 0, 8);
	for (i = 0; i < 16; i++)
	{
		mk[i] = code[i] ^ code[i + 16];
	}

	keyList[0] = 0;
	for (i = 0; i < SYMM_KEY_NUM; i++)
	{		
		mem_cpy8(keyatb, cfgData + FLASH_CFG_KEY + i * 4, 4);
		if(i >= COMM_KEY && i < RESV_KEY)
		{
			flag = keyatb[1] & ~KEY_FLAG_VALID;
		}else{
			flag = keyatb[1] | KEY_FLAG_VALID;
		}

		{
			sn[7] = i;
			for (k = 0; k < 8; k++)
			{
				vss_div[0 + k] = sn[k];
				vss_div[8 + k] = ~sn[k];
			}			
			
			VssSM4Calc(vss_div, 16, mk, 16, 0, 0, key, &noutlen);
		}

		ret = SetSymmKey(i, flag, keyatb[3], key);
		if (ret)
		{
			break;
		}

		if((keyatb[1] & KEY_FLAG_USED) > 0)
		{
			if(i > 9)
			{
				keyList[key_off++] = '0' + (i / 10);
				keyList[key_off++] = '0' + (i % 10);
			}else{
				keyList[key_off++] = '0' + i;
			}
			keyList[key_off++] = ',';
		}
	}

	if(ret)
	{
		g_vssIocb(FLASH_IO_WRITE, FLASH_KEYDATA_OFFSET, backup, SYMM_KEY_NUM * SYMM_CIPHER_LEN);
	}else{
		if(code[31] & 1)
			ret = SetAlg(ALG_GJ);
		else
			ret = SetAlg(ALG_GM);

		SaveKeyCode(32, code);
	}

	return ret;
}

vss_uint32 LoadSymmKey(vss_uint32 keyId,  TSymmKeyItem* key)
{
	vss_uint32 ret = 0;
	vss_uint8 buf[SYMM_CIPHER_LEN];
	vss_uint32 offset = 0;
	vss_uint32 noutlen =0;
	vss_uint8 i = 0;

	if (keyId >= TOTAL_KEY_NUM)
		return ERR_INDEX_INVALID;
	
	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;

	mem_set8((vss_uint8*)key, 0, SYMM_CIPHER_LEN);
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))	
	if(keyId < SYMM_KEY_NUM)
	{
		if(mem_cmp8(g_vssSymmKey[keyId], vss_ZERO, 16))
			mem_cpy8((vss_uint8*)key, g_vssSymmKey[keyId], SYMM_CIPHER_LEN);
	}else{
		if(mem_cmp8(g_vssSessKey[keyId - SYMM_KEY_NUM], vss_ZERO, 16))
			mem_cpy8((vss_uint8*)key, g_vssSessKey[keyId - SYMM_KEY_NUM], SYMM_CIPHER_LEN);
	}

	if(key->cFlag & KEY_FLAG_USED)
		return 0;
#endif
	offset = FLASH_KEYDATA_OFFSET + SYMM_CIPHER_LEN * keyId;

	i = 0;
	while(i++ < FLASH_IO_RETRY_COUNT)
	{
		ret = g_vssIocb(FLASH_IO_READ, offset, buf, SYMM_CIPHER_LEN);
		if (ret == 0)
		{
			break;
		}
	}
	
	if (ret)
	{
		return ERR_READ_FLASH;
	}
	
	if (mem_cmp8(buf, vss_ZERO, 16) == 0)
		return ERR_KEY_NOTFOUND;
	
	ret = VssSM4Calc(buf, SYMM_CIPHER_LEN, (vss_uint8*)vsskeym_KEK, 16, CALC_DEC, PAD_NO_FORCE, (vss_uint8*)key, &noutlen);
	if(ret)
		return ERR_KEY_INVALID;

 	if(key->cKeyLen != 16 && key->cKeyLen != 12 && key->cKeyLen != 8
		&& key->cKeyLen != 4 && key->cKeyLen != 2)
	{
		return ERR_KEYLEN_INVALID;
	}

	//if (key->cFlag & (KEY_FLAG_USED|KEY_FLAG_EXPORT))
	if (key->cFlag & KEY_FLAG_USED)
	{
		ret = VssSM4Calc((vss_uint8*)vss_ZERO, 16, key->value, 16, CALC_ENC, PAD_NO_FORCE, buf, &noutlen);
		if(ret)
			return ERR_KEY_INVALID;

		if(mem_cmp8(buf, key->cv, 3) != 0)
			return ERR_DATA_VERIFY;
		
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
		if(keyId < SYMM_KEY_NUM)
		{
			mem_cpy8(g_vssSymmKey[keyId], (vss_uint8*)key, SYMM_CIPHER_LEN);
		}else{
			mem_cpy8(g_vssSessKey[keyId - SYMM_KEY_NUM], (vss_uint8*)key, SYMM_CIPHER_LEN);
		}
#endif
		
		ret = 0;
	}else{
		ret = ERR_KEY_NOTFOUND;
	}
	
	return ret;
}

vss_uint32 SaveSymmKey(vss_uint32 keyId, TSymmKeyItem* key)
{
	vss_uint32 ret = 0;
	vss_uint32 offset = 0;
	vss_uint32 noutlen =0;
	vss_uint8 buf[SYMM_CIPHER_LEN];
	vss_uint8 i = 0;

	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;
		
	if (keyId >= TOTAL_KEY_NUM)
		return ERR_INDEX_INVALID;

	ret = VssSM4Calc((vss_uint8*)key, SYMM_CIPHER_LEN, (vss_uint8*)vsskeym_KEK, 16, CALC_ENC, PAD_NO_FORCE, buf, &noutlen);
	if(ret)
		return ERR_KEY_INVALID;
	
	offset = FLASH_KEYDATA_OFFSET + SYMM_CIPHER_LEN * keyId;

	i = 0;
	while(i++ < FLASH_IO_RETRY_COUNT)
	{
		ret = g_vssIocb(FLASH_IO_WRITE, offset, buf, SYMM_CIPHER_LEN);
		if (ret == 0)
		{
			break;
		}
	}
	
	if (ret)
	{
		return ERR_WRITE_FLASH;
	}

	return 0;
}

vss_uint32 GetSessKey(vss_uint32 keyId,  vss_uint32* keyLen, vss_uint8* keyData)
{
	if(keyId >= SESS_KEY_NUM)
		return ERR_INDEX_INVALID;
	
	return GetSymmKey(keyId + SYMM_KEY_NUM, keyLen, keyData);
}

vss_uint32 GetSymmKey(vss_uint32 keyId,  vss_uint32* keyLen, vss_uint8* keyData)
{
	vss_uint32 ret = 0;
	TSymmKeyItem key;

	if (keyData == VSS_NULL || keyLen == VSS_NULL)
		return ERR_PARAMETER;

	if(keyId >= TOTAL_KEY_NUM)
		return ERR_INDEX_INVALID;
	
	ret = LoadSymmKey(keyId, &key);
	if(ret)
		return ret;
	
	if(key.cFlag & KEY_FLAG_USED)
	{
		if(key.cFlag & KEY_FLAG_VALID)
		{
			*keyLen = key.cKeyLen;
			mem_cpy8(keyData, key.value, 16);
		}else{
			*keyLen  = 16;
			mem_cpy8(keyData, vsskeym_SECOC, 16);
		}
	}else{
		ret = ERR_KEY_NOTFOUND;
	}

	return ret;
}

vss_uint32 SetKeyActive(vss_uint32 keyId, vss_uint32 valid)
{
	vss_uint32 ret = 0;
	TSymmKeyItem key;
	
	if (keyId >= TOTAL_KEY_NUM)
		return ERR_INDEX_INVALID;

	ret = LoadSymmKey(keyId, &key);
	if(ret)
		return ret;

	if(key.cFlag & KEY_FLAG_USED)
	{
		if(valid)
		{
			if(key.cFlag & KEY_FLAG_VALID)
			{
			}else{
				key.cFlag |= KEY_FLAG_VALID;
				ret = SetSymmKey(keyId, key.cFlag, key.cKeyLen, key.value);
			}
		}else{
			if(key.cFlag & KEY_FLAG_VALID)
			{
				key.cFlag &= ~KEY_FLAG_VALID;
				ret = SetSymmKey(keyId, key.cFlag, key.cKeyLen, key.value);
			}else{
			}
		}
	}else{
		return ERR_KEY_INVALID;
	}
	
	return ret;
}

vss_uint32 GetKeyActive(vss_uint32 keyId, vss_uint8* valid)
{
	vss_uint32 ret = 0;
	TSymmKeyItem key;
	
	if(valid == VSS_NULL)
		return ERR_PARAMETER;
	
	if (keyId >= TOTAL_KEY_NUM)
		return ERR_INDEX_INVALID;

	ret = LoadSymmKey(keyId, &key);
	if(ret)
		return ret;

	if(key.cFlag & KEY_FLAG_USED)
	{
		if(key.cFlag & KEY_FLAG_VALID)
		{
			*valid = 1; 
		}else{
			*valid = 0; 
		}
	}else{
		return ERR_KEY_INVALID;
	}
	
	return ret;
}


vss_uint32 SetSessKey(vss_uint32 keyId, vss_uint32 keyLen, vss_uint8* keyData)
{
	vss_uint32 flag = 0;
	
	if(keyId >= SESS_KEY_NUM)
		return ERR_INDEX_INVALID;
	
	flag = KEY_FLAG_USED | KEY_FLAG_EXPORT | KEY_FLAG_VALID;
	return SetSymmKey(keyId + SYMM_KEY_NUM, flag, keyLen, keyData);
}

vss_uint32 SetSymmKey(vss_uint32 keyId, vss_uint32 flag, vss_uint32 keyLen, vss_uint8* keyData)
{
	vss_uint32 ret = 0;
	vss_uint32 noutlen =0;
	vss_uint8 cv[16];
	TSymmKeyItem key;

	if(keyData == VSS_NULL)
		return ERR_PARAMETER;
	
	if(keyId >= TOTAL_KEY_NUM)
		return ERR_INDEX_INVALID;
	
	if (keyLen != 16 && keyLen != 12 && keyLen != 8 && keyLen != 4 && keyLen != 2)
		return ERR_DATA_INVALID;
	
	mem_set8((vss_uint8*)&key, 0, SYMM_CIPHER_LEN);
	key.cFlag = flag;
	key.cIndex = keyId;
	key.cKeyLen = keyLen;
	mem_set8(key.value, 0xFF, SYMM_KEY_LEN);
	mem_cpy8(key.value, keyData, keyLen);
	ret = VssSM4Calc((vss_uint8*)vss_ZERO, 16, key.value, 16, CALC_ENC, PAD_NO_FORCE, cv, &noutlen);
	if(ret)
		return ERR_KEY_INVALID;
	
	mem_cpy8(key.cv, cv, 3);
	ret = SaveSymmKey(keyId, &key);
	if(ret)
		return ERR_WRITE_FLASH;
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
	if(keyId < SYMM_KEY_NUM)
	{
		mem_cpy8(g_vssSymmKey[keyId], (vss_uint8*)&key, SYMM_CIPHER_LEN);
	}else{
		mem_cpy8(g_vssSessKey[keyId - SYMM_KEY_NUM], (vss_uint8*)&key, SYMM_CIPHER_LEN);
	}
#endif
	return 0;
}

vss_uint32 GenAsymmKey(vss_uint8* x, vss_uint8* y)
{
	vss_uint32 ret = 0;
	vss_uint8 buf[ASYMM_CIPHER_LEN];
	vss_uint8 mac[16];
	vss_uint32 offset = 0;
	vss_uint32 noutlen =0;
	TAsymmKeyItem keyData;

	if(x == VSS_NULL || y == VSS_NULL)
		return ERR_PARAMETER;

	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;
	
	mem_set8((vss_uint8*)&keyData, 0, ASYMM_CIPHER_LEN);
	keyData.cAlg = g_cVssAlg;
	keyData.cUseFlag = 1;
	keyData.cXLen = 32;
	keyData.cYLen = 32;
	keyData.cSKLen = 32;

	if(g_cVssAlg == ALG_GJ)
	{
		ret = VssECCGenKey(keyData.value, keyData.value + 32, keyData.value + 64);
	}else if(g_cVssAlg == ALG_GM)
	{
		ret = VssSM2GenKey(keyData.value, keyData.value + 32, keyData.value + 64);
	}else{
		return ERR_SYSTEM_INIT;
	}
	if(ret)
		return ret;

	ret = VssSM4Mac(keyData.value, ASYMM_KEY_LEN, (vss_uint8*)vsskeym_KEK, 16, mac);
	if(ret)
		return ret;

	mem_cpy8(keyData.mac, mac, ASYMM_KEY_MAC);

	ret = VssSM4Calc((vss_uint8*)&keyData,ASYMM_CIPHER_LEN, (vss_uint8*)vsskeym_KEK, 16, CALC_ENC, PAD_NO_FORCE, buf, &noutlen);
	if(ret || noutlen != ASYMM_CIPHER_LEN)
		return ERR_CALC_FAIL;

	offset = FLASH_ASYMMKEY_OFFSET;
	ret = g_vssIocb(FLASH_IO_WRITE, offset, buf, ASYMM_CIPHER_LEN);
	if (ret)
	{
		return ERR_WRITE_FLASH;
	}
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
	mem_cpy8(g_vssAsymmKey, keyData.value, ASYMM_KEY_LEN);
#endif
	mem_cpy8(x, keyData.value, 32);
	mem_cpy8(y, keyData.value + 32, 32);

	return 0;
}

vss_uint32 LoadAsymmKey(vss_uint8* x, vss_uint8* y, vss_uint8* sk)
{
	vss_uint32 ret = 0;
	vss_uint8 buf[ASYMM_CIPHER_LEN];
	vss_uint8 mac[16];
	vss_uint32 offset = 0;
	vss_uint32 noutlen =0;
	TAsymmKeyItem keyData;

	if(x == VSS_NULL || y == VSS_NULL || sk == VSS_NULL)
		return ERR_PARAMETER;
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
	if(mem_cmp8(g_vssAsymmKey, mac, 16))
	{
		mem_cpy8(x, g_vssAsymmKey, 32);
		mem_cpy8(y, g_vssAsymmKey + 32, 32);
		mem_cpy8(sk, g_vssAsymmKey + 64, 32);

		return 0;
	}
#endif
	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;

	offset = FLASH_ASYMMKEY_OFFSET;
	ret = g_vssIocb(FLASH_IO_READ, offset, buf, ASYMM_CIPHER_LEN);
	if (ret)
	{
		return ERR_READ_FLASH;
	}
	
	ret = VssSM4Calc(buf, ASYMM_CIPHER_LEN, (vss_uint8*)vsskeym_KEK, 16, CALC_DEC, PAD_FORCE, (vss_uint8*)&keyData, &noutlen);
	if(ret || noutlen != ASYMM_CIPHER_LEN)
		return ERR_CALC_FAIL;

	if(keyData.cUseFlag == 0)
		return ERR_KEY_NOTFOUND;
		
	ret = VssSM4Mac(keyData.value, ASYMM_KEY_LEN, (vss_uint8*)vsskeym_KEK, 16, mac);
	if(ret || mem_cmp8(keyData.mac, mac, ASYMM_KEY_MAC))
		return ERR_DATA_VERIFY;
	
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
	mem_cpy8(g_vssAsymmKey, keyData.value, ASYMM_KEY_LEN);
#endif
	mem_cpy8(x, keyData.value, 32);
	mem_cpy8(y, keyData.value + 32, 32);
	mem_cpy8(sk, keyData.value + 64, 32);

	return 0;
}

vss_uint32 SaveAsymmKey(vss_uint8* x, vss_uint8* y, vss_uint8* sk)
{
	vss_uint32 ret = 0;
	vss_uint8 buf[ASYMM_CIPHER_LEN];
	vss_uint8 mac[16];
	vss_uint32 offset = 0;
	vss_uint32 noutlen =0;
	TAsymmKeyItem keyData;

	if(x == VSS_NULL || y == VSS_NULL || sk == VSS_NULL)
		return ERR_PARAMETER;

	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;

	mem_set8((vss_uint8*)&keyData, 0, ASYMM_CIPHER_LEN);
	mem_cpy8(keyData.value, x, 32);
	mem_cpy8(keyData.value + 32, y, 32);
	mem_cpy8(keyData.value + 64, sk, 32);

	keyData.cAlg = g_cVssAlg;
	keyData.cUseFlag = 1;
	keyData.cXLen = 32;
	keyData.cYLen = 32;
	keyData.cSKLen = 32;
		
	ret = VssSM4Mac(keyData.value, ASYMM_KEY_LEN, (vss_uint8*)vsskeym_KEK, 16, mac);
	if(ret)
		return ERR_DATA_INVALID;
	mem_cpy8(keyData.mac, mac, ASYMM_KEY_MAC);
	
	ret = VssSM4Calc((vss_uint8*)&keyData, ASYMM_CIPHER_LEN, (vss_uint8*)vsskeym_KEK, 16, CALC_ENC, PAD_NO_FORCE, buf, &noutlen);
	if(ret || noutlen != ASYMM_CIPHER_LEN)
		return ERR_CALC_FAIL;

	offset = FLASH_ASYMMKEY_OFFSET;
	ret = g_vssIocb(FLASH_IO_WRITE, offset, buf, ASYMM_CIPHER_LEN);
	if (ret)
	{
		return ERR_READ_FLASH;
	}
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))	
	mem_cpy8(g_vssAsymmKey, keyData.value, ASYMM_KEY_LEN);
#endif
	return 0;
}

vss_uint32 LoadCFGData(vss_uint8* cfgData)
{
	vss_uint32 ret = 0;
	vss_uint8 tmp[FLASH_CFG_SIZE];
	vss_uint8 mac[16];
	vss_uint32 outlen = 0;

	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;
	
	ret = g_vssIocb(FLASH_IO_READ, FLASH_CFG_OFFSET, tmp, FLASH_CFG_SIZE);
	if (ret)
	{
		return ERR_READ_FLASH;
	}
	
	ret = VssSM4Mac(tmp, FLASH_CFG_DATA, (vss_uint8*)vsskeym_KEK, 16, mac);
	if(ret || mem_cmp8(mac, tmp + FLASH_CFG_DATA, 16))
	{
		return ERR_DATA_VERIFY;
	}
	
	ret = VssSM4Calc(tmp, FLASH_CFG_DATA, (vss_uint8*)vsskeym_KEK, 16, CALC_DEC, PAD_NO_FORCE, cfgData, &outlen);
	if (ret)
	{
		return ERR_READ_CFG;
	}

	return 0;
}

vss_uint32 SaveCFGData(vss_uint8* cfgData)
{
#if 0
	vss_uint32 ret = 0;
	vss_uint8 tmp[FLASH_CFG_SIZE] = {0};
	vss_uint32 outlen = 0;
	vss_uint8 mac[16]={0};

	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;
	
	ret = VssSM4Calc(cfgData, FLASH_CFG_DATA, (vss_uint8*)vsskeym_KEK, 16, CALC_ENC, PAD_NO_FORCE, tmp, &outlen);
	if (ret)
	{
		return ERR_CALC_FAIL;
	}
	
	ret = VssSM4Mac(tmp, FLASH_CFG_DATA, (vss_uint8*)vsskeym_KEK, 16, tmp + FLASH_CFG_DATA);
	if (ret)
	{
		return ERR_CALC_FAIL;
	}
	
	ret = g_vssIocb(FLASH_IO_WRITE, FLASH_CFG_OFFSET, tmp, FLASH_CFG_SIZE);
	if (ret)
	{
		return ERR_WRITE_FLASH;
	}
	
	return 0;
#else
	return ERR_NOT_SUPPORT;
#endif
}

vss_uint32 SaveKeyCode(vss_uint32 len ,vss_uint8* code)
{
	vss_uint32 ret = 0;
	vss_uint8 tmp[32];
	vss_uint32 outlen = 0;

	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;
	
	ret = VssSM4Calc(code, len, (vss_uint8*)vsskeym_KEK, 16, CALC_ENC, PAD_NO_FORCE, tmp, &outlen);
	if (ret)
	{
		return ERR_CALC_FAIL;
	}

	if(outlen != FLASH_KEYCODE_SIZE)
	{
		return ERR_PARAMETER;
	}

	ret = g_vssIocb(FLASH_IO_WRITE, FLASH_KEYCODE_OFFSET, tmp, FLASH_KEYCODE_SIZE);
	if (ret)
	{
		return ERR_WRITE_FLASH;
	}	

	return 0;
}

vss_uint32 LoadKeyCode(vss_uint32* len ,vss_uint8* code)
{
	vss_uint32 ret = 0;
	vss_uint8 tmp[32];
	vss_uint32 outlen = 0;

	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;
	
	ret = g_vssIocb(FLASH_IO_READ, FLASH_KEYCODE_OFFSET, tmp, FLASH_KEYCODE_SIZE);
	if (ret)
	{
		return ERR_READ_FLASH;
	}

	ret = VssSM4Calc(tmp, FLASH_KEYCODE_SIZE, (vss_uint8*)vsskeym_KEK, 16, CALC_DEC, PAD_NO_FORCE, code, len);
	if (ret)
	{
		return ERR_READ_FLASH;
	}
	
	return ret;
}

vss_uint32 CryptoPaddingTo16(vss_uint8 *in, vss_uint32 inLen, vss_uint32 force) {
	vss_uint32 outlen = 0;
	vss_uint32 i = 0;
	vss_uint32 nPadLen = 16 - inLen % 16;
	vss_uint8 *pTemp;

	if (force == 0 && nPadLen == 16)
		return inLen;

	pTemp = in + inLen;
	*pTemp = 0x80;
	pTemp++;

	for (i = 1; i < nPadLen; i++) {
		*pTemp = 0;
		pTemp++;
	}

	outlen = inLen + nPadLen;
	return outlen;
}

vss_uint32 CryptoUnPadding16(vss_uint8 *in, vss_uint32 inLen) {
	vss_uint32 i = 0;
	vss_uint32 oriLen = inLen;
	vss_uint8 *pTemp = in + inLen - 1;

	for (i = 0; i < 16; i++) {
		if (*pTemp == 0) {
			pTemp--;
			oriLen--;
		} else if (*pTemp == 0x80) {
			*pTemp = 0;
			oriLen--;
			return oriLen;
		} else {
			break;
		}
	}

	return oriLen;
}

vss_uint32 SetAlg(vss_uint8 alg)
{
	vss_uint32 ret = 0;
	
	if(alg != ALG_GJ && alg != ALG_GM)
		return ERR_PARAMETER;

	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;

	if(g_cVssAlg != alg)
	{
		ret = g_vssIocb(FLASH_IO_WRITE, FLASH_CFG_ALG, &alg, 1);
		if(ret == 0)
		{	
			g_cVssAlg = alg;
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
			mem_set8(g_vssAsymmKey, 0, ASYMM_KEY_LEN);
			mem_set8(g_vssRootCert, 0, ROOT_CERT_SIZE);
			mem_set8(g_vssUserCert, 0, USER_CERT_SIZE);
#endif
		}
	}

	return ret;
}

vss_uint32 GetAlg(void)
{

	vss_uint32 ret = 0;
	vss_uint8 load;
	
	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;
	
	ret = g_vssIocb(FLASH_IO_READ, FLASH_CFG_ALG, &load, 1);
	if(ret == 0 && load != 0)
	{	
		g_cVssAlg = load;
	}

	return ret;
}

vss_uint32 SetEnvironment(vss_uint8 env)
{
	vss_uint32 ret = 0;
	
	if(env != ENV_QA && env != ENV_PP && env != ENV_P)
		return ERR_PARAMETER;


	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;
	
	if(g_cVssEnv != env)
	{
		ret = g_vssIocb(FLASH_IO_WRITE, FLASH_CFG_ENV, &env, 1);
		if(ret == 0)
		{	
			g_cVssEnv = env;
#if (defined (_ENABLE_FLASH_BUFF_)&&(_ENABLE_FLASH_BUFF_ == 1U))
			mem_set8(g_vssAsymmKey, 0, ASYMM_KEY_LEN);
			mem_set8(g_vssRootCert, 0, ROOT_CERT_SIZE);
			mem_set8(g_vssUserCert, 0, USER_CERT_SIZE);
#endif
		}
	}

	return ret;
}

vss_uint32 GetEnvironment(void)
{

	vss_uint32 ret = 0;
	vss_uint8 load = 0;
	
	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;
	
	ret = g_vssIocb(FLASH_IO_READ, FLASH_CFG_ENV, &load, 1);
	if(ret == 0 && load != 0)
	{	
		g_cVssEnv = load;
	}

	return ret;
}

vss_uint32 SetWroteFlag(vss_uint8 flag)
{
	vss_uint32 ret = 0;
	
	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;
	
	ret = g_vssIocb(FLASH_IO_WRITE, FLASH_CFG_WROTE, &flag, 1);
	return ret;
}

vss_uint32 GetWroteFLag(vss_uint8* flag)
{

	vss_uint32 ret = 0;
	vss_uint8 load;
	
	if (g_vssIocb == VSS_NULL)
		return ERR_SYSTEM_INIT;
	
	ret = g_vssIocb(FLASH_IO_READ, FLASH_CFG_WROTE, &load, 1);
	if(ret == 0)
	{
		if(load == 1)
			*flag = 1;
		else
			*flag = 0;
	}

	return ret;
}

#pragma section code restore


