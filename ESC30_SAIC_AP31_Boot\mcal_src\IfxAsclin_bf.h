/**
 * \file IfxAsclin_bf.h
 * \brief
 * \copyright Copyright (c) 2016 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC23XADAS_UM_V1.1.R1
 * Specification: tc23xadas_um_sfrs_MCSFR.xml (Revision: UM_V1.1)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Asclin_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Asclin
 * 
 */
#ifndef IFXASCLIN_BF_H
#define IFXASCLIN_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Asclin_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN0 */
#define IFX_ASCLIN_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN0 */
#define IFX_ASCLIN_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN0 */
#define IFX_ASCLIN_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN10 */
#define IFX_ASCLIN_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN10 */
#define IFX_ASCLIN_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN10 */
#define IFX_ASCLIN_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN11 */
#define IFX_ASCLIN_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN11 */
#define IFX_ASCLIN_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN11 */
#define IFX_ASCLIN_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN12 */
#define IFX_ASCLIN_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN12 */
#define IFX_ASCLIN_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN12 */
#define IFX_ASCLIN_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN13 */
#define IFX_ASCLIN_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN13 */
#define IFX_ASCLIN_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN13 */
#define IFX_ASCLIN_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN14 */
#define IFX_ASCLIN_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN14 */
#define IFX_ASCLIN_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN14 */
#define IFX_ASCLIN_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN15 */
#define IFX_ASCLIN_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN15 */
#define IFX_ASCLIN_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN15 */
#define IFX_ASCLIN_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN16 */
#define IFX_ASCLIN_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN16 */
#define IFX_ASCLIN_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN16 */
#define IFX_ASCLIN_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN17 */
#define IFX_ASCLIN_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN17 */
#define IFX_ASCLIN_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN17 */
#define IFX_ASCLIN_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN18 */
#define IFX_ASCLIN_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN18 */
#define IFX_ASCLIN_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN18 */
#define IFX_ASCLIN_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN19 */
#define IFX_ASCLIN_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN19 */
#define IFX_ASCLIN_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN19 */
#define IFX_ASCLIN_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN1 */
#define IFX_ASCLIN_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN1 */
#define IFX_ASCLIN_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN1 */
#define IFX_ASCLIN_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN20 */
#define IFX_ASCLIN_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN20 */
#define IFX_ASCLIN_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN20 */
#define IFX_ASCLIN_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN21 */
#define IFX_ASCLIN_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN21 */
#define IFX_ASCLIN_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN21 */
#define IFX_ASCLIN_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN22 */
#define IFX_ASCLIN_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN22 */
#define IFX_ASCLIN_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN22 */
#define IFX_ASCLIN_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN23 */
#define IFX_ASCLIN_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN23 */
#define IFX_ASCLIN_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN23 */
#define IFX_ASCLIN_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN24 */
#define IFX_ASCLIN_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN24 */
#define IFX_ASCLIN_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN24 */
#define IFX_ASCLIN_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN25 */
#define IFX_ASCLIN_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN25 */
#define IFX_ASCLIN_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN25 */
#define IFX_ASCLIN_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN26 */
#define IFX_ASCLIN_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN26 */
#define IFX_ASCLIN_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN26 */
#define IFX_ASCLIN_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN27 */
#define IFX_ASCLIN_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN27 */
#define IFX_ASCLIN_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN27 */
#define IFX_ASCLIN_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN28 */
#define IFX_ASCLIN_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN28 */
#define IFX_ASCLIN_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN28 */
#define IFX_ASCLIN_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN29 */
#define IFX_ASCLIN_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN29 */
#define IFX_ASCLIN_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN29 */
#define IFX_ASCLIN_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN2 */
#define IFX_ASCLIN_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN2 */
#define IFX_ASCLIN_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN2 */
#define IFX_ASCLIN_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN30 */
#define IFX_ASCLIN_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN30 */
#define IFX_ASCLIN_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN30 */
#define IFX_ASCLIN_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN31 */
#define IFX_ASCLIN_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN31 */
#define IFX_ASCLIN_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN31 */
#define IFX_ASCLIN_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN3 */
#define IFX_ASCLIN_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN3 */
#define IFX_ASCLIN_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN3 */
#define IFX_ASCLIN_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN4 */
#define IFX_ASCLIN_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN4 */
#define IFX_ASCLIN_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN4 */
#define IFX_ASCLIN_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN5 */
#define IFX_ASCLIN_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN5 */
#define IFX_ASCLIN_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN5 */
#define IFX_ASCLIN_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN6 */
#define IFX_ASCLIN_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN6 */
#define IFX_ASCLIN_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN6 */
#define IFX_ASCLIN_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN7 */
#define IFX_ASCLIN_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN7 */
#define IFX_ASCLIN_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN7 */
#define IFX_ASCLIN_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN8 */
#define IFX_ASCLIN_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN8 */
#define IFX_ASCLIN_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN8 */
#define IFX_ASCLIN_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_ASCLIN_ACCEN0_Bits.EN9 */
#define IFX_ASCLIN_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_ACCEN0_Bits.EN9 */
#define IFX_ASCLIN_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_ACCEN0_Bits.EN9 */
#define IFX_ASCLIN_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_ASCLIN_BITCON_Bits.OVERSAMPLING */
#define IFX_ASCLIN_BITCON_OVERSAMPLING_LEN (4u)

/** \brief  Mask for Ifx_ASCLIN_BITCON_Bits.OVERSAMPLING */
#define IFX_ASCLIN_BITCON_OVERSAMPLING_MSK (0xfu)

/** \brief  Offset for Ifx_ASCLIN_BITCON_Bits.OVERSAMPLING */
#define IFX_ASCLIN_BITCON_OVERSAMPLING_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_BITCON_Bits.PRESCALER */
#define IFX_ASCLIN_BITCON_PRESCALER_LEN (12u)

/** \brief  Mask for Ifx_ASCLIN_BITCON_Bits.PRESCALER */
#define IFX_ASCLIN_BITCON_PRESCALER_MSK (0xfffu)

/** \brief  Offset for Ifx_ASCLIN_BITCON_Bits.PRESCALER */
#define IFX_ASCLIN_BITCON_PRESCALER_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_BITCON_Bits.SAMPLEPOINT */
#define IFX_ASCLIN_BITCON_SAMPLEPOINT_LEN (4u)

/** \brief  Mask for Ifx_ASCLIN_BITCON_Bits.SAMPLEPOINT */
#define IFX_ASCLIN_BITCON_SAMPLEPOINT_MSK (0xfu)

/** \brief  Offset for Ifx_ASCLIN_BITCON_Bits.SAMPLEPOINT */
#define IFX_ASCLIN_BITCON_SAMPLEPOINT_OFF (24u)

/** \brief  Length for Ifx_ASCLIN_BITCON_Bits.SM */
#define IFX_ASCLIN_BITCON_SM_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_BITCON_Bits.SM */
#define IFX_ASCLIN_BITCON_SM_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_BITCON_Bits.SM */
#define IFX_ASCLIN_BITCON_SM_OFF (31u)

/** \brief  Length for Ifx_ASCLIN_BRD_Bits.LOWERLIMIT */
#define IFX_ASCLIN_BRD_LOWERLIMIT_LEN (8u)

/** \brief  Mask for Ifx_ASCLIN_BRD_Bits.LOWERLIMIT */
#define IFX_ASCLIN_BRD_LOWERLIMIT_MSK (0xffu)

/** \brief  Offset for Ifx_ASCLIN_BRD_Bits.LOWERLIMIT */
#define IFX_ASCLIN_BRD_LOWERLIMIT_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_BRD_Bits.MEASURED */
#define IFX_ASCLIN_BRD_MEASURED_LEN (12u)

/** \brief  Mask for Ifx_ASCLIN_BRD_Bits.MEASURED */
#define IFX_ASCLIN_BRD_MEASURED_MSK (0xfffu)

/** \brief  Offset for Ifx_ASCLIN_BRD_Bits.MEASURED */
#define IFX_ASCLIN_BRD_MEASURED_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_BRD_Bits.UPPERLIMIT */
#define IFX_ASCLIN_BRD_UPPERLIMIT_LEN (8u)

/** \brief  Mask for Ifx_ASCLIN_BRD_Bits.UPPERLIMIT */
#define IFX_ASCLIN_BRD_UPPERLIMIT_MSK (0xffu)

/** \brief  Offset for Ifx_ASCLIN_BRD_Bits.UPPERLIMIT */
#define IFX_ASCLIN_BRD_UPPERLIMIT_OFF (8u)

/** \brief  Length for Ifx_ASCLIN_BRG_Bits.DENOMINATOR */
#define IFX_ASCLIN_BRG_DENOMINATOR_LEN (12u)

/** \brief  Mask for Ifx_ASCLIN_BRG_Bits.DENOMINATOR */
#define IFX_ASCLIN_BRG_DENOMINATOR_MSK (0xfffu)

/** \brief  Offset for Ifx_ASCLIN_BRG_Bits.DENOMINATOR */
#define IFX_ASCLIN_BRG_DENOMINATOR_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_BRG_Bits.NUMERATOR */
#define IFX_ASCLIN_BRG_NUMERATOR_LEN (12u)

/** \brief  Mask for Ifx_ASCLIN_BRG_Bits.NUMERATOR */
#define IFX_ASCLIN_BRG_NUMERATOR_MSK (0xfffu)

/** \brief  Offset for Ifx_ASCLIN_BRG_Bits.NUMERATOR */
#define IFX_ASCLIN_BRG_NUMERATOR_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_CLC_Bits.DISR */
#define IFX_ASCLIN_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_CLC_Bits.DISR */
#define IFX_ASCLIN_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_CLC_Bits.DISR */
#define IFX_ASCLIN_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_CLC_Bits.DISS */
#define IFX_ASCLIN_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_CLC_Bits.DISS */
#define IFX_ASCLIN_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_CLC_Bits.DISS */
#define IFX_ASCLIN_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_ASCLIN_CLC_Bits.EDIS */
#define IFX_ASCLIN_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_CLC_Bits.EDIS */
#define IFX_ASCLIN_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_CLC_Bits.EDIS */
#define IFX_ASCLIN_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_ASCLIN_CSR_Bits.CLKSEL */
#define IFX_ASCLIN_CSR_CLKSEL_LEN (5u)

/** \brief  Mask for Ifx_ASCLIN_CSR_Bits.CLKSEL */
#define IFX_ASCLIN_CSR_CLKSEL_MSK (0x1fu)

/** \brief  Offset for Ifx_ASCLIN_CSR_Bits.CLKSEL */
#define IFX_ASCLIN_CSR_CLKSEL_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_CSR_Bits.CON */
#define IFX_ASCLIN_CSR_CON_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_CSR_Bits.CON */
#define IFX_ASCLIN_CSR_CON_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_CSR_Bits.CON */
#define IFX_ASCLIN_CSR_CON_OFF (31u)

/** \brief  Length for Ifx_ASCLIN_DATCON_Bits.CSM */
#define IFX_ASCLIN_DATCON_CSM_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_DATCON_Bits.CSM */
#define IFX_ASCLIN_DATCON_CSM_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_DATCON_Bits.CSM */
#define IFX_ASCLIN_DATCON_CSM_OFF (15u)

/** \brief  Length for Ifx_ASCLIN_DATCON_Bits.DATLEN */
#define IFX_ASCLIN_DATCON_DATLEN_LEN (4u)

/** \brief  Mask for Ifx_ASCLIN_DATCON_Bits.DATLEN */
#define IFX_ASCLIN_DATCON_DATLEN_MSK (0xfu)

/** \brief  Offset for Ifx_ASCLIN_DATCON_Bits.DATLEN */
#define IFX_ASCLIN_DATCON_DATLEN_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_DATCON_Bits.HO */
#define IFX_ASCLIN_DATCON_HO_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_DATCON_Bits.HO */
#define IFX_ASCLIN_DATCON_HO_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_DATCON_Bits.HO */
#define IFX_ASCLIN_DATCON_HO_OFF (13u)

/** \brief  Length for Ifx_ASCLIN_DATCON_Bits.RESPONSE */
#define IFX_ASCLIN_DATCON_RESPONSE_LEN (8u)

/** \brief  Mask for Ifx_ASCLIN_DATCON_Bits.RESPONSE */
#define IFX_ASCLIN_DATCON_RESPONSE_MSK (0xffu)

/** \brief  Offset for Ifx_ASCLIN_DATCON_Bits.RESPONSE */
#define IFX_ASCLIN_DATCON_RESPONSE_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_DATCON_Bits.RM */
#define IFX_ASCLIN_DATCON_RM_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_DATCON_Bits.RM */
#define IFX_ASCLIN_DATCON_RM_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_DATCON_Bits.RM */
#define IFX_ASCLIN_DATCON_RM_OFF (14u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.BD */
#define IFX_ASCLIN_FLAGS_BD_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.BD */
#define IFX_ASCLIN_FLAGS_BD_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.BD */
#define IFX_ASCLIN_FLAGS_BD_OFF (21u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.CE */
#define IFX_ASCLIN_FLAGS_CE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.CE */
#define IFX_ASCLIN_FLAGS_CE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.CE */
#define IFX_ASCLIN_FLAGS_CE_OFF (25u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.FE */
#define IFX_ASCLIN_FLAGS_FE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.FE */
#define IFX_ASCLIN_FLAGS_FE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.FE */
#define IFX_ASCLIN_FLAGS_FE_OFF (18u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.FED */
#define IFX_ASCLIN_FLAGS_FED_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.FED */
#define IFX_ASCLIN_FLAGS_FED_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.FED */
#define IFX_ASCLIN_FLAGS_FED_OFF (5u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.HT */
#define IFX_ASCLIN_FLAGS_HT_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.HT */
#define IFX_ASCLIN_FLAGS_HT_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.HT */
#define IFX_ASCLIN_FLAGS_HT_OFF (19u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.LA */
#define IFX_ASCLIN_FLAGS_LA_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.LA */
#define IFX_ASCLIN_FLAGS_LA_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.LA */
#define IFX_ASCLIN_FLAGS_LA_OFF (23u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.LC */
#define IFX_ASCLIN_FLAGS_LC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.LC */
#define IFX_ASCLIN_FLAGS_LC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.LC */
#define IFX_ASCLIN_FLAGS_LC_OFF (24u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.LP */
#define IFX_ASCLIN_FLAGS_LP_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.LP */
#define IFX_ASCLIN_FLAGS_LP_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.LP */
#define IFX_ASCLIN_FLAGS_LP_OFF (22u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.PE */
#define IFX_ASCLIN_FLAGS_PE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.PE */
#define IFX_ASCLIN_FLAGS_PE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.PE */
#define IFX_ASCLIN_FLAGS_PE_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.RED */
#define IFX_ASCLIN_FLAGS_RED_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.RED */
#define IFX_ASCLIN_FLAGS_RED_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.RED */
#define IFX_ASCLIN_FLAGS_RED_OFF (6u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.RFL */
#define IFX_ASCLIN_FLAGS_RFL_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.RFL */
#define IFX_ASCLIN_FLAGS_RFL_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.RFL */
#define IFX_ASCLIN_FLAGS_RFL_OFF (28u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.RFO */
#define IFX_ASCLIN_FLAGS_RFO_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.RFO */
#define IFX_ASCLIN_FLAGS_RFO_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.RFO */
#define IFX_ASCLIN_FLAGS_RFO_OFF (26u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.RFU */
#define IFX_ASCLIN_FLAGS_RFU_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.RFU */
#define IFX_ASCLIN_FLAGS_RFU_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.RFU */
#define IFX_ASCLIN_FLAGS_RFU_OFF (27u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.RH */
#define IFX_ASCLIN_FLAGS_RH_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.RH */
#define IFX_ASCLIN_FLAGS_RH_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.RH */
#define IFX_ASCLIN_FLAGS_RH_OFF (2u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.RR */
#define IFX_ASCLIN_FLAGS_RR_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.RR */
#define IFX_ASCLIN_FLAGS_RR_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.RR */
#define IFX_ASCLIN_FLAGS_RR_OFF (3u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.RT */
#define IFX_ASCLIN_FLAGS_RT_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.RT */
#define IFX_ASCLIN_FLAGS_RT_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.RT */
#define IFX_ASCLIN_FLAGS_RT_OFF (20u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.TC */
#define IFX_ASCLIN_FLAGS_TC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.TC */
#define IFX_ASCLIN_FLAGS_TC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.TC */
#define IFX_ASCLIN_FLAGS_TC_OFF (17u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.TFL */
#define IFX_ASCLIN_FLAGS_TFL_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.TFL */
#define IFX_ASCLIN_FLAGS_TFL_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.TFL */
#define IFX_ASCLIN_FLAGS_TFL_OFF (31u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.TFO */
#define IFX_ASCLIN_FLAGS_TFO_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.TFO */
#define IFX_ASCLIN_FLAGS_TFO_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.TFO */
#define IFX_ASCLIN_FLAGS_TFO_OFF (30u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.TH */
#define IFX_ASCLIN_FLAGS_TH_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.TH */
#define IFX_ASCLIN_FLAGS_TH_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.TH */
#define IFX_ASCLIN_FLAGS_TH_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.THRQ */
#define IFX_ASCLIN_FLAGS_THRQ_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.THRQ */
#define IFX_ASCLIN_FLAGS_THRQ_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.THRQ */
#define IFX_ASCLIN_FLAGS_THRQ_OFF (14u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.TR */
#define IFX_ASCLIN_FLAGS_TR_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.TR */
#define IFX_ASCLIN_FLAGS_TR_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.TR */
#define IFX_ASCLIN_FLAGS_TR_OFF (1u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.TRRQ */
#define IFX_ASCLIN_FLAGS_TRRQ_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.TRRQ */
#define IFX_ASCLIN_FLAGS_TRRQ_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.TRRQ */
#define IFX_ASCLIN_FLAGS_TRRQ_OFF (15u)

/** \brief  Length for Ifx_ASCLIN_FLAGS_Bits.TWRQ */
#define IFX_ASCLIN_FLAGS_TWRQ_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGS_Bits.TWRQ */
#define IFX_ASCLIN_FLAGS_TWRQ_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGS_Bits.TWRQ */
#define IFX_ASCLIN_FLAGS_TWRQ_OFF (13u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.BDC */
#define IFX_ASCLIN_FLAGSCLEAR_BDC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.BDC */
#define IFX_ASCLIN_FLAGSCLEAR_BDC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.BDC */
#define IFX_ASCLIN_FLAGSCLEAR_BDC_OFF (21u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.CEC */
#define IFX_ASCLIN_FLAGSCLEAR_CEC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.CEC */
#define IFX_ASCLIN_FLAGSCLEAR_CEC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.CEC */
#define IFX_ASCLIN_FLAGSCLEAR_CEC_OFF (25u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.FEC */
#define IFX_ASCLIN_FLAGSCLEAR_FEC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.FEC */
#define IFX_ASCLIN_FLAGSCLEAR_FEC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.FEC */
#define IFX_ASCLIN_FLAGSCLEAR_FEC_OFF (18u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.FEDC */
#define IFX_ASCLIN_FLAGSCLEAR_FEDC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.FEDC */
#define IFX_ASCLIN_FLAGSCLEAR_FEDC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.FEDC */
#define IFX_ASCLIN_FLAGSCLEAR_FEDC_OFF (5u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.HTC */
#define IFX_ASCLIN_FLAGSCLEAR_HTC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.HTC */
#define IFX_ASCLIN_FLAGSCLEAR_HTC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.HTC */
#define IFX_ASCLIN_FLAGSCLEAR_HTC_OFF (19u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.LAC */
#define IFX_ASCLIN_FLAGSCLEAR_LAC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.LAC */
#define IFX_ASCLIN_FLAGSCLEAR_LAC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.LAC */
#define IFX_ASCLIN_FLAGSCLEAR_LAC_OFF (23u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.LCC */
#define IFX_ASCLIN_FLAGSCLEAR_LCC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.LCC */
#define IFX_ASCLIN_FLAGSCLEAR_LCC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.LCC */
#define IFX_ASCLIN_FLAGSCLEAR_LCC_OFF (24u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.LPC */
#define IFX_ASCLIN_FLAGSCLEAR_LPC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.LPC */
#define IFX_ASCLIN_FLAGSCLEAR_LPC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.LPC */
#define IFX_ASCLIN_FLAGSCLEAR_LPC_OFF (22u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.PEC */
#define IFX_ASCLIN_FLAGSCLEAR_PEC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.PEC */
#define IFX_ASCLIN_FLAGSCLEAR_PEC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.PEC */
#define IFX_ASCLIN_FLAGSCLEAR_PEC_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.REDC */
#define IFX_ASCLIN_FLAGSCLEAR_REDC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.REDC */
#define IFX_ASCLIN_FLAGSCLEAR_REDC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.REDC */
#define IFX_ASCLIN_FLAGSCLEAR_REDC_OFF (6u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.RFLC */
#define IFX_ASCLIN_FLAGSCLEAR_RFLC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.RFLC */
#define IFX_ASCLIN_FLAGSCLEAR_RFLC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.RFLC */
#define IFX_ASCLIN_FLAGSCLEAR_RFLC_OFF (28u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.RFOC */
#define IFX_ASCLIN_FLAGSCLEAR_RFOC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.RFOC */
#define IFX_ASCLIN_FLAGSCLEAR_RFOC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.RFOC */
#define IFX_ASCLIN_FLAGSCLEAR_RFOC_OFF (26u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.RFUC */
#define IFX_ASCLIN_FLAGSCLEAR_RFUC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.RFUC */
#define IFX_ASCLIN_FLAGSCLEAR_RFUC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.RFUC */
#define IFX_ASCLIN_FLAGSCLEAR_RFUC_OFF (27u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.RHC */
#define IFX_ASCLIN_FLAGSCLEAR_RHC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.RHC */
#define IFX_ASCLIN_FLAGSCLEAR_RHC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.RHC */
#define IFX_ASCLIN_FLAGSCLEAR_RHC_OFF (2u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.RRC */
#define IFX_ASCLIN_FLAGSCLEAR_RRC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.RRC */
#define IFX_ASCLIN_FLAGSCLEAR_RRC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.RRC */
#define IFX_ASCLIN_FLAGSCLEAR_RRC_OFF (3u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.RTC */
#define IFX_ASCLIN_FLAGSCLEAR_RTC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.RTC */
#define IFX_ASCLIN_FLAGSCLEAR_RTC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.RTC */
#define IFX_ASCLIN_FLAGSCLEAR_RTC_OFF (20u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.TCC */
#define IFX_ASCLIN_FLAGSCLEAR_TCC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.TCC */
#define IFX_ASCLIN_FLAGSCLEAR_TCC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.TCC */
#define IFX_ASCLIN_FLAGSCLEAR_TCC_OFF (17u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.TFLC */
#define IFX_ASCLIN_FLAGSCLEAR_TFLC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.TFLC */
#define IFX_ASCLIN_FLAGSCLEAR_TFLC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.TFLC */
#define IFX_ASCLIN_FLAGSCLEAR_TFLC_OFF (31u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.TFOC */
#define IFX_ASCLIN_FLAGSCLEAR_TFOC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.TFOC */
#define IFX_ASCLIN_FLAGSCLEAR_TFOC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.TFOC */
#define IFX_ASCLIN_FLAGSCLEAR_TFOC_OFF (30u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.THC */
#define IFX_ASCLIN_FLAGSCLEAR_THC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.THC */
#define IFX_ASCLIN_FLAGSCLEAR_THC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.THC */
#define IFX_ASCLIN_FLAGSCLEAR_THC_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.THRQC */
#define IFX_ASCLIN_FLAGSCLEAR_THRQC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.THRQC */
#define IFX_ASCLIN_FLAGSCLEAR_THRQC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.THRQC */
#define IFX_ASCLIN_FLAGSCLEAR_THRQC_OFF (14u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.TRC */
#define IFX_ASCLIN_FLAGSCLEAR_TRC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.TRC */
#define IFX_ASCLIN_FLAGSCLEAR_TRC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.TRC */
#define IFX_ASCLIN_FLAGSCLEAR_TRC_OFF (1u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.TRRQC */
#define IFX_ASCLIN_FLAGSCLEAR_TRRQC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.TRRQC */
#define IFX_ASCLIN_FLAGSCLEAR_TRRQC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.TRRQC */
#define IFX_ASCLIN_FLAGSCLEAR_TRRQC_OFF (15u)

/** \brief  Length for Ifx_ASCLIN_FLAGSCLEAR_Bits.TWRQC */
#define IFX_ASCLIN_FLAGSCLEAR_TWRQC_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSCLEAR_Bits.TWRQC */
#define IFX_ASCLIN_FLAGSCLEAR_TWRQC_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSCLEAR_Bits.TWRQC */
#define IFX_ASCLIN_FLAGSCLEAR_TWRQC_OFF (13u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.ABE */
#define IFX_ASCLIN_FLAGSENABLE_ABE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.ABE */
#define IFX_ASCLIN_FLAGSENABLE_ABE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.ABE */
#define IFX_ASCLIN_FLAGSENABLE_ABE_OFF (23u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.BDE */
#define IFX_ASCLIN_FLAGSENABLE_BDE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.BDE */
#define IFX_ASCLIN_FLAGSENABLE_BDE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.BDE */
#define IFX_ASCLIN_FLAGSENABLE_BDE_OFF (21u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.CEE */
#define IFX_ASCLIN_FLAGSENABLE_CEE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.CEE */
#define IFX_ASCLIN_FLAGSENABLE_CEE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.CEE */
#define IFX_ASCLIN_FLAGSENABLE_CEE_OFF (25u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.FEDE */
#define IFX_ASCLIN_FLAGSENABLE_FEDE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.FEDE */
#define IFX_ASCLIN_FLAGSENABLE_FEDE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.FEDE */
#define IFX_ASCLIN_FLAGSENABLE_FEDE_OFF (5u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.FEE */
#define IFX_ASCLIN_FLAGSENABLE_FEE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.FEE */
#define IFX_ASCLIN_FLAGSENABLE_FEE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.FEE */
#define IFX_ASCLIN_FLAGSENABLE_FEE_OFF (18u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.HTE */
#define IFX_ASCLIN_FLAGSENABLE_HTE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.HTE */
#define IFX_ASCLIN_FLAGSENABLE_HTE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.HTE */
#define IFX_ASCLIN_FLAGSENABLE_HTE_OFF (19u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.LCE */
#define IFX_ASCLIN_FLAGSENABLE_LCE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.LCE */
#define IFX_ASCLIN_FLAGSENABLE_LCE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.LCE */
#define IFX_ASCLIN_FLAGSENABLE_LCE_OFF (24u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.LPE */
#define IFX_ASCLIN_FLAGSENABLE_LPE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.LPE */
#define IFX_ASCLIN_FLAGSENABLE_LPE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.LPE */
#define IFX_ASCLIN_FLAGSENABLE_LPE_OFF (22u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.PEE */
#define IFX_ASCLIN_FLAGSENABLE_PEE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.PEE */
#define IFX_ASCLIN_FLAGSENABLE_PEE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.PEE */
#define IFX_ASCLIN_FLAGSENABLE_PEE_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.REDE */
#define IFX_ASCLIN_FLAGSENABLE_REDE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.REDE */
#define IFX_ASCLIN_FLAGSENABLE_REDE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.REDE */
#define IFX_ASCLIN_FLAGSENABLE_REDE_OFF (6u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.RFLE */
#define IFX_ASCLIN_FLAGSENABLE_RFLE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.RFLE */
#define IFX_ASCLIN_FLAGSENABLE_RFLE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.RFLE */
#define IFX_ASCLIN_FLAGSENABLE_RFLE_OFF (28u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.RFOE */
#define IFX_ASCLIN_FLAGSENABLE_RFOE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.RFOE */
#define IFX_ASCLIN_FLAGSENABLE_RFOE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.RFOE */
#define IFX_ASCLIN_FLAGSENABLE_RFOE_OFF (26u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.RFUE */
#define IFX_ASCLIN_FLAGSENABLE_RFUE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.RFUE */
#define IFX_ASCLIN_FLAGSENABLE_RFUE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.RFUE */
#define IFX_ASCLIN_FLAGSENABLE_RFUE_OFF (27u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.RHE */
#define IFX_ASCLIN_FLAGSENABLE_RHE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.RHE */
#define IFX_ASCLIN_FLAGSENABLE_RHE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.RHE */
#define IFX_ASCLIN_FLAGSENABLE_RHE_OFF (2u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.RRE */
#define IFX_ASCLIN_FLAGSENABLE_RRE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.RRE */
#define IFX_ASCLIN_FLAGSENABLE_RRE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.RRE */
#define IFX_ASCLIN_FLAGSENABLE_RRE_OFF (3u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.RTE */
#define IFX_ASCLIN_FLAGSENABLE_RTE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.RTE */
#define IFX_ASCLIN_FLAGSENABLE_RTE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.RTE */
#define IFX_ASCLIN_FLAGSENABLE_RTE_OFF (20u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.TCE */
#define IFX_ASCLIN_FLAGSENABLE_TCE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.TCE */
#define IFX_ASCLIN_FLAGSENABLE_TCE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.TCE */
#define IFX_ASCLIN_FLAGSENABLE_TCE_OFF (17u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.TFLE */
#define IFX_ASCLIN_FLAGSENABLE_TFLE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.TFLE */
#define IFX_ASCLIN_FLAGSENABLE_TFLE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.TFLE */
#define IFX_ASCLIN_FLAGSENABLE_TFLE_OFF (31u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.TFOE */
#define IFX_ASCLIN_FLAGSENABLE_TFOE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.TFOE */
#define IFX_ASCLIN_FLAGSENABLE_TFOE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.TFOE */
#define IFX_ASCLIN_FLAGSENABLE_TFOE_OFF (30u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.THE */
#define IFX_ASCLIN_FLAGSENABLE_THE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.THE */
#define IFX_ASCLIN_FLAGSENABLE_THE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.THE */
#define IFX_ASCLIN_FLAGSENABLE_THE_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_FLAGSENABLE_Bits.TRE */
#define IFX_ASCLIN_FLAGSENABLE_TRE_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSENABLE_Bits.TRE */
#define IFX_ASCLIN_FLAGSENABLE_TRE_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSENABLE_Bits.TRE */
#define IFX_ASCLIN_FLAGSENABLE_TRE_OFF (1u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.BDS */
#define IFX_ASCLIN_FLAGSSET_BDS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.BDS */
#define IFX_ASCLIN_FLAGSSET_BDS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.BDS */
#define IFX_ASCLIN_FLAGSSET_BDS_OFF (21u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.CES */
#define IFX_ASCLIN_FLAGSSET_CES_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.CES */
#define IFX_ASCLIN_FLAGSSET_CES_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.CES */
#define IFX_ASCLIN_FLAGSSET_CES_OFF (25u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.FEDS */
#define IFX_ASCLIN_FLAGSSET_FEDS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.FEDS */
#define IFX_ASCLIN_FLAGSSET_FEDS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.FEDS */
#define IFX_ASCLIN_FLAGSSET_FEDS_OFF (5u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.FES */
#define IFX_ASCLIN_FLAGSSET_FES_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.FES */
#define IFX_ASCLIN_FLAGSSET_FES_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.FES */
#define IFX_ASCLIN_FLAGSSET_FES_OFF (18u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.HTS */
#define IFX_ASCLIN_FLAGSSET_HTS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.HTS */
#define IFX_ASCLIN_FLAGSSET_HTS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.HTS */
#define IFX_ASCLIN_FLAGSSET_HTS_OFF (19u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.LAS */
#define IFX_ASCLIN_FLAGSSET_LAS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.LAS */
#define IFX_ASCLIN_FLAGSSET_LAS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.LAS */
#define IFX_ASCLIN_FLAGSSET_LAS_OFF (23u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.LCS */
#define IFX_ASCLIN_FLAGSSET_LCS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.LCS */
#define IFX_ASCLIN_FLAGSSET_LCS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.LCS */
#define IFX_ASCLIN_FLAGSSET_LCS_OFF (24u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.LPS */
#define IFX_ASCLIN_FLAGSSET_LPS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.LPS */
#define IFX_ASCLIN_FLAGSSET_LPS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.LPS */
#define IFX_ASCLIN_FLAGSSET_LPS_OFF (22u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.PES */
#define IFX_ASCLIN_FLAGSSET_PES_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.PES */
#define IFX_ASCLIN_FLAGSSET_PES_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.PES */
#define IFX_ASCLIN_FLAGSSET_PES_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.REDS */
#define IFX_ASCLIN_FLAGSSET_REDS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.REDS */
#define IFX_ASCLIN_FLAGSSET_REDS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.REDS */
#define IFX_ASCLIN_FLAGSSET_REDS_OFF (6u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.RFLS */
#define IFX_ASCLIN_FLAGSSET_RFLS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.RFLS */
#define IFX_ASCLIN_FLAGSSET_RFLS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.RFLS */
#define IFX_ASCLIN_FLAGSSET_RFLS_OFF (28u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.RFOS */
#define IFX_ASCLIN_FLAGSSET_RFOS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.RFOS */
#define IFX_ASCLIN_FLAGSSET_RFOS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.RFOS */
#define IFX_ASCLIN_FLAGSSET_RFOS_OFF (26u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.RFUS */
#define IFX_ASCLIN_FLAGSSET_RFUS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.RFUS */
#define IFX_ASCLIN_FLAGSSET_RFUS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.RFUS */
#define IFX_ASCLIN_FLAGSSET_RFUS_OFF (27u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.RHS */
#define IFX_ASCLIN_FLAGSSET_RHS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.RHS */
#define IFX_ASCLIN_FLAGSSET_RHS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.RHS */
#define IFX_ASCLIN_FLAGSSET_RHS_OFF (2u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.RRS */
#define IFX_ASCLIN_FLAGSSET_RRS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.RRS */
#define IFX_ASCLIN_FLAGSSET_RRS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.RRS */
#define IFX_ASCLIN_FLAGSSET_RRS_OFF (3u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.RTS */
#define IFX_ASCLIN_FLAGSSET_RTS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.RTS */
#define IFX_ASCLIN_FLAGSSET_RTS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.RTS */
#define IFX_ASCLIN_FLAGSSET_RTS_OFF (20u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.TCS */
#define IFX_ASCLIN_FLAGSSET_TCS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.TCS */
#define IFX_ASCLIN_FLAGSSET_TCS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.TCS */
#define IFX_ASCLIN_FLAGSSET_TCS_OFF (17u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.TFLS */
#define IFX_ASCLIN_FLAGSSET_TFLS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.TFLS */
#define IFX_ASCLIN_FLAGSSET_TFLS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.TFLS */
#define IFX_ASCLIN_FLAGSSET_TFLS_OFF (31u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.TFOS */
#define IFX_ASCLIN_FLAGSSET_TFOS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.TFOS */
#define IFX_ASCLIN_FLAGSSET_TFOS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.TFOS */
#define IFX_ASCLIN_FLAGSSET_TFOS_OFF (30u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.THRQS */
#define IFX_ASCLIN_FLAGSSET_THRQS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.THRQS */
#define IFX_ASCLIN_FLAGSSET_THRQS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.THRQS */
#define IFX_ASCLIN_FLAGSSET_THRQS_OFF (14u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.THS */
#define IFX_ASCLIN_FLAGSSET_THS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.THS */
#define IFX_ASCLIN_FLAGSSET_THS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.THS */
#define IFX_ASCLIN_FLAGSSET_THS_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.TRRQS */
#define IFX_ASCLIN_FLAGSSET_TRRQS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.TRRQS */
#define IFX_ASCLIN_FLAGSSET_TRRQS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.TRRQS */
#define IFX_ASCLIN_FLAGSSET_TRRQS_OFF (15u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.TRS */
#define IFX_ASCLIN_FLAGSSET_TRS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.TRS */
#define IFX_ASCLIN_FLAGSSET_TRS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.TRS */
#define IFX_ASCLIN_FLAGSSET_TRS_OFF (1u)

/** \brief  Length for Ifx_ASCLIN_FLAGSSET_Bits.TWRQS */
#define IFX_ASCLIN_FLAGSSET_TWRQS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FLAGSSET_Bits.TWRQS */
#define IFX_ASCLIN_FLAGSSET_TWRQS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FLAGSSET_Bits.TWRQS */
#define IFX_ASCLIN_FLAGSSET_TWRQS_OFF (13u)

/** \brief  Length for Ifx_ASCLIN_FRAMECON_Bits.CEN */
#define IFX_ASCLIN_FRAMECON_CEN_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FRAMECON_Bits.CEN */
#define IFX_ASCLIN_FRAMECON_CEN_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FRAMECON_Bits.CEN */
#define IFX_ASCLIN_FRAMECON_CEN_OFF (29u)

/** \brief  Length for Ifx_ASCLIN_FRAMECON_Bits.IDLE */
#define IFX_ASCLIN_FRAMECON_IDLE_LEN (3u)

/** \brief  Mask for Ifx_ASCLIN_FRAMECON_Bits.IDLE */
#define IFX_ASCLIN_FRAMECON_IDLE_MSK (0x7u)

/** \brief  Offset for Ifx_ASCLIN_FRAMECON_Bits.IDLE */
#define IFX_ASCLIN_FRAMECON_IDLE_OFF (6u)

/** \brief  Length for Ifx_ASCLIN_FRAMECON_Bits.LEAD */
#define IFX_ASCLIN_FRAMECON_LEAD_LEN (3u)

/** \brief  Mask for Ifx_ASCLIN_FRAMECON_Bits.LEAD */
#define IFX_ASCLIN_FRAMECON_LEAD_MSK (0x7u)

/** \brief  Offset for Ifx_ASCLIN_FRAMECON_Bits.LEAD */
#define IFX_ASCLIN_FRAMECON_LEAD_OFF (12u)

/** \brief  Length for Ifx_ASCLIN_FRAMECON_Bits.MODE */
#define IFX_ASCLIN_FRAMECON_MODE_LEN (2u)

/** \brief  Mask for Ifx_ASCLIN_FRAMECON_Bits.MODE */
#define IFX_ASCLIN_FRAMECON_MODE_MSK (0x3u)

/** \brief  Offset for Ifx_ASCLIN_FRAMECON_Bits.MODE */
#define IFX_ASCLIN_FRAMECON_MODE_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_FRAMECON_Bits.MSB */
#define IFX_ASCLIN_FRAMECON_MSB_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FRAMECON_Bits.MSB */
#define IFX_ASCLIN_FRAMECON_MSB_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FRAMECON_Bits.MSB */
#define IFX_ASCLIN_FRAMECON_MSB_OFF (28u)

/** \brief  Length for Ifx_ASCLIN_FRAMECON_Bits.ODD */
#define IFX_ASCLIN_FRAMECON_ODD_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FRAMECON_Bits.ODD */
#define IFX_ASCLIN_FRAMECON_ODD_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FRAMECON_Bits.ODD */
#define IFX_ASCLIN_FRAMECON_ODD_OFF (31u)

/** \brief  Length for Ifx_ASCLIN_FRAMECON_Bits.PEN */
#define IFX_ASCLIN_FRAMECON_PEN_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_FRAMECON_Bits.PEN */
#define IFX_ASCLIN_FRAMECON_PEN_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_FRAMECON_Bits.PEN */
#define IFX_ASCLIN_FRAMECON_PEN_OFF (30u)

/** \brief  Length for Ifx_ASCLIN_FRAMECON_Bits.STOP */
#define IFX_ASCLIN_FRAMECON_STOP_LEN (3u)

/** \brief  Mask for Ifx_ASCLIN_FRAMECON_Bits.STOP */
#define IFX_ASCLIN_FRAMECON_STOP_MSK (0x7u)

/** \brief  Offset for Ifx_ASCLIN_FRAMECON_Bits.STOP */
#define IFX_ASCLIN_FRAMECON_STOP_OFF (9u)

/** \brief  Length for Ifx_ASCLIN_ID_Bits.MODNUMBER */
#define IFX_ASCLIN_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_ASCLIN_ID_Bits.MODNUMBER */
#define IFX_ASCLIN_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_ASCLIN_ID_Bits.MODNUMBER */
#define IFX_ASCLIN_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_ID_Bits.MODREV */
#define IFX_ASCLIN_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_ASCLIN_ID_Bits.MODREV */
#define IFX_ASCLIN_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_ASCLIN_ID_Bits.MODREV */
#define IFX_ASCLIN_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_ID_Bits.MODTYPE */
#define IFX_ASCLIN_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_ASCLIN_ID_Bits.MODTYPE */
#define IFX_ASCLIN_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_ASCLIN_ID_Bits.MODTYPE */
#define IFX_ASCLIN_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_ASCLIN_IOCR_Bits.ALTI */
#define IFX_ASCLIN_IOCR_ALTI_LEN (3u)

/** \brief  Mask for Ifx_ASCLIN_IOCR_Bits.ALTI */
#define IFX_ASCLIN_IOCR_ALTI_MSK (0x7u)

/** \brief  Offset for Ifx_ASCLIN_IOCR_Bits.ALTI */
#define IFX_ASCLIN_IOCR_ALTI_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_IOCR_Bits.CPOL */
#define IFX_ASCLIN_IOCR_CPOL_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_IOCR_Bits.CPOL */
#define IFX_ASCLIN_IOCR_CPOL_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_IOCR_Bits.CPOL */
#define IFX_ASCLIN_IOCR_CPOL_OFF (26u)

/** \brief  Length for Ifx_ASCLIN_IOCR_Bits.CTS */
#define IFX_ASCLIN_IOCR_CTS_LEN (2u)

/** \brief  Mask for Ifx_ASCLIN_IOCR_Bits.CTS */
#define IFX_ASCLIN_IOCR_CTS_MSK (0x3u)

/** \brief  Offset for Ifx_ASCLIN_IOCR_Bits.CTS */
#define IFX_ASCLIN_IOCR_CTS_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_IOCR_Bits.CTSEN */
#define IFX_ASCLIN_IOCR_CTSEN_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_IOCR_Bits.CTSEN */
#define IFX_ASCLIN_IOCR_CTSEN_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_IOCR_Bits.CTSEN */
#define IFX_ASCLIN_IOCR_CTSEN_OFF (29u)

/** \brief  Length for Ifx_ASCLIN_IOCR_Bits.DEPTH */
#define IFX_ASCLIN_IOCR_DEPTH_LEN (6u)

/** \brief  Mask for Ifx_ASCLIN_IOCR_Bits.DEPTH */
#define IFX_ASCLIN_IOCR_DEPTH_MSK (0x3fu)

/** \brief  Offset for Ifx_ASCLIN_IOCR_Bits.DEPTH */
#define IFX_ASCLIN_IOCR_DEPTH_OFF (4u)

/** \brief  Length for Ifx_ASCLIN_IOCR_Bits.LB */
#define IFX_ASCLIN_IOCR_LB_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_IOCR_Bits.LB */
#define IFX_ASCLIN_IOCR_LB_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_IOCR_Bits.LB */
#define IFX_ASCLIN_IOCR_LB_OFF (28u)

/** \brief  Length for Ifx_ASCLIN_IOCR_Bits.RCPOL */
#define IFX_ASCLIN_IOCR_RCPOL_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_IOCR_Bits.RCPOL */
#define IFX_ASCLIN_IOCR_RCPOL_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_IOCR_Bits.RCPOL */
#define IFX_ASCLIN_IOCR_RCPOL_OFF (25u)

/** \brief  Length for Ifx_ASCLIN_IOCR_Bits.RXM */
#define IFX_ASCLIN_IOCR_RXM_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_IOCR_Bits.RXM */
#define IFX_ASCLIN_IOCR_RXM_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_IOCR_Bits.RXM */
#define IFX_ASCLIN_IOCR_RXM_OFF (30u)

/** \brief  Length for Ifx_ASCLIN_IOCR_Bits.SPOL */
#define IFX_ASCLIN_IOCR_SPOL_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_IOCR_Bits.SPOL */
#define IFX_ASCLIN_IOCR_SPOL_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_IOCR_Bits.SPOL */
#define IFX_ASCLIN_IOCR_SPOL_OFF (27u)

/** \brief  Length for Ifx_ASCLIN_IOCR_Bits.TXM */
#define IFX_ASCLIN_IOCR_TXM_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_IOCR_Bits.TXM */
#define IFX_ASCLIN_IOCR_TXM_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_IOCR_Bits.TXM */
#define IFX_ASCLIN_IOCR_TXM_OFF (31u)

/** \brief  Length for Ifx_ASCLIN_KRST0_Bits.RST */
#define IFX_ASCLIN_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_KRST0_Bits.RST */
#define IFX_ASCLIN_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_KRST0_Bits.RST */
#define IFX_ASCLIN_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_KRST0_Bits.RSTSTAT */
#define IFX_ASCLIN_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_KRST0_Bits.RSTSTAT */
#define IFX_ASCLIN_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_KRST0_Bits.RSTSTAT */
#define IFX_ASCLIN_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_ASCLIN_KRST1_Bits.RST */
#define IFX_ASCLIN_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_KRST1_Bits.RST */
#define IFX_ASCLIN_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_KRST1_Bits.RST */
#define IFX_ASCLIN_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_KRSTCLR_Bits.CLR */
#define IFX_ASCLIN_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_KRSTCLR_Bits.CLR */
#define IFX_ASCLIN_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_KRSTCLR_Bits.CLR */
#define IFX_ASCLIN_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_LIN_BTIMER_Bits.BREAK */
#define IFX_ASCLIN_LIN_BTIMER_BREAK_LEN (6u)

/** \brief  Mask for Ifx_ASCLIN_LIN_BTIMER_Bits.BREAK */
#define IFX_ASCLIN_LIN_BTIMER_BREAK_MSK (0x3fu)

/** \brief  Offset for Ifx_ASCLIN_LIN_BTIMER_Bits.BREAK */
#define IFX_ASCLIN_LIN_BTIMER_BREAK_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_LIN_CON_Bits.ABD */
#define IFX_ASCLIN_LIN_CON_ABD_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_LIN_CON_Bits.ABD */
#define IFX_ASCLIN_LIN_CON_ABD_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_LIN_CON_Bits.ABD */
#define IFX_ASCLIN_LIN_CON_ABD_OFF (27u)

/** \brief  Length for Ifx_ASCLIN_LIN_CON_Bits.CSEN */
#define IFX_ASCLIN_LIN_CON_CSEN_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_LIN_CON_Bits.CSEN */
#define IFX_ASCLIN_LIN_CON_CSEN_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_LIN_CON_Bits.CSEN */
#define IFX_ASCLIN_LIN_CON_CSEN_OFF (25u)

/** \brief  Length for Ifx_ASCLIN_LIN_CON_Bits.CSI */
#define IFX_ASCLIN_LIN_CON_CSI_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_LIN_CON_Bits.CSI */
#define IFX_ASCLIN_LIN_CON_CSI_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_LIN_CON_Bits.CSI */
#define IFX_ASCLIN_LIN_CON_CSI_OFF (23u)

/** \brief  Length for Ifx_ASCLIN_LIN_CON_Bits.MS */
#define IFX_ASCLIN_LIN_CON_MS_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_LIN_CON_Bits.MS */
#define IFX_ASCLIN_LIN_CON_MS_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_LIN_CON_Bits.MS */
#define IFX_ASCLIN_LIN_CON_MS_OFF (26u)

/** \brief  Length for Ifx_ASCLIN_LIN_HTIMER_Bits.HEADER */
#define IFX_ASCLIN_LIN_HTIMER_HEADER_LEN (8u)

/** \brief  Mask for Ifx_ASCLIN_LIN_HTIMER_Bits.HEADER */
#define IFX_ASCLIN_LIN_HTIMER_HEADER_MSK (0xffu)

/** \brief  Offset for Ifx_ASCLIN_LIN_HTIMER_Bits.HEADER */
#define IFX_ASCLIN_LIN_HTIMER_HEADER_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_OCS_Bits.SUS */
#define IFX_ASCLIN_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_ASCLIN_OCS_Bits.SUS */
#define IFX_ASCLIN_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_ASCLIN_OCS_Bits.SUS */
#define IFX_ASCLIN_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_ASCLIN_OCS_Bits.SUS_P */
#define IFX_ASCLIN_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_OCS_Bits.SUS_P */
#define IFX_ASCLIN_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_OCS_Bits.SUS_P */
#define IFX_ASCLIN_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_ASCLIN_OCS_Bits.SUSSTA */
#define IFX_ASCLIN_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_OCS_Bits.SUSSTA */
#define IFX_ASCLIN_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_OCS_Bits.SUSSTA */
#define IFX_ASCLIN_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_ASCLIN_RXDATA_Bits.DATA */
#define IFX_ASCLIN_RXDATA_DATA_LEN (32u)

/** \brief  Mask for Ifx_ASCLIN_RXDATA_Bits.DATA */
#define IFX_ASCLIN_RXDATA_DATA_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ASCLIN_RXDATA_Bits.DATA */
#define IFX_ASCLIN_RXDATA_DATA_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_RXDATAD_Bits.DATA */
#define IFX_ASCLIN_RXDATAD_DATA_LEN (32u)

/** \brief  Mask for Ifx_ASCLIN_RXDATAD_Bits.DATA */
#define IFX_ASCLIN_RXDATAD_DATA_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ASCLIN_RXDATAD_Bits.DATA */
#define IFX_ASCLIN_RXDATAD_DATA_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_RXFIFOCON_Bits.BUF */
#define IFX_ASCLIN_RXFIFOCON_BUF_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_RXFIFOCON_Bits.BUF */
#define IFX_ASCLIN_RXFIFOCON_BUF_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_RXFIFOCON_Bits.BUF */
#define IFX_ASCLIN_RXFIFOCON_BUF_OFF (31u)

/** \brief  Length for Ifx_ASCLIN_RXFIFOCON_Bits.ENI */
#define IFX_ASCLIN_RXFIFOCON_ENI_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_RXFIFOCON_Bits.ENI */
#define IFX_ASCLIN_RXFIFOCON_ENI_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_RXFIFOCON_Bits.ENI */
#define IFX_ASCLIN_RXFIFOCON_ENI_OFF (1u)

/** \brief  Length for Ifx_ASCLIN_RXFIFOCON_Bits.FILL */
#define IFX_ASCLIN_RXFIFOCON_FILL_LEN (5u)

/** \brief  Mask for Ifx_ASCLIN_RXFIFOCON_Bits.FILL */
#define IFX_ASCLIN_RXFIFOCON_FILL_MSK (0x1fu)

/** \brief  Offset for Ifx_ASCLIN_RXFIFOCON_Bits.FILL */
#define IFX_ASCLIN_RXFIFOCON_FILL_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_RXFIFOCON_Bits.FLUSH */
#define IFX_ASCLIN_RXFIFOCON_FLUSH_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_RXFIFOCON_Bits.FLUSH */
#define IFX_ASCLIN_RXFIFOCON_FLUSH_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_RXFIFOCON_Bits.FLUSH */
#define IFX_ASCLIN_RXFIFOCON_FLUSH_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_RXFIFOCON_Bits.INTLEVEL */
#define IFX_ASCLIN_RXFIFOCON_INTLEVEL_LEN (4u)

/** \brief  Mask for Ifx_ASCLIN_RXFIFOCON_Bits.INTLEVEL */
#define IFX_ASCLIN_RXFIFOCON_INTLEVEL_MSK (0xfu)

/** \brief  Offset for Ifx_ASCLIN_RXFIFOCON_Bits.INTLEVEL */
#define IFX_ASCLIN_RXFIFOCON_INTLEVEL_OFF (8u)

/** \brief  Length for Ifx_ASCLIN_RXFIFOCON_Bits.OUTW */
#define IFX_ASCLIN_RXFIFOCON_OUTW_LEN (2u)

/** \brief  Mask for Ifx_ASCLIN_RXFIFOCON_Bits.OUTW */
#define IFX_ASCLIN_RXFIFOCON_OUTW_MSK (0x3u)

/** \brief  Offset for Ifx_ASCLIN_RXFIFOCON_Bits.OUTW */
#define IFX_ASCLIN_RXFIFOCON_OUTW_OFF (6u)

/** \brief  Length for Ifx_ASCLIN_TXDATA_Bits.DATA */
#define IFX_ASCLIN_TXDATA_DATA_LEN (32u)

/** \brief  Mask for Ifx_ASCLIN_TXDATA_Bits.DATA */
#define IFX_ASCLIN_TXDATA_DATA_MSK (0xffffffffu)

/** \brief  Offset for Ifx_ASCLIN_TXDATA_Bits.DATA */
#define IFX_ASCLIN_TXDATA_DATA_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_TXFIFOCON_Bits.ENO */
#define IFX_ASCLIN_TXFIFOCON_ENO_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_TXFIFOCON_Bits.ENO */
#define IFX_ASCLIN_TXFIFOCON_ENO_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_TXFIFOCON_Bits.ENO */
#define IFX_ASCLIN_TXFIFOCON_ENO_OFF (1u)

/** \brief  Length for Ifx_ASCLIN_TXFIFOCON_Bits.FILL */
#define IFX_ASCLIN_TXFIFOCON_FILL_LEN (5u)

/** \brief  Mask for Ifx_ASCLIN_TXFIFOCON_Bits.FILL */
#define IFX_ASCLIN_TXFIFOCON_FILL_MSK (0x1fu)

/** \brief  Offset for Ifx_ASCLIN_TXFIFOCON_Bits.FILL */
#define IFX_ASCLIN_TXFIFOCON_FILL_OFF (16u)

/** \brief  Length for Ifx_ASCLIN_TXFIFOCON_Bits.FLUSH */
#define IFX_ASCLIN_TXFIFOCON_FLUSH_LEN (1u)

/** \brief  Mask for Ifx_ASCLIN_TXFIFOCON_Bits.FLUSH */
#define IFX_ASCLIN_TXFIFOCON_FLUSH_MSK (0x1u)

/** \brief  Offset for Ifx_ASCLIN_TXFIFOCON_Bits.FLUSH */
#define IFX_ASCLIN_TXFIFOCON_FLUSH_OFF (0u)

/** \brief  Length for Ifx_ASCLIN_TXFIFOCON_Bits.INTLEVEL */
#define IFX_ASCLIN_TXFIFOCON_INTLEVEL_LEN (4u)

/** \brief  Mask for Ifx_ASCLIN_TXFIFOCON_Bits.INTLEVEL */
#define IFX_ASCLIN_TXFIFOCON_INTLEVEL_MSK (0xfu)

/** \brief  Offset for Ifx_ASCLIN_TXFIFOCON_Bits.INTLEVEL */
#define IFX_ASCLIN_TXFIFOCON_INTLEVEL_OFF (8u)

/** \brief  Length for Ifx_ASCLIN_TXFIFOCON_Bits.INW */
#define IFX_ASCLIN_TXFIFOCON_INW_LEN (2u)

/** \brief  Mask for Ifx_ASCLIN_TXFIFOCON_Bits.INW */
#define IFX_ASCLIN_TXFIFOCON_INW_MSK (0x3u)

/** \brief  Offset for Ifx_ASCLIN_TXFIFOCON_Bits.INW */
#define IFX_ASCLIN_TXFIFOCON_INW_OFF (6u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXASCLIN_BF_H */
