<?xml version='1.0'?>
<datamodel version="3.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/08/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/08/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd" >
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Irq" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Irq" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/AURIX/Irq"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPreCompile">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="IrqGeneral" type="IDENTIFIABLE">
                <d:var name="IrqOsekEnable" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ctr name="IrqGeneral" type="IDENTIFIABLE">
                  <d:var name="IrqOsekEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
              </d:ctr>
              <d:lst name="IrqAscLinConfig" type="MAP">
                <d:ctr name="IrqAscLinConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqAscLinCatConfig" type="IDENTIFIABLE">
                    <d:ctr name="IrqAscLinTxCatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAscLin0TxCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAscLin1TxCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAscLinRxCatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAscLin0RxCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAscLin1RxCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAscLinErrCatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAscLin0ErrorCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAscLin1ErrorCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IrqAscLinPrioConfig" type="IDENTIFIABLE">
                    <d:ctr name="IrqAscLinTxPrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAscLin0TxPrio" type="INTEGER" value="155"/>
                      <d:var name="IrqAscLin1TxPrio" type="INTEGER" value="156"/>
                    </d:ctr>
                    <d:ctr name="IrqAscLinRxPrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAscLin0RxPrio" type="INTEGER" value="157"/>
                      <d:var name="IrqAscLin1RxPrio" type="INTEGER" value="158"/>
                    </d:ctr>
                    <d:ctr name="IrqAscLinErrPrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAscLin0ErrorPrio" type="INTEGER" 
                             value="159"/>
                      <d:var name="IrqAscLin1ErrorPrio" type="INTEGER" 
                             value="160"/>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IrqAscLinTosConfig" type="IDENTIFIABLE">
                    <d:ctr name="IrqAscLinTxTosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAscLin0TxTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAscLin1TxTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAscLinRxTosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAscLin0RxTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAscLin1RxTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAscLinErrTosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAscLin0ErrTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAscLin1ErrTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqQspiConfig" type="MAP">
                <d:ctr name="IrqQspiConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqQspiCatConfig" type="IDENTIFIABLE">
                    <d:ctr name="IrqQspiTxCatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0TxCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi1TxCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi2TxCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3TxCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqQspiRxCatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0RxCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi1RxCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi2RxCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3RxCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqQspiErrCatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0ErrCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi1ErrCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi2ErrCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3ErrCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqQspiPTCatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0PTCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi1PTCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi2PTCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3PTCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqQspiHCCatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi2HCCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3HCCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqQspiUDCatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0UDCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi1UDCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi2UDCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3UDCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IrqQspiPrioConfig" type="IDENTIFIABLE">
                    <d:ctr name="IrqQspiTxPrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0TxPrio" type="INTEGER" value="1"/>
                      <d:var name="IrqQspi1TxPrio" type="INTEGER" value="4"/>
                      <d:var name="IrqQspi2TxPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3TxPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqQspiRxPrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0RxPrio" type="INTEGER" value="2"/>
                      <d:var name="IrqQspi1RxPrio" type="INTEGER" value="5"/>
                      <d:var name="IrqQspi2RxPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3RxPrio" type="INTEGER" value="3"/>
                    </d:ctr>
                    <d:ctr name="IrqQspiErrPrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0ErrPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi1ErrPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi2ErrPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3ErrPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqQspiPTPrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0PTPrio" type="INTEGER" value="181"/>
                      <d:var name="IrqQspi1PTPrio" type="INTEGER" value="177"/>
                      <d:var name="IrqQspi2PTPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3PTPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqQspiHCPrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi2HCPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3HCPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqQspiUDPrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0UDPrio" type="INTEGER" value="182"/>
                      <d:var name="IrqQspi1UDPrio" type="INTEGER" value="178"/>
                      <d:var name="IrqQspi2UDPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3UDPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IrqQspiTosConfig" type="IDENTIFIABLE">
                    <d:ctr name="IrqQspiTxTosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0TxTos" type="ENUMERATION" value="DMA"/>
                      <d:var name="IrqQspi1TxTos" type="ENUMERATION" value="DMA"/>
                      <d:var name="IrqQspi2TxTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3TxTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqQspiRxTosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0RxTos" type="ENUMERATION" value="DMA"/>
                      <d:var name="IrqQspi1RxTos" type="ENUMERATION" value="DMA"/>
                      <d:var name="IrqQspi2RxTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3RxTos" type="ENUMERATION" value="DMA"/>
                    </d:ctr>
                    <d:ctr name="IrqQspiErrTosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0ErrTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi1ErrTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi2ErrTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3ErrTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqQspiPTTosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0PTTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi1PTTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi2PTTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3PTTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqQspiHCTosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi2HCTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3HCTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqQspiUDTosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqQspi0UDTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi1UDTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi2UDTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqQspi3UDTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqCCU6Config" type="MAP">
                <d:ctr name="IrqCCU6Config_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqCCU60Config" type="IDENTIFIABLE">
                    <d:ctr name="IrqCCU60CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqCCU60SR0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU60SR1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU60SR2Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU60SR3Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqCCU60PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqCCU60SR0Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU60SR1Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU60SR2Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU60SR3Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqCCU60TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqCCU60SR0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU60SR1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU60SR2Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU60SR3Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IrqCCU61Config" type="IDENTIFIABLE">
                    <d:ctr name="IrqCCU61CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqCCU61SR0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU61SR1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU61SR2Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU61SR3Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqCCU61PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqCCU61SR0Prio" type="INTEGER" value="210"/>
                      <d:var name="IrqCCU61SR1Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU61SR2Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU61SR3Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqCCU61TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqCCU61SR0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU61SR1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU61SR2Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqCCU61SR3Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqGptConfig" type="MAP">
                <d:ctr name="IrqGptConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqGpt120Config" type="IDENTIFIABLE">
                    <d:ctr name="IrqGpt120CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGpt120CARPELCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T2Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T3Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T4Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T5Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T6Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGpt120PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGpt120CARPELPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T2Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T3Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T4Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T5Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T6Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGpt120TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGpt120CARPELTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T2Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T3Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T4Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T5Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGpt120T6Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqStmConfig" type="MAP">
                <d:ctr name="IrqStmConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqStm0Config" type="IDENTIFIABLE">
                    <d:ctr name="IrqStm0CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqStm0SR0Cat" type="ENUMERATION" 
                             value="CAT1"/>
                      <d:var name="IrqStm0SR1Cat" type="ENUMERATION" 
                             value="CAT1"/>
                    </d:ctr>
                    <d:ctr name="IrqStm0PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqStm0SR0Prio" type="INTEGER" value="80"/>
                      <d:var name="IrqStm0SR1Prio" type="INTEGER" value="81"/>
                    </d:ctr>
                    <d:ctr name="IrqStm0TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqStm0SR0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqStm0SR1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqDmaConfig" type="MAP">
                <d:ctr name="IrqDmaConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqDmaCatConfig" type="IDENTIFIABLE">
                    <d:var name="IrqDmaErrSRCat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh0SRCat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh1SRCat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh2SRCat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh3SRCat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh4SRCat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh5SRCat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh6SRCat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh7SRCat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh8SRCat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh9SRCat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh10SRCat" type="ENUMERATION" 
                           value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh11SRCat" type="ENUMERATION" 
                           value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh12SRCat" type="ENUMERATION" 
                           value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh13SRCat" type="ENUMERATION" 
                           value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh14SRCat" type="ENUMERATION" 
                           value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh15SRCat" type="ENUMERATION" 
                           value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="IrqDmaPrioConfig" type="IDENTIFIABLE">
                    <d:var name="IrqDmaErrSRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh0SRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh1SRPrio" type="INTEGER" value="180"/>
                    <d:var name="IrqDmaCh2SRPrio" type="INTEGER" value="179"/>
                    <d:var name="IrqDmaCh3SRPrio" type="INTEGER" value="183"/>
                    <d:var name="IrqDmaCh4SRPrio" type="INTEGER" value="176"/>
                    <d:var name="IrqDmaCh5SRPrio" type="INTEGER" value="175"/>
                    <d:var name="IrqDmaCh6SRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh7SRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh8SRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh9SRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh10SRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh11SRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh12SRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh13SRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh14SRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh15SRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="IrqDmaTosConfig" type="IDENTIFIABLE">
                    <d:var name="IrqDmaErrSRTos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh0SRTos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh1SRTos" type="ENUMERATION" value="CPU0"/>
                    <d:var name="IrqDmaCh2SRTos" type="ENUMERATION" value="CPU0"/>
                    <d:var name="IrqDmaCh3SRTos" type="ENUMERATION" value="CPU0"/>
                    <d:var name="IrqDmaCh4SRTos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh5SRTos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh6SRTos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh7SRTos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh8SRTos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh9SRTos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh10SRTos" type="ENUMERATION" 
                           value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh11SRTos" type="ENUMERATION" 
                           value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh12SRTos" type="ENUMERATION" 
                           value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh13SRTos" type="ENUMERATION" 
                           value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh14SRTos" type="ENUMERATION" 
                           value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqDmaCh15SRTos" type="ENUMERATION" 
                           value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqETHConfig" type="MAP">
                <d:ctr name="IrqETHConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqETHCatConfig" type="IDENTIFIABLE">
                    <d:var name="IrqETHSRCat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="IrqETHPrioConfig" type="IDENTIFIABLE">
                    <d:var name="IrqETHSRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="IrqETHTosConfig" type="IDENTIFIABLE">
                    <d:var name="IrqETHSRTos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqCanConfig" type="MAP">
                <d:ctr name="IrqCanConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqCanCatConfig" type="IDENTIFIABLE">
                    <d:var name="IrqCanSR0Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR1Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR2Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR3Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR4Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR5Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR6Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR7Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR8Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR9Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR10Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR11Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR12Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR13Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR14Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR15Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR16Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR17Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR18Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR19Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR20Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR21Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR22Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR23Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="IrqCanPrioConfig" type="IDENTIFIABLE">
                    <d:var name="IrqCanSR0Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR1Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR2Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR3Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR4Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR5Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR6Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR7Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR8Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR9Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR10Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR11Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR12Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR13Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR14Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR15Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR16Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR17Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR18Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR19Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR20Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR21Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR22Prio" type="INTEGER" value="0"/>
                    <d:var name="IrqCanSR23Prio" type="INTEGER" value="0"/>
                  </d:ctr>
                  <d:ctr name="IrqCanTosConfig" type="IDENTIFIABLE">
                    <d:var name="IrqCanSR0Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR1Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR2Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR3Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR4Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR5Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR6Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR7Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR8Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR9Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR10Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR11Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR12Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR13Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR14Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR15Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR16Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR17Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR18Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR19Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR20Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR21Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR22Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqCanSR23Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqAdcConfig" type="MAP">
                <d:ctr name="IrqAdcConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqAdc0Config" type="IDENTIFIABLE">
                    <d:ctr name="IrqAdc0CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdc0SR0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc0SR1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc0SR2Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc0SR3Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAdc0PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdc0SR0Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc0SR1Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc0SR2Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc0SR3Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAdc0TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdc0SR0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc0SR1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc0SR2Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc0SR3Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IrqAdc1Config" type="IDENTIFIABLE">
                    <d:ctr name="IrqAdc1CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdc1SR0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc1SR1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc1SR2Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc1SR3Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAdc1PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdc1SR0Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc1SR1Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc1SR2Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc1SR3Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAdc1TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdc1SR0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc1SR1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc1SR2Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc1SR3Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IrqAdc2Config" type="IDENTIFIABLE">
                    <d:ctr name="IrqAdc2CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdc2SR0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc2SR1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc2SR2Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc2SR3Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAdc2PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdc2SR0Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc2SR1Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc2SR2Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc2SR3Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAdc2TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdc2SR0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc2SR1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc2SR2Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc2SR3Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IrqAdc3Config" type="IDENTIFIABLE">
                    <d:ctr name="IrqAdc3CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdc3SR0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc3SR1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc3SR2Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc3SR3Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAdc3PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdc3SR0Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc3SR1Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc3SR2Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc3SR3Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAdc3TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdc3SR0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc3SR1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc3SR2Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdc3SR3Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IrqAdcCG0Config" type="IDENTIFIABLE">
                    <d:ctr name="IrqAdcCG0CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdcCG0SR0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdcCG0SR1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdcCG0SR2Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdcCG0SR3Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAdcCG0PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdcCG0SR0Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdcCG0SR1Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdcCG0SR2Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdcCG0SR3Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqAdcCG0TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqAdcCG0SR0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdcCG0SR1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdcCG0SR2Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqAdcCG0SR3Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqFlexRayConfig" type="MAP">
                <d:ctr name="IrqFlexRayConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqFlexRay0Config" type="IDENTIFIABLE">
                    <d:ctr name="IrqFlexRay0CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqFlexRay0SR0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0SR1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0TimerInt0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0TimerInt1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0NewData0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0NewData1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0MBSC0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0MBSC1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0OBBusyCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0IBBusyCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqFlexRay0PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqFlexRay0SR0Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0SR1Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0TimerInt0Prio" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0TimerInt1Prio" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0NewData0Prio" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0NewData1Prio" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0MBSC0Prio" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0MBSC1Prio" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0OBBusyPrio" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0IBBusyPrio" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqFlexRay0TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqFlexRay0SR0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0SR1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0TimerInt0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0TimerInt1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0NewData0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0NewData1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0MBSC0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0MBSC1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0OBBusyTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqFlexRay0IBBusyTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqPMU0Config" type="MAP">
                <d:ctr name="IrqPMU0Config_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqPMU0CatConfig" type="IDENTIFIABLE">
                    <d:var name="IrqPMU0SR0Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqPMU0SR1Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="IrqPMU0PrioConfig" type="IDENTIFIABLE">
                    <d:var name="IrqPMU0SR0Prio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqPMU0SR1Prio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="IrqPMU0TosConfig" type="IDENTIFIABLE">
                    <d:var name="IrqPMU0SR0Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqPMU0SR1Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqScuConfig" type="MAP">
                <d:ctr name="IrqScuConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqScuCatConfig" type="IDENTIFIABLE">
                    <d:var name="IrqScuDtsSRCat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqScuEruSR0Cat" type="ENUMERATION" 
                           value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqScuEruSR1Cat" type="ENUMERATION" 
                           value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqScuEruSR2Cat" type="ENUMERATION" 
                           value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqScuEruSR3Cat" type="ENUMERATION" 
                           value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="IrqScuPrioConfig" type="IDENTIFIABLE">
                    <d:var name="IrqScuDtsSRPrio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqScuEruSR0Prio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqScuEruSR1Prio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqScuEruSR2Prio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqScuEruSR3Prio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="IrqScuTosConfig" type="IDENTIFIABLE">
                    <d:var name="IrqScuDtsSRTos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqScuEruSR0Tos" type="ENUMERATION" 
                           value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqScuEruSR1Tos" type="ENUMERATION" 
                           value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqScuEruSR2Tos" type="ENUMERATION" 
                           value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqScuEruSR3Tos" type="ENUMERATION" 
                           value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqGPSRGroupConfig" type="MAP">
                <d:ctr name="IrqGPSRGroupConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqGPSRGroup0Config" type="IDENTIFIABLE">
                    <d:ctr name="IrqGPSRGroup0CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGPSRGroup0SR0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGPSRGroup0SR1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGPSRGroup0SR2Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGPSRGroup0SR3Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGPSRGroup0PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGPSRGroup0SR0Prio" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGPSRGroup0SR1Prio" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGPSRGroup0SR2Prio" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGPSRGroup0SR3Prio" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGPSRGroup0TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGPSRGroup0SR0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGPSRGroup0SR1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGPSRGroup0SR2Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGPSRGroup0SR3Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqGtmConfig" type="MAP">
                <d:ctr name="IrqGtmConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqGtmAEIConfig" type="IDENTIFIABLE">
                    <d:ctr name="IrqGtmAEICatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmAEICat" type="ENUMERATION" value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGtmAEIPrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmAEIPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGtmAEITosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmAEITos" type="ENUMERATION" value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IrqGtmERRConfig" type="IDENTIFIABLE">
                    <d:ctr name="IrqGtmERRCatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmERRSRCat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGtmERRPrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmERRSRPrio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGtmERRTosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmERRSRTos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IrqGtmTIM0Config" type="IDENTIFIABLE">
                    <a:a name="SHORT-NAME" value="IrqGtmTIM0Config"/>
                    <d:ctr name="IrqGtmTIM0CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmTIM0SR0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR2Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR3Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR4Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR5Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR6Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR7Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGtmTIM0PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmTIM0SR0Prio" type="INTEGER" value="66"/>
                      <d:var name="IrqGtmTIM0SR1Prio" type="INTEGER" value="67"/>
                      <d:var name="IrqGtmTIM0SR2Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR3Prio" type="INTEGER" value="68"/>
                      <d:var name="IrqGtmTIM0SR4Prio" type="INTEGER" value="69"/>
                      <d:var name="IrqGtmTIM0SR5Prio" type="INTEGER" value="70"/>
                      <d:var name="IrqGtmTIM0SR6Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR7Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGtmTIM0TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmTIM0SR0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR2Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR3Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR4Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR5Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR6Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTIM0SR7Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IrqGtmTOM0Config" type="IDENTIFIABLE">
                    <d:ctr name="IrqGtmTOM0CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmTOM0SR0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR2Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR3Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR4Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR5Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR6Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR7Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGtmTOM0PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmTOM0SR0Prio" type="INTEGER" value="94"/>
                      <d:var name="IrqGtmTOM0SR1Prio" type="INTEGER" value="95"/>
                      <d:var name="IrqGtmTOM0SR2Prio" type="INTEGER" value="96"/>
                      <d:var name="IrqGtmTOM0SR3Prio" type="INTEGER" value="97"/>
                      <d:var name="IrqGtmTOM0SR4Prio" type="INTEGER" value="98"/>
                      <d:var name="IrqGtmTOM0SR5Prio" type="INTEGER" value="99"/>
                      <d:var name="IrqGtmTOM0SR6Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR7Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGtmTOM0TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmTOM0SR0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR2Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR3Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR4Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR5Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR6Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM0SR7Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IrqGtmTOM1Config" type="IDENTIFIABLE">
                    <d:ctr name="IrqGtmTOM1CatConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmTOM1SR0Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR1Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR2Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR3Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR4Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR5Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR6Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR7Cat" type="ENUMERATION" 
                             value="CAT1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGtmTOM1PrioConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmTOM1SR0Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR1Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR2Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR3Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR4Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR5Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR6Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR7Prio" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IrqGtmTOM1TosConfig" type="IDENTIFIABLE">
                      <d:var name="IrqGtmTOM1SR0Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR1Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR2Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR3Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR4Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR5Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR6Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IrqGtmTOM1SR7Tos" type="ENUMERATION" 
                             value="CPU0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqSentConfig" type="MAP">
                <d:ctr name="IrqSentConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqSentCatConfig" type="IDENTIFIABLE">
                    <d:var name="IrqSentSR0Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqSentSR1Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqSentSR2Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqSentSR3Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="IrqSentPrioConfig" type="IDENTIFIABLE">
                    <d:var name="IrqSentSR0Prio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqSentSR1Prio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqSentSR2Prio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqSentSR3Prio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="IrqSentTosConfig" type="IDENTIFIABLE">
                    <d:var name="IrqSentSR0Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqSentSR1Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqSentSR2Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqSentSR3Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:lst name="IrqHSMConfig" type="MAP">
                <d:ctr name="IrqHSMConfig_0" type="IDENTIFIABLE">
                  <d:ctr name="IrqHSMCatConfig" type="IDENTIFIABLE">
                    <d:var name="IrqHSMSR0Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqHSMSR1Cat" type="ENUMERATION" value="CAT1">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="IrqHSMPrioConfig" type="IDENTIFIABLE">
                    <d:var name="IrqHSMSR0Prio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqHSMSR1Prio" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="IrqHSMTosConfig" type="IDENTIFIABLE">
                    <d:var name="IrqHSMSR0Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IrqHSMSR1Tos" type="ENUMERATION" value="CPU0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
              </d:lst>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArMajorVersion" type="INTEGER" value="0"/>
                <d:var name="ArMinorVersion" type="INTEGER" value="0"/>
                <d:var name="ArPatchVersion" type="INTEGER" value="0"/>
                <d:var name="SwMajorVersion" type="INTEGER" value="2"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="1"/>
                <d:var name="SwPatchVersion" type="INTEGER" value="1"/>
                <d:var name="ModuleId" type="INTEGER" value="255">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="17">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="Release" type="STRING" value="_TRICORE_TC234">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
