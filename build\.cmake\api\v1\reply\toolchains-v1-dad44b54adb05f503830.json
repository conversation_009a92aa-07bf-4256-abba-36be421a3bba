{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {}, "path": "D:/mingw64/bin/gcc.exe", "version": ""}, "language": "ASM", "sourceFileExtensions": ["s", "S", "asm"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["D:/mingw64/lib/gcc/x86_64-w64-mingw32/13.1.0/include", "D:/mingw64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed", "D:/mingw64/x86_64-w64-mingw32/include"], "linkDirectories": ["D:/mingw64/lib/gcc/x86_64-w64-mingw32/13.1.0", "D:/mingw64/lib/gcc", "D:/mingw64/x86_64-w64-mingw32/lib", "D:/mingw64/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["mingw32", "gcc", "moldname", "mingwex", "kernel32", "advapi32", "shell32", "user32", "kernel32", "iconv", "mingw32", "gcc", "moldname", "mingwex", "kernel32"]}, "path": "D:/mingw64/bin/gcc.exe", "version": "13.1.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"implicit": {}, "path": "D:/mingw64/bin/windres.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}