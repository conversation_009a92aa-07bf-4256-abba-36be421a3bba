/*******************************************************************************
**                                                                            **
** Copyright (C) Infineon Technologies (2013)                                 **
**                                                                            **
** All rights reserved.                                                       **
**                                                                            **
** This document contains proprietary information belonging to Infineon       **
** Technologies. Passing on and copying of this document, and communication   **
** of its contents is not permitted without prior written authorization.      **
**                                                                            **
********************************************************************************
**                                                                            **
**   $FILENAME   : Compiler_Cfg.h $                                           **
**                                                                            **
**   $CC VERSION : \main\8 $                                                  **
**                                                                            **
**   $DATE       : 2016-06-22 $                                               **
**                                                                            **
**   AUTHOR      : DL-AUTOSAR-Engineering                                     **
**                                                                            **
**   VENDOR      : Infineon Technologies                                      **
**                                                                            **
**   DESCRIPTION :File contains the module/component specific memclass        **
**              parameters that are passed to the macros defined in Compiler.h**
**              Module specific macros given in this file are not used in     **
**              AURIX MCAL.                                                   **
**                                                                            **
**   MAY BE CHANGED BY USER [yes/no]: yes                                     **
**                                                                            **
*******************************************************************************/
/*******************************************************************************
**  TRACEABILITY : [cover parentID= SAS_NAS_ALL_PR758,SAS_NAS_ALL_PR755] 
                   [/cover]
*******************************************************************************/


#ifndef COMPILER_CFG_H 
#define COMPILER_CFG_H  



/* 

 * The following memory and pointer classes can be configured per module.

 * Those defines are passed to the compiler abstraction macros in Compiler.h

 */
 


/*******************************************************************************

**                                 MCU                                        **

*******************************************************************************/

/*To be used for code*/

#define MCU_CODE



/*To be used for all global or static variables that are never initialized*/

#define MCU_VAR_NOINIT 



/*To be used for all global or static variables that are initialized only after power on reset*/

#define MCU_VAR_POWER_ON_INIT 



/* To be used for all global or static variables that have at least one of the 

following properties:

-accessed bitwise

-frequently used

-high number of accesses in source code*/

#define MCU_VAR_FAST 



/*To be used for global or static variables that are initialized after every 

reset.*/

#define MCU_VAR



/*To be used for global or static constants*/

#define MCU_CONST



/*To be used for references on application data (expected to be in RAM or ROM) passed via API*/

#define MCU_APPL_DATA



/*To be used for references on application constants (expected to be certainly in ROM, for instance pointer of Init-function) passed via API*/

#define MCU_APPL_CONST



/*==== DEM ==================================================================*/
#define DEM_CODE                /* code */
#define DEM_VAR_INIT            /* initialized global/static variables */
#define DEM_VAR_ZERO_INIT       /* initialized global/static variables with initialization value 0 */
#define DEM_VAR_NOINIT          /* uninitialized global/static variables */
#define DEM_VAR_UNCACHED        /* uninitialized global/static un-cached variables */
#define DEM_CONST               /* global/static constants */
#define DEM_CONST_ROOT          /* global/static constants */
#define DEM_PBCFG               /* global/static constants for PostBuild */
#define DEM_PBCFG_ROOT          /* global/static constants for PostBuild */
#define DEM_VAR_PBCFG           /* post-buildable RAM */
#define DEM_DCM_DATA            /* variables/buffers passed by Dcm to Dem */
#define DEM_J1939DCM_DATA       /* variables/buffers passed by J1939Dcm to Dem */
#define DEM_DLT_DATA            /* variables/buffers passed by Dlt to Dem */
#define DEM_NVM_DATA            /* variables/buffers managed by NvM */
#define DEM_APPL_CODE           /* callback functions outside of Dem */
#define DEM_APPL_DATA           /* variables/buffers outside of Dem */
#define DEM_APPL_CONST          /* constants outside Dem */
#define DEM_SHARED_DATA         /* variables/buffers shared by all */
#define DEM_CAL_PRM             /* calibrateable constants */
#define DEM_NVM_DATA_NOINIT     DEM_NVM_DATA
#define DEM_VAR_SATELLITE       /* variables/buffers of Dem satellites */
/*===========================================================================*/

#define MCU_APPL_CODE



/*******************************************************************************

**                                 PORT                                        *

*******************************************************************************/

/*To be used for code*/

#define PORT_CODE



/*To be used for all global or static variables that are never initialized*/

#define PORT_VAR_NOINIT 



/*To be used for all global or static variables that are initialized only after power on reset*/

#define PORT_VAR_POWER_ON_INIT 



/* To be used for all global or static variables that have at least one of the 

following properties:

-accessed bitwise

-frequently used

-high number of accesses in source code*/

#define PORT_VAR_FAST 



/*To be used for global or static variables that are initialized after every 

reset.*/

#define PORT_VAR



/*To be used for global or static constants*/

#define PORT_CONST



/*To be used for references on application data (expected to be in RAM or ROM) passed via API*/

#define PORT_APPL_DATA



/*To be used for references on application constants (expected to be certainly in ROM, for instance pointer of Init-function) passed via API*/

#define PORT_APPL_CONST



/*To be used for references on application functions. (e.g. call back function pointers)*/

#define PORT_APPL_CODE



/*******************************************************************************

**                                 DIO                                        **

*******************************************************************************/

/*To be used for code*/

#define DIO_CODE



/*To be used for all global or static variables that are never initialized*/

#define DIO_VAR_NOINIT 



/*To be used for all global or static variables that are initialized only after power on reset*/

#define DIO_VAR_POWER_ON_INIT 



/* To be used for all global or static variables that have at least one of the 

following properties:

-accessed bitwise

-frequently used

-high number of accesses in source code*/

#define DIO_VAR_FAST 



/*To be used for global or static variables that are initialized after every 

reset.*/

#define DIO_VAR



/*To be used for global or static constants*/

#define DIO_CONST



/*To be used for references on application data (expected to be in RAM or ROM) passed via API*/

#define DIO_APPL_DATA



/*To be used for references on application constants (expected to be certainly in ROM, for instance pointer of Init-function) passed via API*/

#define DIO_APPL_CONST



/*To be used for references on application functions. (e.g. call back function pointers)*/

#define DIO_APPL_CODE



/*******************************************************************************

**                                 ADC                                        **

*******************************************************************************/

/*To be used for code*/

#define ADC_CODE



/*To be used for all global or static variables that are never initialized*/

#define ADC_VAR_NOINIT 



/*To be used for all global or static variables that are initialized only after power on reset*/

#define ADC_VAR_POWER_ON_INIT 



/* To be used for all global or static variables that have at least one of the 

following properties:

-accessed bitwise

-frequently used

-high number of accesses in source code*/

#define ADC_VAR_FAST 



/*To be used for global or static variables that are initialized after every 

reset.*/

#define ADC_VAR



/*To be used for global or static constants*/

#define ADC_CONST



/*To be used for references on application data (expected to be in RAM or ROM) passed via API*/

#define ADC_APPL_DATA



/*To be used for references on application constants (expected to be certainly in ROM, for instance pointer of Init-function) passed via API*/

#define ADC_APPL_CONST



/*To be used for references on application functions. (e.g. call back function pointers)*/

#define ADC_APPL_CODE



/*******************************************************************************

**                                 GPTA                                        *

*******************************************************************************/

/*To be used for code*/

#define FEE_CODE
#define FEE_CONST
#define FEE_APPL_DATA
#define FEE_APPL_CONST
#define FEE_APPL_CODE
#define FEE_CALLOUT_CODE
#define FEE_VAR_NOINIT
#define FEE_VAR_POWER_ON_INIT
#define FEE_VAR_FAST
#define FEE_VAR
#define FEE_REGSPACE
 
/**********************************************************************************************************************
 *  FEE END
 *********************************************************************************************************************/
 
 /**********************************************************************************************************************
 *  FLS START
 *********************************************************************************************************************/

#define FLS_CODE
#define FLS_CONST
#define FLS_APPL_DATA
#define FLS_APPL_CONST
#define FLS_APPL_CODE
#define FLS_CALLOUT_CODE
#define FLS_VAR_NOINIT
#define FLS_VAR_POWER_ON_INIT
#define FLS_VAR_FAST
#define FLS_VAR
#define FLS_REGSPACE
#define FLS_17_PMU_CODE

/*******************************************************************************

**                                 GPT                                        **

*******************************************************************************/

/*To be used for code*/

#define GPT_CODE



/*To be used for all global or static variables that are never initialized*/

#define GPT_VAR_NOINIT 



/*To be used for all global or static variables that are initialized only after power on reset*/

#define GPT_VAR_POWER_ON_INIT 



/* To be used for all global or static variables that have at least one of the 

following properties:

-accessed bitwise

-frequently used

-high number of accesses in source code*/

#define GPT_VAR_FAST 



/*To be used for global or static variables that are initialized after every 

reset.*/

#define GPT_VAR



/*To be used for global or static constants*/

#define GPT_CONST



/*To be used for references on application data (expected to be in RAM or ROM) passed via API*/

#define GPT_APPL_DATA



/*To be used for references on application constants (expected to be certainly in ROM, for instance pointer of Init-function) passed via API*/

#define GPT_APPL_CONST



/*To be used for references on application functions. (e.g. call back function pointers)*/

#define GPT_APPL_CODE



/*******************************************************************************

**                                 ICU                                        **

*******************************************************************************/

/*To be used for code*/

#define ICU_CODE



/*To be used for all global or static variables that are never initialized*/

#define ICU_VAR_NOINIT 



/*To be used for all global or static variables that are initialized only after power on reset*/

#define ICU_VAR_POWER_ON_INIT 



/* To be used for all global or static variables that have at least one of the 

following properties:

-accessed bitwise

-frequently used

-high number of accesses in source code*/

#define ICU_VAR_FAST 



/*To be used for global or static variables that are initialized after every 

reset.*/

#define ICU_VAR



/*To be used for global or static constants*/

#define ICU_CONST



/*To be used for references on application data (expected to be in RAM or ROM) passed via API*/

#define ICU_APPL_DATA



/*To be used for references on application constants (expected to be certainly in ROM, for instance pointer of Init-function) passed via API*/

#define ICU_APPL_CONST



/*To be used for references on application functions. (e.g. call back function pointers)*/

#define ICU_APPL_CODE



/*******************************************************************************

**                                 PWM                                        **

*******************************************************************************/

/*To be used for code*/

#define PWM_CODE



/*To be used for all global or static variables that are never initialized*/

#define PWM_VAR_NOINIT 



/*To be used for all global or static variables that are initialized only after power on reset*/

#define PWM_VAR_POWER_ON_INIT 



/* To be used for all global or static variables that have at least one of the 

following properties:

-accessed bitwise

-frequently used

-high number of accesses in source code*/

#define PWM_VAR_FAST 



/*To be used for global or static variables that are initialized after every 

reset.*/

#define PWM_VAR



/*To be used for global or static constants*/

#define PWM_CONST



/*To be used for references on application data (expected to be in RAM or ROM) passed via API*/

#define PWM_APPL_DATA



/*To be used for references on application constants (expected to be certainly in ROM, for instance pointer of Init-function) passed via API*/

#define PWM_APPL_CONST



/*To be used for references on application functions. (e.g. call back function pointers)*/

#define PWM_APPL_CODE



/*******************************************************************************

**                                 SPI                                        **

*******************************************************************************/

/*To be used for code*/

#define SPI_CODE



/*To be used for all global or static variables that are never initialized*/

#define SPI_VAR_NOINIT 



/*To be used for all global or static variables that are initialized only after power on reset*/

#define SPI_VAR_POWER_ON_INIT 



/* To be used for all global or static variables that have at least one of the 

following properties:

-accessed bitwise

-frequently used

-high number of accesses in source code*/

#define SPI_VAR_FAST 



/*To be used for global or static variables that are initialized after every 

reset.*/

#define SPI_VAR



/*To be used for global or static constants*/

#define SPI_CONST



/*To be used for references on application data (expected to be in RAM or ROM) passed via API*/

#define SPI_APPL_DATA



/*To be used for references on application constants (expected to be certainly in ROM, for instance pointer of Init-function) passed via API*/

#define SPI_APPL_CONST



/*To be used for references on application functions. (e.g. call back function pointers)*/

#define SPI_APPL_CODE



/*******************************************************************************

**                                 CAN                                        **

*******************************************************************************/

/*To be used for code*/

#define CAN_CODE



/*To be used for all global or static variables that are never initialized*/

#define CAN_VAR_NOINIT 



/*To be used for all global or static variables that are initialized only after power on reset*/

#define CAN_VAR_POWER_ON_INIT 



/* To be used for all global or static variables that have at least one of the 

following properties:

-accessed bitwise

-frequently used

-high number of accesses in source code*/

#define CAN_VAR_FAST 



/*To be used for global or static variables that are initialized after every 

reset.*/

#define CAN_VAR



/*To be used for global or static constants*/

#define CAN_CONST



/*To be used for references on application data (expected to be in RAM or ROM) passed via API*/

#define CAN_APPL_DATA



/*To be used for references on application constants (expected to be certainly in ROM, for instance pointer of Init-function) passed via API*/

#define CAN_APPL_CONST



/*To be used for references on application functions. (e.g. call back function pointers)*/

#define CAN_APPL_CODE



/*******************************************************************************

**                                 WDG                                        **

*******************************************************************************/

/*To be used for code*/

#define WDG_CODE



/*To be used for all global or static variables that are never initialized*/

#define WDG_VAR_NOINIT 



/*To be used for all global or static variables that are initialized only after power on reset*/

#define WDG_VAR_POWER_ON_INIT 



/* To be used for all global or static variables that have at least one of the 

following properties:

-accessed bitwise

-frequently used

-high number of accesses in source code*/

#define WDG_VAR_FAST 



/*To be used for global or static variables that are initialized after every 

reset.*/

#define WDG_VAR



/*To be used for global or static constants*/

#define WDG_CONST



/*To be used for references on application data (expected to be in RAM or ROM) passed via API*/

#define WDG_APPL_DATA



/*To be used for references on application constants (expected to be certainly in ROM, for instance pointer of Init-function) passed via API*/

#define WDG_APPL_CONST



/*To be used for references on application functions. (e.g. call back function pointers)*/

#define WDG_APPL_CODE



/*******************************************************************************

**                                 CANTRCV                                     *

*******************************************************************************/

/*To be used for code*/

#define CANTRCV_CODE



/*To be used for all global or static variables that are never initialized*/

#define CANTRCV_VAR_NOINIT 



/*To be used for all global or static variables that are initialized only after power on reset*/

#define CANTRCV_VAR_POWER_ON_INIT 



/* To be used for all global or static variables that have at least one of the 

following properties:

-accessed bitwise

-frequently used

-high number of accesses in source code*/

#define CANTRCV_VAR_FAST 



/*To be used for global or static variables that are initialized after every 

reset.*/

#define CANTRCV_VAR



/*To be used for global or static constants*/

#define CANTRCV_CONST



/*To be used for references on application data (expected to be in RAM or ROM) passed via API*/

#define CANTRCV_APPL_DATA



/*To be used for references on application constants (expected to be certainly in ROM, for instance pointer of Init-function) passed via API*/

#define CANTRCV_APPL_CONST



/*To be used for references on application functions. (e.g. call back function pointers)*/

#define CANTRCV_APPL_CODE



#endif /* COMPILER_CFG_H */

