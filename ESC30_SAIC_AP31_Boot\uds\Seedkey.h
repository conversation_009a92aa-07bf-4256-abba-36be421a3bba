/*
 * Seedkey.h
 *
 *  Created on: 2019-9-16
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 */

#ifndef SEEDKEY_H_
#define SEEDKEY_H_

#include "Platform_Types.h"
#include "SecM.h"
#ifndef SECM_OK
#define SECM_OK 0x00u
#define SECM_NOT_OK 0x01u
#endif


extern SecM_StatusType SecM_GenerateSeed(SecM_SeedType *seed);
extern SecM_StatusType SecM_ComputeKey(SecM_SeedType seed,unsigned char* mask,SecM_KeyType *key);
extern SecM_StatusType SecM_CompareKey(SecM_KeyType key,SecM_SeedType seed);
#endif /* SEEDKEY_H_ */
