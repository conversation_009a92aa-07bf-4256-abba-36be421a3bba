/*
 * CDD_Adc.c
 *
 *  Created on: 2020-12-8
 *      Author: fanghongqing
 */


#include "CDD_Adc.h"

uint16 u16_Kl30_voltage=13500;

void CDD_AdcTrigger(void)
{
//	Adc_StartGroupConversion(AdcConf_AdcGroup_AdcGroup_0);
	Adc_StartGroupConversion(AdcConf_AdcGroup_AdcGroup_1);

}
uint8 CDD_AdcRead(void)
{
	uint8 ren;
	uint8 i=0;
	static uint16 u16_Kl30_voltage_raw[5]={13500,13500,13500,13500,13500};
	static uint16 u16_Kl30_counter=0;
	uint16 min=0xFFFF,max=0,kl30_raw;
	uint32 sum;

	ren=E_OK;
	kl30_raw=*((uint32*)0xF0020B00)&0xfff;
	kl30_raw=(uint16)((float)kl30_raw*7.234)+1000;

	if(u16_Kl30_counter>=5)
	{
		u16_Kl30_counter=0;

	}
	u16_Kl30_voltage_raw[u16_Kl30_counter++]=kl30_raw;

	sum=0;
	for(i=0;i<5;i++)
	{
		if(u16_Kl30_voltage_raw[i]<min)
		{
			min=u16_Kl30_voltage_raw[i];
		}
		if(u16_Kl30_voltage_raw[i]>max)
		{
			max=u16_Kl30_voltage_raw[i];
		}
		sum+=u16_Kl30_voltage_raw[i];
	}
	u16_Kl30_voltage=(uint16)((sum-min-max)/3.0);

	return ren;
}
