; --------------------------------------------------------------------------------
; @Title: Generic Flash script file for TC23x devices
; @Description:
;   Script for flash declaration and programming of Infineon TriCore TC23x
;   internal flash.
; @Keywords: AURIX, Flash, Infineon, TriCore
; @Author: MOB, WRD
; @Chip: TC23*
; @Copyright: (C) 1989-2014 Lauterbach GmbH, licensed for use with TRACE32(R) only
; --------------------------------------------------------------------------------
; $Rev: 5234 $
; $Id: tc23x.cmm 5234 2018-02-05 15:33:24Z mobermaier $


; Important information, read this before using this script:
;
;   Do not edit this script. Instead, call this script from your project using the
;   command line arguments described below. See the triboard demo scripts for an
;   example.
;
;   If changing the sector type from NOP to TARGET is required, use the command
;   FLASH.CHANGETYPE instead of modifying this script.
;
;   This script checks the programmed application to avoid permanent locking of the
;   device. This script is constantly improved, but it is not guaranteed that all
;   cases are covered.
;
; Script arguments:
;
;   DO tc23x [PREPAREONLY] [CHECKBMHD] [CPU=<cpu>] [DUALPORT=0|1]
;
;     PREPAREONLY only declares flash but does not execute flash programming
;
;     CHECKBMHD only checks if at least one valid Boot Mode Header can be found.
;             The script returns:
;               BMHD_OK       if at least one valid Boot Mode Header was found
;               BMHD_MISSING  if no valid Boot Mode Header was found
;
;     CPU=<cpu> selects CPU derivative <cpu>
;
;     DUALPORT default value is 0 (disabled). If DualPort mode is enabled
;             flash algorithm stays running until flash programming is
;             finished. Data is tranferred via dual port memory access.
;
; Example:
;
;   DO ~~/demo/tricore/flash/tc23x CPU=TC234LP DUALPORT=1 PREPAREONLY
;
; Supported devices:
;
;   All TC23x devices, e.g. TC234LP, TC233LF.
;
; Flash:
;
;   2 MByte on-chip program flash      at 0x80000000--0x801FFFFF (cached)
;                                      or 0xA0000000--0xA01FFFFF (non cached)
;   128 kByte data flash EEPROM        at 0xAF000000--0xAF01FFFF
;   16 kByte data flash UCB sectors    at 0xAF100000--0xAF103FFF
;
; RAM:
;
;   184 kByte Data Scratch-Pad RAM (DSPR) at      0xD0000000--0xD002DFFF
;   8 kByte Instruction Scratch-Pad RAM (PSPR) at 0xC0000000--0xC0001FFF
;
; CAUTION:
;
;   Do not reboot or unpower your device in case all BMHD (Boot Mode Headers)
;   do not contain valid information. This is normally the case after having
;   erased the internal program flash or loading an object or binary file
;   without a valid BMHD. The BMHD are located at:
;     BMHD0 0xA0000000--0xA000001F
;     BMHD1 0xA0020000--0xA002001F
;     BMHD2 0xA000FFE0--0xA000FFFF
;     BMHD3 0xA001FFE0--0xA001FFFF
;   See the Infineon documentation for more information.
;
;   Do not enable HSM boot when no valid HSM code is present. This will lock
;   your device permanently. See the Infineon documentation and contact your
;   Infineon FAE for more information on HSM.
;   See ~~/demo/tricore/flash/tc29x-hsm.cmm for programming the HSM.
;
;   Pay special attention when modifying the UCB. An invalid or erroneous
;   content will lock your device permanently. This also happens in case the
;   confirmation code is neither "unlocked" nor "confirmed". See the Infineon
;   documentation for more information.
;   See ~~/demo/tricore/flash/tc2xx-ucb.cmm for programming the UCB.
;
; HINTS:
;
;   Erased PFlash is showing bus error caused by ECC error. The ECC width of
;   PFlash an aligned group of 32 bytes.
;
;   The flash algorithm is programming PFlash in an aligned group of 32 bytes.
;   When using FLASH.Program command the download has to care about this
;   alignment.


LOCAL &parameters &param_prepareonly &param_checkbmhd &param_cpu &param_dualport
ENTRY %LINE &parameters

&param_prepareonly=(STRing.SCAN(STRing.UPpeR("&parameters"),"PREPAREONLY",0)!=-1)
&param_checkbmhd=(STRing.SCAN(STRing.UPpeR("&parameters"),"CHECKBMHD",0)!=-1)
&param_cpu=STRing.SCANAndExtract(STRing.UPpeR("&parameters"),"CPU=","")
&param_dualport=0
IF VERSION.BUILD.BASE()>=48610.
  &param_dualport=STRing.SCANAndExtract(STRing.UPpeR("&parameters"),"DUALPORT=","0")

; ------------------------------------------------------------------------------
; Initialize

IF SYStem.MODE()<5.
(
  SYStem.RESet

  IF "&param_cpu"!=""
    SYStem.CPU &param_cpu
  IF !CPUIS(TC23*)
    SYStem.CPU TC23*

  IF CHIP.EmulationDevice()
  (
    ; Emulation Devices in LQFP packages (except Fusion Quad) do not support
    ; JTAG (TDI pin is used as VDDPSB)
    IF (ID.CABLE()==0x0029)||(ID.CABLE()==0x4155||(ID.CABLE()==0x4150))
    (
      ; DAP only supported by
      ; - Bi-directional OCDS Debug Cable (0x0029)
      ; - Debug Cable Automotive (0x4155)
      ; - Debug Cable Automotive PRO (0x4150)
      SYStem.CONFIG.DEBUGPORTTYPE DAP2
    )
    ELSE
    (
      DIALOG.OK CPU()+" not supported by debug cable."
      ENDDO
    )
  )
  SYStem.Up
)

IF &param_checkbmhd
(
  LOCAL &validBmhdFound
  GOSUB CheckAllBootModeHeaders
  ENTRY &validBmhdFound
  IF &validBmhdFound
    ENDDO BMHD_OK
  ENDDO BMHD_MISSING
)

; Disable Watchdog Timers on TC23xD[E]

; Disable the Safety Watchdog Timer (overall system level watchdog):
PER.Set.simple D:0xF00360F4 %Long 0x00000008 // SCU_WDTSCON1, Request to disable the WDT
; Disable the CPU Watchdog Timer:
PER.Set.simple D:0xF0036104 %Long 0x00000008 // SCU_WDTCPU0CON1, Request to disable the WDT

; ------------------------------------------------------------------------------
; Flash Declaration

FLASH.RESet

; Program flash PF0
FLASH.Create 1. 0xA0000000--0xA000BFFF  0x4000 TARGET Long /BootModeHeaDer 0xA0000000--0xA000001F  ; PS0, S0..S2
FLASH.Create 1. 0xA000C000--0xA0013FFF  0x4000 TARGET Long /BootModeHeaDer 0xA000FFE0--0xA000FFFF  ; PS0, S3..S4
FLASH.Create 1. 0xA0014000--0xA0017FFF  0x4000 NOP    Long /INFO "Tuning Protection"               ; PS0, S5
FLASH.Create 1. 0xA0018000--0xA001BFFF  0x4000 NOP    Long /INFO "HSM code sector"                 ; PS0, S6
FLASH.Create 1. 0xA001C000--0xA001FFFF  0x4000 TARGET Long /BootModeHeaDer 0xA001FFE0--0xA001FFFF  ; PS0, S7
FLASH.Create 1. 0xA0020000--0xA005FFFF  0x8000 TARGET Long /BootModeHeaDer 0xA0020000--0xA002001F  ; PS0, S8..S15
FLASH.Create 1. 0xA0060000--0xA007FFFF 0x10000 NOP    Long /INFO "HSM code sector"                 ; PS0, S16..S17
FLASH.Create 2. 0xA0080000--0xA009FFFF 0x10000 TARGET Long   ; PS1, S18..S19
FLASH.Create 2. 0xA00A0000--0xA00FFFFF 0x20000 TARGET Long   ; PS1, S20..S22
FLASH.Create 3. 0xA0100000--0xA017FFFF 0x40000 TARGET Long   ; PS2, S23..S24
FLASH.Create 4. 0xA0180000--0xA01FFFFF 0x40000 TARGET Long   ; PS3, S25..S26

; Data flash DF0
FLASH.Create 5. 0xAF000000--0xAF01FFFF  0x2000 TARGET Long /INFO "EEPROM"          ; DF0, EEPROM0..EEPROM15
FLASH.Create 6. 0xAF100000--0xAF1003FF   0x400 NOP    Long /INFO "UCB_PFlash"      ; DF0, UCB0
FLASH.Create 6. 0xAF100400--0xAF1007FF   0x400 NOP    Long /INFO "UCB_DFlash"      ; DF0, UCB1
FLASH.Create 6. 0xAF100800--0xAF100BFF   0x400 NOP    Long /INFO "UCB_HSMCOTP"     ; DF0, UCB2
FLASH.Create 6. 0xAF100C00--0xAF100FFF   0x400 NOP    Long /INFO "UCB_OTP"         ; DF0, UCB3
FLASH.Create 6. 0xAF101000--0xAF1013FF   0x400 NOP    Long /INFO "UCB_IFX"         ; DF0, UCB4
FLASH.Create 6. 0xAF101400--0xAF1017FF   0x400 NOP    Long /INFO "UCB_DBG"         ; DF0, UCB5
FLASH.Create 6. 0xAF101800--0xAF101BFF   0x400 NOP    Long /INFO "UCB_HSM"         ; DF0, UCB6
FLASH.Create 6. 0xAF101C00--0xAF101FFF   0x400 NOP    Long /INFO "UCB_HSMCFG"      ; DF0, UCB7
FLASH.Create 6. 0xAF102000--0xAF102FFF   0x400 NOP    Long                         ; DF0, UCB8..UCB11
FLASH.Create 6. 0xAF103000--0xAF103FFF   0x400 NOP    Long /INFO "Erase Counter"   ; DF0, UCB12..UCB15

; Cached program flash address range
FLASH.CreateALIAS 0x80000000--0x80FFFFFF 0xA0000000

IF &param_dualport==0
  FLASH.TARGET 0xC0000000 0xD0000000 0x2000 ~~/demo/tricore/flash/long/tc2.bin
ELSE
  FLASH.TARGET 0xC0000000 0xD0000000 0x2000 ~~/demo/tricore/flash/long/tc2.bin /DualPort

; Flash script ends here if called with parameter PREPAREONLY
IF &param_prepareonly
  ENDDO PREPAREDONE

; ------------------------------------------------------------------------------
; Example for download

DIALOG.YESNO "Program internal flash memory?"
LOCAL &progflash
ENTRY &progflash
IF &progflash
(
  ; Enable flash programming
  FLASH.ReProgram.ALL

  Data.LOAD.auto ././Debug/CSUV_PGM_FBL.elf
  
  ; Use the following commands to manually program a valid default BMHD (see comment above)
  ; Data.Set 0xA0000000++0x17 %Long 0xA0000020 0xB3590070 0x00000000 0x00000000 0x00000000 0x00000000
  ; Data.SUM 0xA0000000++0x17 /Long /ByteSWAP /CRC32
  ; Data.Set 0xA0000018 %Long Data.SUM()
  ; Data.Set 0xA000001C %Long ~Data.SUM()

  GOSUB CheckAllBootModeHeaders
  ENTRY &progflash
  IF !&progflash
  (
    DIALOG.YESNO "No valid Boot Mode Header found!" \
                 "Flashing now can lock your device." \
                 "Do you really want to program flash?"
    ENTRY &progflash
  )

  IF &progflash
  (
    ; Finally program flash memory
    FLASH.ReProgram.off
  )
  ELSE
  (
    ; Revert loaded data
    IF VERSION.BUILD()>=51257.
      FLASH.ReProgram.CANCEL
    ELSE
    (
      FLASH.ReProgram.ALL
      FLASH.ReProgram.off
    )
  )
)

ENDDO

; --------------------------------------------------------------------------------
; Check if at least one valid boot mode header is available
;
CheckAllBootModeHeaders:
(
  LOCAL &bmhdValid
  GOSUB CheckBootModeHeader 0xA0000000
  ENTRY &bmhdValid
  IF &bmhdValid
    RETURN &bmhdValid
  GOSUB CheckBootModeHeader 0xA0020000
  ENTRY &bmhdValid
  IF &bmhdValid
    RETURN &bmhdValid
  GOSUB CheckBootModeHeader 0xA000FFE0
  ENTRY &bmhdValid
  IF &bmhdValid
    RETURN &bmhdValid
  GOSUB CheckBootModeHeader 0xA001FFE0
  ENTRY &bmhdValid
  RETURN &bmhdValid
)

; --------------------------------------------------------------------------------
; Check if Boot Mode Header has valid contents
;
CheckBootModeHeader:
(
  LOCAL &addr &result &bmhdid
  ENTRY &addr
  &result=TRUE()

  ON.ERROR GOSUB
  (
    &result=FALSE()
    RETURN
  )

  IF !STRing.FIND("&addr",":")
    &addr="C:&addr"

  ; Check Boot Mode Header ID
  &bmhdid=Data.Word(&addr+0x6)
  IF &result
  (
    IF &bmhdid!=0xB359
      &result=FALSE()
  )

  ; Check Boot Mode Header CRC
  IF &result
  (
    Data.SUM &addr++0x17 /Long /ByteSWAP /CRC32
    IF &result
    (
      Data.Set &addr+0x18 %Long Data.SUM() /DIFF
      IF &result
      (
        &result=!FOUND()
        IF &result
        (
          Data.Set &addr+0x1C %Long ~Data.SUM() /DIFF
          IF &result
          (
            &result=!FOUND()
          )
        )
      )
    )
  )

  RETURN &result
)
