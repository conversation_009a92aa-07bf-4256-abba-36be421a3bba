/*============================================================================*/
/*** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  
 *  @file       <CanTp.c>
 *  @brief      <canTp configuration file>
 *  
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *  
 *  <AUTHOR> Yu>
 *  @date       <2012-12-28>
 */
/*============================================================================*/

/*========[R E V I S I O N   H I S T O R Y]===================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20121228    Tommy Yu    Initial version
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/

#include "CanTp.h"
#include "CanTp_Cfg.h"

/*=======[E X T E R N A L   D A T A]==========================================*/
const CanTp_RxSduType CanTp_RxSdu[CANTP_RX_CHANNEL_NUM] =
    {
        {
            /* rxDcmId */
            /* @type:uint16 range:0x0000~0xFFFF note:auto generate */
            0xCCu,

            /* rxPduId */
            /* @type:uint16 range:0x0000~0xFFFF note:reference to Can_RxPdu->pduId */
            0x11u,

            /* addressingFormat */
            /* @type:ENUM range:NONE note:NONE */
            CANTP_STANDARD,

            /* nSa */
            /* @type:uint8 range:0x00~0xFF note:NONE */
            0x00u,

            /* txFcPduId */
            /* @type:uint16 range:0x0000~0xFFFF note:reference to Can_TxPdu->pduId */
            0x01u
        },
        {
            /* rxDcmId */
            /* @type:uint16 range:0x0000~0xFFFF note:auto generate */
            0xFFu,

            /* rxPduId */
            /* @type:uint16 range:0x0000~0xFFFF note:reference to Can_RxPdu->pduId */
            0x12u,

            /* addressingFormat */
            /* @type:ENUM range:NONE note:NONE */
            CANTP_STANDARD,

            /* nSa */
            /* @type:uint8 range:0x00~0xFF note:NONE */
            0x00u,

            /* txFcPduId */
            /* @type:uint16 range:0x0000~0xFFFF note:reference to Can_TxPdu->pduId */
            0x01u
        },
    };

const CanTp_TxSduType CanTp_TxSdu[CANTP_TX_CHANNEL_NUM] =
    {
        {
            /* txDcmId */
            /* @type:uint16 range:0x0000~0xFFFF note:auto generate */
            0xAAu,

            /* txPduId */
            /* @type:uint16 range:0x0000~0xFFFF note:reference to Can_TxPdu->pduId */
            0x01u,

            /* rxFcPduId */
            /* @type:uint16 range:0x0000~0xFFFF note:reference to Can_RxPdu->pduId */
            0x11u,
        },
    };

/*=======[E N D   O F   F I L E]==============================================*/

