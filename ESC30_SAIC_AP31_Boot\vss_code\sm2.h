
#ifndef _VSS_SM2_H_
#define _VSS_SM2_H_

#include "ecc.h"
#include "vssconf.h"
#define RET_SM2_SUCCESS  (('R'<<24)|('S'<<16)|('2'<<8)|('S'))
#define RET_SM2_FAILED  (('R'<<24)|('S'<<16)|('2'<<8)|('F'))

#define mod8(x)   		(x%8)
#define WORDLENGTH_8	(8)
#define SM2_PARA_WLEN 8
#define SM2_NULL        0

extern const vss_uint32 G_SM2_Gx[8];
extern const vss_uint32 G_SM2_Gy[8];
extern const vss_uint32 G_SM2_a[8];
extern const vss_uint32 G_SM2_b[8];
extern const vss_uint32 G_SM2_p[8];
extern const vss_uint32 G_SM2_n[8];

typedef ecc_point_a sm2_point; 
typedef ecc_fp sm2_fp_para;

typedef struct
{
    vss_uint32  *r;
    vss_uint32  *s;
} sm2_sig;

#define RET_SM2_RAND_ERROR 				(('R'<<24)|('S'<<16)|('R'<<8)|('E'))
#define RET_SM2_GENKEY_ERROR			(('R'<<24)|('S'<<16)|('K'<<8)|('E'))
#define RET_SM2_GENKEY_SUCCESS			(('R'<<24)|('S'<<16)|('K'<<8)|('S'))
#define	RET_SM2_VERIFYPUBKEY_ERROR			(('R'<<24)|('S'<<16)|('V'<<8)|('E'))
#define RET_SM2_VERIFYPUBKEY_SUCCESS		(('R'<<24)|('S'<<16)|('V'<<8)|('S'))
#define RET_SM2_GENSIG_ERROR			(('R'<<24)|('S'<<16)|('G'<<8)|('E'))
#define RET_SM2_GENSIG_SUCCESS			(('R'<<24)|('S'<<16)|('G'<<8)|('S'))
#define RET_SM2_CHECKSIG_ERROR			(('R'<<24)|('S'<<16)|('C'<<8)|('E'))
#define RET_SM2_CHECKSIG_SUCCESS		(('R'<<24)|('S'<<16)|('C'<<8)|('S'))
#define RET_SM2_VERIFYSIG_ERROR			(('R'<<24)|('S'<<16)|('S'<<8)|('E'))
#define RET_SM2_VERIFYSIG_SUCCESS		(('R'<<24)|('S'<<16)|('S'<<8)|('S'))
#define RET_SM2_ENC_ERROR				(('R'<<24)|('S'<<16)|('E'<<8)|('E'))
#define RET_SM2_ENC_SUCCESS				(('R'<<24)|('S'<<16)|('E'<<8)|('S'))
#define RET_SM2_DEC_ERROR				(('R'<<24)|('S'<<16)|('D'<<8)|('E'))
#define RET_SM2_DEC_SUCCESS				(('R'<<24)|('S'<<16)|('D'<<8)|('S'))

vss_uint32 sm2_version(void);
vss_uint32 sm2_genkey(sm2_point *pa, vss_uint32 *da, vss_uint32 *sm2_rand);
vss_uint32 sm2_gensig(sm2_sig *sig, vss_uint32* content, vss_uint32 *da, vss_uint32 *sm2_rand);
vss_uint32 sm2_verifysig(sm2_sig *sig, vss_uint32 *content, sm2_point *pa);
vss_uint32 sm2_enc_c1(sm2_point *c1, sm2_point *kdf_para, sm2_point *pa, vss_uint32 *sm2_rand);
vss_uint32 sm2_enc_c2(vss_uint32 *c2, vss_uint32 *content, vss_uint32 data_bitlen, sm2_point *kdf_para, vss_uint32 *ct);
vss_uint32 sm2_dec_c1(sm2_point *kdf_para, sm2_point *c1, vss_uint32 *da, vss_uint32 *sm2_rand);
vss_uint32 sm2_dec_c2(vss_uint32 *result, vss_uint32 *c2, vss_uint32 data_bitlen, sm2_point *kdf_para, vss_uint32 *ct);
vss_uint32 sm2_c2(vss_uint32 *result, vss_uint32 *content, vss_uint32 data_bitlen, sm2_point *buf, vss_uint32 *ct);
vss_uint8 sm2_pub_enc(vss_uint8 sn, vss_uint32* x, vss_uint32* y, vss_uint8* in, vss_uint32 inLen, vss_uint8* out);
vss_uint8 sm2_pri_dec(vss_uint8 sn, vss_uint32* priKey, vss_uint8* in, vss_uint32 inLen, vss_uint8* out);

#endif
