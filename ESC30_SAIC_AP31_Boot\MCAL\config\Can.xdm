<?xml version='1.0'?>
<datamodel version="3.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/08/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/08/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd" >
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Can" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Can" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/AURIX/Can"/>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuildSelectable">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:lst name="CanConfigSet" type="MAP">
                <d:ctr name="CanConfigSet_0" type="IDENTIFIABLE">
                  <d:lst name="CanController" type="MAP">
                    <d:ctr name="CanController_0" type="IDENTIFIABLE">
                      <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                             value="POLLING"/>
                      <d:var name="CanControllerLoopbackEnable" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanControllerActivation" type="BOOLEAN" 
                             value="true"/>
                      <d:var name="CanControllerBaseAddress" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanControllerId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="CanHwControllerId" type="ENUMERATION" 
                             value="CONTROLLER_1_MULTICANPLUS_NODE1"/>
                      <d:var name="CanRxProcessing" type="ENUMERATION" 
                             value="POLLING"/>
                      <d:var name="CanTxProcessing" type="ENUMERATION" 
                             value="POLLING"/>
                      <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                             value="INTERRUPT">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanWakeupSupport" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanRxInputSelection" type="ENUMERATION" 
                             value="RXDCANxA"/>
                      <d:ref name="CanCpuClockRef" type="REFERENCE" 
                             value="ASPath:/Mcu/Mcu/McuModuleConfiguration_0/McuClockSettingConfig_0/McuClockReferencePoint"/>
                      <d:ref name="CanControllerDefaultBaudrate" 
                             type="REFERENCE" 
                             value="ASPath:/Can/Can/CanConfigSet_0/CanController_0/CanControllerBaudrateConfig_0"/>
                      <d:lst name="CanWakeupSourceRef"/>
                      <d:lst name="CanControllerBaudrateConfig" type="MAP">
                        <d:ctr name="CanControllerBaudrateConfig_0" 
                               type="IDENTIFIABLE">
                          <d:var name="CanControllerBaudRate" type="INTEGER" 
                                 value="500">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="5"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                        </d:ctr>
                      </d:lst>
                      <d:lst name="CanControllerFdBaudrateConfig" type="MAP">
                        <d:ctr name="CanControllerFdBaudrateConfig_0" 
                               type="IDENTIFIABLE">
                          <d:var name="CanControllerFdBaudRate" type="INTEGER" 
                                 value="2000"/>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="4"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="2"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="3"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="2"/>
                          <d:var 
                                 name="CanControllerTrcvDelayCompensationOffset" 
                                 type="INTEGER" value="0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CanControllerTxBitRateSwitch" 
                                 type="BOOLEAN" value="true"/>
                        </d:ctr>
                      </d:lst>
                      <d:lst name="CanFilterMask" type="MAP">
                        <d:ctr name="CanFilterMask_0" type="IDENTIFIABLE">
                          <d:var name="CanFilterMaskValue" type="INTEGER" 
                                 value="2047"/>
                        </d:ctr>
                      </d:lst>
                      <d:lst name="CanTTController" type="MAP"/>
                    </d:ctr>
                  </d:lst>
                  <d:lst name="CanHardwareObject" type="MAP">
                    <d:ctr name="CanHardwareObject_0" type="IDENTIFIABLE">
                      <d:var name="CanHandleType" type="ENUMERATION" 
                             value="FULL">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanIdType" type="ENUMERATION" 
                             value="STANDARD">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanIdValue" type="INTEGER" value="1875"/>
                      <d:var name="CanObjectId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="CanObjectType" type="ENUMERATION" 
                             value="RECEIVE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanControllerRef" type="REFERENCE" 
                             value="ASPath:/Can/Can/CanConfigSet_0/CanController_0"/>
                      <d:lst name="CanFilterMaskRef">
                        <d:ref type="REFERENCE" 
                               value="ASPath:/Can/Can/CanConfigSet_0/CanController_0/CanFilterMask_0"/>
                      </d:lst>
                      <d:var name="CanRxUserHandle" type="BOOLEAN" value="false"/>
                      <d:var name="CanMultiplexedHwObjects" type="INTEGER" 
                             value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTxObjPriorityClass" type="ENUMERATION" 
                             value="PRI_2">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:lst name="CanMainFunctionRWPeriodRef"/>
                      <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                    </d:ctr>
                    <d:ctr name="CanHardwareObject_1" type="IDENTIFIABLE">
                      <d:var name="CanHandleType" type="ENUMERATION" 
                             value="FULL">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanIdType" type="ENUMERATION" 
                             value="STANDARD">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanIdValue" type="INTEGER" value="2015"/>
                      <d:var name="CanObjectId" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="CanObjectType" type="ENUMERATION" 
                             value="RECEIVE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanControllerRef" type="REFERENCE" 
                             value="ASPath:/Can/Can/CanConfigSet_0/CanController_0"/>
                      <d:lst name="CanFilterMaskRef">
                        <d:ref type="REFERENCE" 
                               value="ASPath:/Can/Can/CanConfigSet_0/CanController_0/CanFilterMask_0"/>
                      </d:lst>
                      <d:var name="CanRxUserHandle" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanMultiplexedHwObjects" type="INTEGER" 
                             value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTxObjPriorityClass" type="ENUMERATION" 
                             value="PRI_2">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:lst name="CanMainFunctionRWPeriodRef"/>
                      <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                    </d:ctr>
                    <d:ctr name="CanHardwareObject_2" type="IDENTIFIABLE">
                      <d:var name="CanHandleType" type="ENUMERATION" 
                             value="FULL">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanIdType" type="ENUMERATION" 
                             value="STANDARD">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanIdValue" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanObjectId" type="INTEGER" value="2">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="CanObjectType" type="ENUMERATION" 
                             value="TRANSMIT"/>
                      <d:ref name="CanControllerRef" type="REFERENCE" 
                             value="ASPath:/Can/Can/CanConfigSet_0/CanController_0"/>
                      <d:lst name="CanFilterMaskRef"/>
                      <d:var name="CanRxUserHandle" type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanMultiplexedHwObjects" type="INTEGER" 
                             value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTxObjPriorityClass" type="ENUMERATION" 
                             value="PRI_2">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:lst name="CanMainFunctionRWPeriodRef"/>
                      <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                    </d:ctr>
                  </d:lst>
                  <d:ctr name="CanClockConfiguration" type="IDENTIFIABLE">
                    <d:var name="CanClockStepValue" type="INTEGER" value="1023">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="CanClockDividerMode" type="ENUMERATION" 
                           value="NORMAL_DIVIDER">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:lst name="CanFifoConfiguration" type="MAP"/>
                </d:ctr>
              </d:lst>
              <d:ctr name="CanGeneral" type="IDENTIFIABLE">
                <d:var name="CanDevErrorDetection" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanBaudRateClkSelErayPll" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanBaudRateClkSelExtOsc" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanHardwareCancellation" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanIdenticalIdCancellation" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanIndex" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="CanLPduReceiveCalloutFunction">
                  <d:var type="FUNCTION-NAME" 
                         value="CanLPudReceiveCalloutFunction"/>
                </d:lst>
                <d:lst name="CanMainFunctionBusoffPeriod">
                  <d:var type="FLOAT" value="0.0010">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:lst>
                <d:var name="CanMainFunctionModePeriod" type="FLOAT" 
                       value="0.0010">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="CanMainFunctionWakeupPeriod"/>
                <d:var name="CanMultiplexedTransmission" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanTimeoutDuration" type="FLOAT" value="0.0010">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanTimeoutDurationFactor" type="INTEGER" 
                       value="400">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanVersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanPBFixedAddress" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanDebugSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanChangeBaudrateApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanResetSfrAtInit" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanDeInitApi" type="BOOLEAN" value="true"/>
                <d:var name="CanISOFrameSupport" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanProtocolExceptionFeature" type="BOOLEAN" 
                       value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanFDMessageBytesLengthSupport" type="BOOLEAN" 
                       value="true"/>
                <d:ref name="CanCounterRef" type="REFERENCE" >
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:ref name="CanSupportTTCANRef" type="REFERENCE" >
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:lst name="CanMainFunctionRWPeriods" type="MAP">
                  <d:ctr name="CanMainFunctionRWPeriods_0" type="IDENTIFIABLE">
                    <d:lst name="CanMainFunctionReadPeriod">
                      <d:var type="FLOAT" value="0.0010">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:lst>
                    <d:lst name="CanMainFunctionWritePeriod">
                      <d:var type="FLOAT" value="0.0010">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:lst>
                  </d:ctr>
                </d:lst>
                <d:var name="CanRunningInUser0Mode" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanUserModeInitApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanUserModeDeInitApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanUserModeRuntimeApiEnable" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:lst name="CanDemEventParameterRefs" type="MAP"/>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArMajorVersion" type="INTEGER" value="4"/>
                <d:var name="ArMinorVersion" type="INTEGER" value="0"/>
                <d:var name="ArPatchVersion" type="INTEGER" value="0"/>
                <d:var name="SwMajorVersion" type="INTEGER" value="3"/>
                <d:var name="SwMinorVersion" type="INTEGER" value="2"/>
                <d:var name="SwPatchVersion" type="INTEGER" value="0"/>
                <d:var name="ModuleId" type="INTEGER" value="80">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="17">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="MCanP">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="Release" type="STRING" value="_TRICORE_TC233">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
