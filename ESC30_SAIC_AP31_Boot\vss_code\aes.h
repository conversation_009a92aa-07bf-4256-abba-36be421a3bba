
#ifndef _VSS_AES_H_
#define _VSS_AES_H_

#include "vsstype.h"
#include "vssconf.h"

#ifdef  __cplusplus
extern "C" {
#endif

#define TT_AES_ENCRYPT     1
#define TT_AES_DECRYPT     0

typedef struct
{
	vss_uint8 expansionkey[15*16];
} aes_context;

vss_uint32 aes_set_key(aes_context* ctx, vss_uint8 *key);
vss_uint32 aes_ecb_encrypt(vss_uint8 *in, vss_uint8 *out, vss_uint32 length, aes_context* ctx, vss_uint32 enc);

#ifdef  __cplusplus
}
#endif

#endif /* !HEADER_AES_H */
