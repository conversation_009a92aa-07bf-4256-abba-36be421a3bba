/*******************************************************************************
**                                                                            **
** Copyright (C) Infineon Technologies (2018)                                 **
**                                                                            **
** All rights reserved.                                                       **
**                                                                            **
** This document contains proprietary information belonging to Infineon       **
** Technologies. Passing on and copying of this document, and communication   **
** of its contents is not permitted without prior written authorization.      **
**                                                                            **
********************************************************************************
**                                                                            **
**  FILENAME  : Spi_PBCfg.c                                                   **
**                                                                            **
**  $CC VERSION : \main\94 $                                                 **
**                                                                            **
**  DATE, TIME: 2020-07-10, 19:49:42                                         **
**                                                                            **
**  GENERATOR : Build b141014-0350                                            **
**                                                                            **
**  AUTHOR    : DL-AUTOSAR-Engineering                                        **
**                                                                            **
**  VENDOR    : Infineon Technologies                                         **
**                                                                            **
**  DESCRIPTION  : SPI configuration generated out of ECU configuration       **
**                 file                                                       **
**                                                                            **
**  MAY BE CHANGED BY USER [yes/no]: no                                       **
**                                                                            **
*******************************************************************************/


/*******************************************************************************
**                      Includes                                              **
*******************************************************************************/

/* Include SPI Module File */
/* [cover parentID=DS_NAS_SPI_PR699,DS_NAS_SPI_PR709] */

#include "Spi.h"
/* Inclusion of Mcal Specific Global Header File */
#include "Mcal.h"

/*******************************************************************************
**                      Private Macro Definitions                             **
*******************************************************************************/
/*******************************************************************************
**                      Imported Compiler Switch Check                        **
*******************************************************************************/
/*******************************************************************************
**                      Private Type Definitions                              **
*******************************************************************************/
/*******************************************************************************
**                      Private Function Declarations                         **
*******************************************************************************/
/*******************************************************************************
**                      Global Constant Definitions                           **
*******************************************************************************/
/* MISRA RULE 87 VIOLATION: Inclusion of MemMap.h in between the code can't
   be avoided as it is required for mapping global variables, constants
   and code
*/
/* Violates MISRA Required Rule 16.9,
            function identifier used without '&' or parenthisized parameter list
           when using function pointer in configurations
*/

/*
                     Container: SpiChannelConfiguration
*/
#define SPI_START_SEC_POSTBUILDCFG
/*
 * To be used for global or static constants (unspecified size)
*/
#include "MemMap.h"
/*
Configuration : Channel Configuration Constant Structure.
The IB Channels are configured first followed by EB.
*/
static const Spi_ChannelConfigType Spi_kChannelConfig0[] =
{
/* EB Channel: SpiChannel_Micron_Master Configuration */
  {
    /* Default Data, SPI_DEFAULT_DATA */
    (uint32)0x00000000U,
    /* Data Configuration */
    Spi_DataConfig(8U, /* Data Width */
                   SPI_DATA_MSB_FIRST), /* Transfer Start */
    /* EB Channels : SPI112: Max EB Buffers  */
    (Spi_NumberOfDataType)511U,
    /* Buffer Type, SPI_CHANNEL_TYPE */
    (uint8)SPI_EB_BUFFER,
  },
};

/*
                    Container: SpiJobConfiguration
*/
/* Notification Function of SpiJob_Micron_Master is NULL_PTR */


/*
Configuration: Channel Assignment
*/
/*Channel Assignment of Job: SpiJob_Micron_Master*/
static const Spi_ChannelType SpiJob_Micron_Master_ChannelLinkPtr[] =
{
  SpiConf_SpiChannel_SpiChannel_Micron_Master,
  SPI_CHANNEL_LINK_DELIMITER
};

/*
Configuration: Job Configuration Constant Structure.
*/
static const Spi_JobConfigType Spi_kJobConfig0[] =
{

/* Job: SpiJob_Micron_Master Configuration */
  {
   /* Job End Notification: Spi_JobEndNotification, SPI118 */
    NULL_PTR,
    /* User given name outside the naming convention */
    /* Spi_ChannelLinkPtr */
    SpiJob_Micron_Master_ChannelLinkPtr,
    /* Baud Rate (10000000 Hz) Hw configuration Parameters */
    Spi_BaudRateParams(/*TQ*/(0x1U), /*Q*/(0x0U),
              /*A*/(0x2U), /*B*/(0x1U), /*C*/(0x1U)),

    /* Time Delay Configuration */
      (uint32)Spi_DelayParams(/*IPRE*/0x02U,/*IDLE*/ 0x01U,
        /*LPRE*/0x02U, /*LEAD*/0x01U,
        /*TPRE*/0x02U, /*TRAIL*/0x01U),

    SPI_CS_HW, /* Hw CS Pin is Selected */

    /*CS Active level Polarity*/
    SPI_CS_POLARITY_LOW,

 /* Shift Clock Configuration : Clock Idle Polarity: SPI_SHIFT_CLOCK_IDLE_LEVEL,
                                  Clock Phase: SPI_DATA_SHIFT_EDGE */
    Spi_ShiftClkConfig(SPI_CLK_IDLE_LOW,
                      SPI_DATA_SHIFT_TRAIL),
   /* Job Priority escalated to Maximum as it is mapped
      to one or more non-interruptible sequence */
    SPI_JOB_PRIORITY_3,

    /* Spi HW Unit. bit[7:4]: Channel no, bit[3:0]: hw module no */
      (uint8)((uint8)SPI_QSPI_CHANNEL10 << 4U) | (SPI_QSPI1_INDEX),

    /* Channel Based Chip Select */
    (uint8)0U,
    
    /* Spi Parity Selection */
    (uint8)SPI_QSPI_PARITY_UNUSED,
    
  }
};

/*
                     Container: Spi_SequenceConfiguration
*/
/* Notification Function of Sequence: SpiSequence_Micron_Master is NULL_PTR */

/*
Configuration: Job Assignment
*/
/* Job Assignment of Sequence: SpiSequence_Micron_Master */
static const Spi_JobType SpiSequence_Micron_Master_JobLinkPtr[] =
{
  SpiConf_SpiJob_SpiJob_Micron_Master,
  SPI_JOB_LINK_DELIMITER
};

/*
Configuration: Sequence Configuration Constant Structure.
*/
static const Spi_SequenceConfigType Spi_kSequenceConfig0[] =
{   /* Sequence: SpiSequence_Micron_Master Configuration */
  {
    /* Spi_SeqEndNotification */
    NULL_PTR,
    /* User given name outside the naming convention */    /* Spi_JobLinkPtr */
    SpiSequence_Micron_Master_JobLinkPtr,
    /* User given name outside the naming convention */
    /* Sequences that share jobs with this sequence */
    NULL_PTR,
     /* This holds the total number of jobs linked to this sequence */
    1U,
    /* Sequence Interruptible or Not (SPI125, SPI126) */
    SPI_SEQ_INT_FALSE   }
};

static const Spi_DmaConfigType Spi_kDmaConfig0[]=
{

  { /* QSPI0 Module Hw Dma Config */
    DMA_CHANNEL_INVALID,  /* Tx */    DMA_CHANNEL_INVALID   /* Rx */  },

  { /* QSPI1 Module Hw Dma Config */
    DMA_CHANNEL4,  /* Tx */    DMA_CHANNEL5   /* Rx */  },

  { /* QSPI2 Module Hw Dma Config */
    DMA_CHANNEL_INVALID,  /* Tx */    DMA_CHANNEL_INVALID   /* Rx */  },

  { /* QSPI3 Module Hw Dma Config */
    DMA_CHANNEL_INVALID,  /* Tx */    DMA_CHANNEL_INVALID   /* Rx */  },

};

static const Spi_HWModuleConfigType Spi_kModuleConfig0[]=
{
  /* QSPI0 Module */
  {
    SPI_0_NOT_CONFIGURED,
    (uint32)0x0U,
    SPI_0_NOT_CONFIGURED,
    NULL_PTR,
  },
  /* QSPI1 Module */
  {
      /*Clock Settings:Sleep Control Disabled*/
      SPI_CLK_SLEEP_DISABLE,
	  /*SSOC register value for QSPI1*/
	  (uint32)0x0U,
      SPI_QSPI1_MRIS_SEL,
      &Spi_kDmaConfig0[1U],
  },
  /* QSPI2 Module */
  {
    SPI_2_NOT_CONFIGURED,
    (uint32)0x0U,
    SPI_2_NOT_CONFIGURED,
    NULL_PTR,
  },
  /* QSPI3 Module */
  {
    SPI_3_NOT_CONFIGURED,
    (uint32)0x0U,
    SPI_3_NOT_CONFIGURED,
    NULL_PTR,
  },
};


static const Spi_BaudrateEconType Spi_kBaudrateEcon0[]=
{
  {
    Spi_BaudRateECON(0x0U, 0x2U, 0x1U, 0x1U,
    SPI_DATA_SHIFT_TRAIL,
    SPI_CLK_IDLE_LOW,
    SPI_QSPI_PARITY_DISABLE),
    (uint8)((uint8)SPI_QSPI_CHANNEL10 << 4U) | (SPI_QSPI1_INDEX)
  }
};

const Spi_ConfigType Spi_ConfigRoot[1U] =
{
  {
    Spi_kChannelConfig0,
    Spi_kJobConfig0,
    Spi_kSequenceConfig0,
    {
      &Spi_kModuleConfig0[0U],
      &Spi_kModuleConfig0[1U],
      &Spi_kModuleConfig0[2U],
      &Spi_kModuleConfig0[3U],
    },
    Spi_kBaudrateEcon0,
    (Spi_JobType)(sizeof(Spi_kJobConfig0) / sizeof(Spi_JobConfigType)),
    (Spi_ChannelType)(sizeof(Spi_kChannelConfig0) / \
                      sizeof(Spi_ChannelConfigType)),
    (Spi_SequenceType)(sizeof(Spi_kSequenceConfig0) / \
                                        sizeof(Spi_SequenceConfigType)),
    (uint8)(sizeof(Spi_kBaudrateEcon0) / sizeof(Spi_BaudrateEconType)),
  }
};


#define SPI_STOP_SEC_POSTBUILDCFG
/* Allows to map variables, constants and code of modules to individual
  memory sections.*/
/*IFX_MISRA_RULE_19_01_STATUS=File inclusion after pre-processor directives is
allowed only for MemMap.h*/  
#include "MemMap.h"

/*******************************************************************************
**                      Global Variable Definitions                           **
*******************************************************************************/
/*******************************************************************************
**                      Private Constant Definitions                          **
*******************************************************************************/
/*******************************************************************************
**                      Private Variable Definitions                          **
*******************************************************************************/
/*******************************************************************************
**                      Global Function Definitions                           **
*******************************************************************************/
/*******************************************************************************
**                      Private Function Definitions                          **
*******************************************************************************/
/* General Notes */
/*
SPI095: The code file structure shall not be defined within this specification
completely. At this point it shall be pointed out that the code-file structure
shall include the following file named:
- Spi_Lcfg.c ?for link time and for post-build configurable parameters and
- Spi_PBcfg.c ?for post build time configurable parameters.
These files shall contain all link time and post-build time configurable
parameters.
This file shall contain all link time and post-build time configurable
parameters.
For the implementation of VariantPC, the implementation stores the
pre compile time parameters that have to be defined as constants in this file.

SPI123: In this configuration, all Sequences declared are considered as Non
Interruptible Sequences. That means, their dedicated parameter
SPI_INTERRUPTIBLE_SEQUENCE (see SPI064 & SPI106) could be omitted or the
FALSE value should be used as default.

*/


