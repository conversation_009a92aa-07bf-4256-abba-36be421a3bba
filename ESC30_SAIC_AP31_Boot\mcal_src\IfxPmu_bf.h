/**
 * \file IfxPmu_bf.h
 * \brief
 * \copyright Copyright (c) 2016 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC23XADAS_UM_V1.1.R1
 * Specification: tc23xadas_um_sfrs_MCSFR.xml (Revision: UM_V1.1)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Pmu_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Pmu
 * 
 */
#ifndef IFXPMU_BF_H
#define IFXPMU_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Pmu_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_PMU_ID_Bits.MODNUMBER */
#define IFX_PMU_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_PMU_ID_Bits.MODNUMBER */
#define IFX_PMU_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_PMU_ID_Bits.MODNUMBER */
#define IFX_PMU_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_PMU_ID_Bits.MODREV */
#define IFX_PMU_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_PMU_ID_Bits.MODREV */
#define IFX_PMU_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_PMU_ID_Bits.MODREV */
#define IFX_PMU_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_PMU_ID_Bits.MODTYPE */
#define IFX_PMU_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_PMU_ID_Bits.MODTYPE */
#define IFX_PMU_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_PMU_ID_Bits.MODTYPE */
#define IFX_PMU_ID_MODTYPE_OFF (8u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXPMU_BF_H */
