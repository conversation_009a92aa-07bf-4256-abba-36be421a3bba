/*============================================================================*/
/** Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
 *  
 *  All rights reserved. This software is iSOFT property. Duplication 
 *  or disclosure without iSOFT written authorization is prohibited.
 *  
 *  @file       <Dcm_Dsp.h>
 *  @brief      <Macro and function decalrations for Dcm Module>
 *  
 *  <Compiler: CodeWarrior    MCU:9S12G64>
 *  
 *  <AUTHOR>
 *  @date       <2012-11-09>
 */
/*============================================================================*/

#ifndef DCM_DSP_H
#define DCM_DSP_H

/*=======[R E V I S I O N   H I S T O R Y]====================================*/
/** <VERSION>  <DATE>  <AUTHOR>     <REVISION LOG>
 *  V1.0    20121109    Gary       Initial version
 */
/*============================================================================*/

/*=======[I N C L U D E S]====================================================*/
#include "Dcm.h"
#include "Dcm_Cfg.h"
#include "Seedkey.h"
#include "FL.h"

/*=======[E X T E R N A L   F U N C T I O N   D E C L A R A T I O N S]========*/
extern void Dcm_RecvMsg10(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);
#if(DCM_SERVICE_11_ENABLE == STD_ON)
extern void Dcm_RecvMsg11(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);
#endif
#if((DCM_READDID_NUM > 0) && (DCM_SERVICE_22_ENABLE == STD_ON))
extern void Dcm_RecvMsg22(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);
#endif
extern void Dcm_RecvMsg2E(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_RecvMsg27(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_RecvMsg28(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_RecvMsg34(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_RecvMsg36(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_RecvMsg37(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_RecvMsg31(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_CheckProgPreCondition(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_CheckProgDependencies(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_CheckSWVerification(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_CheckMemory(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_EraseMemory(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_RawEraseMemory(const uint32 startAdd,const uint32 length);


extern void Dcm_RecvMsg3E(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_RecvMsg85(const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_Pending27(const FL_ResultType errorCode,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_Pending2E(const FL_ResultType errorCode,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_Pending36(const FL_ResultType errorCode,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_Pending34(const FL_ResultType errorCode,const Dcm_BuffType * rxBuff,Dcm_BuffType * txBuff);
extern void Dcm_Pending37(const FL_ResultType errorCode,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);
extern void Dcm_Pending31(const FL_ResultType errorCode,
    const Dcm_BuffType * rxBuff,
    Dcm_BuffType * txBuff);

extern void Dcm_DIDEraseMemory(const Dcm_BuffType * rxBuff,
	    Dcm_BuffType * txBuff);

#endif/* endof DCM_DSP_H */

/*=======[E N D   O F   F I L E]==============================================*/
