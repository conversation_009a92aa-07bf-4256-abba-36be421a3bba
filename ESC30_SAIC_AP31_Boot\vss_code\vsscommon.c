#include "vsscommon.h"
#include "vssconf.h"

#pragma section code "vss_api_code" 

vss_sint32 VssA2I(const char*  content)
{
	vss_sint32 rt = 0;
	vss_sint32 sig = 1;
	vss_uint32 off = 0;

	if(content == VSS_NULL)
		return 0;

	if(content[off] == '-')
		sig = -1;

	if(content[off] == '-' ||content[off] == '+' )
		off++;

	while (content[off] >= '0' && content[off] <= '9')
	{
		rt = rt * 10 + (content[off] - '0');
		off++;
	}

	return rt * sig;
}


//len:输入数据字数
void mem_set(vss_uint32 *result, const vss_uint32 content, vss_uint32 len)
{
	vss_uint32 i = 0;
	if(len < 1)	return;

	for (; i < len; i++)
		result[i]=content;
}

//len:输入数据字节数
void mem_set8(vss_uint8 *result, const vss_uint8 content, vss_uint32 len)
{
#if (defined (_MPU_LINUX_)&&(_MPU_LINUX_ == 1U))
	memset(result, content, len);
#else
	vss_uint32 i = 0;
	if(len < 1)	return;

	for (; i < len; i++)
		result[i] = content;
#endif
}

//len:输入数据字节数
void mem_cpy(vss_uint32 *result, const vss_uint32 *content, vss_uint32 len)
{
#if (defined (_MPU_LINUX_)&&(_MPU_LINUX_ == 1U))
	memcpy(result, content, len * sizeof(vss_uint32));
#else
	vss_uint32 i = 0;
	if(len < 1)	return;

	for (; i < len; i++)
		result[i]=content[i];
#endif
}

//len:锟斤拷锟斤拷锟斤拷锟斤拷纸锟斤拷锟?
void mem_cpy8(vss_uint8 *result, const vss_uint8 *content, vss_uint32 len)
{
#if (defined (_MPU_LINUX_)&&(_MPU_LINUX_ == 1U))
	memcpy(result, content, len);
#else
	vss_uint32 i = 0;

	if(len < 1)	return;

	for (; i < len; i++)
		result[i] = content[i];
#endif
}

//len:输入数据字节数
vss_uint8 mem_cmp(const   vss_uint32 *data1, const vss_uint32 *data2, vss_uint32 len)
{
	vss_sint32 i;
	
	if(len < 1)	return EQUAL;

	for (i=len-1;i>=0;i--)
		if (data1[i]>data2[i])
			return BIGGER;
		else if (data1[i]<data2[i])
			return SMALLER;

	return EQUAL;
}

//len:输入数据字节数
vss_uint8 mem_cmp8(const vss_uint8 *data1, const vss_uint8 *data2, vss_uint32 len)
{
#if (defined (_MPU_LINUX_)&&(_MPU_LINUX_ == 1U))
	memcmp(data1, data2, len);
#else
	vss_sint32 i = 0;
	
	if(len < 1)	return EQUAL;

	for (i = len - 1; i >= 0; i--)
	{
		if (data1[i] > data2[i])
			return BIGGER;
		else if (data1[i] < data2[i])
			return SMALLER;
	}

	return EQUAL;
#endif
}

//len:输入数据字数
vss_uint8 mem_cmp2(const vss_uint32 *data1, const vss_uint32 data2, vss_uint32 len)
{
	vss_uint32 i;

	for (i=len-1;i>=1;i--)
		if (data1[i]!=0)
			return BIGGER;
	if (data1[0]==data2)
		return EQUAL;
	else if (data1[0]>data2)
		return BIGGER;
	else
		return SMALLER;
}

//len:输入数据字数
void mem_swap(vss_uint32 *data1, vss_uint32 *data2, vss_uint32 len)
{
	vss_uint32 i;
	vss_uint32 temp;

	for (i=0;i<len;i++)
	{
		temp=data1[i];
		data1[i]=data2[i];
		data2[i]=temp;
	}
}

//len:输入数据字数
void mem_and(vss_uint32 *result, const vss_uint32 *data1, const vss_uint32 *data2, vss_uint32 len)
{
	vss_uint32 i;

	for (i=0;i<len;i++)
		result[i]=data1[i] & data2[i];
}

//len:输入数据字数
void mem_or(vss_uint32 *result, const vss_uint32 *data1, const vss_uint32 *data2, vss_uint32 len)
{
	vss_uint32 i;

	for (i=0;i<len;i++)
		result[i]=data1[i] | data2[i];
}

//len:输入数据字数
void mem_xor(vss_uint32 *result, const vss_uint32 *data1, const vss_uint32 *data2, vss_uint32 len)
{
	vss_uint32 i;

	for (i=0;i<len;i++)
		result[i]=data1[i] ^ data2[i];
}

vss_sint32 VssStrLen(const char*  content)
{
 vss_sint32 rt = 0;

 if(content == VSS_NULL)
  return 0;


 while (content[rt++] != '\0')
 {
 }

 return rt;
}


#pragma section code restore


