/**
 * \file IfxGtm_bf.h
 * \brief
 * \copyright Copyright (c) 2016 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC23XADAS_UM_V1.1.R1
 * Specification: tc23xadas_um_sfrs_MCSFR.xml (Revision: UM_V1.1)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Gtm_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Gtm
 * 
 */
#ifndef IFXGTM_BF_H
#define IFXGTM_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Gtm_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN0 */
#define IFX_GTM_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN0 */
#define IFX_GTM_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN0 */
#define IFX_GTM_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN10 */
#define IFX_GTM_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN10 */
#define IFX_GTM_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN10 */
#define IFX_GTM_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN11 */
#define IFX_GTM_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN11 */
#define IFX_GTM_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN11 */
#define IFX_GTM_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN12 */
#define IFX_GTM_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN12 */
#define IFX_GTM_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN12 */
#define IFX_GTM_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN13 */
#define IFX_GTM_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN13 */
#define IFX_GTM_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN13 */
#define IFX_GTM_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN14 */
#define IFX_GTM_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN14 */
#define IFX_GTM_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN14 */
#define IFX_GTM_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN15 */
#define IFX_GTM_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN15 */
#define IFX_GTM_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN15 */
#define IFX_GTM_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN16 */
#define IFX_GTM_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN16 */
#define IFX_GTM_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN16 */
#define IFX_GTM_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN17 */
#define IFX_GTM_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN17 */
#define IFX_GTM_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN17 */
#define IFX_GTM_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN18 */
#define IFX_GTM_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN18 */
#define IFX_GTM_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN18 */
#define IFX_GTM_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN19 */
#define IFX_GTM_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN19 */
#define IFX_GTM_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN19 */
#define IFX_GTM_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN1 */
#define IFX_GTM_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN1 */
#define IFX_GTM_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN1 */
#define IFX_GTM_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN20 */
#define IFX_GTM_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN20 */
#define IFX_GTM_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN20 */
#define IFX_GTM_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN21 */
#define IFX_GTM_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN21 */
#define IFX_GTM_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN21 */
#define IFX_GTM_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN22 */
#define IFX_GTM_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN22 */
#define IFX_GTM_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN22 */
#define IFX_GTM_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN23 */
#define IFX_GTM_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN23 */
#define IFX_GTM_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN23 */
#define IFX_GTM_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN24 */
#define IFX_GTM_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN24 */
#define IFX_GTM_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN24 */
#define IFX_GTM_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN25 */
#define IFX_GTM_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN25 */
#define IFX_GTM_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN25 */
#define IFX_GTM_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN26 */
#define IFX_GTM_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN26 */
#define IFX_GTM_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN26 */
#define IFX_GTM_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN27 */
#define IFX_GTM_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN27 */
#define IFX_GTM_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN27 */
#define IFX_GTM_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN28 */
#define IFX_GTM_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN28 */
#define IFX_GTM_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN28 */
#define IFX_GTM_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN29 */
#define IFX_GTM_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN29 */
#define IFX_GTM_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN29 */
#define IFX_GTM_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN2 */
#define IFX_GTM_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN2 */
#define IFX_GTM_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN2 */
#define IFX_GTM_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN30 */
#define IFX_GTM_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN30 */
#define IFX_GTM_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN30 */
#define IFX_GTM_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN31 */
#define IFX_GTM_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN31 */
#define IFX_GTM_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN31 */
#define IFX_GTM_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN3 */
#define IFX_GTM_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN3 */
#define IFX_GTM_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN3 */
#define IFX_GTM_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN4 */
#define IFX_GTM_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN4 */
#define IFX_GTM_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN4 */
#define IFX_GTM_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN5 */
#define IFX_GTM_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN5 */
#define IFX_GTM_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN5 */
#define IFX_GTM_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN6 */
#define IFX_GTM_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN6 */
#define IFX_GTM_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN6 */
#define IFX_GTM_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN7 */
#define IFX_GTM_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN7 */
#define IFX_GTM_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN7 */
#define IFX_GTM_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN8 */
#define IFX_GTM_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN8 */
#define IFX_GTM_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN8 */
#define IFX_GTM_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_GTM_ACCEN0_Bits.EN9 */
#define IFX_GTM_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_GTM_ACCEN0_Bits.EN9 */
#define IFX_GTM_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ACCEN0_Bits.EN9 */
#define IFX_GTM_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_GTM_ADCTRIG0OUT0_Bits.SEL0 */
#define IFX_GTM_ADCTRIG0OUT0_SEL0_LEN (4u)

/** \brief  Mask for Ifx_GTM_ADCTRIG0OUT0_Bits.SEL0 */
#define IFX_GTM_ADCTRIG0OUT0_SEL0_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_ADCTRIG0OUT0_Bits.SEL0 */
#define IFX_GTM_ADCTRIG0OUT0_SEL0_OFF (0u)

/** \brief  Length for Ifx_GTM_ADCTRIG0OUT0_Bits.SEL1 */
#define IFX_GTM_ADCTRIG0OUT0_SEL1_LEN (4u)

/** \brief  Mask for Ifx_GTM_ADCTRIG0OUT0_Bits.SEL1 */
#define IFX_GTM_ADCTRIG0OUT0_SEL1_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_ADCTRIG0OUT0_Bits.SEL1 */
#define IFX_GTM_ADCTRIG0OUT0_SEL1_OFF (4u)

/** \brief  Length for Ifx_GTM_ADCTRIG0OUT0_Bits.SEL2 */
#define IFX_GTM_ADCTRIG0OUT0_SEL2_LEN (4u)

/** \brief  Mask for Ifx_GTM_ADCTRIG0OUT0_Bits.SEL2 */
#define IFX_GTM_ADCTRIG0OUT0_SEL2_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_ADCTRIG0OUT0_Bits.SEL2 */
#define IFX_GTM_ADCTRIG0OUT0_SEL2_OFF (8u)

/** \brief  Length for Ifx_GTM_ADCTRIG0OUT0_Bits.SEL3 */
#define IFX_GTM_ADCTRIG0OUT0_SEL3_LEN (4u)

/** \brief  Mask for Ifx_GTM_ADCTRIG0OUT0_Bits.SEL3 */
#define IFX_GTM_ADCTRIG0OUT0_SEL3_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_ADCTRIG0OUT0_Bits.SEL3 */
#define IFX_GTM_ADCTRIG0OUT0_SEL3_OFF (12u)

/** \brief  Length for Ifx_GTM_ADCTRIG1OUT0_Bits.SEL0 */
#define IFX_GTM_ADCTRIG1OUT0_SEL0_LEN (4u)

/** \brief  Mask for Ifx_GTM_ADCTRIG1OUT0_Bits.SEL0 */
#define IFX_GTM_ADCTRIG1OUT0_SEL0_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_ADCTRIG1OUT0_Bits.SEL0 */
#define IFX_GTM_ADCTRIG1OUT0_SEL0_OFF (0u)

/** \brief  Length for Ifx_GTM_ADCTRIG1OUT0_Bits.SEL1 */
#define IFX_GTM_ADCTRIG1OUT0_SEL1_LEN (4u)

/** \brief  Mask for Ifx_GTM_ADCTRIG1OUT0_Bits.SEL1 */
#define IFX_GTM_ADCTRIG1OUT0_SEL1_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_ADCTRIG1OUT0_Bits.SEL1 */
#define IFX_GTM_ADCTRIG1OUT0_SEL1_OFF (4u)

/** \brief  Length for Ifx_GTM_ADCTRIG1OUT0_Bits.SEL2 */
#define IFX_GTM_ADCTRIG1OUT0_SEL2_LEN (4u)

/** \brief  Mask for Ifx_GTM_ADCTRIG1OUT0_Bits.SEL2 */
#define IFX_GTM_ADCTRIG1OUT0_SEL2_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_ADCTRIG1OUT0_Bits.SEL2 */
#define IFX_GTM_ADCTRIG1OUT0_SEL2_OFF (8u)

/** \brief  Length for Ifx_GTM_ADCTRIG1OUT0_Bits.SEL3 */
#define IFX_GTM_ADCTRIG1OUT0_SEL3_LEN (4u)

/** \brief  Mask for Ifx_GTM_ADCTRIG1OUT0_Bits.SEL3 */
#define IFX_GTM_ADCTRIG1OUT0_SEL3_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_ADCTRIG1OUT0_Bits.SEL3 */
#define IFX_GTM_ADCTRIG1OUT0_SEL3_OFF (12u)

/** \brief  Length for Ifx_GTM_AEI_ADDR_XPT_Bits.TO_ADDR */
#define IFX_GTM_AEI_ADDR_XPT_TO_ADDR_LEN (20u)

/** \brief  Mask for Ifx_GTM_AEI_ADDR_XPT_Bits.TO_ADDR */
#define IFX_GTM_AEI_ADDR_XPT_TO_ADDR_MSK (0xfffffu)

/** \brief  Offset for Ifx_GTM_AEI_ADDR_XPT_Bits.TO_ADDR */
#define IFX_GTM_AEI_ADDR_XPT_TO_ADDR_OFF (0u)

/** \brief  Length for Ifx_GTM_AEI_ADDR_XPT_Bits.TO_W1R0 */
#define IFX_GTM_AEI_ADDR_XPT_TO_W1R0_LEN (1u)

/** \brief  Mask for Ifx_GTM_AEI_ADDR_XPT_Bits.TO_W1R0 */
#define IFX_GTM_AEI_ADDR_XPT_TO_W1R0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_AEI_ADDR_XPT_Bits.TO_W1R0 */
#define IFX_GTM_AEI_ADDR_XPT_TO_W1R0_OFF (20u)

/** \brief  Length for Ifx_GTM_BRIDGE_MODE_Bits.BRG_MODE */
#define IFX_GTM_BRIDGE_MODE_BRG_MODE_LEN (1u)

/** \brief  Mask for Ifx_GTM_BRIDGE_MODE_Bits.BRG_MODE */
#define IFX_GTM_BRIDGE_MODE_BRG_MODE_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_BRIDGE_MODE_Bits.BRG_MODE */
#define IFX_GTM_BRIDGE_MODE_BRG_MODE_OFF (0u)

/** \brief  Length for Ifx_GTM_BRIDGE_MODE_Bits.BRG_RST */
#define IFX_GTM_BRIDGE_MODE_BRG_RST_LEN (1u)

/** \brief  Mask for Ifx_GTM_BRIDGE_MODE_Bits.BRG_RST */
#define IFX_GTM_BRIDGE_MODE_BRG_RST_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_BRIDGE_MODE_Bits.BRG_RST */
#define IFX_GTM_BRIDGE_MODE_BRG_RST_OFF (16u)

/** \brief  Length for Ifx_GTM_BRIDGE_MODE_Bits.BUFF_DPT */
#define IFX_GTM_BRIDGE_MODE_BUFF_DPT_LEN (8u)

/** \brief  Mask for Ifx_GTM_BRIDGE_MODE_Bits.BUFF_DPT */
#define IFX_GTM_BRIDGE_MODE_BUFF_DPT_MSK (0xffu)

/** \brief  Offset for Ifx_GTM_BRIDGE_MODE_Bits.BUFF_DPT */
#define IFX_GTM_BRIDGE_MODE_BUFF_DPT_OFF (24u)

/** \brief  Length for Ifx_GTM_BRIDGE_MODE_Bits.BUFF_OVL */
#define IFX_GTM_BRIDGE_MODE_BUFF_OVL_LEN (1u)

/** \brief  Mask for Ifx_GTM_BRIDGE_MODE_Bits.BUFF_OVL */
#define IFX_GTM_BRIDGE_MODE_BUFF_OVL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_BRIDGE_MODE_Bits.BUFF_OVL */
#define IFX_GTM_BRIDGE_MODE_BUFF_OVL_OFF (9u)

/** \brief  Length for Ifx_GTM_BRIDGE_MODE_Bits.MODE_UP_PGR */
#define IFX_GTM_BRIDGE_MODE_MODE_UP_PGR_LEN (1u)

/** \brief  Mask for Ifx_GTM_BRIDGE_MODE_Bits.MODE_UP_PGR */
#define IFX_GTM_BRIDGE_MODE_MODE_UP_PGR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_BRIDGE_MODE_Bits.MODE_UP_PGR */
#define IFX_GTM_BRIDGE_MODE_MODE_UP_PGR_OFF (8u)

/** \brief  Length for Ifx_GTM_BRIDGE_MODE_Bits.MSK_WR_RSP */
#define IFX_GTM_BRIDGE_MODE_MSK_WR_RSP_LEN (1u)

/** \brief  Mask for Ifx_GTM_BRIDGE_MODE_Bits.MSK_WR_RSP */
#define IFX_GTM_BRIDGE_MODE_MSK_WR_RSP_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_BRIDGE_MODE_Bits.MSK_WR_RSP */
#define IFX_GTM_BRIDGE_MODE_MSK_WR_RSP_OFF (1u)

/** \brief  Length for Ifx_GTM_BRIDGE_MODE_Bits.SYNC_INPUT_REG */
#define IFX_GTM_BRIDGE_MODE_SYNC_INPUT_REG_LEN (1u)

/** \brief  Mask for Ifx_GTM_BRIDGE_MODE_Bits.SYNC_INPUT_REG */
#define IFX_GTM_BRIDGE_MODE_SYNC_INPUT_REG_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_BRIDGE_MODE_Bits.SYNC_INPUT_REG */
#define IFX_GTM_BRIDGE_MODE_SYNC_INPUT_REG_OFF (12u)

/** \brief  Length for Ifx_GTM_BRIDGE_PTR1_Bits.ABT_TRAN_PGR */
#define IFX_GTM_BRIDGE_PTR1_ABT_TRAN_PGR_LEN (5u)

/** \brief  Mask for Ifx_GTM_BRIDGE_PTR1_Bits.ABT_TRAN_PGR */
#define IFX_GTM_BRIDGE_PTR1_ABT_TRAN_PGR_MSK (0x1fu)

/** \brief  Offset for Ifx_GTM_BRIDGE_PTR1_Bits.ABT_TRAN_PGR */
#define IFX_GTM_BRIDGE_PTR1_ABT_TRAN_PGR_OFF (15u)

/** \brief  Length for Ifx_GTM_BRIDGE_PTR1_Bits.FBC */
#define IFX_GTM_BRIDGE_PTR1_FBC_LEN (6u)

/** \brief  Mask for Ifx_GTM_BRIDGE_PTR1_Bits.FBC */
#define IFX_GTM_BRIDGE_PTR1_FBC_MSK (0x3fu)

/** \brief  Offset for Ifx_GTM_BRIDGE_PTR1_Bits.FBC */
#define IFX_GTM_BRIDGE_PTR1_FBC_OFF (20u)

/** \brief  Length for Ifx_GTM_BRIDGE_PTR1_Bits.FIRST_RSP_PTR */
#define IFX_GTM_BRIDGE_PTR1_FIRST_RSP_PTR_LEN (5u)

/** \brief  Mask for Ifx_GTM_BRIDGE_PTR1_Bits.FIRST_RSP_PTR */
#define IFX_GTM_BRIDGE_PTR1_FIRST_RSP_PTR_MSK (0x1fu)

/** \brief  Offset for Ifx_GTM_BRIDGE_PTR1_Bits.FIRST_RSP_PTR */
#define IFX_GTM_BRIDGE_PTR1_FIRST_RSP_PTR_OFF (5u)

/** \brief  Length for Ifx_GTM_BRIDGE_PTR1_Bits.NEW_TRAN_PTR */
#define IFX_GTM_BRIDGE_PTR1_NEW_TRAN_PTR_LEN (5u)

/** \brief  Mask for Ifx_GTM_BRIDGE_PTR1_Bits.NEW_TRAN_PTR */
#define IFX_GTM_BRIDGE_PTR1_NEW_TRAN_PTR_MSK (0x1fu)

/** \brief  Offset for Ifx_GTM_BRIDGE_PTR1_Bits.NEW_TRAN_PTR */
#define IFX_GTM_BRIDGE_PTR1_NEW_TRAN_PTR_OFF (0u)

/** \brief  Length for Ifx_GTM_BRIDGE_PTR1_Bits.RSP_TRAN_RDY */
#define IFX_GTM_BRIDGE_PTR1_RSP_TRAN_RDY_LEN (6u)

/** \brief  Mask for Ifx_GTM_BRIDGE_PTR1_Bits.RSP_TRAN_RDY */
#define IFX_GTM_BRIDGE_PTR1_RSP_TRAN_RDY_MSK (0x3fu)

/** \brief  Offset for Ifx_GTM_BRIDGE_PTR1_Bits.RSP_TRAN_RDY */
#define IFX_GTM_BRIDGE_PTR1_RSP_TRAN_RDY_OFF (26u)

/** \brief  Length for Ifx_GTM_BRIDGE_PTR1_Bits.TRAN_IN_PGR */
#define IFX_GTM_BRIDGE_PTR1_TRAN_IN_PGR_LEN (5u)

/** \brief  Mask for Ifx_GTM_BRIDGE_PTR1_Bits.TRAN_IN_PGR */
#define IFX_GTM_BRIDGE_PTR1_TRAN_IN_PGR_MSK (0x1fu)

/** \brief  Offset for Ifx_GTM_BRIDGE_PTR1_Bits.TRAN_IN_PGR */
#define IFX_GTM_BRIDGE_PTR1_TRAN_IN_PGR_OFF (10u)

/** \brief  Length for Ifx_GTM_BRIDGE_PTR2_Bits.TRAN_IN_PGR2 */
#define IFX_GTM_BRIDGE_PTR2_TRAN_IN_PGR2_LEN (5u)

/** \brief  Mask for Ifx_GTM_BRIDGE_PTR2_Bits.TRAN_IN_PGR2 */
#define IFX_GTM_BRIDGE_PTR2_TRAN_IN_PGR2_MSK (0x1fu)

/** \brief  Offset for Ifx_GTM_BRIDGE_PTR2_Bits.TRAN_IN_PGR2 */
#define IFX_GTM_BRIDGE_PTR2_TRAN_IN_PGR2_OFF (0u)

/** \brief  Length for Ifx_GTM_CLC_Bits.DISR */
#define IFX_GTM_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_GTM_CLC_Bits.DISR */
#define IFX_GTM_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_CLC_Bits.DISR */
#define IFX_GTM_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_GTM_CLC_Bits.DISS */
#define IFX_GTM_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_GTM_CLC_Bits.DISS */
#define IFX_GTM_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_CLC_Bits.DISS */
#define IFX_GTM_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_GTM_CLC_Bits.EDIS */
#define IFX_GTM_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_GTM_CLC_Bits.EDIS */
#define IFX_GTM_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_CLC_Bits.EDIS */
#define IFX_GTM_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_GTM_CMU_CLK0_5_CTRL_Bits.CLK_CNT */
#define IFX_GTM_CMU_CLK0_5_CTRL_CLK_CNT_LEN (24u)

/** \brief  Mask for Ifx_GTM_CMU_CLK0_5_CTRL_Bits.CLK_CNT */
#define IFX_GTM_CMU_CLK0_5_CTRL_CLK_CNT_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_CMU_CLK0_5_CTRL_Bits.CLK_CNT */
#define IFX_GTM_CMU_CLK0_5_CTRL_CLK_CNT_OFF (0u)

/** \brief  Length for Ifx_GTM_CMU_CLK_6_CTRL_Bits.CLK6_SEL */
#define IFX_GTM_CMU_CLK_6_CTRL_CLK6_SEL_LEN (1u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_6_CTRL_Bits.CLK6_SEL */
#define IFX_GTM_CMU_CLK_6_CTRL_CLK6_SEL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_6_CTRL_Bits.CLK6_SEL */
#define IFX_GTM_CMU_CLK_6_CTRL_CLK6_SEL_OFF (24u)

/** \brief  Length for Ifx_GTM_CMU_CLK_6_CTRL_Bits.CLK_CNT */
#define IFX_GTM_CMU_CLK_6_CTRL_CLK_CNT_LEN (24u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_6_CTRL_Bits.CLK_CNT */
#define IFX_GTM_CMU_CLK_6_CTRL_CLK_CNT_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_CMU_CLK_6_CTRL_Bits.CLK_CNT */
#define IFX_GTM_CMU_CLK_6_CTRL_CLK_CNT_OFF (0u)

/** \brief  Length for Ifx_GTM_CMU_CLK_7_CTRL_Bits.CLK7_SEL */
#define IFX_GTM_CMU_CLK_7_CTRL_CLK7_SEL_LEN (1u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_7_CTRL_Bits.CLK7_SEL */
#define IFX_GTM_CMU_CLK_7_CTRL_CLK7_SEL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_7_CTRL_Bits.CLK7_SEL */
#define IFX_GTM_CMU_CLK_7_CTRL_CLK7_SEL_OFF (24u)

/** \brief  Length for Ifx_GTM_CMU_CLK_7_CTRL_Bits.CLK_CNT */
#define IFX_GTM_CMU_CLK_7_CTRL_CLK_CNT_LEN (24u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_7_CTRL_Bits.CLK_CNT */
#define IFX_GTM_CMU_CLK_7_CTRL_CLK_CNT_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_CMU_CLK_7_CTRL_Bits.CLK_CNT */
#define IFX_GTM_CMU_CLK_7_CTRL_CLK_CNT_OFF (0u)

/** \brief  Length for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK0 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK0_LEN (2u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK0 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK0 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK0_OFF (0u)

/** \brief  Length for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK1 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK1_LEN (2u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK1 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK1 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK1_OFF (2u)

/** \brief  Length for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK2 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK2_LEN (2u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK2 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK2 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK2_OFF (4u)

/** \brief  Length for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK3 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK3_LEN (2u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK3 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK3 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK3_OFF (6u)

/** \brief  Length for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK4 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK4_LEN (2u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK4 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK4 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK4_OFF (8u)

/** \brief  Length for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK5 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK5_LEN (2u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK5 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK5 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK5_OFF (10u)

/** \brief  Length for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK6 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK6_LEN (2u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK6 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK6 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK6_OFF (12u)

/** \brief  Length for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK7 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK7_LEN (2u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK7 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_EN_Bits.EN_CLK7 */
#define IFX_GTM_CMU_CLK_EN_EN_CLK7_OFF (14u)

/** \brief  Length for Ifx_GTM_CMU_CLK_EN_Bits.EN_ECLK0 */
#define IFX_GTM_CMU_CLK_EN_EN_ECLK0_LEN (2u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_EN_Bits.EN_ECLK0 */
#define IFX_GTM_CMU_CLK_EN_EN_ECLK0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_EN_Bits.EN_ECLK0 */
#define IFX_GTM_CMU_CLK_EN_EN_ECLK0_OFF (16u)

/** \brief  Length for Ifx_GTM_CMU_CLK_EN_Bits.EN_ECLK1 */
#define IFX_GTM_CMU_CLK_EN_EN_ECLK1_LEN (2u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_EN_Bits.EN_ECLK1 */
#define IFX_GTM_CMU_CLK_EN_EN_ECLK1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_EN_Bits.EN_ECLK1 */
#define IFX_GTM_CMU_CLK_EN_EN_ECLK1_OFF (18u)

/** \brief  Length for Ifx_GTM_CMU_CLK_EN_Bits.EN_ECLK2 */
#define IFX_GTM_CMU_CLK_EN_EN_ECLK2_LEN (2u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_EN_Bits.EN_ECLK2 */
#define IFX_GTM_CMU_CLK_EN_EN_ECLK2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_EN_Bits.EN_ECLK2 */
#define IFX_GTM_CMU_CLK_EN_EN_ECLK2_OFF (20u)

/** \brief  Length for Ifx_GTM_CMU_CLK_EN_Bits.EN_FXCLK */
#define IFX_GTM_CMU_CLK_EN_EN_FXCLK_LEN (2u)

/** \brief  Mask for Ifx_GTM_CMU_CLK_EN_Bits.EN_FXCLK */
#define IFX_GTM_CMU_CLK_EN_EN_FXCLK_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_CMU_CLK_EN_Bits.EN_FXCLK */
#define IFX_GTM_CMU_CLK_EN_EN_FXCLK_OFF (22u)

/** \brief  Length for Ifx_GTM_CMU_ECLK_DEN_Bits.ECLK_DEN */
#define IFX_GTM_CMU_ECLK_DEN_ECLK_DEN_LEN (24u)

/** \brief  Mask for Ifx_GTM_CMU_ECLK_DEN_Bits.ECLK_DEN */
#define IFX_GTM_CMU_ECLK_DEN_ECLK_DEN_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_CMU_ECLK_DEN_Bits.ECLK_DEN */
#define IFX_GTM_CMU_ECLK_DEN_ECLK_DEN_OFF (0u)

/** \brief  Length for Ifx_GTM_CMU_ECLK_NUM_Bits.ECLK_NUM */
#define IFX_GTM_CMU_ECLK_NUM_ECLK_NUM_LEN (24u)

/** \brief  Mask for Ifx_GTM_CMU_ECLK_NUM_Bits.ECLK_NUM */
#define IFX_GTM_CMU_ECLK_NUM_ECLK_NUM_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_CMU_ECLK_NUM_Bits.ECLK_NUM */
#define IFX_GTM_CMU_ECLK_NUM_ECLK_NUM_OFF (0u)

/** \brief  Length for Ifx_GTM_CMU_FXCLK_CTRL_Bits.FXCLK_SEL */
#define IFX_GTM_CMU_FXCLK_CTRL_FXCLK_SEL_LEN (4u)

/** \brief  Mask for Ifx_GTM_CMU_FXCLK_CTRL_Bits.FXCLK_SEL */
#define IFX_GTM_CMU_FXCLK_CTRL_FXCLK_SEL_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_CMU_FXCLK_CTRL_Bits.FXCLK_SEL */
#define IFX_GTM_CMU_FXCLK_CTRL_FXCLK_SEL_OFF (0u)

/** \brief  Length for Ifx_GTM_CMU_GCLK_DEN_Bits.GCLK_DEN */
#define IFX_GTM_CMU_GCLK_DEN_GCLK_DEN_LEN (24u)

/** \brief  Mask for Ifx_GTM_CMU_GCLK_DEN_Bits.GCLK_DEN */
#define IFX_GTM_CMU_GCLK_DEN_GCLK_DEN_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_CMU_GCLK_DEN_Bits.GCLK_DEN */
#define IFX_GTM_CMU_GCLK_DEN_GCLK_DEN_OFF (0u)

/** \brief  Length for Ifx_GTM_CMU_GCLK_NUM_Bits.GCLK_NUM */
#define IFX_GTM_CMU_GCLK_NUM_GCLK_NUM_LEN (24u)

/** \brief  Mask for Ifx_GTM_CMU_GCLK_NUM_Bits.GCLK_NUM */
#define IFX_GTM_CMU_GCLK_NUM_GCLK_NUM_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_CMU_GCLK_NUM_Bits.GCLK_NUM */
#define IFX_GTM_CMU_GCLK_NUM_GCLK_NUM_OFF (0u)

/** \brief  Length for Ifx_GTM_CTRL_Bits.RF_PROT */
#define IFX_GTM_CTRL_RF_PROT_LEN (1u)

/** \brief  Mask for Ifx_GTM_CTRL_Bits.RF_PROT */
#define IFX_GTM_CTRL_RF_PROT_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_CTRL_Bits.RF_PROT */
#define IFX_GTM_CTRL_RF_PROT_OFF (0u)

/** \brief  Length for Ifx_GTM_CTRL_Bits.TO_MODE */
#define IFX_GTM_CTRL_TO_MODE_LEN (1u)

/** \brief  Mask for Ifx_GTM_CTRL_Bits.TO_MODE */
#define IFX_GTM_CTRL_TO_MODE_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_CTRL_Bits.TO_MODE */
#define IFX_GTM_CTRL_TO_MODE_OFF (1u)

/** \brief  Length for Ifx_GTM_CTRL_Bits.TO_VAL */
#define IFX_GTM_CTRL_TO_VAL_LEN (5u)

/** \brief  Mask for Ifx_GTM_CTRL_Bits.TO_VAL */
#define IFX_GTM_CTRL_TO_VAL_MSK (0x1fu)

/** \brief  Offset for Ifx_GTM_CTRL_Bits.TO_VAL */
#define IFX_GTM_CTRL_TO_VAL_OFF (4u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.I1SEL_1 */
#define IFX_GTM_DTM_CH_CTRL1_I1SEL_1_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.I1SEL_1 */
#define IFX_GTM_DTM_CH_CTRL1_I1SEL_1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.I1SEL_1 */
#define IFX_GTM_DTM_CH_CTRL1_I1SEL_1_OFF (9u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.I1SEL_2 */
#define IFX_GTM_DTM_CH_CTRL1_I1SEL_2_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.I1SEL_2 */
#define IFX_GTM_DTM_CH_CTRL1_I1SEL_2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.I1SEL_2 */
#define IFX_GTM_DTM_CH_CTRL1_I1SEL_2_OFF (17u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.I1SEL_3 */
#define IFX_GTM_DTM_CH_CTRL1_I1SEL_3_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.I1SEL_3 */
#define IFX_GTM_DTM_CH_CTRL1_I1SEL_3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.I1SEL_3 */
#define IFX_GTM_DTM_CH_CTRL1_I1SEL_3_OFF (25u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.O1F_0 */
#define IFX_GTM_DTM_CH_CTRL1_O1F_0_LEN (2u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.O1F_0 */
#define IFX_GTM_DTM_CH_CTRL1_O1F_0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.O1F_0 */
#define IFX_GTM_DTM_CH_CTRL1_O1F_0_OFF (4u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.O1F_1 */
#define IFX_GTM_DTM_CH_CTRL1_O1F_1_LEN (2u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.O1F_1 */
#define IFX_GTM_DTM_CH_CTRL1_O1F_1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.O1F_1 */
#define IFX_GTM_DTM_CH_CTRL1_O1F_1_OFF (12u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.O1F_2 */
#define IFX_GTM_DTM_CH_CTRL1_O1F_2_LEN (2u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.O1F_2 */
#define IFX_GTM_DTM_CH_CTRL1_O1F_2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.O1F_2 */
#define IFX_GTM_DTM_CH_CTRL1_O1F_2_OFF (20u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.O1F_3 */
#define IFX_GTM_DTM_CH_CTRL1_O1F_3_LEN (2u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.O1F_3 */
#define IFX_GTM_DTM_CH_CTRL1_O1F_3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.O1F_3 */
#define IFX_GTM_DTM_CH_CTRL1_O1F_3_OFF (28u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.O1SEL_0 */
#define IFX_GTM_DTM_CH_CTRL1_O1SEL_0_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.O1SEL_0 */
#define IFX_GTM_DTM_CH_CTRL1_O1SEL_0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.O1SEL_0 */
#define IFX_GTM_DTM_CH_CTRL1_O1SEL_0_OFF (0u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.O1SEL_1 */
#define IFX_GTM_DTM_CH_CTRL1_O1SEL_1_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.O1SEL_1 */
#define IFX_GTM_DTM_CH_CTRL1_O1SEL_1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.O1SEL_1 */
#define IFX_GTM_DTM_CH_CTRL1_O1SEL_1_OFF (8u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.O1SEL_2 */
#define IFX_GTM_DTM_CH_CTRL1_O1SEL_2_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.O1SEL_2 */
#define IFX_GTM_DTM_CH_CTRL1_O1SEL_2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.O1SEL_2 */
#define IFX_GTM_DTM_CH_CTRL1_O1SEL_2_OFF (16u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.O1SEL_3 */
#define IFX_GTM_DTM_CH_CTRL1_O1SEL_3_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.O1SEL_3 */
#define IFX_GTM_DTM_CH_CTRL1_O1SEL_3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.O1SEL_3 */
#define IFX_GTM_DTM_CH_CTRL1_O1SEL_3_OFF (24u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.SH_EN_1 */
#define IFX_GTM_DTM_CH_CTRL1_SH_EN_1_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.SH_EN_1 */
#define IFX_GTM_DTM_CH_CTRL1_SH_EN_1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.SH_EN_1 */
#define IFX_GTM_DTM_CH_CTRL1_SH_EN_1_OFF (10u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.SH_EN_2 */
#define IFX_GTM_DTM_CH_CTRL1_SH_EN_2_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.SH_EN_2 */
#define IFX_GTM_DTM_CH_CTRL1_SH_EN_2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.SH_EN_2 */
#define IFX_GTM_DTM_CH_CTRL1_SH_EN_2_OFF (18u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.SH_EN_3 */
#define IFX_GTM_DTM_CH_CTRL1_SH_EN_3_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.SH_EN_3 */
#define IFX_GTM_DTM_CH_CTRL1_SH_EN_3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.SH_EN_3 */
#define IFX_GTM_DTM_CH_CTRL1_SH_EN_3_OFF (26u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.SWAP_0 */
#define IFX_GTM_DTM_CH_CTRL1_SWAP_0_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.SWAP_0 */
#define IFX_GTM_DTM_CH_CTRL1_SWAP_0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.SWAP_0 */
#define IFX_GTM_DTM_CH_CTRL1_SWAP_0_OFF (3u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.SWAP_1 */
#define IFX_GTM_DTM_CH_CTRL1_SWAP_1_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.SWAP_1 */
#define IFX_GTM_DTM_CH_CTRL1_SWAP_1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.SWAP_1 */
#define IFX_GTM_DTM_CH_CTRL1_SWAP_1_OFF (11u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.SWAP_2 */
#define IFX_GTM_DTM_CH_CTRL1_SWAP_2_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.SWAP_2 */
#define IFX_GTM_DTM_CH_CTRL1_SWAP_2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.SWAP_2 */
#define IFX_GTM_DTM_CH_CTRL1_SWAP_2_OFF (19u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL1_Bits.SWAP_3 */
#define IFX_GTM_DTM_CH_CTRL1_SWAP_3_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL1_Bits.SWAP_3 */
#define IFX_GTM_DTM_CH_CTRL1_SWAP_3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL1_Bits.SWAP_3 */
#define IFX_GTM_DTM_CH_CTRL1_SWAP_3_OFF (27u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.DT0_0 */
#define IFX_GTM_DTM_CH_CTRL2_DT0_0_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.DT0_0 */
#define IFX_GTM_DTM_CH_CTRL2_DT0_0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.DT0_0 */
#define IFX_GTM_DTM_CH_CTRL2_DT0_0_OFF (3u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.DT0_1 */
#define IFX_GTM_DTM_CH_CTRL2_DT0_1_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.DT0_1 */
#define IFX_GTM_DTM_CH_CTRL2_DT0_1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.DT0_1 */
#define IFX_GTM_DTM_CH_CTRL2_DT0_1_OFF (11u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.DT0_2 */
#define IFX_GTM_DTM_CH_CTRL2_DT0_2_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.DT0_2 */
#define IFX_GTM_DTM_CH_CTRL2_DT0_2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.DT0_2 */
#define IFX_GTM_DTM_CH_CTRL2_DT0_2_OFF (19u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.DT0_3 */
#define IFX_GTM_DTM_CH_CTRL2_DT0_3_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.DT0_3 */
#define IFX_GTM_DTM_CH_CTRL2_DT0_3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.DT0_3 */
#define IFX_GTM_DTM_CH_CTRL2_DT0_3_OFF (27u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.DT1_0 */
#define IFX_GTM_DTM_CH_CTRL2_DT1_0_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.DT1_0 */
#define IFX_GTM_DTM_CH_CTRL2_DT1_0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.DT1_0 */
#define IFX_GTM_DTM_CH_CTRL2_DT1_0_OFF (7u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.DT1_1 */
#define IFX_GTM_DTM_CH_CTRL2_DT1_1_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.DT1_1 */
#define IFX_GTM_DTM_CH_CTRL2_DT1_1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.DT1_1 */
#define IFX_GTM_DTM_CH_CTRL2_DT1_1_OFF (15u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.DT1_2 */
#define IFX_GTM_DTM_CH_CTRL2_DT1_2_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.DT1_2 */
#define IFX_GTM_DTM_CH_CTRL2_DT1_2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.DT1_2 */
#define IFX_GTM_DTM_CH_CTRL2_DT1_2_OFF (23u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.DT1_3 */
#define IFX_GTM_DTM_CH_CTRL2_DT1_3_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.DT1_3 */
#define IFX_GTM_DTM_CH_CTRL2_DT1_3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.DT1_3 */
#define IFX_GTM_DTM_CH_CTRL2_DT1_3_OFF (31u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.OC0_0 */
#define IFX_GTM_DTM_CH_CTRL2_OC0_0_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.OC0_0 */
#define IFX_GTM_DTM_CH_CTRL2_OC0_0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.OC0_0 */
#define IFX_GTM_DTM_CH_CTRL2_OC0_0_OFF (1u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.OC0_1 */
#define IFX_GTM_DTM_CH_CTRL2_OC0_1_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.OC0_1 */
#define IFX_GTM_DTM_CH_CTRL2_OC0_1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.OC0_1 */
#define IFX_GTM_DTM_CH_CTRL2_OC0_1_OFF (9u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.OC0_2 */
#define IFX_GTM_DTM_CH_CTRL2_OC0_2_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.OC0_2 */
#define IFX_GTM_DTM_CH_CTRL2_OC0_2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.OC0_2 */
#define IFX_GTM_DTM_CH_CTRL2_OC0_2_OFF (17u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.OC0_3 */
#define IFX_GTM_DTM_CH_CTRL2_OC0_3_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.OC0_3 */
#define IFX_GTM_DTM_CH_CTRL2_OC0_3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.OC0_3 */
#define IFX_GTM_DTM_CH_CTRL2_OC0_3_OFF (25u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.OC1_0 */
#define IFX_GTM_DTM_CH_CTRL2_OC1_0_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.OC1_0 */
#define IFX_GTM_DTM_CH_CTRL2_OC1_0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.OC1_0 */
#define IFX_GTM_DTM_CH_CTRL2_OC1_0_OFF (5u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.OC1_1 */
#define IFX_GTM_DTM_CH_CTRL2_OC1_1_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.OC1_1 */
#define IFX_GTM_DTM_CH_CTRL2_OC1_1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.OC1_1 */
#define IFX_GTM_DTM_CH_CTRL2_OC1_1_OFF (13u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.OC1_2 */
#define IFX_GTM_DTM_CH_CTRL2_OC1_2_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.OC1_2 */
#define IFX_GTM_DTM_CH_CTRL2_OC1_2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.OC1_2 */
#define IFX_GTM_DTM_CH_CTRL2_OC1_2_OFF (21u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.OC1_3 */
#define IFX_GTM_DTM_CH_CTRL2_OC1_3_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.OC1_3 */
#define IFX_GTM_DTM_CH_CTRL2_OC1_3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.OC1_3 */
#define IFX_GTM_DTM_CH_CTRL2_OC1_3_OFF (29u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.POL0_0 */
#define IFX_GTM_DTM_CH_CTRL2_POL0_0_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.POL0_0 */
#define IFX_GTM_DTM_CH_CTRL2_POL0_0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.POL0_0 */
#define IFX_GTM_DTM_CH_CTRL2_POL0_0_OFF (0u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.POL0_1 */
#define IFX_GTM_DTM_CH_CTRL2_POL0_1_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.POL0_1 */
#define IFX_GTM_DTM_CH_CTRL2_POL0_1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.POL0_1 */
#define IFX_GTM_DTM_CH_CTRL2_POL0_1_OFF (8u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.POL0_2 */
#define IFX_GTM_DTM_CH_CTRL2_POL0_2_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.POL0_2 */
#define IFX_GTM_DTM_CH_CTRL2_POL0_2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.POL0_2 */
#define IFX_GTM_DTM_CH_CTRL2_POL0_2_OFF (16u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.POL0_3 */
#define IFX_GTM_DTM_CH_CTRL2_POL0_3_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.POL0_3 */
#define IFX_GTM_DTM_CH_CTRL2_POL0_3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.POL0_3 */
#define IFX_GTM_DTM_CH_CTRL2_POL0_3_OFF (24u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.POL1_0 */
#define IFX_GTM_DTM_CH_CTRL2_POL1_0_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.POL1_0 */
#define IFX_GTM_DTM_CH_CTRL2_POL1_0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.POL1_0 */
#define IFX_GTM_DTM_CH_CTRL2_POL1_0_OFF (4u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.POL1_1 */
#define IFX_GTM_DTM_CH_CTRL2_POL1_1_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.POL1_1 */
#define IFX_GTM_DTM_CH_CTRL2_POL1_1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.POL1_1 */
#define IFX_GTM_DTM_CH_CTRL2_POL1_1_OFF (12u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.POL1_2 */
#define IFX_GTM_DTM_CH_CTRL2_POL1_2_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.POL1_2 */
#define IFX_GTM_DTM_CH_CTRL2_POL1_2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.POL1_2 */
#define IFX_GTM_DTM_CH_CTRL2_POL1_2_OFF (20u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.POL1_3 */
#define IFX_GTM_DTM_CH_CTRL2_POL1_3_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.POL1_3 */
#define IFX_GTM_DTM_CH_CTRL2_POL1_3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.POL1_3 */
#define IFX_GTM_DTM_CH_CTRL2_POL1_3_OFF (28u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.SL0_0 */
#define IFX_GTM_DTM_CH_CTRL2_SL0_0_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.SL0_0 */
#define IFX_GTM_DTM_CH_CTRL2_SL0_0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.SL0_0 */
#define IFX_GTM_DTM_CH_CTRL2_SL0_0_OFF (2u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.SL0_1 */
#define IFX_GTM_DTM_CH_CTRL2_SL0_1_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.SL0_1 */
#define IFX_GTM_DTM_CH_CTRL2_SL0_1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.SL0_1 */
#define IFX_GTM_DTM_CH_CTRL2_SL0_1_OFF (10u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.SL0_2 */
#define IFX_GTM_DTM_CH_CTRL2_SL0_2_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.SL0_2 */
#define IFX_GTM_DTM_CH_CTRL2_SL0_2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.SL0_2 */
#define IFX_GTM_DTM_CH_CTRL2_SL0_2_OFF (18u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.SL0_3 */
#define IFX_GTM_DTM_CH_CTRL2_SL0_3_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.SL0_3 */
#define IFX_GTM_DTM_CH_CTRL2_SL0_3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.SL0_3 */
#define IFX_GTM_DTM_CH_CTRL2_SL0_3_OFF (26u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.SL1_0 */
#define IFX_GTM_DTM_CH_CTRL2_SL1_0_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.SL1_0 */
#define IFX_GTM_DTM_CH_CTRL2_SL1_0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.SL1_0 */
#define IFX_GTM_DTM_CH_CTRL2_SL1_0_OFF (6u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.SL1_1 */
#define IFX_GTM_DTM_CH_CTRL2_SL1_1_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.SL1_1 */
#define IFX_GTM_DTM_CH_CTRL2_SL1_1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.SL1_1 */
#define IFX_GTM_DTM_CH_CTRL2_SL1_1_OFF (14u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.SL1_2 */
#define IFX_GTM_DTM_CH_CTRL2_SL1_2_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.SL1_2 */
#define IFX_GTM_DTM_CH_CTRL2_SL1_2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.SL1_2 */
#define IFX_GTM_DTM_CH_CTRL2_SL1_2_OFF (22u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_Bits.SL1_3 */
#define IFX_GTM_DTM_CH_CTRL2_SL1_3_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_Bits.SL1_3 */
#define IFX_GTM_DTM_CH_CTRL2_SL1_3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_Bits.SL1_3 */
#define IFX_GTM_DTM_CH_CTRL2_SL1_3_OFF (30u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT0_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT0_0_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT0_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT0_0_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT0_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT0_0_SR_OFF (3u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT0_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT0_1_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT0_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT0_1_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT0_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT0_1_SR_OFF (11u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT0_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT0_2_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT0_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT0_2_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT0_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT0_2_SR_OFF (19u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT0_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT0_3_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT0_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT0_3_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT0_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT0_3_SR_OFF (27u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT1_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT1_0_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT1_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT1_0_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT1_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT1_0_SR_OFF (7u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT1_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT1_1_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT1_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT1_1_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT1_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT1_1_SR_OFF (15u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT1_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT1_2_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT1_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT1_2_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT1_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT1_2_SR_OFF (23u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT1_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT1_3_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT1_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT1_3_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.DT1_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_DT1_3_SR_OFF (31u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC0_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC0_0_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC0_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC0_0_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC0_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC0_0_SR_OFF (1u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC0_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC0_1_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC0_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC0_1_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC0_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC0_1_SR_OFF (9u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC0_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC0_2_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC0_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC0_2_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC0_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC0_2_SR_OFF (17u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC0_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC0_3_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC0_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC0_3_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC0_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC0_3_SR_OFF (25u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC1_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC1_0_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC1_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC1_0_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC1_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC1_0_SR_OFF (5u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC1_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC1_1_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC1_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC1_1_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC1_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC1_1_SR_OFF (13u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC1_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC1_2_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC1_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC1_2_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC1_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC1_2_SR_OFF (21u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC1_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC1_3_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC1_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC1_3_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.OC1_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_OC1_3_SR_OFF (29u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL0_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL0_0_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL0_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL0_0_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL0_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL0_0_SR_OFF (0u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL0_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL0_1_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL0_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL0_1_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL0_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL0_1_SR_OFF (8u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL0_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL0_2_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL0_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL0_2_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL0_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL0_2_SR_OFF (16u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL0_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL0_3_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL0_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL0_3_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL0_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL0_3_SR_OFF (24u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL1_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL1_0_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL1_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL1_0_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL1_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL1_0_SR_OFF (4u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL1_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL1_1_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL1_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL1_1_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL1_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL1_1_SR_OFF (12u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL1_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL1_2_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL1_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL1_2_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL1_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL1_2_SR_OFF (20u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL1_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL1_3_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL1_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL1_3_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.POL1_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_POL1_3_SR_OFF (28u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL0_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL0_0_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL0_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL0_0_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL0_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL0_0_SR_OFF (2u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL0_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL0_1_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL0_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL0_1_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL0_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL0_1_SR_OFF (10u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL0_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL0_2_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL0_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL0_2_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL0_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL0_2_SR_OFF (18u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL0_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL0_3_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL0_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL0_3_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL0_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL0_3_SR_OFF (26u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL1_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL1_0_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL1_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL1_0_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL1_0_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL1_0_SR_OFF (6u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL1_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL1_1_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL1_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL1_1_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL1_1_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL1_1_SR_OFF (14u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL1_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL1_2_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL1_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL1_2_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL1_2_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL1_2_SR_OFF (22u)

/** \brief  Length for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL1_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL1_3_SR_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL1_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL1_3_SR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_CH_CTRL2_SR_Bits.SL1_3_SR */
#define IFX_GTM_DTM_CH_CTRL2_SR_SL1_3_SR_OFF (30u)

/** \brief  Length for Ifx_GTM_DTM_CTRL_Bits.CLK_SEL */
#define IFX_GTM_DTM_CTRL_CLK_SEL_LEN (2u)

/** \brief  Mask for Ifx_GTM_DTM_CTRL_Bits.CLK_SEL */
#define IFX_GTM_DTM_CTRL_CLK_SEL_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_DTM_CTRL_Bits.CLK_SEL */
#define IFX_GTM_DTM_CTRL_CLK_SEL_OFF (0u)

/** \brief  Length for Ifx_GTM_DTM_CTRL_Bits.UPD_MODE */
#define IFX_GTM_DTM_CTRL_UPD_MODE_LEN (3u)

/** \brief  Mask for Ifx_GTM_DTM_CTRL_Bits.UPD_MODE */
#define IFX_GTM_DTM_CTRL_UPD_MODE_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_DTM_CTRL_Bits.UPD_MODE */
#define IFX_GTM_DTM_CTRL_UPD_MODE_OFF (4u)

/** \brief  Length for Ifx_GTM_DTM_DTV_CH_Bits.RELFALL */
#define IFX_GTM_DTM_DTV_CH_RELFALL_LEN (10u)

/** \brief  Mask for Ifx_GTM_DTM_DTV_CH_Bits.RELFALL */
#define IFX_GTM_DTM_DTV_CH_RELFALL_MSK (0x3ffu)

/** \brief  Offset for Ifx_GTM_DTM_DTV_CH_Bits.RELFALL */
#define IFX_GTM_DTM_DTV_CH_RELFALL_OFF (16u)

/** \brief  Length for Ifx_GTM_DTM_DTV_CH_Bits.RELRISE */
#define IFX_GTM_DTM_DTV_CH_RELRISE_LEN (10u)

/** \brief  Mask for Ifx_GTM_DTM_DTV_CH_Bits.RELRISE */
#define IFX_GTM_DTM_DTV_CH_RELRISE_MSK (0x3ffu)

/** \brief  Offset for Ifx_GTM_DTM_DTV_CH_Bits.RELRISE */
#define IFX_GTM_DTM_DTV_CH_RELRISE_OFF (0u)

/** \brief  Length for Ifx_GTM_DTM_PS_CTRL_Bits.IN_POL */
#define IFX_GTM_DTM_PS_CTRL_IN_POL_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_PS_CTRL_Bits.IN_POL */
#define IFX_GTM_DTM_PS_CTRL_IN_POL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_PS_CTRL_Bits.IN_POL */
#define IFX_GTM_DTM_PS_CTRL_IN_POL_OFF (17u)

/** \brief  Length for Ifx_GTM_DTM_PS_CTRL_Bits.PSU_IN_SEL */
#define IFX_GTM_DTM_PS_CTRL_PSU_IN_SEL_LEN (1u)

/** \brief  Mask for Ifx_GTM_DTM_PS_CTRL_Bits.PSU_IN_SEL */
#define IFX_GTM_DTM_PS_CTRL_PSU_IN_SEL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_DTM_PS_CTRL_Bits.PSU_IN_SEL */
#define IFX_GTM_DTM_PS_CTRL_PSU_IN_SEL_OFF (16u)

/** \brief  Length for Ifx_GTM_DTM_PS_CTRL_Bits.RELBLK */
#define IFX_GTM_DTM_PS_CTRL_RELBLK_LEN (10u)

/** \brief  Mask for Ifx_GTM_DTM_PS_CTRL_Bits.RELBLK */
#define IFX_GTM_DTM_PS_CTRL_RELBLK_MSK (0x3ffu)

/** \brief  Offset for Ifx_GTM_DTM_PS_CTRL_Bits.RELBLK */
#define IFX_GTM_DTM_PS_CTRL_RELBLK_OFF (0u)

/** \brief  Length for Ifx_GTM_DTM_PS_CTRL_Bits.SHIFT_SEL */
#define IFX_GTM_DTM_PS_CTRL_SHIFT_SEL_LEN (2u)

/** \brief  Mask for Ifx_GTM_DTM_PS_CTRL_Bits.SHIFT_SEL */
#define IFX_GTM_DTM_PS_CTRL_SHIFT_SEL_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_DTM_PS_CTRL_Bits.SHIFT_SEL */
#define IFX_GTM_DTM_PS_CTRL_SHIFT_SEL_OFF (20u)

/** \brief  Length for Ifx_GTM_EIRQ_EN_Bits.AEI_IM_ADDR_EIRQ_EN */
#define IFX_GTM_EIRQ_EN_AEI_IM_ADDR_EIRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_EIRQ_EN_Bits.AEI_IM_ADDR_EIRQ_EN */
#define IFX_GTM_EIRQ_EN_AEI_IM_ADDR_EIRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_EIRQ_EN_Bits.AEI_IM_ADDR_EIRQ_EN */
#define IFX_GTM_EIRQ_EN_AEI_IM_ADDR_EIRQ_EN_OFF (2u)

/** \brief  Length for Ifx_GTM_EIRQ_EN_Bits.AEI_TO_XPT_EIRQ_EN */
#define IFX_GTM_EIRQ_EN_AEI_TO_XPT_EIRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_EIRQ_EN_Bits.AEI_TO_XPT_EIRQ_EN */
#define IFX_GTM_EIRQ_EN_AEI_TO_XPT_EIRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_EIRQ_EN_Bits.AEI_TO_XPT_EIRQ_EN */
#define IFX_GTM_EIRQ_EN_AEI_TO_XPT_EIRQ_EN_OFF (0u)

/** \brief  Length for Ifx_GTM_EIRQ_EN_Bits.AEI_USP_ADDR_EIRQ_EN */
#define IFX_GTM_EIRQ_EN_AEI_USP_ADDR_EIRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_EIRQ_EN_Bits.AEI_USP_ADDR_EIRQ_EN */
#define IFX_GTM_EIRQ_EN_AEI_USP_ADDR_EIRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_EIRQ_EN_Bits.AEI_USP_ADDR_EIRQ_EN */
#define IFX_GTM_EIRQ_EN_AEI_USP_ADDR_EIRQ_EN_OFF (1u)

/** \brief  Length for Ifx_GTM_EIRQ_EN_Bits.AEI_USP_BE_EIRQ_EN */
#define IFX_GTM_EIRQ_EN_AEI_USP_BE_EIRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_EIRQ_EN_Bits.AEI_USP_BE_EIRQ_EN */
#define IFX_GTM_EIRQ_EN_AEI_USP_BE_EIRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_EIRQ_EN_Bits.AEI_USP_BE_EIRQ_EN */
#define IFX_GTM_EIRQ_EN_AEI_USP_BE_EIRQ_EN_OFF (3u)

/** \brief  Length for Ifx_GTM_HW_CONF_Bits.AEI_IN */
#define IFX_GTM_HW_CONF_AEI_IN_LEN (1u)

/** \brief  Mask for Ifx_GTM_HW_CONF_Bits.AEI_IN */
#define IFX_GTM_HW_CONF_AEI_IN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_HW_CONF_Bits.AEI_IN */
#define IFX_GTM_HW_CONF_AEI_IN_OFF (2u)

/** \brief  Length for Ifx_GTM_HW_CONF_Bits.BRIDGE_MODE_RST */
#define IFX_GTM_HW_CONF_BRIDGE_MODE_RST_LEN (1u)

/** \brief  Mask for Ifx_GTM_HW_CONF_Bits.BRIDGE_MODE_RST */
#define IFX_GTM_HW_CONF_BRIDGE_MODE_RST_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_HW_CONF_Bits.BRIDGE_MODE_RST */
#define IFX_GTM_HW_CONF_BRIDGE_MODE_RST_OFF (1u)

/** \brief  Length for Ifx_GTM_HW_CONF_Bits.GRSTEN */
#define IFX_GTM_HW_CONF_GRSTEN_LEN (1u)

/** \brief  Mask for Ifx_GTM_HW_CONF_Bits.GRSTEN */
#define IFX_GTM_HW_CONF_GRSTEN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_HW_CONF_Bits.GRSTEN */
#define IFX_GTM_HW_CONF_GRSTEN_OFF (0u)

/** \brief  Length for Ifx_GTM_HW_CONF_Bits.IRQ_MODE_LEVEL */
#define IFX_GTM_HW_CONF_IRQ_MODE_LEVEL_LEN (1u)

/** \brief  Mask for Ifx_GTM_HW_CONF_Bits.IRQ_MODE_LEVEL */
#define IFX_GTM_HW_CONF_IRQ_MODE_LEVEL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_HW_CONF_Bits.IRQ_MODE_LEVEL */
#define IFX_GTM_HW_CONF_IRQ_MODE_LEVEL_OFF (16u)

/** \brief  Length for Ifx_GTM_HW_CONF_Bits.IRQ_MODE_PULSE */
#define IFX_GTM_HW_CONF_IRQ_MODE_PULSE_LEN (1u)

/** \brief  Mask for Ifx_GTM_HW_CONF_Bits.IRQ_MODE_PULSE */
#define IFX_GTM_HW_CONF_IRQ_MODE_PULSE_MSK (0x1u)

/** \brief  Length for Ifx_GTM_HW_CONF_Bits.IRQ_MODE_PULSE_NOTIFY */
#define IFX_GTM_HW_CONF_IRQ_MODE_PULSE_NOTIFY_LEN (1u)

/** \brief  Mask for Ifx_GTM_HW_CONF_Bits.IRQ_MODE_PULSE_NOTIFY */
#define IFX_GTM_HW_CONF_IRQ_MODE_PULSE_NOTIFY_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_HW_CONF_Bits.IRQ_MODE_PULSE_NOTIFY */
#define IFX_GTM_HW_CONF_IRQ_MODE_PULSE_NOTIFY_OFF (18u)

/** \brief  Offset for Ifx_GTM_HW_CONF_Bits.IRQ_MODE_PULSE */
#define IFX_GTM_HW_CONF_IRQ_MODE_PULSE_OFF (17u)

/** \brief  Length for Ifx_GTM_HW_CONF_Bits.IRQ_MODE_SINGLE_PULSE */
#define IFX_GTM_HW_CONF_IRQ_MODE_SINGLE_PULSE_LEN (1u)

/** \brief  Mask for Ifx_GTM_HW_CONF_Bits.IRQ_MODE_SINGLE_PULSE */
#define IFX_GTM_HW_CONF_IRQ_MODE_SINGLE_PULSE_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_HW_CONF_Bits.IRQ_MODE_SINGLE_PULSE */
#define IFX_GTM_HW_CONF_IRQ_MODE_SINGLE_PULSE_OFF (19u)

/** \brief  Length for Ifx_GTM_HW_CONF_Bits.TOM_OUT_RST */
#define IFX_GTM_HW_CONF_TOM_OUT_RST_LEN (1u)

/** \brief  Mask for Ifx_GTM_HW_CONF_Bits.TOM_OUT_RST */
#define IFX_GTM_HW_CONF_TOM_OUT_RST_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_HW_CONF_Bits.TOM_OUT_RST */
#define IFX_GTM_HW_CONF_TOM_OUT_RST_OFF (8u)

/** \brief  Length for Ifx_GTM_HW_CONF_Bits.TOM_TRIG_CHAIN */
#define IFX_GTM_HW_CONF_TOM_TRIG_CHAIN_LEN (3u)

/** \brief  Mask for Ifx_GTM_HW_CONF_Bits.TOM_TRIG_CHAIN */
#define IFX_GTM_HW_CONF_TOM_TRIG_CHAIN_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_HW_CONF_Bits.TOM_TRIG_CHAIN */
#define IFX_GTM_HW_CONF_TOM_TRIG_CHAIN_OFF (9u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_0_Bits.AEI_IRQ */
#define IFX_GTM_ICM_IRQG_0_AEI_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_0_Bits.AEI_IRQ */
#define IFX_GTM_ICM_IRQG_0_AEI_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_0_Bits.AEI_IRQ */
#define IFX_GTM_ICM_IRQG_0_AEI_IRQ_OFF (4u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH0_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH0_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH0_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH0_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH0_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH0_IRQ_OFF (0u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH1_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH1_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH1_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH1_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH1_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH1_IRQ_OFF (1u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH2_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH2_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH2_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH2_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH2_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH2_IRQ_OFF (2u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH3_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH3_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH3_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH3_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH3_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH3_IRQ_OFF (3u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH4_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH4_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH4_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH4_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH4_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH4_IRQ_OFF (4u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH5_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH5_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH5_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH5_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH5_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH5_IRQ_OFF (5u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH6_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH6_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH6_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH6_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH6_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH6_IRQ_OFF (6u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH7_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH7_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH7_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH7_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_2_Bits.TIM0_CH7_IRQ */
#define IFX_GTM_ICM_IRQG_2_TIM0_CH7_IRQ_OFF (7u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH0_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH0_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH0_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH0_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH0_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH0_IRQ_OFF (0u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH10_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH10_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH10_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH10_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH10_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH10_IRQ_OFF (10u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH11_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH11_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH11_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH11_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH11_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH11_IRQ_OFF (11u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH12_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH12_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH12_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH12_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH12_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH12_IRQ_OFF (12u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH13_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH13_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH13_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH13_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH13_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH13_IRQ_OFF (13u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH14_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH14_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH14_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH14_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH14_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH14_IRQ_OFF (14u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH15_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH15_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH15_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH15_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH15_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH15_IRQ_OFF (15u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH1_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH1_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH1_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH1_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH1_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH1_IRQ_OFF (1u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH2_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH2_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH2_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH2_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH2_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH2_IRQ_OFF (2u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH3_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH3_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH3_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH3_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH3_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH3_IRQ_OFF (3u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH4_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH4_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH4_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH4_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH4_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH4_IRQ_OFF (4u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH5_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH5_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH5_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH5_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH5_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH5_IRQ_OFF (5u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH6_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH6_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH6_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH6_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH6_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH6_IRQ_OFF (6u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH7_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH7_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH7_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH7_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH7_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH7_IRQ_OFF (7u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH8_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH8_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH8_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH8_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH8_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH8_IRQ_OFF (8u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH9_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH9_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH9_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH9_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM0_CH9_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM0_CH9_IRQ_OFF (9u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH0_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH0_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH0_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH0_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH0_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH0_IRQ_OFF (16u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH10_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH10_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH10_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH10_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH10_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH10_IRQ_OFF (26u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH11_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH11_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH11_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH11_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH11_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH11_IRQ_OFF (27u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH12_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH12_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH12_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH12_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH12_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH12_IRQ_OFF (28u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH13_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH13_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH13_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH13_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH13_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH13_IRQ_OFF (29u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH14_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH14_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH14_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH14_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH14_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH14_IRQ_OFF (30u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH15_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH15_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH15_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH15_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH15_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH15_IRQ_OFF (31u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH1_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH1_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH1_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH1_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH1_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH1_IRQ_OFF (17u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH2_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH2_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH2_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH2_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH2_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH2_IRQ_OFF (18u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH3_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH3_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH3_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH3_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH3_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH3_IRQ_OFF (19u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH4_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH4_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH4_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH4_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH4_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH4_IRQ_OFF (20u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH5_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH5_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH5_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH5_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH5_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH5_IRQ_OFF (21u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH6_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH6_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH6_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH6_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH6_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH6_IRQ_OFF (22u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH7_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH7_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH7_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH7_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH7_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH7_IRQ_OFF (23u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH8_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH8_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH8_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH8_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH8_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH8_IRQ_OFF (24u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH9_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH9_IRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH9_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH9_IRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_6_Bits.TOM1_CH9_IRQ */
#define IFX_GTM_ICM_IRQG_6_TOM1_CH9_IRQ_OFF (25u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH0_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH0_EIRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH0_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH0_EIRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH0_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH0_EIRQ_OFF (0u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH1_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH1_EIRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH1_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH1_EIRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH1_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH1_EIRQ_OFF (1u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH2_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH2_EIRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH2_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH2_EIRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH2_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH2_EIRQ_OFF (2u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH3_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH3_EIRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH3_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH3_EIRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH3_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH3_EIRQ_OFF (3u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH4_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH4_EIRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH4_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH4_EIRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH4_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH4_EIRQ_OFF (4u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH5_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH5_EIRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH5_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH5_EIRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH5_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH5_EIRQ_OFF (5u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH6_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH6_EIRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH6_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH6_EIRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH6_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH6_EIRQ_OFF (6u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH7_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH7_EIRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH7_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH7_EIRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_CEI1_Bits.TIM0_CH7_EIRQ */
#define IFX_GTM_ICM_IRQG_CEI1_TIM0_CH7_EIRQ_OFF (7u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_MEI_Bits.GTM_EIRQ */
#define IFX_GTM_ICM_IRQG_MEI_GTM_EIRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_MEI_Bits.GTM_EIRQ */
#define IFX_GTM_ICM_IRQG_MEI_GTM_EIRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_MEI_Bits.GTM_EIRQ */
#define IFX_GTM_ICM_IRQG_MEI_GTM_EIRQ_OFF (0u)

/** \brief  Length for Ifx_GTM_ICM_IRQG_MEI_Bits.TIM0_EIRQ */
#define IFX_GTM_ICM_IRQG_MEI_TIM0_EIRQ_LEN (1u)

/** \brief  Mask for Ifx_GTM_ICM_IRQG_MEI_Bits.TIM0_EIRQ */
#define IFX_GTM_ICM_IRQG_MEI_TIM0_EIRQ_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ICM_IRQG_MEI_Bits.TIM0_EIRQ */
#define IFX_GTM_ICM_IRQG_MEI_TIM0_EIRQ_OFF (4u)

/** \brief  Length for Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits.SEL0 */
#define IFX_GTM_INOUTSEL_CAN_OUTSEL_SEL0_LEN (4u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits.SEL0 */
#define IFX_GTM_INOUTSEL_CAN_OUTSEL_SEL0_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits.SEL0 */
#define IFX_GTM_INOUTSEL_CAN_OUTSEL_SEL0_OFF (0u)

/** \brief  Length for Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits.SEL1 */
#define IFX_GTM_INOUTSEL_CAN_OUTSEL_SEL1_LEN (4u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits.SEL1 */
#define IFX_GTM_INOUTSEL_CAN_OUTSEL_SEL1_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits.SEL1 */
#define IFX_GTM_INOUTSEL_CAN_OUTSEL_SEL1_OFF (4u)

/** \brief  Length for Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits.SEL2 */
#define IFX_GTM_INOUTSEL_CAN_OUTSEL_SEL2_LEN (4u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits.SEL2 */
#define IFX_GTM_INOUTSEL_CAN_OUTSEL_SEL2_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits.SEL2 */
#define IFX_GTM_INOUTSEL_CAN_OUTSEL_SEL2_OFF (8u)

/** \brief  Length for Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits.SEL3 */
#define IFX_GTM_INOUTSEL_CAN_OUTSEL_SEL3_LEN (4u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits.SEL3 */
#define IFX_GTM_INOUTSEL_CAN_OUTSEL_SEL3_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits.SEL3 */
#define IFX_GTM_INOUTSEL_CAN_OUTSEL_SEL3_OFF (12u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL0 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL0_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL0 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL0 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL0_OFF (0u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL10 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL10_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL10 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL10_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL10 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL10_OFF (20u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL11 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL11_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL11 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL11_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL11 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL11_OFF (22u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL12 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL12_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL12 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL12_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL12 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL12_OFF (24u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL13 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL13_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL13 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL13_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL13 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL13_OFF (26u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL14 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL14_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL14 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL14_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL14 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL14_OFF (28u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL15 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL15_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL15 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL15_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL15 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL15_OFF (30u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL1 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL1_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL1 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL1 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL1_OFF (2u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL2 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL2_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL2 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL2 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL2_OFF (4u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL3 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL3_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL3 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL3 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL3_OFF (6u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL4 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL4_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL4 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL4 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL4_OFF (8u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL5 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL5_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL5 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL5 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL5_OFF (10u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL6 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL6_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL6 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL6 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL6_OFF (12u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL7 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL7_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL7 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL7 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL7_OFF (14u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL8 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL8_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL8 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL8_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL8 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL8_OFF (16u)

/** \brief  Length for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL9 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL9_LEN (2u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL9 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL9_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_INOUTSEL_T_OUTSEL_Bits.SEL9 */
#define IFX_GTM_INOUTSEL_T_OUTSEL_SEL9_OFF (18u)

/** \brief  Length for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH0SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH0SEL_LEN (4u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH0SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH0SEL_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH0SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH0SEL_OFF (0u)

/** \brief  Length for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH1SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH1SEL_LEN (4u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH1SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH1SEL_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH1SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH1SEL_OFF (4u)

/** \brief  Length for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH2SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH2SEL_LEN (4u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH2SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH2SEL_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH2SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH2SEL_OFF (8u)

/** \brief  Length for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH3SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH3SEL_LEN (4u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH3SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH3SEL_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH3SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH3SEL_OFF (12u)

/** \brief  Length for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH4SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH4SEL_LEN (4u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH4SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH4SEL_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH4SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH4SEL_OFF (16u)

/** \brief  Length for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH5SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH5SEL_LEN (4u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH5SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH5SEL_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH5SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH5SEL_OFF (20u)

/** \brief  Length for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH6SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH6SEL_LEN (4u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH6SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH6SEL_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH6SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH6SEL_OFF (24u)

/** \brief  Length for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH7SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH7SEL_LEN (4u)

/** \brief  Mask for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH7SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH7SEL_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_INOUTSEL_TIM_INSEL_Bits.CH7SEL */
#define IFX_GTM_INOUTSEL_TIM_INSEL_CH7SEL_OFF (28u)

/** \brief  Length for Ifx_GTM_IRQ_EN_Bits.AEI_IM_ADDR_IRQ_EN */
#define IFX_GTM_IRQ_EN_AEI_IM_ADDR_IRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_IRQ_EN_Bits.AEI_IM_ADDR_IRQ_EN */
#define IFX_GTM_IRQ_EN_AEI_IM_ADDR_IRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_IRQ_EN_Bits.AEI_IM_ADDR_IRQ_EN */
#define IFX_GTM_IRQ_EN_AEI_IM_ADDR_IRQ_EN_OFF (2u)

/** \brief  Length for Ifx_GTM_IRQ_EN_Bits.AEI_TO_XPT_IRQ_EN */
#define IFX_GTM_IRQ_EN_AEI_TO_XPT_IRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_IRQ_EN_Bits.AEI_TO_XPT_IRQ_EN */
#define IFX_GTM_IRQ_EN_AEI_TO_XPT_IRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_IRQ_EN_Bits.AEI_TO_XPT_IRQ_EN */
#define IFX_GTM_IRQ_EN_AEI_TO_XPT_IRQ_EN_OFF (0u)

/** \brief  Length for Ifx_GTM_IRQ_EN_Bits.AEI_USP_ADDR_IRQ_EN */
#define IFX_GTM_IRQ_EN_AEI_USP_ADDR_IRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_IRQ_EN_Bits.AEI_USP_ADDR_IRQ_EN */
#define IFX_GTM_IRQ_EN_AEI_USP_ADDR_IRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_IRQ_EN_Bits.AEI_USP_ADDR_IRQ_EN */
#define IFX_GTM_IRQ_EN_AEI_USP_ADDR_IRQ_EN_OFF (1u)

/** \brief  Length for Ifx_GTM_IRQ_EN_Bits.AEI_USP_BE_IRQ_EN */
#define IFX_GTM_IRQ_EN_AEI_USP_BE_IRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_IRQ_EN_Bits.AEI_USP_BE_IRQ_EN */
#define IFX_GTM_IRQ_EN_AEI_USP_BE_IRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_IRQ_EN_Bits.AEI_USP_BE_IRQ_EN */
#define IFX_GTM_IRQ_EN_AEI_USP_BE_IRQ_EN_OFF (3u)

/** \brief  Length for Ifx_GTM_IRQ_FORCINT_Bits.TRG_AEI_IM_ADDR */
#define IFX_GTM_IRQ_FORCINT_TRG_AEI_IM_ADDR_LEN (1u)

/** \brief  Mask for Ifx_GTM_IRQ_FORCINT_Bits.TRG_AEI_IM_ADDR */
#define IFX_GTM_IRQ_FORCINT_TRG_AEI_IM_ADDR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_IRQ_FORCINT_Bits.TRG_AEI_IM_ADDR */
#define IFX_GTM_IRQ_FORCINT_TRG_AEI_IM_ADDR_OFF (2u)

/** \brief  Length for Ifx_GTM_IRQ_FORCINT_Bits.TRG_AEI_TO_XPT */
#define IFX_GTM_IRQ_FORCINT_TRG_AEI_TO_XPT_LEN (1u)

/** \brief  Mask for Ifx_GTM_IRQ_FORCINT_Bits.TRG_AEI_TO_XPT */
#define IFX_GTM_IRQ_FORCINT_TRG_AEI_TO_XPT_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_IRQ_FORCINT_Bits.TRG_AEI_TO_XPT */
#define IFX_GTM_IRQ_FORCINT_TRG_AEI_TO_XPT_OFF (0u)

/** \brief  Length for Ifx_GTM_IRQ_FORCINT_Bits.TRG_AEI_USP_ADDR */
#define IFX_GTM_IRQ_FORCINT_TRG_AEI_USP_ADDR_LEN (1u)

/** \brief  Mask for Ifx_GTM_IRQ_FORCINT_Bits.TRG_AEI_USP_ADDR */
#define IFX_GTM_IRQ_FORCINT_TRG_AEI_USP_ADDR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_IRQ_FORCINT_Bits.TRG_AEI_USP_ADDR */
#define IFX_GTM_IRQ_FORCINT_TRG_AEI_USP_ADDR_OFF (1u)

/** \brief  Length for Ifx_GTM_IRQ_FORCINT_Bits.TRG_AEI_USP_BE */
#define IFX_GTM_IRQ_FORCINT_TRG_AEI_USP_BE_LEN (1u)

/** \brief  Mask for Ifx_GTM_IRQ_FORCINT_Bits.TRG_AEI_USP_BE */
#define IFX_GTM_IRQ_FORCINT_TRG_AEI_USP_BE_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_IRQ_FORCINT_Bits.TRG_AEI_USP_BE */
#define IFX_GTM_IRQ_FORCINT_TRG_AEI_USP_BE_OFF (3u)

/** \brief  Length for Ifx_GTM_IRQ_MODE_Bits.IRQ_MODE */
#define IFX_GTM_IRQ_MODE_IRQ_MODE_LEN (2u)

/** \brief  Mask for Ifx_GTM_IRQ_MODE_Bits.IRQ_MODE */
#define IFX_GTM_IRQ_MODE_IRQ_MODE_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_IRQ_MODE_Bits.IRQ_MODE */
#define IFX_GTM_IRQ_MODE_IRQ_MODE_OFF (0u)

/** \brief  Length for Ifx_GTM_IRQ_NOTIFY_Bits.AEI_IM_ADDR */
#define IFX_GTM_IRQ_NOTIFY_AEI_IM_ADDR_LEN (1u)

/** \brief  Mask for Ifx_GTM_IRQ_NOTIFY_Bits.AEI_IM_ADDR */
#define IFX_GTM_IRQ_NOTIFY_AEI_IM_ADDR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_IRQ_NOTIFY_Bits.AEI_IM_ADDR */
#define IFX_GTM_IRQ_NOTIFY_AEI_IM_ADDR_OFF (2u)

/** \brief  Length for Ifx_GTM_IRQ_NOTIFY_Bits.AEI_TO_XPT */
#define IFX_GTM_IRQ_NOTIFY_AEI_TO_XPT_LEN (1u)

/** \brief  Mask for Ifx_GTM_IRQ_NOTIFY_Bits.AEI_TO_XPT */
#define IFX_GTM_IRQ_NOTIFY_AEI_TO_XPT_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_IRQ_NOTIFY_Bits.AEI_TO_XPT */
#define IFX_GTM_IRQ_NOTIFY_AEI_TO_XPT_OFF (0u)

/** \brief  Length for Ifx_GTM_IRQ_NOTIFY_Bits.AEI_USP_ADDR */
#define IFX_GTM_IRQ_NOTIFY_AEI_USP_ADDR_LEN (1u)

/** \brief  Mask for Ifx_GTM_IRQ_NOTIFY_Bits.AEI_USP_ADDR */
#define IFX_GTM_IRQ_NOTIFY_AEI_USP_ADDR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_IRQ_NOTIFY_Bits.AEI_USP_ADDR */
#define IFX_GTM_IRQ_NOTIFY_AEI_USP_ADDR_OFF (1u)

/** \brief  Length for Ifx_GTM_IRQ_NOTIFY_Bits.AEI_USP_BE */
#define IFX_GTM_IRQ_NOTIFY_AEI_USP_BE_LEN (1u)

/** \brief  Mask for Ifx_GTM_IRQ_NOTIFY_Bits.AEI_USP_BE */
#define IFX_GTM_IRQ_NOTIFY_AEI_USP_BE_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_IRQ_NOTIFY_Bits.AEI_USP_BE */
#define IFX_GTM_IRQ_NOTIFY_AEI_USP_BE_OFF (3u)

/** \brief  Length for Ifx_GTM_KRST0_Bits.RST */
#define IFX_GTM_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_GTM_KRST0_Bits.RST */
#define IFX_GTM_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_KRST0_Bits.RST */
#define IFX_GTM_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_GTM_KRST0_Bits.RSTSTAT */
#define IFX_GTM_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_GTM_KRST0_Bits.RSTSTAT */
#define IFX_GTM_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_KRST0_Bits.RSTSTAT */
#define IFX_GTM_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_GTM_KRST1_Bits.RST */
#define IFX_GTM_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_GTM_KRST1_Bits.RST */
#define IFX_GTM_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_KRST1_Bits.RST */
#define IFX_GTM_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_GTM_KRSTCLR_Bits.CLR */
#define IFX_GTM_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_GTM_KRSTCLR_Bits.CLR */
#define IFX_GTM_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_KRSTCLR_Bits.CLR */
#define IFX_GTM_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_GTM_OCS_Bits.SUS */
#define IFX_GTM_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_GTM_OCS_Bits.SUS */
#define IFX_GTM_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_OCS_Bits.SUS */
#define IFX_GTM_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_GTM_OCS_Bits.SUS_P */
#define IFX_GTM_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_GTM_OCS_Bits.SUS_P */
#define IFX_GTM_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_OCS_Bits.SUS_P */
#define IFX_GTM_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_GTM_OCS_Bits.SUSSTA */
#define IFX_GTM_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_GTM_OCS_Bits.SUSSTA */
#define IFX_GTM_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_OCS_Bits.SUSSTA */
#define IFX_GTM_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_GTM_ODA_Bits.DDREN */
#define IFX_GTM_ODA_DDREN_LEN (1u)

/** \brief  Mask for Ifx_GTM_ODA_Bits.DDREN */
#define IFX_GTM_ODA_DDREN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ODA_Bits.DDREN */
#define IFX_GTM_ODA_DDREN_OFF (0u)

/** \brief  Length for Ifx_GTM_ODA_Bits.DREN */
#define IFX_GTM_ODA_DREN_LEN (1u)

/** \brief  Mask for Ifx_GTM_ODA_Bits.DREN */
#define IFX_GTM_ODA_DREN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_ODA_Bits.DREN */
#define IFX_GTM_ODA_DREN_OFF (1u)

/** \brief  Length for Ifx_GTM_OTBU0T_Bits.CM */
#define IFX_GTM_OTBU0T_CM_LEN (2u)

/** \brief  Mask for Ifx_GTM_OTBU0T_Bits.CM */
#define IFX_GTM_OTBU0T_CM_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_OTBU0T_Bits.CM */
#define IFX_GTM_OTBU0T_CM_OFF (28u)

/** \brief  Length for Ifx_GTM_OTBU0T_Bits.CV */
#define IFX_GTM_OTBU0T_CV_LEN (27u)

/** \brief  Mask for Ifx_GTM_OTBU0T_Bits.CV */
#define IFX_GTM_OTBU0T_CV_MSK (0x7ffffffu)

/** \brief  Offset for Ifx_GTM_OTBU0T_Bits.CV */
#define IFX_GTM_OTBU0T_CV_OFF (0u)

/** \brief  Length for Ifx_GTM_OTBU1T_Bits.CV */
#define IFX_GTM_OTBU1T_CV_LEN (24u)

/** \brief  Mask for Ifx_GTM_OTBU1T_Bits.CV */
#define IFX_GTM_OTBU1T_CV_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_OTBU1T_Bits.CV */
#define IFX_GTM_OTBU1T_CV_OFF (0u)

/** \brief  Length for Ifx_GTM_OTBU1T_Bits.EN */
#define IFX_GTM_OTBU1T_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_OTBU1T_Bits.EN */
#define IFX_GTM_OTBU1T_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_OTBU1T_Bits.EN */
#define IFX_GTM_OTBU1T_EN_OFF (28u)

/** \brief  Length for Ifx_GTM_OTBU2T_Bits.CV */
#define IFX_GTM_OTBU2T_CV_LEN (24u)

/** \brief  Mask for Ifx_GTM_OTBU2T_Bits.CV */
#define IFX_GTM_OTBU2T_CV_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_OTBU2T_Bits.CV */
#define IFX_GTM_OTBU2T_CV_OFF (0u)

/** \brief  Length for Ifx_GTM_OTBU2T_Bits.EN */
#define IFX_GTM_OTBU2T_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_OTBU2T_Bits.EN */
#define IFX_GTM_OTBU2T_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_OTBU2T_Bits.EN */
#define IFX_GTM_OTBU2T_EN_OFF (28u)

/** \brief  Length for Ifx_GTM_OTSC0_Bits.B0HMI */
#define IFX_GTM_OTSC0_B0HMI_LEN (4u)

/** \brief  Mask for Ifx_GTM_OTSC0_Bits.B0HMI */
#define IFX_GTM_OTSC0_B0HMI_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_OTSC0_Bits.B0HMI */
#define IFX_GTM_OTSC0_B0HMI_OFF (12u)

/** \brief  Length for Ifx_GTM_OTSC0_Bits.B0HMT */
#define IFX_GTM_OTSC0_B0HMT_LEN (3u)

/** \brief  Mask for Ifx_GTM_OTSC0_Bits.B0HMT */
#define IFX_GTM_OTSC0_B0HMT_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_OTSC0_Bits.B0HMT */
#define IFX_GTM_OTSC0_B0HMT_OFF (8u)

/** \brief  Length for Ifx_GTM_OTSC0_Bits.B0LMI */
#define IFX_GTM_OTSC0_B0LMI_LEN (4u)

/** \brief  Mask for Ifx_GTM_OTSC0_Bits.B0LMI */
#define IFX_GTM_OTSC0_B0LMI_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_OTSC0_Bits.B0LMI */
#define IFX_GTM_OTSC0_B0LMI_OFF (4u)

/** \brief  Length for Ifx_GTM_OTSC0_Bits.B0LMT */
#define IFX_GTM_OTSC0_B0LMT_LEN (3u)

/** \brief  Mask for Ifx_GTM_OTSC0_Bits.B0LMT */
#define IFX_GTM_OTSC0_B0LMT_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_OTSC0_Bits.B0LMT */
#define IFX_GTM_OTSC0_B0LMT_OFF (0u)

/** \brief  Length for Ifx_GTM_OTSC0_Bits.B1HMI */
#define IFX_GTM_OTSC0_B1HMI_LEN (4u)

/** \brief  Mask for Ifx_GTM_OTSC0_Bits.B1HMI */
#define IFX_GTM_OTSC0_B1HMI_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_OTSC0_Bits.B1HMI */
#define IFX_GTM_OTSC0_B1HMI_OFF (28u)

/** \brief  Length for Ifx_GTM_OTSC0_Bits.B1HMT */
#define IFX_GTM_OTSC0_B1HMT_LEN (3u)

/** \brief  Mask for Ifx_GTM_OTSC0_Bits.B1HMT */
#define IFX_GTM_OTSC0_B1HMT_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_OTSC0_Bits.B1HMT */
#define IFX_GTM_OTSC0_B1HMT_OFF (24u)

/** \brief  Length for Ifx_GTM_OTSC0_Bits.B1LMI */
#define IFX_GTM_OTSC0_B1LMI_LEN (4u)

/** \brief  Mask for Ifx_GTM_OTSC0_Bits.B1LMI */
#define IFX_GTM_OTSC0_B1LMI_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_OTSC0_Bits.B1LMI */
#define IFX_GTM_OTSC0_B1LMI_OFF (20u)

/** \brief  Length for Ifx_GTM_OTSC0_Bits.B1LMT */
#define IFX_GTM_OTSC0_B1LMT_LEN (3u)

/** \brief  Mask for Ifx_GTM_OTSC0_Bits.B1LMT */
#define IFX_GTM_OTSC0_B1LMT_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_OTSC0_Bits.B1LMT */
#define IFX_GTM_OTSC0_B1LMT_OFF (16u)

/** \brief  Length for Ifx_GTM_OTSS_Bits.OTGB0 */
#define IFX_GTM_OTSS_OTGB0_LEN (4u)

/** \brief  Mask for Ifx_GTM_OTSS_Bits.OTGB0 */
#define IFX_GTM_OTSS_OTGB0_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_OTSS_Bits.OTGB0 */
#define IFX_GTM_OTSS_OTGB0_OFF (0u)

/** \brief  Length for Ifx_GTM_OTSS_Bits.OTGB1 */
#define IFX_GTM_OTSS_OTGB1_LEN (4u)

/** \brief  Mask for Ifx_GTM_OTSS_Bits.OTGB1 */
#define IFX_GTM_OTSS_OTGB1_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_OTSS_Bits.OTGB1 */
#define IFX_GTM_OTSS_OTGB1_OFF (8u)

/** \brief  Length for Ifx_GTM_OTSS_Bits.OTGB2 */
#define IFX_GTM_OTSS_OTGB2_LEN (4u)

/** \brief  Mask for Ifx_GTM_OTSS_Bits.OTGB2 */
#define IFX_GTM_OTSS_OTGB2_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_OTSS_Bits.OTGB2 */
#define IFX_GTM_OTSS_OTGB2_OFF (16u)

/** \brief  Length for Ifx_GTM_REV_Bits.DEV_CODE0 */
#define IFX_GTM_REV_DEV_CODE0_LEN (4u)

/** \brief  Mask for Ifx_GTM_REV_Bits.DEV_CODE0 */
#define IFX_GTM_REV_DEV_CODE0_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_REV_Bits.DEV_CODE0 */
#define IFX_GTM_REV_DEV_CODE0_OFF (20u)

/** \brief  Length for Ifx_GTM_REV_Bits.DEV_CODE1 */
#define IFX_GTM_REV_DEV_CODE1_LEN (4u)

/** \brief  Mask for Ifx_GTM_REV_Bits.DEV_CODE1 */
#define IFX_GTM_REV_DEV_CODE1_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_REV_Bits.DEV_CODE1 */
#define IFX_GTM_REV_DEV_CODE1_OFF (24u)

/** \brief  Length for Ifx_GTM_REV_Bits.DEV_CODE2 */
#define IFX_GTM_REV_DEV_CODE2_LEN (4u)

/** \brief  Mask for Ifx_GTM_REV_Bits.DEV_CODE2 */
#define IFX_GTM_REV_DEV_CODE2_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_REV_Bits.DEV_CODE2 */
#define IFX_GTM_REV_DEV_CODE2_OFF (28u)

/** \brief  Length for Ifx_GTM_REV_Bits.MAJOR */
#define IFX_GTM_REV_MAJOR_LEN (4u)

/** \brief  Mask for Ifx_GTM_REV_Bits.MAJOR */
#define IFX_GTM_REV_MAJOR_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_REV_Bits.MAJOR */
#define IFX_GTM_REV_MAJOR_OFF (16u)

/** \brief  Length for Ifx_GTM_REV_Bits.MINOR */
#define IFX_GTM_REV_MINOR_LEN (4u)

/** \brief  Mask for Ifx_GTM_REV_Bits.MINOR */
#define IFX_GTM_REV_MINOR_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_REV_Bits.MINOR */
#define IFX_GTM_REV_MINOR_OFF (12u)

/** \brief  Length for Ifx_GTM_REV_Bits.NO */
#define IFX_GTM_REV_NO_LEN (4u)

/** \brief  Mask for Ifx_GTM_REV_Bits.NO */
#define IFX_GTM_REV_NO_MSK (0xfu)

/** \brief  Offset for Ifx_GTM_REV_Bits.NO */
#define IFX_GTM_REV_NO_OFF (8u)

/** \brief  Length for Ifx_GTM_REV_Bits.STEP */
#define IFX_GTM_REV_STEP_LEN (8u)

/** \brief  Mask for Ifx_GTM_REV_Bits.STEP */
#define IFX_GTM_REV_STEP_MSK (0xffu)

/** \brief  Offset for Ifx_GTM_REV_Bits.STEP */
#define IFX_GTM_REV_STEP_OFF (0u)

/** \brief  Length for Ifx_GTM_RST_Bits.RST */
#define IFX_GTM_RST_RST_LEN (1u)

/** \brief  Mask for Ifx_GTM_RST_Bits.RST */
#define IFX_GTM_RST_RST_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_RST_Bits.RST */
#define IFX_GTM_RST_RST_OFF (0u)

/** \brief  Length for Ifx_GTM_TBU_CH0_BASE_Bits.BASE */
#define IFX_GTM_TBU_CH0_BASE_BASE_LEN (27u)

/** \brief  Mask for Ifx_GTM_TBU_CH0_BASE_Bits.BASE */
#define IFX_GTM_TBU_CH0_BASE_BASE_MSK (0x7ffffffu)

/** \brief  Offset for Ifx_GTM_TBU_CH0_BASE_Bits.BASE */
#define IFX_GTM_TBU_CH0_BASE_BASE_OFF (0u)

/** \brief  Length for Ifx_GTM_TBU_CH0_CTRL_Bits.CH_CLK_SRC */
#define IFX_GTM_TBU_CH0_CTRL_CH_CLK_SRC_LEN (3u)

/** \brief  Mask for Ifx_GTM_TBU_CH0_CTRL_Bits.CH_CLK_SRC */
#define IFX_GTM_TBU_CH0_CTRL_CH_CLK_SRC_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_TBU_CH0_CTRL_Bits.CH_CLK_SRC */
#define IFX_GTM_TBU_CH0_CTRL_CH_CLK_SRC_OFF (1u)

/** \brief  Length for Ifx_GTM_TBU_CH0_CTRL_Bits.LOW_RES */
#define IFX_GTM_TBU_CH0_CTRL_LOW_RES_LEN (1u)

/** \brief  Mask for Ifx_GTM_TBU_CH0_CTRL_Bits.LOW_RES */
#define IFX_GTM_TBU_CH0_CTRL_LOW_RES_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TBU_CH0_CTRL_Bits.LOW_RES */
#define IFX_GTM_TBU_CH0_CTRL_LOW_RES_OFF (0u)

/** \brief  Length for Ifx_GTM_TBU_CH1_BASE_Bits.BASE */
#define IFX_GTM_TBU_CH1_BASE_BASE_LEN (24u)

/** \brief  Mask for Ifx_GTM_TBU_CH1_BASE_Bits.BASE */
#define IFX_GTM_TBU_CH1_BASE_BASE_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_TBU_CH1_BASE_Bits.BASE */
#define IFX_GTM_TBU_CH1_BASE_BASE_OFF (0u)

/** \brief  Length for Ifx_GTM_TBU_CH1_CTRL_Bits.CH_CLK_SRC */
#define IFX_GTM_TBU_CH1_CTRL_CH_CLK_SRC_LEN (3u)

/** \brief  Mask for Ifx_GTM_TBU_CH1_CTRL_Bits.CH_CLK_SRC */
#define IFX_GTM_TBU_CH1_CTRL_CH_CLK_SRC_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_TBU_CH1_CTRL_Bits.CH_CLK_SRC */
#define IFX_GTM_TBU_CH1_CTRL_CH_CLK_SRC_OFF (1u)

/** \brief  Length for Ifx_GTM_TBU_CH1_CTRL_Bits.CH_MODE */
#define IFX_GTM_TBU_CH1_CTRL_CH_MODE_LEN (1u)

/** \brief  Mask for Ifx_GTM_TBU_CH1_CTRL_Bits.CH_MODE */
#define IFX_GTM_TBU_CH1_CTRL_CH_MODE_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TBU_CH1_CTRL_Bits.CH_MODE */
#define IFX_GTM_TBU_CH1_CTRL_CH_MODE_OFF (0u)

/** \brief  Length for Ifx_GTM_TBU_CH2_BASE_Bits.BASE */
#define IFX_GTM_TBU_CH2_BASE_BASE_LEN (24u)

/** \brief  Mask for Ifx_GTM_TBU_CH2_BASE_Bits.BASE */
#define IFX_GTM_TBU_CH2_BASE_BASE_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_TBU_CH2_BASE_Bits.BASE */
#define IFX_GTM_TBU_CH2_BASE_BASE_OFF (0u)

/** \brief  Length for Ifx_GTM_TBU_CH2_CTRL_Bits.CH_CLK_SRC */
#define IFX_GTM_TBU_CH2_CTRL_CH_CLK_SRC_LEN (3u)

/** \brief  Mask for Ifx_GTM_TBU_CH2_CTRL_Bits.CH_CLK_SRC */
#define IFX_GTM_TBU_CH2_CTRL_CH_CLK_SRC_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_TBU_CH2_CTRL_Bits.CH_CLK_SRC */
#define IFX_GTM_TBU_CH2_CTRL_CH_CLK_SRC_OFF (1u)

/** \brief  Length for Ifx_GTM_TBU_CH2_CTRL_Bits.CH_MODE */
#define IFX_GTM_TBU_CH2_CTRL_CH_MODE_LEN (1u)

/** \brief  Mask for Ifx_GTM_TBU_CH2_CTRL_Bits.CH_MODE */
#define IFX_GTM_TBU_CH2_CTRL_CH_MODE_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TBU_CH2_CTRL_Bits.CH_MODE */
#define IFX_GTM_TBU_CH2_CTRL_CH_MODE_OFF (0u)

/** \brief  Length for Ifx_GTM_TBU_CHEN_Bits.ENDIS_CH0 */
#define IFX_GTM_TBU_CHEN_ENDIS_CH0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TBU_CHEN_Bits.ENDIS_CH0 */
#define IFX_GTM_TBU_CHEN_ENDIS_CH0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TBU_CHEN_Bits.ENDIS_CH0 */
#define IFX_GTM_TBU_CHEN_ENDIS_CH0_OFF (0u)

/** \brief  Length for Ifx_GTM_TBU_CHEN_Bits.ENDIS_CH1 */
#define IFX_GTM_TBU_CHEN_ENDIS_CH1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TBU_CHEN_Bits.ENDIS_CH1 */
#define IFX_GTM_TBU_CHEN_ENDIS_CH1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TBU_CHEN_Bits.ENDIS_CH1 */
#define IFX_GTM_TBU_CHEN_ENDIS_CH1_OFF (2u)

/** \brief  Length for Ifx_GTM_TBU_CHEN_Bits.ENDIS_CH2 */
#define IFX_GTM_TBU_CHEN_ENDIS_CH2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TBU_CHEN_Bits.ENDIS_CH2 */
#define IFX_GTM_TBU_CHEN_ENDIS_CH2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TBU_CHEN_Bits.ENDIS_CH2 */
#define IFX_GTM_TBU_CHEN_ENDIS_CH2_OFF (4u)

/** \brief  Length for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH0 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH0_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH0 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH0 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH0_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH1 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH1_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH1 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH1 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH1_OFF (1u)

/** \brief  Length for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH2 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH2_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH2 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH2 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH2_OFF (2u)

/** \brief  Length for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH3 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH3_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH3 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH3 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH3_OFF (3u)

/** \brief  Length for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH4 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH4_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH4 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH4_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH4 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH4_OFF (4u)

/** \brief  Length for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH5 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH5_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH5 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH5_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH5 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH5_OFF (5u)

/** \brief  Length for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH6 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH6_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH6 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH6_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH6 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH6_OFF (6u)

/** \brief  Length for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH7 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH7_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH7 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH7_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_AUX_IN_SRC_Bits.SRC_CH7 */
#define IFX_GTM_TIM_AUX_IN_SRC_SRC_CH7_OFF (7u)

/** \brief  Length for Ifx_GTM_TIM_CH_CNT_Bits.CNT */
#define IFX_GTM_TIM_CH_CNT_CNT_LEN (24u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CNT_Bits.CNT */
#define IFX_GTM_TIM_CH_CNT_CNT_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_TIM_CH_CNT_Bits.CNT */
#define IFX_GTM_TIM_CH_CNT_CNT_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_CNTS_Bits.CNTS */
#define IFX_GTM_TIM_CH_CNTS_CNTS_LEN (24u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CNTS_Bits.CNTS */
#define IFX_GTM_TIM_CH_CNTS_CNTS_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_TIM_CH_CNTS_Bits.CNTS */
#define IFX_GTM_TIM_CH_CNTS_CNTS_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_CNTS_Bits.ECNT */
#define IFX_GTM_TIM_CH_CNTS_ECNT_LEN (8u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CNTS_Bits.ECNT */
#define IFX_GTM_TIM_CH_CNTS_ECNT_MSK (0xffu)

/** \brief  Offset for Ifx_GTM_TIM_CH_CNTS_Bits.ECNT */
#define IFX_GTM_TIM_CH_CNTS_ECNT_OFF (24u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.CICTRL */
#define IFX_GTM_TIM_CH_CTRL_CICTRL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.CICTRL */
#define IFX_GTM_TIM_CH_CTRL_CICTRL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.CICTRL */
#define IFX_GTM_TIM_CH_CTRL_CICTRL_OFF (6u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.CLK_SEL */
#define IFX_GTM_TIM_CH_CTRL_CLK_SEL_LEN (3u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.CLK_SEL */
#define IFX_GTM_TIM_CH_CTRL_CLK_SEL_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.CLK_SEL */
#define IFX_GTM_TIM_CH_CTRL_CLK_SEL_OFF (24u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.CNTS_SEL */
#define IFX_GTM_TIM_CH_CTRL_CNTS_SEL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.CNTS_SEL */
#define IFX_GTM_TIM_CH_CTRL_CNTS_SEL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.CNTS_SEL */
#define IFX_GTM_TIM_CH_CTRL_CNTS_SEL_OFF (12u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.DSL */
#define IFX_GTM_TIM_CH_CTRL_DSL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.DSL */
#define IFX_GTM_TIM_CH_CTRL_DSL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.DSL */
#define IFX_GTM_TIM_CH_CTRL_DSL_OFF (13u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.ECNT_RESET */
#define IFX_GTM_TIM_CH_CTRL_ECNT_RESET_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.ECNT_RESET */
#define IFX_GTM_TIM_CH_CTRL_ECNT_RESET_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.ECNT_RESET */
#define IFX_GTM_TIM_CH_CTRL_ECNT_RESET_OFF (15u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.EGPR0_SEL */
#define IFX_GTM_TIM_CH_CTRL_EGPR0_SEL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.EGPR0_SEL */
#define IFX_GTM_TIM_CH_CTRL_EGPR0_SEL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.EGPR0_SEL */
#define IFX_GTM_TIM_CH_CTRL_EGPR0_SEL_OFF (28u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.EGPR1_SEL */
#define IFX_GTM_TIM_CH_CTRL_EGPR1_SEL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.EGPR1_SEL */
#define IFX_GTM_TIM_CH_CTRL_EGPR1_SEL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.EGPR1_SEL */
#define IFX_GTM_TIM_CH_CTRL_EGPR1_SEL_OFF (29u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.EXT_CAP_EN */
#define IFX_GTM_TIM_CH_CTRL_EXT_CAP_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.EXT_CAP_EN */
#define IFX_GTM_TIM_CH_CTRL_EXT_CAP_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.EXT_CAP_EN */
#define IFX_GTM_TIM_CH_CTRL_EXT_CAP_EN_OFF (19u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_CNT_FRQ */
#define IFX_GTM_TIM_CH_CTRL_FLT_CNT_FRQ_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_CNT_FRQ */
#define IFX_GTM_TIM_CH_CTRL_FLT_CNT_FRQ_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_CNT_FRQ */
#define IFX_GTM_TIM_CH_CTRL_FLT_CNT_FRQ_OFF (17u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_CTR_FE */
#define IFX_GTM_TIM_CH_CTRL_FLT_CTR_FE_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_CTR_FE */
#define IFX_GTM_TIM_CH_CTRL_FLT_CTR_FE_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_CTR_FE */
#define IFX_GTM_TIM_CH_CTRL_FLT_CTR_FE_OFF (23u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_CTR_RE */
#define IFX_GTM_TIM_CH_CTRL_FLT_CTR_RE_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_CTR_RE */
#define IFX_GTM_TIM_CH_CTRL_FLT_CTR_RE_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_CTR_RE */
#define IFX_GTM_TIM_CH_CTRL_FLT_CTR_RE_OFF (21u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_EN */
#define IFX_GTM_TIM_CH_CTRL_FLT_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_EN */
#define IFX_GTM_TIM_CH_CTRL_FLT_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_EN */
#define IFX_GTM_TIM_CH_CTRL_FLT_EN_OFF (16u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_MODE_FE */
#define IFX_GTM_TIM_CH_CTRL_FLT_MODE_FE_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_MODE_FE */
#define IFX_GTM_TIM_CH_CTRL_FLT_MODE_FE_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_MODE_FE */
#define IFX_GTM_TIM_CH_CTRL_FLT_MODE_FE_OFF (22u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_MODE_RE */
#define IFX_GTM_TIM_CH_CTRL_FLT_MODE_RE_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_MODE_RE */
#define IFX_GTM_TIM_CH_CTRL_FLT_MODE_RE_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.FLT_MODE_RE */
#define IFX_GTM_TIM_CH_CTRL_FLT_MODE_RE_OFF (20u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.FR_ECNT_OFL */
#define IFX_GTM_TIM_CH_CTRL_FR_ECNT_OFL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.FR_ECNT_OFL */
#define IFX_GTM_TIM_CH_CTRL_FR_ECNT_OFL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.FR_ECNT_OFL */
#define IFX_GTM_TIM_CH_CTRL_FR_ECNT_OFL_OFF (27u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.GPR0_SEL */
#define IFX_GTM_TIM_CH_CTRL_GPR0_SEL_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.GPR0_SEL */
#define IFX_GTM_TIM_CH_CTRL_GPR0_SEL_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.GPR0_SEL */
#define IFX_GTM_TIM_CH_CTRL_GPR0_SEL_OFF (8u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.GPR1_SEL */
#define IFX_GTM_TIM_CH_CTRL_GPR1_SEL_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.GPR1_SEL */
#define IFX_GTM_TIM_CH_CTRL_GPR1_SEL_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.GPR1_SEL */
#define IFX_GTM_TIM_CH_CTRL_GPR1_SEL_OFF (10u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.ISL */
#define IFX_GTM_TIM_CH_CTRL_ISL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.ISL */
#define IFX_GTM_TIM_CH_CTRL_ISL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.ISL */
#define IFX_GTM_TIM_CH_CTRL_ISL_OFF (14u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.OSM */
#define IFX_GTM_TIM_CH_CTRL_OSM_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.OSM */
#define IFX_GTM_TIM_CH_CTRL_OSM_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.OSM */
#define IFX_GTM_TIM_CH_CTRL_OSM_OFF (4u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.TBU0x_SEL */
#define IFX_GTM_TIM_CH_CTRL_TBU0X_SEL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.TBU0x_SEL */
#define IFX_GTM_TIM_CH_CTRL_TBU0X_SEL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.TBU0x_SEL */
#define IFX_GTM_TIM_CH_CTRL_TBU0X_SEL_OFF (7u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.TIM_EN */
#define IFX_GTM_TIM_CH_CTRL_TIM_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.TIM_EN */
#define IFX_GTM_TIM_CH_CTRL_TIM_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.TIM_EN */
#define IFX_GTM_TIM_CH_CTRL_TIM_EN_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.TIM_MODE */
#define IFX_GTM_TIM_CH_CTRL_TIM_MODE_LEN (3u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.TIM_MODE */
#define IFX_GTM_TIM_CH_CTRL_TIM_MODE_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.TIM_MODE */
#define IFX_GTM_TIM_CH_CTRL_TIM_MODE_OFF (1u)

/** \brief  Length for Ifx_GTM_TIM_CH_CTRL_Bits.TOCTRL */
#define IFX_GTM_TIM_CH_CTRL_TOCTRL_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_CH_CTRL_Bits.TOCTRL */
#define IFX_GTM_TIM_CH_CTRL_TOCTRL_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_CH_CTRL_Bits.TOCTRL */
#define IFX_GTM_TIM_CH_CTRL_TOCTRL_OFF (30u)

/** \brief  Length for Ifx_GTM_TIM_CH_ECNT_Bits.ECNT */
#define IFX_GTM_TIM_CH_ECNT_ECNT_LEN (16u)

/** \brief  Mask for Ifx_GTM_TIM_CH_ECNT_Bits.ECNT */
#define IFX_GTM_TIM_CH_ECNT_ECNT_MSK (0xffffu)

/** \brief  Offset for Ifx_GTM_TIM_CH_ECNT_Bits.ECNT */
#define IFX_GTM_TIM_CH_ECNT_ECNT_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_ECTRL_Bits.EXT_CAP_SRC */
#define IFX_GTM_TIM_CH_ECTRL_EXT_CAP_SRC_LEN (3u)

/** \brief  Mask for Ifx_GTM_TIM_CH_ECTRL_Bits.EXT_CAP_SRC */
#define IFX_GTM_TIM_CH_ECTRL_EXT_CAP_SRC_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_TIM_CH_ECTRL_Bits.EXT_CAP_SRC */
#define IFX_GTM_TIM_CH_ECTRL_EXT_CAP_SRC_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.CNTOFL_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_CNTOFL_EIRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.CNTOFL_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_CNTOFL_EIRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.CNTOFL_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_CNTOFL_EIRQ_EN_OFF (2u)

/** \brief  Length for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.ECNTOFL_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_ECNTOFL_EIRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.ECNTOFL_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_ECNTOFL_EIRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.ECNTOFL_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_ECNTOFL_EIRQ_EN_OFF (1u)

/** \brief  Length for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.GLITCHDET_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_GLITCHDET_EIRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.GLITCHDET_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_GLITCHDET_EIRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.GLITCHDET_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_GLITCHDET_EIRQ_EN_OFF (5u)

/** \brief  Length for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.GPRzOFL_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_GPRZOFL_EIRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.GPRzOFL_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_GPRZOFL_EIRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.GPRzOFL_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_GPRZOFL_EIRQ_EN_OFF (3u)

/** \brief  Length for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.NEWVAL_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_NEWVAL_EIRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.NEWVAL_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_NEWVAL_EIRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.NEWVAL_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_NEWVAL_EIRQ_EN_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.TODET_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_TODET_EIRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.TODET_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_TODET_EIRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_EIRQ_EN_Bits.TODET_EIRQ_EN */
#define IFX_GTM_TIM_CH_EIRQ_EN_TODET_EIRQ_EN_OFF (4u)

/** \brief  Length for Ifx_GTM_TIM_CH_FLT_FE_Bits.FLT_FE */
#define IFX_GTM_TIM_CH_FLT_FE_FLT_FE_LEN (24u)

/** \brief  Mask for Ifx_GTM_TIM_CH_FLT_FE_Bits.FLT_FE */
#define IFX_GTM_TIM_CH_FLT_FE_FLT_FE_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_TIM_CH_FLT_FE_Bits.FLT_FE */
#define IFX_GTM_TIM_CH_FLT_FE_FLT_FE_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_FLT_RE_Bits.FLT_RE */
#define IFX_GTM_TIM_CH_FLT_RE_FLT_RE_LEN (24u)

/** \brief  Mask for Ifx_GTM_TIM_CH_FLT_RE_Bits.FLT_RE */
#define IFX_GTM_TIM_CH_FLT_RE_FLT_RE_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_TIM_CH_FLT_RE_Bits.FLT_RE */
#define IFX_GTM_TIM_CH_FLT_RE_FLT_RE_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_GPR0_Bits.ECNT */
#define IFX_GTM_TIM_CH_GPR0_ECNT_LEN (8u)

/** \brief  Mask for Ifx_GTM_TIM_CH_GPR0_Bits.ECNT */
#define IFX_GTM_TIM_CH_GPR0_ECNT_MSK (0xffu)

/** \brief  Offset for Ifx_GTM_TIM_CH_GPR0_Bits.ECNT */
#define IFX_GTM_TIM_CH_GPR0_ECNT_OFF (24u)

/** \brief  Length for Ifx_GTM_TIM_CH_GPR0_Bits.GPR0 */
#define IFX_GTM_TIM_CH_GPR0_GPR0_LEN (24u)

/** \brief  Mask for Ifx_GTM_TIM_CH_GPR0_Bits.GPR0 */
#define IFX_GTM_TIM_CH_GPR0_GPR0_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_TIM_CH_GPR0_Bits.GPR0 */
#define IFX_GTM_TIM_CH_GPR0_GPR0_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_GPR1_Bits.ECNT */
#define IFX_GTM_TIM_CH_GPR1_ECNT_LEN (8u)

/** \brief  Mask for Ifx_GTM_TIM_CH_GPR1_Bits.ECNT */
#define IFX_GTM_TIM_CH_GPR1_ECNT_MSK (0xffu)

/** \brief  Offset for Ifx_GTM_TIM_CH_GPR1_Bits.ECNT */
#define IFX_GTM_TIM_CH_GPR1_ECNT_OFF (24u)

/** \brief  Length for Ifx_GTM_TIM_CH_GPR1_Bits.GPR1 */
#define IFX_GTM_TIM_CH_GPR1_GPR1_LEN (24u)

/** \brief  Mask for Ifx_GTM_TIM_CH_GPR1_Bits.GPR1 */
#define IFX_GTM_TIM_CH_GPR1_GPR1_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_TIM_CH_GPR1_Bits.GPR1 */
#define IFX_GTM_TIM_CH_GPR1_GPR1_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_EN_Bits.CNTOFL_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_CNTOFL_IRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_EN_Bits.CNTOFL_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_CNTOFL_IRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_EN_Bits.CNTOFL_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_CNTOFL_IRQ_EN_OFF (2u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_EN_Bits.ECNTOFL_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_ECNTOFL_IRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_EN_Bits.ECNTOFL_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_ECNTOFL_IRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_EN_Bits.ECNTOFL_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_ECNTOFL_IRQ_EN_OFF (1u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_EN_Bits.GLITCHDET_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_GLITCHDET_IRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_EN_Bits.GLITCHDET_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_GLITCHDET_IRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_EN_Bits.GLITCHDET_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_GLITCHDET_IRQ_EN_OFF (5u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_EN_Bits.GPRzOFL_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_GPRZOFL_IRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_EN_Bits.GPRzOFL_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_GPRZOFL_IRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_EN_Bits.GPRzOFL_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_GPRZOFL_IRQ_EN_OFF (3u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_EN_Bits.NEWVAL_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_NEWVAL_IRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_EN_Bits.NEWVAL_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_NEWVAL_IRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_EN_Bits.NEWVAL_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_NEWVAL_IRQ_EN_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_EN_Bits.TODET_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_TODET_IRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_EN_Bits.TODET_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_TODET_IRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_EN_Bits.TODET_IRQ_EN */
#define IFX_GTM_TIM_CH_IRQ_EN_TODET_IRQ_EN_OFF (4u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_CNTOFL */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_CNTOFL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_CNTOFL */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_CNTOFL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_CNTOFL */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_CNTOFL_OFF (2u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_ECNTOFL */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_ECNTOFL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_ECNTOFL */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_ECNTOFL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_ECNTOFL */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_ECNTOFL_OFF (1u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_GLITCHDET */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_GLITCHDET_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_GLITCHDET */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_GLITCHDET_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_GLITCHDET */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_GLITCHDET_OFF (5u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_GPRzOFL */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_GPRZOFL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_GPRzOFL */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_GPRZOFL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_GPRzOFL */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_GPRZOFL_OFF (3u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_NEWVAL */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_NEWVAL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_NEWVAL */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_NEWVAL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_NEWVAL */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_NEWVAL_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_TODET */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_TODET_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_TODET */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_TODET_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits.TRG_TODET */
#define IFX_GTM_TIM_CH_IRQ_FORCINT_TRG_TODET_OFF (4u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_MODE_Bits.IRQ_MODE */
#define IFX_GTM_TIM_CH_IRQ_MODE_IRQ_MODE_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_MODE_Bits.IRQ_MODE */
#define IFX_GTM_TIM_CH_IRQ_MODE_IRQ_MODE_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_MODE_Bits.IRQ_MODE */
#define IFX_GTM_TIM_CH_IRQ_MODE_IRQ_MODE_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.CNTOFL */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_CNTOFL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.CNTOFL */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_CNTOFL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.CNTOFL */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_CNTOFL_OFF (2u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.ECNTOFL */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_ECNTOFL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.ECNTOFL */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_ECNTOFL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.ECNTOFL */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_ECNTOFL_OFF (1u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.GLITCHDET */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_GLITCHDET_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.GLITCHDET */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_GLITCHDET_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.GLITCHDET */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_GLITCHDET_OFF (5u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.GPRzOFL */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_GPRZOFL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.GPRzOFL */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_GPRZOFL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.GPRzOFL */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_GPRZOFL_OFF (3u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.NEWVAL */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_NEWVAL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.NEWVAL */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_NEWVAL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.NEWVAL */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_NEWVAL_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.TODET */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_TODET_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.TODET */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_TODET_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits.TODET */
#define IFX_GTM_TIM_CH_IRQ_NOTIFY_TODET_OFF (4u)

/** \brief  Length for Ifx_GTM_TIM_CH_TDUC_Bits.TO_CNT */
#define IFX_GTM_TIM_CH_TDUC_TO_CNT_LEN (8u)

/** \brief  Mask for Ifx_GTM_TIM_CH_TDUC_Bits.TO_CNT */
#define IFX_GTM_TIM_CH_TDUC_TO_CNT_MSK (0xffu)

/** \brief  Offset for Ifx_GTM_TIM_CH_TDUC_Bits.TO_CNT */
#define IFX_GTM_TIM_CH_TDUC_TO_CNT_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_CH_TDUV_Bits.TCS */
#define IFX_GTM_TIM_CH_TDUV_TCS_LEN (3u)

/** \brief  Mask for Ifx_GTM_TIM_CH_TDUV_Bits.TCS */
#define IFX_GTM_TIM_CH_TDUV_TCS_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_TIM_CH_TDUV_Bits.TCS */
#define IFX_GTM_TIM_CH_TDUV_TCS_OFF (28u)

/** \brief  Length for Ifx_GTM_TIM_CH_TDUV_Bits.TOV */
#define IFX_GTM_TIM_CH_TDUV_TOV_LEN (8u)

/** \brief  Mask for Ifx_GTM_TIM_CH_TDUV_Bits.TOV */
#define IFX_GTM_TIM_CH_TDUV_TOV_MSK (0xffu)

/** \brief  Offset for Ifx_GTM_TIM_CH_TDUV_Bits.TOV */
#define IFX_GTM_TIM_CH_TDUV_TOV_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.MODE_0 */
#define IFX_GTM_TIM_IN_SRC_MODE_0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.MODE_0 */
#define IFX_GTM_TIM_IN_SRC_MODE_0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.MODE_0 */
#define IFX_GTM_TIM_IN_SRC_MODE_0_OFF (2u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.MODE_1 */
#define IFX_GTM_TIM_IN_SRC_MODE_1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.MODE_1 */
#define IFX_GTM_TIM_IN_SRC_MODE_1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.MODE_1 */
#define IFX_GTM_TIM_IN_SRC_MODE_1_OFF (6u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.MODE_2 */
#define IFX_GTM_TIM_IN_SRC_MODE_2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.MODE_2 */
#define IFX_GTM_TIM_IN_SRC_MODE_2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.MODE_2 */
#define IFX_GTM_TIM_IN_SRC_MODE_2_OFF (10u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.MODE_3 */
#define IFX_GTM_TIM_IN_SRC_MODE_3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.MODE_3 */
#define IFX_GTM_TIM_IN_SRC_MODE_3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.MODE_3 */
#define IFX_GTM_TIM_IN_SRC_MODE_3_OFF (14u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.MODE_4 */
#define IFX_GTM_TIM_IN_SRC_MODE_4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.MODE_4 */
#define IFX_GTM_TIM_IN_SRC_MODE_4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.MODE_4 */
#define IFX_GTM_TIM_IN_SRC_MODE_4_OFF (18u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.MODE_5 */
#define IFX_GTM_TIM_IN_SRC_MODE_5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.MODE_5 */
#define IFX_GTM_TIM_IN_SRC_MODE_5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.MODE_5 */
#define IFX_GTM_TIM_IN_SRC_MODE_5_OFF (22u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.MODE_6 */
#define IFX_GTM_TIM_IN_SRC_MODE_6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.MODE_6 */
#define IFX_GTM_TIM_IN_SRC_MODE_6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.MODE_6 */
#define IFX_GTM_TIM_IN_SRC_MODE_6_OFF (26u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.MODE_7 */
#define IFX_GTM_TIM_IN_SRC_MODE_7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.MODE_7 */
#define IFX_GTM_TIM_IN_SRC_MODE_7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.MODE_7 */
#define IFX_GTM_TIM_IN_SRC_MODE_7_OFF (30u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.VAL_0 */
#define IFX_GTM_TIM_IN_SRC_VAL_0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.VAL_0 */
#define IFX_GTM_TIM_IN_SRC_VAL_0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.VAL_0 */
#define IFX_GTM_TIM_IN_SRC_VAL_0_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.VAL_1 */
#define IFX_GTM_TIM_IN_SRC_VAL_1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.VAL_1 */
#define IFX_GTM_TIM_IN_SRC_VAL_1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.VAL_1 */
#define IFX_GTM_TIM_IN_SRC_VAL_1_OFF (4u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.VAL_2 */
#define IFX_GTM_TIM_IN_SRC_VAL_2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.VAL_2 */
#define IFX_GTM_TIM_IN_SRC_VAL_2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.VAL_2 */
#define IFX_GTM_TIM_IN_SRC_VAL_2_OFF (8u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.VAL_3 */
#define IFX_GTM_TIM_IN_SRC_VAL_3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.VAL_3 */
#define IFX_GTM_TIM_IN_SRC_VAL_3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.VAL_3 */
#define IFX_GTM_TIM_IN_SRC_VAL_3_OFF (12u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.VAL_4 */
#define IFX_GTM_TIM_IN_SRC_VAL_4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.VAL_4 */
#define IFX_GTM_TIM_IN_SRC_VAL_4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.VAL_4 */
#define IFX_GTM_TIM_IN_SRC_VAL_4_OFF (16u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.VAL_5 */
#define IFX_GTM_TIM_IN_SRC_VAL_5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.VAL_5 */
#define IFX_GTM_TIM_IN_SRC_VAL_5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.VAL_5 */
#define IFX_GTM_TIM_IN_SRC_VAL_5_OFF (20u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.VAL_6 */
#define IFX_GTM_TIM_IN_SRC_VAL_6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.VAL_6 */
#define IFX_GTM_TIM_IN_SRC_VAL_6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.VAL_6 */
#define IFX_GTM_TIM_IN_SRC_VAL_6_OFF (24u)

/** \brief  Length for Ifx_GTM_TIM_IN_SRC_Bits.VAL_7 */
#define IFX_GTM_TIM_IN_SRC_VAL_7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TIM_IN_SRC_Bits.VAL_7 */
#define IFX_GTM_TIM_IN_SRC_VAL_7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TIM_IN_SRC_Bits.VAL_7 */
#define IFX_GTM_TIM_IN_SRC_VAL_7_OFF (28u)

/** \brief  Length for Ifx_GTM_TIM_INP_VAL_Bits.F_IN */
#define IFX_GTM_TIM_INP_VAL_F_IN_LEN (8u)

/** \brief  Mask for Ifx_GTM_TIM_INP_VAL_Bits.F_IN */
#define IFX_GTM_TIM_INP_VAL_F_IN_MSK (0xffu)

/** \brief  Offset for Ifx_GTM_TIM_INP_VAL_Bits.F_IN */
#define IFX_GTM_TIM_INP_VAL_F_IN_OFF (8u)

/** \brief  Length for Ifx_GTM_TIM_INP_VAL_Bits.F_OUT */
#define IFX_GTM_TIM_INP_VAL_F_OUT_LEN (8u)

/** \brief  Mask for Ifx_GTM_TIM_INP_VAL_Bits.F_OUT */
#define IFX_GTM_TIM_INP_VAL_F_OUT_MSK (0xffu)

/** \brief  Offset for Ifx_GTM_TIM_INP_VAL_Bits.F_OUT */
#define IFX_GTM_TIM_INP_VAL_F_OUT_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_INP_VAL_Bits.TIM_IN */
#define IFX_GTM_TIM_INP_VAL_TIM_IN_LEN (8u)

/** \brief  Mask for Ifx_GTM_TIM_INP_VAL_Bits.TIM_IN */
#define IFX_GTM_TIM_INP_VAL_TIM_IN_MSK (0xffu)

/** \brief  Offset for Ifx_GTM_TIM_INP_VAL_Bits.TIM_IN */
#define IFX_GTM_TIM_INP_VAL_TIM_IN_OFF (16u)

/** \brief  Length for Ifx_GTM_TIM_RST_Bits.RST_CH0 */
#define IFX_GTM_TIM_RST_RST_CH0_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_RST_Bits.RST_CH0 */
#define IFX_GTM_TIM_RST_RST_CH0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_RST_Bits.RST_CH0 */
#define IFX_GTM_TIM_RST_RST_CH0_OFF (0u)

/** \brief  Length for Ifx_GTM_TIM_RST_Bits.RST_CH1 */
#define IFX_GTM_TIM_RST_RST_CH1_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_RST_Bits.RST_CH1 */
#define IFX_GTM_TIM_RST_RST_CH1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_RST_Bits.RST_CH1 */
#define IFX_GTM_TIM_RST_RST_CH1_OFF (1u)

/** \brief  Length for Ifx_GTM_TIM_RST_Bits.RST_CH2 */
#define IFX_GTM_TIM_RST_RST_CH2_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_RST_Bits.RST_CH2 */
#define IFX_GTM_TIM_RST_RST_CH2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_RST_Bits.RST_CH2 */
#define IFX_GTM_TIM_RST_RST_CH2_OFF (2u)

/** \brief  Length for Ifx_GTM_TIM_RST_Bits.RST_CH3 */
#define IFX_GTM_TIM_RST_RST_CH3_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_RST_Bits.RST_CH3 */
#define IFX_GTM_TIM_RST_RST_CH3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_RST_Bits.RST_CH3 */
#define IFX_GTM_TIM_RST_RST_CH3_OFF (3u)

/** \brief  Length for Ifx_GTM_TIM_RST_Bits.RST_CH4 */
#define IFX_GTM_TIM_RST_RST_CH4_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_RST_Bits.RST_CH4 */
#define IFX_GTM_TIM_RST_RST_CH4_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_RST_Bits.RST_CH4 */
#define IFX_GTM_TIM_RST_RST_CH4_OFF (4u)

/** \brief  Length for Ifx_GTM_TIM_RST_Bits.RST_CH5 */
#define IFX_GTM_TIM_RST_RST_CH5_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_RST_Bits.RST_CH5 */
#define IFX_GTM_TIM_RST_RST_CH5_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_RST_Bits.RST_CH5 */
#define IFX_GTM_TIM_RST_RST_CH5_OFF (5u)

/** \brief  Length for Ifx_GTM_TIM_RST_Bits.RST_CH6 */
#define IFX_GTM_TIM_RST_RST_CH6_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_RST_Bits.RST_CH6 */
#define IFX_GTM_TIM_RST_RST_CH6_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_RST_Bits.RST_CH6 */
#define IFX_GTM_TIM_RST_RST_CH6_OFF (6u)

/** \brief  Length for Ifx_GTM_TIM_RST_Bits.RST_CH7 */
#define IFX_GTM_TIM_RST_RST_CH7_LEN (1u)

/** \brief  Mask for Ifx_GTM_TIM_RST_Bits.RST_CH7 */
#define IFX_GTM_TIM_RST_RST_CH7_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TIM_RST_Bits.RST_CH7 */
#define IFX_GTM_TIM_RST_RST_CH7_OFF (7u)

/** \brief  Length for Ifx_GTM_TOM_CH_CM0_Bits.CM0 */
#define IFX_GTM_TOM_CH_CM0_CM0_LEN (16u)

/** \brief  Mask for Ifx_GTM_TOM_CH_CM0_Bits.CM0 */
#define IFX_GTM_TOM_CH_CM0_CM0_MSK (0xffffu)

/** \brief  Offset for Ifx_GTM_TOM_CH_CM0_Bits.CM0 */
#define IFX_GTM_TOM_CH_CM0_CM0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_CH_CM1_Bits.CM1 */
#define IFX_GTM_TOM_CH_CM1_CM1_LEN (16u)

/** \brief  Mask for Ifx_GTM_TOM_CH_CM1_Bits.CM1 */
#define IFX_GTM_TOM_CH_CM1_CM1_MSK (0xffffu)

/** \brief  Offset for Ifx_GTM_TOM_CH_CM1_Bits.CM1 */
#define IFX_GTM_TOM_CH_CM1_CM1_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_CH_CN0_Bits.CN0 */
#define IFX_GTM_TOM_CH_CN0_CN0_LEN (16u)

/** \brief  Mask for Ifx_GTM_TOM_CH_CN0_Bits.CN0 */
#define IFX_GTM_TOM_CH_CN0_CN0_MSK (0xffffu)

/** \brief  Offset for Ifx_GTM_TOM_CH_CN0_Bits.CN0 */
#define IFX_GTM_TOM_CH_CN0_CN0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_CH_CTRL_Bits.BITREV */
#define IFX_GTM_TOM_CH_CTRL_BITREV_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_CTRL_Bits.BITREV */
#define IFX_GTM_TOM_CH_CTRL_BITREV_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_CTRL_Bits.BITREV */
#define IFX_GTM_TOM_CH_CTRL_BITREV_OFF (27u)

/** \brief  Length for Ifx_GTM_TOM_CH_CTRL_Bits.CLK_SRC_SR */
#define IFX_GTM_TOM_CH_CTRL_CLK_SRC_SR_LEN (3u)

/** \brief  Mask for Ifx_GTM_TOM_CH_CTRL_Bits.CLK_SRC_SR */
#define IFX_GTM_TOM_CH_CTRL_CLK_SRC_SR_MSK (0x7u)

/** \brief  Offset for Ifx_GTM_TOM_CH_CTRL_Bits.CLK_SRC_SR */
#define IFX_GTM_TOM_CH_CTRL_CLK_SRC_SR_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_CH_CTRL_Bits.EXT_TRIG */
#define IFX_GTM_TOM_CH_CTRL_EXT_TRIG_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_CTRL_Bits.EXT_TRIG */
#define IFX_GTM_TOM_CH_CTRL_EXT_TRIG_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_CTRL_Bits.EXT_TRIG */
#define IFX_GTM_TOM_CH_CTRL_EXT_TRIG_OFF (22u)

/** \brief  Length for Ifx_GTM_TOM_CH_CTRL_Bits.EXTTRIGOUT */
#define IFX_GTM_TOM_CH_CTRL_EXTTRIGOUT_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_CTRL_Bits.EXTTRIGOUT */
#define IFX_GTM_TOM_CH_CTRL_EXTTRIGOUT_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_CTRL_Bits.EXTTRIGOUT */
#define IFX_GTM_TOM_CH_CTRL_EXTTRIGOUT_OFF (23u)

/** \brief  Length for Ifx_GTM_TOM_CH_CTRL_Bits.OSM */
#define IFX_GTM_TOM_CH_CTRL_OSM_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_CTRL_Bits.OSM */
#define IFX_GTM_TOM_CH_CTRL_OSM_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_CTRL_Bits.OSM */
#define IFX_GTM_TOM_CH_CTRL_OSM_OFF (26u)

/** \brief  Length for Ifx_GTM_TOM_CH_CTRL_Bits.OSM_TRIG */
#define IFX_GTM_TOM_CH_CTRL_OSM_TRIG_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_CTRL_Bits.OSM_TRIG */
#define IFX_GTM_TOM_CH_CTRL_OSM_TRIG_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_CTRL_Bits.OSM_TRIG */
#define IFX_GTM_TOM_CH_CTRL_OSM_TRIG_OFF (21u)

/** \brief  Length for Ifx_GTM_TOM_CH_CTRL_Bits.RST_CCU0 */
#define IFX_GTM_TOM_CH_CTRL_RST_CCU0_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_CTRL_Bits.RST_CCU0 */
#define IFX_GTM_TOM_CH_CTRL_RST_CCU0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_CTRL_Bits.RST_CCU0 */
#define IFX_GTM_TOM_CH_CTRL_RST_CCU0_OFF (20u)

/** \brief  Length for Ifx_GTM_TOM_CH_CTRL_Bits.SL */
#define IFX_GTM_TOM_CH_CTRL_SL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_CTRL_Bits.SL */
#define IFX_GTM_TOM_CH_CTRL_SL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_CTRL_Bits.SL */
#define IFX_GTM_TOM_CH_CTRL_SL_OFF (11u)

/** \brief  Length for Ifx_GTM_TOM_CH_CTRL_Bits.TRIGOUT */
#define IFX_GTM_TOM_CH_CTRL_TRIGOUT_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_CTRL_Bits.TRIGOUT */
#define IFX_GTM_TOM_CH_CTRL_TRIGOUT_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_CTRL_Bits.TRIGOUT */
#define IFX_GTM_TOM_CH_CTRL_TRIGOUT_OFF (24u)

/** \brief  Length for Ifx_GTM_TOM_CH_IRQ_EN_Bits.CCU0TC_IRQ_EN */
#define IFX_GTM_TOM_CH_IRQ_EN_CCU0TC_IRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_IRQ_EN_Bits.CCU0TC_IRQ_EN */
#define IFX_GTM_TOM_CH_IRQ_EN_CCU0TC_IRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_IRQ_EN_Bits.CCU0TC_IRQ_EN */
#define IFX_GTM_TOM_CH_IRQ_EN_CCU0TC_IRQ_EN_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_CH_IRQ_EN_Bits.CCU1TC_IRQ_EN */
#define IFX_GTM_TOM_CH_IRQ_EN_CCU1TC_IRQ_EN_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_IRQ_EN_Bits.CCU1TC_IRQ_EN */
#define IFX_GTM_TOM_CH_IRQ_EN_CCU1TC_IRQ_EN_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_IRQ_EN_Bits.CCU1TC_IRQ_EN */
#define IFX_GTM_TOM_CH_IRQ_EN_CCU1TC_IRQ_EN_OFF (1u)

/** \brief  Length for Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits.TRG_CCU0TC0 */
#define IFX_GTM_TOM_CH_IRQ_FORCINT_TRG_CCU0TC0_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits.TRG_CCU0TC0 */
#define IFX_GTM_TOM_CH_IRQ_FORCINT_TRG_CCU0TC0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits.TRG_CCU0TC0 */
#define IFX_GTM_TOM_CH_IRQ_FORCINT_TRG_CCU0TC0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits.TRG_CCU1TC0 */
#define IFX_GTM_TOM_CH_IRQ_FORCINT_TRG_CCU1TC0_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits.TRG_CCU1TC0 */
#define IFX_GTM_TOM_CH_IRQ_FORCINT_TRG_CCU1TC0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits.TRG_CCU1TC0 */
#define IFX_GTM_TOM_CH_IRQ_FORCINT_TRG_CCU1TC0_OFF (1u)

/** \brief  Length for Ifx_GTM_TOM_CH_IRQ_MODE_Bits.IRQ_MODE */
#define IFX_GTM_TOM_CH_IRQ_MODE_IRQ_MODE_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_CH_IRQ_MODE_Bits.IRQ_MODE */
#define IFX_GTM_TOM_CH_IRQ_MODE_IRQ_MODE_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_CH_IRQ_MODE_Bits.IRQ_MODE */
#define IFX_GTM_TOM_CH_IRQ_MODE_IRQ_MODE_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits.CCU0TC */
#define IFX_GTM_TOM_CH_IRQ_NOTIFY_CCU0TC_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits.CCU0TC */
#define IFX_GTM_TOM_CH_IRQ_NOTIFY_CCU0TC_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits.CCU0TC */
#define IFX_GTM_TOM_CH_IRQ_NOTIFY_CCU0TC_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits.CCU1TC */
#define IFX_GTM_TOM_CH_IRQ_NOTIFY_CCU1TC_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits.CCU1TC */
#define IFX_GTM_TOM_CH_IRQ_NOTIFY_CCU1TC_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits.CCU1TC */
#define IFX_GTM_TOM_CH_IRQ_NOTIFY_CCU1TC_OFF (1u)

/** \brief  Length for Ifx_GTM_TOM_CH_SR0_Bits.SR0 */
#define IFX_GTM_TOM_CH_SR0_SR0_LEN (16u)

/** \brief  Mask for Ifx_GTM_TOM_CH_SR0_Bits.SR0 */
#define IFX_GTM_TOM_CH_SR0_SR0_MSK (0xffffu)

/** \brief  Offset for Ifx_GTM_TOM_CH_SR0_Bits.SR0 */
#define IFX_GTM_TOM_CH_SR0_SR0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_CH_SR1_Bits.SR1 */
#define IFX_GTM_TOM_CH_SR1_SR1_LEN (16u)

/** \brief  Mask for Ifx_GTM_TOM_CH_SR1_Bits.SR1 */
#define IFX_GTM_TOM_CH_SR1_SR1_MSK (0xffffu)

/** \brief  Offset for Ifx_GTM_TOM_CH_SR1_Bits.SR1 */
#define IFX_GTM_TOM_CH_SR1_SR1_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_CH_STAT_Bits.OL */
#define IFX_GTM_TOM_CH_STAT_OL_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_CH_STAT_Bits.OL */
#define IFX_GTM_TOM_CH_STAT_OL_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_CH_STAT_Bits.OL */
#define IFX_GTM_TOM_CH_STAT_OL_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ACT_TB_Bits.ACT_TB */
#define IFX_GTM_TOM_TGC0_ACT_TB_ACT_TB_LEN (24u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ACT_TB_Bits.ACT_TB */
#define IFX_GTM_TOM_TGC0_ACT_TB_ACT_TB_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ACT_TB_Bits.ACT_TB */
#define IFX_GTM_TOM_TGC0_ACT_TB_ACT_TB_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ACT_TB_Bits.TB_TRIG */
#define IFX_GTM_TOM_TGC0_ACT_TB_TB_TRIG_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ACT_TB_Bits.TB_TRIG */
#define IFX_GTM_TOM_TGC0_ACT_TB_TB_TRIG_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ACT_TB_Bits.TB_TRIG */
#define IFX_GTM_TOM_TGC0_ACT_TB_TB_TRIG_OFF (24u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ACT_TB_Bits.TBU_SEL */
#define IFX_GTM_TOM_TGC0_ACT_TB_TBU_SEL_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ACT_TB_Bits.TBU_SEL */
#define IFX_GTM_TOM_TGC0_ACT_TB_TBU_SEL_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ACT_TB_Bits.TBU_SEL */
#define IFX_GTM_TOM_TGC0_ACT_TB_TBU_SEL_OFF (25u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL0 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL0 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL0 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL1 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL1 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL1 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL1_OFF (2u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL2 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL2 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL2 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL2_OFF (4u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL3 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL3 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL3 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL3_OFF (6u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL4 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL4 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL4 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL4_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL5 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL5 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL5 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL5_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL6 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL6 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL6 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL6_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL7 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL7 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits.ENDIS_CTRL7 */
#define IFX_GTM_TOM_TGC0_ENDIS_CTRL_ENDIS_CTRL7_OFF (14u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT0 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT0 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT0 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT1 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT1 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT1 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT1_OFF (2u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT2 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT2 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT2 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT2_OFF (4u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT3 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT3 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT3 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT3_OFF (6u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT4 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT4 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT4 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT4_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT5 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT5 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT5 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT5_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT6 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT6 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT6 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT6_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT7 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT7 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits.ENDIS_STAT7 */
#define IFX_GTM_TOM_TGC0_ENDIS_STAT_ENDIS_STAT7_OFF (14u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL0 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL0 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL0 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL1 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL1 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL1 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL1_OFF (2u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL2 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL2 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL2 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL2_OFF (4u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL3 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL3 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL3 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL3_OFF (6u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL4 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL4 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL4 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL4_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL5 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL5 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL5 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL5_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL6 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL6 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL6 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL6_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL7 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL7 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.FUPD_CTRL7 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_FUPD_CTRL7_OFF (14u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH0 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH0 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH0 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH0_OFF (16u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH1 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH1 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH1 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH1_OFF (18u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH2 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH2 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH2 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH2_OFF (20u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH3 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH3 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH3 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH3_OFF (22u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH4 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH4 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH4 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH4_OFF (24u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH5 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH5 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH5 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH5_OFF (26u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH6 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH6 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH6 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH6_OFF (28u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH7 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH7 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits.RSTCN0_CH7 */
#define IFX_GTM_TOM_TGC0_FUPD_CTRL_RSTCN0_CH7_OFF (30u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.HOST_TRIG */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_HOST_TRIG_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.HOST_TRIG */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_HOST_TRIG_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.HOST_TRIG */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_HOST_TRIG_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH0 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH0_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH0 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH0 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH0_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH1 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH1_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH1 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH1 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH1_OFF (9u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH2 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH2_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH2 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH2 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH2_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH3 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH3_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH3 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH3 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH3_OFF (11u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH4 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH4_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH4 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH4_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH4 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH4_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH5 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH5_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH5 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH5_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH5 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH5_OFF (13u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH6 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH6_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH6 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH6_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH6 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH6_OFF (14u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH7 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH7_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH7 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH7_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.RST_CH7 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_RST_CH7_OFF (15u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL0 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL0 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL0 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL0_OFF (16u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL1 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL1 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL1 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL1_OFF (18u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL2 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL2 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL2 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL2_OFF (20u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL3 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL3 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL3 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL3_OFF (22u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL4 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL4 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL4 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL4_OFF (24u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL5 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL5 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL5 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL5_OFF (26u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL6 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL6 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL6 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL6_OFF (28u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL7 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL7 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits.UPEN_CTRL7 */
#define IFX_GTM_TOM_TGC0_GLB_CTRL_UPEN_CTRL7_OFF (30u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG0 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG0 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG0 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG1 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG1 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG1 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG1_OFF (2u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG2 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG2 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG2 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG2_OFF (4u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG3 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG3 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG3 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG3_OFF (6u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG4 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG4 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG4 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG4_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG5 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG5 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG5 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG5_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG6 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG6 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG6 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG6_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG7 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG7 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_INT_TRIG_Bits.INT_TRIG7 */
#define IFX_GTM_TOM_TGC0_INT_TRIG_INT_TRIG7_OFF (14u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL0 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL0 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL0 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL1 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL1 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL1 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL1_OFF (2u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL2 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL2 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL2 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL2_OFF (4u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL3 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL3 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL3 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL3_OFF (6u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL4 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL4 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL4 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL4_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL5 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL5 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL5 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL5_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL6 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL6 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL6 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL6_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL7 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL7 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits.OUTEN_CTRL7 */
#define IFX_GTM_TOM_TGC0_OUTEN_CTRL_OUTEN_CTRL7_OFF (14u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT0 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT0 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT0 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT1 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT1 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT1 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT1_OFF (2u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT2 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT2 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT2 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT2_OFF (4u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT3 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT3 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT3 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT3_OFF (6u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT4 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT4 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT4 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT4_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT5 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT5 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT5 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT5_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT6 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT6 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT6 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT6_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT7 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT7 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits.OUTEN_STAT7 */
#define IFX_GTM_TOM_TGC0_OUTEN_STAT_OUTEN_STAT7_OFF (14u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ACT_TB_Bits.ACT_TB */
#define IFX_GTM_TOM_TGC1_ACT_TB_ACT_TB_LEN (24u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ACT_TB_Bits.ACT_TB */
#define IFX_GTM_TOM_TGC1_ACT_TB_ACT_TB_MSK (0xffffffu)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ACT_TB_Bits.ACT_TB */
#define IFX_GTM_TOM_TGC1_ACT_TB_ACT_TB_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ACT_TB_Bits.TB_TRIG */
#define IFX_GTM_TOM_TGC1_ACT_TB_TB_TRIG_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ACT_TB_Bits.TB_TRIG */
#define IFX_GTM_TOM_TGC1_ACT_TB_TB_TRIG_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ACT_TB_Bits.TB_TRIG */
#define IFX_GTM_TOM_TGC1_ACT_TB_TB_TRIG_OFF (24u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ACT_TB_Bits.TBU_SEL */
#define IFX_GTM_TOM_TGC1_ACT_TB_TBU_SEL_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ACT_TB_Bits.TBU_SEL */
#define IFX_GTM_TOM_TGC1_ACT_TB_TBU_SEL_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ACT_TB_Bits.TBU_SEL */
#define IFX_GTM_TOM_TGC1_ACT_TB_TBU_SEL_OFF (25u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL0 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL0 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL0 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL1 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL1 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL1 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL1_OFF (2u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL2 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL2 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL2 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL2_OFF (4u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL3 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL3 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL3 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL3_OFF (6u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL4 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL4 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL4 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL4_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL5 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL5 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL5 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL5_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL6 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL6 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL6 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL6_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL7 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL7 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits.ENDIS_CTRL7 */
#define IFX_GTM_TOM_TGC1_ENDIS_CTRL_ENDIS_CTRL7_OFF (14u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT0 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT0 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT0 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT1 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT1 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT1 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT1_OFF (2u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT2 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT2 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT2 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT2_OFF (4u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT3 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT3 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT3 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT3_OFF (6u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT4 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT4 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT4 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT4_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT5 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT5 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT5 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT5_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT6 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT6 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT6 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT6_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT7 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT7 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits.ENDIS_STAT7 */
#define IFX_GTM_TOM_TGC1_ENDIS_STAT_ENDIS_STAT7_OFF (14u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL0 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL0 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL0 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL1 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL1 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL1 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL1_OFF (2u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL2 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL2 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL2 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL2_OFF (4u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL3 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL3 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL3 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL3_OFF (6u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL4 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL4 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL4 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL4_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL5 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL5 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL5 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL5_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL6 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL6 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL6 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL6_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL7 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL7 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.FUPD_CTRL7 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_FUPD_CTRL7_OFF (14u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH0 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH0 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH0 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH0_OFF (16u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH1 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH1 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH1 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH1_OFF (18u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH2 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH2 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH2 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH2_OFF (20u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH3 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH3 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH3 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH3_OFF (22u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH4 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH4 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH4 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH4_OFF (24u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH5 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH5 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH5 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH5_OFF (26u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH6 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH6 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH6 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH6_OFF (28u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH7 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH7 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits.RSTCN0_CH7 */
#define IFX_GTM_TOM_TGC1_FUPD_CTRL_RSTCN0_CH7_OFF (30u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.HOST_TRIG */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_HOST_TRIG_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.HOST_TRIG */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_HOST_TRIG_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.HOST_TRIG */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_HOST_TRIG_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH0 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH0_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH0 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH0_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH0 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH0_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH1 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH1_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH1 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH1_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH1 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH1_OFF (9u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH2 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH2_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH2 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH2_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH2 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH2_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH3 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH3_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH3 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH3_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH3 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH3_OFF (11u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH4 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH4_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH4 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH4_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH4 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH4_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH5 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH5_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH5 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH5_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH5 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH5_OFF (13u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH6 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH6_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH6 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH6_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH6 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH6_OFF (14u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH7 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH7_LEN (1u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH7 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH7_MSK (0x1u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.RST_CH7 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_RST_CH7_OFF (15u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL0 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL0 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL0 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL0_OFF (16u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL1 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL1 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL1 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL1_OFF (18u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL2 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL2 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL2 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL2_OFF (20u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL3 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL3 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL3 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL3_OFF (22u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL4 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL4 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL4 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL4_OFF (24u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL5 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL5 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL5 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL5_OFF (26u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL6 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL6 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL6 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL6_OFF (28u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL7 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL7 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits.UPEN_CTRL7 */
#define IFX_GTM_TOM_TGC1_GLB_CTRL_UPEN_CTRL7_OFF (30u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG0 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG0 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG0 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG1 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG1 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG1 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG1_OFF (2u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG2 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG2 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG2 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG2_OFF (4u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG3 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG3 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG3 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG3_OFF (6u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG4 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG4 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG4 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG4_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG5 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG5 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG5 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG5_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG6 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG6 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG6 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG6_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG7 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG7 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_INT_TRIG_Bits.INT_TRIG7 */
#define IFX_GTM_TOM_TGC1_INT_TRIG_INT_TRIG7_OFF (14u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL0 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL0 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL0 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL1 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL1 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL1 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL1_OFF (2u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL2 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL2 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL2 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL2_OFF (4u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL3 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL3 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL3 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL3_OFF (6u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL4 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL4 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL4 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL4_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL5 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL5 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL5 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL5_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL6 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL6 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL6 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL6_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL7 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL7 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits.OUTEN_CTRL7 */
#define IFX_GTM_TOM_TGC1_OUTEN_CTRL_OUTEN_CTRL7_OFF (14u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT0 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT0_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT0 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT0_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT0 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT0_OFF (0u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT1 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT1_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT1 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT1_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT1 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT1_OFF (2u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT2 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT2_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT2 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT2_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT2 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT2_OFF (4u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT3 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT3_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT3 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT3_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT3 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT3_OFF (6u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT4 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT4_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT4 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT4_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT4 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT4_OFF (8u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT5 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT5_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT5 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT5_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT5 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT5_OFF (10u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT6 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT6_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT6 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT6_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT6 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT6_OFF (12u)

/** \brief  Length for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT7 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT7_LEN (2u)

/** \brief  Mask for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT7 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT7_MSK (0x3u)

/** \brief  Offset for Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits.OUTEN_STAT7 */
#define IFX_GTM_TOM_TGC1_OUTEN_STAT_OUTEN_STAT7_OFF (14u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXGTM_BF_H */
