/***************************************************
 * Introduction: VSS涓氬姟鎺ュ彛
 * author: wu<PERSON>alin
 * date created: 2020-2-1
 * date modified: 2021-4-21
 * version: V1.4
 * recently modified by: wu<PERSON><PERSON><PERSON>
 *
 * Copyright (C) 2017, 2021, Thinktech, Inc.
 ***************************************************/
#ifndef __VSS_API_H__
#define __VSS_API_H__

#include <stdio.h>
#include <stdint.h>


/*---functions---*/
#ifdef __cplusplus
extern "C" {
#endif

#include "vsstype.h"


enum ALG_TYPE {
	ALG_GJ = 1,	
	ALG_GM,
};

enum ENV_TYPE {
	ENV_QA = 1,
	ENV_PP,
	ENV_P,
};

enum{
	CERT_TYPE_ROOT = 0,
	CERT_TYPE_USR,
};

#define ROOT_CERT_SIZE  144
#define USER_CERT_SIZE  176
#define ECU_CERT_SIZE   164

enum CERT_TYPE {
  CERTTYPE_ROOT    = 0x10,
  CERTTYPE_ECU     = 0x20,
  CERTTYPE_USR     = 0x30,
  CERTTYPE_USRREQ  = 0x31,
};

#define INNER_KEY_MODE 0
#define OUTER_KEY_MODE 1
#define CALC_ENC 0
#define CALC_DEC 1
#define PAD_NO_FORCE 0
#define PAD_FORCE 1
#define HASH_NO_CALC 0
#define HASH_CALC 1

/* Function Table Base Address */
#define VSSAPI_FUNC_TABLE_BASE          0x80048000//0xA007A800

/* Function Index In Func Table */
enum{
	VSSAPI_VssAESCMac = 0,
	VSSAPI_VssAESCMacIndex,
	VSSAPI_VssAESCalc,
	VSSAPI_VssAESCalcIndex,
	VSSAPI_VssAESMac,
	VSSAPI_VssAsymGenKey,
	VSSAPI_VssAsymGenKeyIndex,
	VSSAPI_VssAsymmCalc,
	VSSAPI_VssAsymmCalcIndex,
	VSSAPI_VssCMac,
	VSSAPI_VssCMacIndex,
	VSSAPI_VssCalcEcu,
	VSSAPI_VssCalcFinishHmac,
	VSSAPI_VssCertExport,
	VSSAPI_VssCertImport,
	VSSAPI_VssCertPkEnc,
	VSSAPI_VssChipRead,
	VSSAPI_VssChipWrite,
	VSSAPI_VssCryptoInit,
	VSSAPI_VssECCCalc,
	VSSAPI_VssECCCalcIndex,
	VSSAPI_VssECCGenKey,
	VSSAPI_VssECCGenKeyIndex,
	VSSAPI_VssECCSign,
	VSSAPI_VssECCSignIndex,
	VSSAPI_VssECCVerify,
	VSSAPI_VssExportAtKey,
	VSSAPI_VssExportAtPin,
	VSSAPI_VssExportKey,
	VSSAPI_VssExportSessKey,
	VSSAPI_VssGenCertReq,
	VSSAPI_VssGenRandom,
	VSSAPI_VssGenSessionKey,
	VSSAPI_VssGenerateKeyByCode,
	VSSAPI_VssGetAlgFlag,
	VSSAPI_VssGetCertInfo,
	VSSAPI_VssGetChipID,
	VSSAPI_VssGetWroteFlag,
	VSSAPI_VssHMAC,
	VSSAPI_VssHash,
	VSSAPI_VssHashFinal,
	VSSAPI_VssHashInit,
	VSSAPI_VssHashUpdate,
	VSSAPI_VssImportSessKey,
	VSSAPI_VssKeyCodeFeedback,
	VSSAPI_VssMac,
	VSSAPI_VssResetSessionKey,
	VSSAPI_VssSM2Calc,
	VSSAPI_VssSM2CalcIndex,
	VSSAPI_VssSM2GenKey,
	VSSAPI_VssSM2GenKeyIndex,
	VSSAPI_VssSM2Sign,
	VSSAPI_VssSM2SignIndex,
	VSSAPI_VssSM2Verify,
	VSSAPI_VssSM4CMac,
	VSSAPI_VssSM4CMacIndex,
	VSSAPI_VssSM4Calc,
	VSSAPI_VssSM4CalcIndex,
	VSSAPI_VssSM4Mac,
	VSSAPI_VssSetKeyActive,
	VSSAPI_VssSetWroteFlag,
	VSSAPI_VssSign,
	VSSAPI_VssSignData,
	VSSAPI_VssSignIndex,
	VSSAPI_VssSignVerify,
	VSSAPI_VssStSymmCalc,
	VSSAPI_VssSymmCalc,
	VSSAPI_VssSymmCalcIndex,
	VSSAPI_VssUpdateEnvironment,
	VSSAPI_VssUpdateMasterKey,
	VSSAPI_VssVerifyCert,
	VSSAPI_VssVerifyCertValid,
	VSSAPI_VssVerifyEcuSign,
	VSSAPI_VssVerifyToolCert,
	VSSAPI_VssZucCalc,
	VSSAPI_VssZucCalcData,
	VSSAPI_VssZucSetKey,
	VSSAPI_VssGetChipVersion,
	VSSAPI_VssGetChipState,
	VSSAPI_VssGetVersion,
	VSSAPI_VssVerifySignCertValid,
	VSSAPI_VssGetEnvironment,
	VSSAPI_VssGetKeyActive,

	VSSAPI_FUN_COUNT
};

/******************************************************************************
鎽樿: 绠楁硶搴撳垵濮嬪寲锛岄�氳繃璇诲彇FLASH鎸囧畾鐨勭畻娉曟潵鍒濆鍖栫郴缁熺畻娉曟爣璇嗭紝榛樿绠楁硶涓哄浗瀵嗙畻娉�
   闇�瑕侀�氳繃璇ユ柟娉曞皢FLASH璇诲啓鐨勬帴鍙ｆ寚閽堟敞鍐屽埌VSS涓紝VSS鍙互閫氳繃璇ユ帴鍙ｈ鍐橣LASH涓寚瀹氱殑瀵嗛挜瀛樺偍鍖哄煙銆�
   鐢变簬闈炲绉扮畻娉曠殑杩愮畻鏃堕棿杈冮暱锛堜緥濡傚畬鏁磋蒋绠楁硶楠岀鍙兘闇�瑕�500ms锛夛紝杩欏氨闇�瑕佺畻娉曞唴閮ㄨ繍绠楁椂鍒嗘澶嶄綅WDT鐨勬椂閽燂紝鍚﹀垯浼氬鑷碝CU澶嶄綅銆�

鍙傛暟: type[in]    --鎸囧畾绠楁硶搴撶殑杩愮畻绫诲瀷锛�0-杞畻娉曪紱1-M300鑺墖
	group[in]  --棰勭暀鍙傛暟锛屾き鍦嗘洸鐜嘔D锛�0-NIST prime256v1; 1-brainpoolP256r1
           鐩墠鍙敮鎸�0-NIST prime256v1鏇茬巼
     flashCb[in]  --璇诲啓FLASH瀵嗛挜瀛樺偍鍖哄煙鐨勫洖璋冨嚱鏁版寚閽堬紝濡備笉闇�浣跨敤锛屼紶鍏ョ┖鎸囬拡鍗冲彲
     wdtCb[in]   --澶嶄綅鐪嬮棬鐙楄鏃跺櫒鐨勫洖璋冨嚱鏁版寚閽堬紝濡備笉闇�浣跨敤锛屼紶鍏ョ┖鎸囬拡鍗冲彲

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --杩愮畻绫诲瀷涓嶆槸鎸囧畾鐨勬暟鍊�
        0x1C    --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssCryptoInit(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb)
{
    vss_uint32 (*api_func_raw)(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 type, vss_uint32 group, flash_io_cb* flashCb, wdt_rst_cb* wdtCb))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCryptoInit));
    return api_func_raw(type, group, flashCb, wdtCb);
}

/******************************************************************************
鎽樿: 闅忔満鐢熸垚SM2闈炲绉板瘑閽ュ骞跺鍑烘帴鍙�

鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
      szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�
      szSK[out]     --绉侀挜SK锛堝畾闀�32瀛楄妭锛�

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --浼犲叆绌烘寚閽�
        0x08    --璁＄畻澶辫触
        0x18    --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssSM2GenKey(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK)
{
    vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2GenKey));
    return api_func_raw(szX, szY, szSK);
}

/******************************************************************************
鎽樿: 闅忔満鐢熸垚SM2闈炲绉板瘑閽ュ骞跺瓨鍌ㄦ帴鍙�

鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
      szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --szX鎴杝zY浼犲叆绌烘寚閽�
        0x08    --璁＄畻澶辫触
        0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1D    --鍐欑紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssSM2GenKeyIndex(vss_uint8 *szX, vss_uint8 *szY)
{
    vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2GenKeyIndex));
    return api_func_raw(szX, szY);
}

/******************************************************************************
鎽樿: 浠ヨ緭鍏ョ殑杞﹁締瀹夊叏鐮佷綔涓虹瀛愶紝鏍规嵁閰嶇疆鏂囦欢浜х敓绯诲垪鐨勫绉板瘑閽�

鍙傛暟: len[in]     --杞﹁締瀹夊叏鐮侀暱搴︼紝鍥哄畾32
     code[in]    --杞﹁締瀹夊叏鐮侊紝鍥哄畾32瀛楄妭锛岀敱绗�32瀛楄妭鎸囧畾绠楁硶锛屽綋璇ユ暟涓哄伓鏁版椂鎸囧畾涓哄浗瀵嗙畻娉曪紝涓哄鏁版椂鎸囧畾涓哄浗闄呯畻娉曪紙鍗砤lg=code[31]&1; 0-鍥藉瘑锛�1-鍥介檯锛�
     szKeyIdList[out] --鐢熸垚鎴愬姛鐨勫瘑閽D娓呭崟锛屼緥濡傗��1,2,5鈥�
     AutoSetWroteFlag[in] -璁�1鍦ㄥ姛鑳藉畬鎴愬悗鑷姩缃畐rote Flag涓�1锛涜0涓嶅仛浠讳綍鎿嶄綔

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --浼犲叆绌烘寚閽�
        0x12    --瀹夊叏鐮侀暱搴︿笉涓�32
        0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1D    --鍐欑紦瀛樻暟鎹け璐�
        0x21    --鐮佸崟宸茬粡琚敓鎴愯繃
******************************************************************************/
static inline vss_uint32 VssGenerateKeyByCode(vss_uint32 len, vss_uint8* code, vss_char8* szKeyIdList, vss_uint8 AutoSetWroteFlag)
{
    vss_uint32 (*api_func_raw)(vss_uint32 len, vss_uint8* code, vss_char8* szKeyIdList, vss_uint8 AutoSetWroteFlag) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 len, vss_uint8* code, vss_char8* szKeyIdList, vss_uint8 AutoSetWroteFlag))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGenerateKeyByCode));
    return api_func_raw(len, code, szKeyIdList, AutoSetWroteFlag);
}

/******************************************************************************
鎽樿: 闅忔満鐢熸垚ECC闈炲绉板瘑閽ュ骞跺鍑烘帴鍙�

鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
      szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�
      szSK[out]     --绉侀挜SK锛堝畾闀�32瀛楄妭锛�

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --浼犲叆绌烘寚閽�
        0x08    --璁＄畻澶辫触
        0x18    --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssECCGenKey(vss_uint8 *szX, vss_uint8 *szY, vss_uint8 *szSK)
{
    vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY, vss_uint8 *szSK))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCGenKey));
    return api_func_raw(szX, szY, szSK);
}


/******************************************************************************
鎽樿: 闅忔満鐢熸垚ECC闈炲绉板瘑閽ュ骞跺瓨鍌ㄦ帴鍙�

鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
      szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --szX鎴杝zY浼犲叆绌烘寚閽�
        0x08    --璁＄畻澶辫触
        0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1D    --鍐欑紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssECCGenKeyIndex(vss_uint8 *szX, vss_uint8 *szY)
{
    vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8*szY) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8*szY))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCGenKeyIndex));
    return api_func_raw(szX, szY);
}

/********************************************************************************
鎽樿: 璇佷功瀵煎叆

鍙傛暟:nCertType[in]    --璇佷功绫诲瀷: 0:鏍硅瘉涔�;1:韬唤璇佷功
	nLen[in]         --璇佷功闀垮害
	szCert[in]        --璇佷功鏁版嵁

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨璇佷功绫诲瀷涓嶄负0-1
        0x12      --璇佷功闀垮害闈炴硶锛屾牴璇佷功144锛岃韩浠借瘉涔�176
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1D      --鍐欑紦瀛樻暟鎹け璐�
********************************************************************************/
static inline vss_uint32 VssCertImport(vss_uint32 nCertType, vss_uint32 nLen, vss_uint8* szCert)
{
    vss_uint32 (*api_func_raw)(vss_uint32 nCertType, vss_uint32 nLen, vss_uint8* szCert) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 nCertType, vss_uint32 nLen, vss_uint8* szCert))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCertImport));
    return api_func_raw(nCertType, nLen, szCert);
}

/********************************************************************************
鎽樿: 璇佷功璇诲彇
鍙傛暟: nCertType[in]    --璇佷功绫诲瀷: 0:鏍硅瘉涔�;1:韬唤璇佷功
nCertLen[out]    --瀵煎嚭璇佷功闀垮害
szCert[out]      --瀵煎嚭璇佷功鏁版嵁
	info[out]         --杈撳嚭16瀛楄妭鎸囧畾淇℃伅鏍煎紡
                     璇佷功绫诲瀷  4B
                     鏈夋晥鏃ユ湡  4B 鏍煎紡涓篩YMMDD
                     璇佷功搴忓彿  4B
                     璇佷功灞炴��  4B [绠楁硶锛屾牴绱㈠紩锛岄鐣欙紝棰勭暀]
                               绠楁硶锛�0-鍥藉瘑锛�1-鍥介檯
                               鏍圭储寮曪細0x10-娴嬭瘯鍥藉瘑鏍�
                                       0x11-娴嬭瘯鍥介檯鏍�
                                       0x20-棰勭敓浜у浗瀵嗘牴
                                       0x21-棰勭敓浜у浗闄呮牴
                                       0x30-鐢熶骇鍥藉瘑鏍�
                                       0x31-鐢熶骇鍥介檯鏍�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨璇佷功绫诲瀷涓嶄负0-1
        0x0E      --璇佷功涓嶅瓨鍦�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
********************************************************************************/
static inline vss_uint32 VssCertExport(vss_uint32 nCertType, vss_uint32* nCertLen, vss_uint8* szCert, vss_uint8* info)
{
    vss_uint32 (*api_func_raw)(vss_uint32 nCertType, vss_uint32* nCertLen, vss_uint8* szCert, vss_uint8* info) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 nCertType, vss_uint32* nCertLen, vss_uint8* szCert, vss_uint8* info))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCertExport));
    return api_func_raw(nCertType, nCertLen, szCert, info);
}

/******************************************************************************
鎽樿: 瀵煎叆鏄庢枃浼氳瘽瀵嗛挜

鍙傛暟:nKeylen[in]  --瀵嗛挜鐨勯暱搴︼紝瀹氶暱16
    szKey[in]    --瀵嗛挜鍊�

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --瀵嗛挜涓虹┖鎸囬拡
        0x19    --瀵嗛挜闀垮害涓嶄负16
        0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1D    --鍐欑紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssImportSessKey(vss_uint32 nKeyLen, vss_uint8* szKey)
{
    vss_uint32 (*api_func_raw)(vss_uint32 nKeyLen, vss_uint8* szKey) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 nKeyLen, vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssImportSessKey));
    return api_func_raw(nKeyLen, szKey);
}

/******************************************************************************
鎽樿: 璇诲彇浼氳瘽瀵嗛挜鏄庢枃

鍙傛暟: index[in]    --瀵嗛挜瀛樺偍鐨勭储寮曞彿锛�0-褰撳墠閫氫俊瀵嗛挜锛�1-澶囦唤閫氫俊瀵嗛挜
    nKeylen[out]  --瀵嗛挜鐨勯暱搴�
    szKey[out]    --瀵嗛挜鍊�

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --瀵嗛挜闀垮害鎴栧瘑閽ュ�间负绌烘寚閽�
        0x05    --鎸囧畾瀵嗛挜涓嶅瓨鍦�
        0x0F    --瀵嗛挜绱㈠紩鍙烽潪娉曪紝涓嶄负0-1
        0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C    --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssExportSessKey(vss_uint32 index, vss_uint32* nKeyLen,vss_uint8* szKey)
{
    vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint32* nKeyLen,vss_uint8* szKey) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint32* nKeyLen,vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportSessKey));
    return api_func_raw(index, nKeyLen, szKey);
}

/******************************************************************************
鎽樿: 淇敼瀵嗛挜鐨勬縺娲诲睘鎬�

鍙傛暟: keyId[in]   --瀵嗛挜ID锛堝彧閽堝5-8鍙烽�氫俊瀵嗛挜鏈夋晥锛�
     valid[in]    --鍙敤鏍囪瘑: 0-涓嶅彲鐢紱1-鍙敤

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --鍙敤鏍囪瘑涓嶆槸鎸囧畾鍊�
        0x05    --瀵嗛挜涓嶅瓨鍦�
        0x0F    --瀵嗛挜ID闈炴硶锛屼笉涓�5-8
        0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1D    --鍐欑紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssSetKeyActive(vss_uint32 keyId, vss_uint32 valid)
{
    vss_uint32 (*api_func_raw)(vss_uint32 keyId, vss_uint32 valid) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 keyId, vss_uint32 valid))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSetKeyActive));
    return api_func_raw(keyId, valid);
}

/******************************************************************************
鎽樿: 璇诲彇闃茬洍瀵嗛挜鏄庢枃锛岄槻鐩楀瘑閽ヨ嚦澶氫粎涓�鏉℃湁鏁堬紝濡傛灉瀛樺湪闃茬洍瀵嗛挜杩斿洖瀵瑰簲瀵嗛挜锛屽鏋滀笉瀛樺湪杩斿洖05--瀵嗛挜涓嶅彲鐢�

鍙傛暟:index[in]	  --瀵嗛挜绱㈠紩锛�0-3
	nKeyLen[out]  --瀵嗛挜鐨勯暱搴�
    szKey[out]    --瀵嗛挜鍊�

杩斿洖鍊硷細0        --鎴愬姛
        0x04     --瀵嗛挜鎴栧瘑閽ラ暱搴︿紶鍏ョ┖鎸囬拡
        0x05     --鎸囧畾瀵嗛挜涓嶅瓨鍦�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssExportAtKey(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey)
{
    vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportAtKey));
    return api_func_raw(index, nKeyLen, szKey);
}

/******************************************************************************
鎽樿: 璇诲彇闃茬洍PIN鏄庢枃

鍙傛暟: nPinLen[out]  --PIN鐨勯暱搴�
    szPin[out]    --PIN鍊�

杩斿洖鍊硷細0        --鎴愬姛
        0x04     --浼犲叆绌烘寚閽�
        0x05     --鎸囧畾瀵嗛挜涓嶅瓨鍦�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssExportAtPin(vss_uint32* nPinLen, vss_uint8* szPin)
{
    vss_uint32 (*api_func_raw)(vss_uint32* nPinLen, vss_uint8* szPin) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32* nPinLen, vss_uint8* szPin))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportAtPin));
    return api_func_raw(nPinLen, szPin);
}

/******************************************************************************
鎽樿: 鍙嶉杞﹁締瀹夊叏鐮佽緭鍏ユ儏鍐�

鍙傛暟: szFeedback[out]    --杞﹁締瀹夊叏鐮佸弽棣堝�硷紝瀹氶暱32瀛楄妭
                        濡傛灉32瀛楄妭杞﹁締瀹夊叏鐮佹病缁欏畨鍏ㄧ畻娉曞寘杈撳叆杩囷紝璇ユ帴鍙ｅ悙鍑哄�间负鍏‵F
濡傛灉32瀛楄妭杞﹁締瀹夊叏鐮佸凡缁忚緭鍏ュ畨鍏ㄧ畻娉曞寘锛屽唴瀹瑰涓嬶細
鍥藉瘑绠楁硶锛氳鎺ュ彛鍚愬嚭鍊间负鍏�00
鍥介檯绠楁硶锛氳鎺ュ彛鍚愬嚭鍊间负鍓�31瀛楄妭鍏�00锛屾渶鍚庝竴瀛楄妭0x01

杩斿洖鍊硷細0        --鎴愬姛
        0x04     --浼犲叆绌烘寚閽�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssKeyCodeFeedback(vss_uint8* szFeedback)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szFeedback) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szFeedback))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssKeyCodeFeedback));
    return api_func_raw(szFeedback);
}

/******************************************************************************
鎽樿: 鏍规嵁杈撳叆鐨勭爜鍗曠殑鏈�鍚庝竴浣嶆潵鍒ゆ柇鍥藉瘑/鍥介檯绠楁硶鏍囪瘑锛屾湰鎺ュ彛杩斿洖绠楁硶鏍囪瘑
      濡傛灉杞﹁締瀹夊叏鐮佹湭杈撳叆锛屽垯杩斿洖鈥�2-鍥藉瘑绠楁硶鈥�

鍙傛暟: nAlgFlag[out]    --绠楁硶鏍囪瘑: 1-鍥介檯绠楁硶锛�2-鍥藉瘑绠楁硶
                     
杩斿洖鍊硷細0        --鎴愬姛
        0x04     --浼犲叆绌烘寚閽�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssGetAlgFlag(vss_uint32* nAlgFlag)
{
    vss_uint32 (*api_func_raw)(vss_uint32* nAlgFlag) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32* nAlgFlag))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetAlgFlag));
    return api_func_raw(nAlgFlag);
}

/******************************************************************************
鎽樿: ECC闈炲绉板姞瑙ｅ瘑鎺ュ彛

鍙傛暟: szInData[in]      --杈撳叆鏁版嵁
      nInLen[in]       --杈撳叆鏁版嵁闀垮害锛屽姞瀵嗘渶澶�64瀛楄妭锛� 瑙ｅ瘑鍥哄畾192瀛楄妭
      calcFlag[in]      --杩愮畻鏍囪瘑: 0-鍔犲瘑锛�1-瑙ｅ瘑
      szKey[in]        --杩愮畻瀵嗛挜锛氬鏋滄槸鍔犲瘑杩愮畻锛岃鍊间负鍔犲瘑X||鍔犲瘑Y;
	      	  	  	  	  	 濡傛灉鏄В瀵嗚繍绠楋紝璇ュ�间负瑙ｅ瘑SK
      nKeyLen[in]       --杩愮畻瀵嗛挜闀垮害锛屽姞瀵嗕负64锛岃В瀵嗕负32
      szOutData[out]    --杈撳嚭杩愮畻缁撴灉
      pOutLen[out]      --杈撳嚭缁撴灉闀垮害

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x08      --璁＄畻閿欒
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x19      --瀵嗛挜闀垮害闈炴硶
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙�
******************************************************************************/
static inline vss_uint32 VssECCCalc(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCCalc));
    return api_func_raw(szInData, nInLen, calcFlag, szKey, nKeyLen, szOutData, pOutLen);
}

/******************************************************************************
鎽樿: ECC闈炲绉板姞瑙ｅ瘑鎺ュ彛

鍙傛暟: szInData[in]      --杈撳叆鏁版嵁
      nInLen[in]       --杈撳叆鏁版嵁闀垮害锛屽姞瀵嗘渶澶�64瀛楄妭锛� 瑙ｅ瘑鍥哄畾192瀛楄妭
      calcFlag[in]      --杩愮畻鏍囪瘑: 0-鍔犲瘑锛�1-瑙ｅ瘑
      szOutData[out]    --杈撳嚭杩愮畻缁撴灉
      pOutLen[out]      --杈撳嚭缁撴灉闀垮害

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x05      --瀵嗛挜涓嶅瓨鍦�
        0x08      --璁＄畻閿欒
        0x0B      --杩愮畻鏍囪瘑涓嶄负鎸囧畾鍊�
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssECCCalcIndex(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCCalcIndex));
    return api_func_raw(szInData, nInLen, calcFlag, szOutData, pOutLen);
}

/******************************************************************************
鎽樿: ECC鏁版嵁绛惧悕

鍙傛暟: key[in]              --绛惧悕绉侀挜锛屽畾闀�32瀛楄妭
     data[in]             --杩愮畻鏁版嵁
     dataLen[in]          --杩愮畻鏁版嵁闀垮害锛屼笉鍝堝笇鐩存帴绛惧悕鏃舵寚瀹�32锛屽搱甯岀鍚嶆椂鏁版嵁闀垮害澶т簬0
hashFlag[in]          --鍝堝笇鏍囪瘑: 0-涓嶅搱甯岀洿鎺ョ鍚�; 1-鍏圫HA256鍐嶇鍚�
     sig[out]             --绛惧悕缁撴灉锛屽畾闀�64瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍝堝笇鏍囪瘑闈炴硶
        0x08      --璁＄畻閿欒
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssECCSign(vss_uint8* key, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* key, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* key, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCSign));
    return api_func_raw(key, data, dataLen, hashFlag, sig);
}

/******************************************************************************
鎽樿: ECC鏁版嵁绛惧悕

鍙傛暟: data[in]             --杩愮畻鏁版嵁
     dataLen[in]          --杩愮畻鏁版嵁闀垮害锛屼笉鍝堝笇鐩存帴绛惧悕鏃舵寚瀹�32锛屽搱甯岀鍚嶆椂鏁版嵁闀垮害澶т簬0
     hashFlag[in]         --鍝堝笇鏍囪瘑: 0-涓嶅搱甯岀洿鎺ョ鍚�; 1-鍏圫HA256鍐嶇鍚�
     sig[out]             --绛惧悕缁撴灉锛屽畾闀�64瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍝堝笇鏍囪瘑闈炴硶
        0x05      --瀵嗛挜涓嶅瓨鍦�
        0x08      --璁＄畻閿欒
        0x18      --绠楁硶涓嶆敮鎸�
        0x12      --鏁版嵁闀垮害闈炴硶
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssECCSignIndex(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCSignIndex));
    return api_func_raw(data, dataLen, hashFlag, sig);
}

/******************************************************************************
鎽樿: ECC楠岃瘉绛惧悕

鍙傛暟: pk[in]              --楠岃瘉绛惧悕鍏挜锛屽畾闀�64瀛楄妭
     data[in]             --杩愮畻鏁版嵁
     dataLen[in]          --杩愮畻鏁版嵁闀垮害锛屼笉鍝堝笇鐩存帴绛惧悕鏃舵寚瀹�32锛屽搱甯岀鍚嶆椂鏁版嵁闀垮害澶т簬0
     hashFlag[in]         --鍝堝笇鏍囪瘑: 0-涓嶅搱甯岀洿鎺ョ鍚�; 1-鍏圫HA256鍐嶇鍚�
     sig[in]              --绛惧悕缁撴灉锛屽畾闀�64瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍝堝笇鏍囪瘑闈炴硶
        0x08      --楠岀澶辫触
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssECCVerify(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssECCVerify));
    return api_func_raw(pk, data, dataLen, hashFlag, sig);
}

/******************************************************************************
鎽樿: AES瀵圭О杩愮畻

鍙傛暟: in[in]         --寰呰繍绠楁暟鎹�
     inLen[in]      --寰呰繍绠楁暟鎹暱搴�
     key[in]        --瀵嗛挜
      keyLen[in]    --瀵嗛挜闀垮害锛屽畾闀�16
      calcFlag[in]   --鍔犺В瀵嗘爣璇嗭細0-鍔犲瘑锛�1-瑙ｅ瘑
      padding[in]   --琛ヤ綅鏍囪瘑锛�0-涓嶅己鍒惰ˉ浣嶏紱1-寮哄埗琛ヤ綅
      out[out]      --鍔犲瘑鍚庢暟鎹�
      pOutLen[out]  --鍔犲瘑鍚庢暟鎹暱搴�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍔犺В瀵嗘爣璇嗐�佽ˉ浣嶆爣璇嗛潪娉�
        0x19      --瀵嗛挜闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssAESCalc(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAESCalc));
    return api_func_raw(in, inLen, key, keyLen, calcFlag, padding, out, pOutLen);
}

/******************************************************************************
鎽樿: AES瀵圭О杩愮畻

鍙傛暟: index[in]     --瀵嗛挜绱㈠紩锛屾寚瀹�0-13
in[in]         --寰呰繍绠楁暟鎹�
      inLen[in]      --寰呰繍绠楁暟鎹暱搴�
      calcFlag[in]   --鍔犺В瀵嗘爣璇嗭細0-鍔犲瘑锛�1-瑙ｅ瘑
      padding[in]   --琛ヤ綅鏍囪瘑锛�0-涓嶅己鍒惰ˉ浣嶏紱1-寮哄埗琛ヤ綅
      out[out]      --鍔犲瘑鍚庢暟鎹�
      pOutLen[out]  --鍔犲瘑鍚庢暟鎹暱搴�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍔犺В瀵嗘爣璇嗐�佽ˉ浣嶆爣璇嗛潪娉�
        0x05      --瀵嗛挜涓嶅瓨鍦�
        0x0F      --瀵嗛挜绱㈠紩闈炴硶锛屼笉涓�0-13
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssAESCalcIndex(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAESCalcIndex));
    return api_func_raw(index, in, inLen, calcFlag, padding, out, pOutLen);
}


/******************************************************************************
鎽樿: AES璁＄畻MAC

鍙傛暟: in[in]         --寰呰繍绠楁暟鎹�
      inLen[in]      --寰呰繍绠楁暟鎹暱搴�
      key[in]      --瀵嗛挜
      keyLen[in]   --瀵嗛挜闀垮害锛屽畾闀�16
      out[out]      --mac缁撴灉锛屽畾闀�16瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x18      --绠楁硶涓嶆敮鎸�
        0x19      --瀵嗛挜闀垮害涓嶄负16
******************************************************************************/
static inline vss_uint32 VssAESMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAESMac));
    return api_func_raw(in, inLen, key, keyLen, out);
}

/******************************************************************************
鎽樿: AES璁＄畻CMAC

鍙傛暟: in[in]         --寰呰繍绠楁暟鎹�
      inLen[in]      --寰呰繍绠楁暟鎹暱搴�
      key[in]      --瀵嗛挜
      keyLen[in]   --瀵嗛挜闀垮害锛屽畾闀�16
      out[out]      --mac缁撴灉锛屽畾闀�16瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x18      --绠楁硶涓嶆敮鎸�
        0x19      --瀵嗛挜闀垮害涓嶄负16
******************************************************************************/
static inline vss_uint32 VssAESCMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAESCMac));
    return api_func_raw(in, inLen, key, keyLen, out);
}

/******************************************************************************
鎽樿: AES璁＄畻CMAC锛屼娇鐢ㄥ唴閮ㄥ瘑閽�

鍙傛暟: in[in]         --寰呰繍绠楁暟鎹�
     inLen[in]      --寰呰繍绠楁暟鎹暱搴�
     index[in]      --瀵嗛挜绱㈠紩锛屾寚瀹�0-13
      out[out]      --mac缁撴灉锛屽畾闀�16瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x05      --鎸囧畾瀵嗛挜涓嶅瓨鍦�
        0x0F      --瀵嗛挜绱㈠紩闈炴硶锛屼笉涓�0-13
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssAESCMacIndex(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAESCMacIndex));
    return api_func_raw(in, inLen, index, out);
}

/******************************************************************************
鎽樿: ZUC瀵嗛挜鍒濆鍖栵紝閫傜敤浜庤繍绠楁暟鎹畾闀�8瀛楄妭

鍙傛暟: key[in]	     --瀵嗛挜锛屽畾闀�16瀛楄妭
     zuc_key[out]	--ZUC杩愮畻鏃跺瘑閽�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssZucSetKey(vss_uint8* key, TZucKey* zuc_key)
{
    vss_uint32 (*api_func_raw)(vss_uint8* key, TZucKey* zuc_key) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* key, TZucKey* zuc_key))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssZucSetKey));
    return api_func_raw(key, zuc_key);
}

/******************************************************************************
鎽樿: ZUC杩愮畻锛岃鎺ュ彛閫傜敤浜庡畾闀�8瀛楄妭鏁版嵁

鍙傛暟: zuc_key[in]	--杞爜鍚庣殑瀵嗛挜
in[in]	     --寰呰繍绠楁暟鎹紝瀹氶暱8瀛楄妭
      out[out]	     --缁撴灉鏁版嵁锛屽畾闀�8瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssZucCalc(TZucKey* zuc_key, vss_uint8* in, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(TZucKey* zuc_key, vss_uint8* in, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(TZucKey* zuc_key, vss_uint8* in, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssZucCalc));
    return api_func_raw(zuc_key, in, out);
}

/******************************************************************************
鎽樿: SM4瀵圭О杩愮畻

鍙傛暟: in[in]         --寰呰繍绠楁暟鎹�
      inLen[in]      --寰呰繍绠楁暟鎹暱搴�
      key[in]       --瀵嗛挜
      keyLen[in]    --瀵嗛挜闀垮害锛屽畾闀�16
      calcFlag[in]   --鍔犺В瀵嗘爣璇嗭細0-鍔犲瘑锛�1-瑙ｅ瘑
      padding[in]   --琛ヤ綅鏍囪瘑锛�0-涓嶅己鍒惰ˉ浣嶏紱1-寮哄埗琛ヤ綅
      out[out]      --鍔犲瘑鍚庢暟鎹�
      pOutLen[out]  --鍔犲瘑鍚庢暟鎹暱搴�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍔犺В瀵嗘爣璇嗐�佽ˉ浣嶆爣璇嗛潪娉�
        0x19      --瀵嗛挜闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssSM4Calc(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM4Calc));
    return api_func_raw(in, inLen, key, keyLen, calcFlag, padding, out, pOutLen);
}

/******************************************************************************
鎽樿: SM4瀵圭О杩愮畻

鍙傛暟: index[in]     --瀵嗛挜绱㈠紩锛屾寚瀹�0-13
in[in]         --寰呰繍绠楁暟鎹�
      inLen[in]      --寰呰繍绠楁暟鎹暱搴�
      calcFlag[in]   --鍔犺В瀵嗘爣璇嗭細0-鍔犲瘑锛�1-瑙ｅ瘑
      padding[in]   --琛ヤ綅鏍囪瘑锛�0-涓嶅己鍒惰ˉ浣嶏紱1-寮哄埗琛ヤ綅
      out[out]      --鍔犲瘑鍚庢暟鎹�
      pOutLen[out]  --鍔犲瘑鍚庢暟鎹暱搴�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍔犺В瀵嗘爣璇嗐�佽ˉ浣嶆爣璇嗛潪娉�
        0x05      --瀵嗛挜涓嶅瓨鍦�
        0x0F      --瀵嗛挜绱㈠紩闈炴硶锛屼笉涓�0-13
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssSM4CalcIndex(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM4CalcIndex));
    return api_func_raw(index, in, inLen, calcFlag, padding, out, pOutLen);
}

/******************************************************************************
鎽樿: ZUC瀵圭О杩愮畻

鍙傛暟: in[in]         --寰呰繍绠楁暟鎹�
      inLen[in]      --寰呰繍绠楁暟鎹暱搴︼紝澶т簬0涓斾负8鐨勬暣鏁板��
      key[in]      --瀵嗛挜锛屽畾闀�16瀛楄妭
      out[out]      --杩愮畻鍚庢暟鎹紝闀垮害=杈撳叆鏁版嵁闀垮害

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssZucCalcData(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssZucCalcData));
    return api_func_raw(in, inLen, key, out);
}

/******************************************************************************
鎽樿: SM4璁＄畻MAC

鍙傛暟: in[in]         --寰呰繍绠楁暟鎹�
      inLen[in]      --寰呰繍绠楁暟鎹暱搴�
      key[in]      --瀵嗛挜
      keyLen[in]   --瀵嗛挜闀垮害锛屽畾闀�16
      out[out]      --mac缁撴灉锛屽畾闀�16瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x18      --绠楁硶涓嶆敮鎸�
        0x19      --瀵嗛挜闀垮害涓嶄负16
******************************************************************************/
static inline vss_uint32 VssSM4Mac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint32 keyLen, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint32 keyLen, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM4Mac));
    return api_func_raw(in, inLen, key, keyLen, out);
}

/******************************************************************************
鎽樿: SM4璁＄畻CMAC

鍙傛暟: in[in]         --寰呰繍绠楁暟鎹�
      inLen[in]      --寰呰繍绠楁暟鎹暱搴�
      key[in]      --瀵嗛挜
      keyLen[in]   --瀵嗛挜闀垮害锛屽畾闀�16
      out[out]      --mac缁撴灉锛屽畾闀�16瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x18      --绠楁硶涓嶆敮鎸�
        0x19      --瀵嗛挜闀垮害涓嶄负16
******************************************************************************/
static inline vss_uint32 VssSM4CMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint32 keyLen, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8* key, vss_uint32 keyLen, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM4CMac));
    return api_func_raw(in, inLen, key, keyLen, out);
}

/******************************************************************************
鎽樿: SM4璁＄畻CMAC锛屼娇鐢ㄥ唴閮ㄥ瘑閽�

鍙傛暟: in[in]         --寰呰繍绠楁暟鎹�
     inLen[in]      --寰呰繍绠楁暟鎹暱搴�
     index[in]      --瀵嗛挜绱㈠紩锛屾寚瀹�0-13
      out[out]      --mac缁撴灉锛屽畾闀�16瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x05      --鎸囧畾瀵嗛挜涓嶅瓨鍦�
        0x0F      --瀵嗛挜绱㈠紩闈炴硶锛屼笉涓�0-13
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssSM4CMacIndex(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM4CMacIndex));
    return api_func_raw(in, inLen, index, out);
}

/******************************************************************************
鎽樿: 浜х敓闅忔満鏁�

鍙傛暟: len[in]        --闅忔満鏁伴暱搴�
      out[out]      --闅忔満鏁扮紦瀛�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x12      --鎸囧畾闀垮害绛変簬0
******************************************************************************/
static inline vss_uint32 VssGenRandom(vss_uint32 len, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint32 len, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 len, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGenRandom));
    return api_func_raw(len, out);
}

/******************************************************************************
鎽樿: HASH璁＄畻锛岄�氳繃绠楁硶搴撳垵濮嬪寲鎺ュ彛鑷�傚簲绠楁硶

鍙傛暟: data[in]             --杩愮畻鏁版嵁
      dataLen[in]          --杩愮畻鏁版嵁闀垮害
      out[out]             --HASH缁撴灉
      pOutLen[out]       --缁撴灉闀垮害锛屽畾闀�32瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x12      --鎸囧畾闀垮害绛変簬0
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssHash(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssHash));
    return api_func_raw(data, dataLen, out, pOutLen);
}

/******************************************************************************
鎽樿: HASH璁＄畻鍒濆鍖栵紝閫氳繃绠楁硶搴撳垵濮嬪寲鎺ュ彛鑷�傚簲绠楁硶

鍙傛暟: ctx[in]    --涓婁笅鏂囩幆澧�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssHashInit(THashCtx* ctx)
{
    vss_uint32 (*api_func_raw)(THashCtx* ctx) = 0;

    api_func_raw = (vss_uint32 (*)(THashCtx* ctx))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssHashInit));
    return api_func_raw(ctx);
}

/******************************************************************************
鎽樿: HASH璁＄畻鏁版嵁鏇存柊锛岄�氳繃绠楁硶搴撳垵濮嬪寲鎺ュ彛鑷�傚簲绠楁硶

鍙傛暟:  ctx[in]              --涓婁笅鏂囩幆澧�
      data[in]             --杩愮畻鏁版嵁
      dataLen[in]          --杩愮畻鏁版嵁闀垮害

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssHashUpdate(THashCtx* ctx, vss_uint8* data, vss_uint32 dataLen)
{
    vss_uint32 (*api_func_raw)(THashCtx* ctx, vss_uint8* data, vss_uint32 dataLen) = 0;

    api_func_raw = (vss_uint32 (*)(THashCtx* ctx, vss_uint8* data, vss_uint32 dataLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssHashUpdate));
    return api_func_raw(ctx, data, dataLen);
}

/******************************************************************************
鎽樿: HASH璁＄畻缁撴潫锛岄�氳繃绠楁硶搴撳垵濮嬪寲鎺ュ彛鑷�傚簲绠楁硶

鍙傛暟: ctx[in]               --涓婁笅鏂囩幆澧�
      out[out]             --HASH缁撴灉
      pOutLen[out]        --缁撴灉闀垮害

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssHashFinal(THashCtx* ctx, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(THashCtx* ctx, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(THashCtx* ctx, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssHashFinal));
    return api_func_raw(ctx, out, pOutLen);
}

/******************************************************************************
鎽樿: HMAC璁＄畻锛岄�氳繃绠楁硶搴撳垵濮嬪寲鎺ュ彛鑷�傚簲绠楁硶

鍙傛暟: key[in]             --瀵嗛挜
     keyLen[in]          --瀵嗛挜闀垮害
     data[in]             --杩愮畻鏁版嵁
     dataLen[in]          --杩愮畻鏁版嵁闀垮害
     out[out]             --HMAC缁撴灉
      pOutLen[out]       --缁撴灉闀垮害

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x12      --鎸囧畾闀垮害绛変簬0
        0x18      --绠楁硶涓嶆敮鎸�
        0x19      --瀵嗛挜闀垮害涓�0
******************************************************************************/
static inline vss_uint32 VssHMAC(vss_uint8* key, vss_uint32 keyLen, vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* key, vss_uint32 keyLen, vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* key, vss_uint32 keyLen, vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssHMAC));
    return api_func_raw(key, keyLen, data, dataLen, out, pOutLen);
}

/******************************************************************************
鎽樿: SM2闈炲绉板姞瑙ｅ瘑鎺ュ彛

鍙傛暟: szInData[in]      --杈撳叆鏁版嵁
      nInLen[in]        --杈撳叆鏁版嵁闀垮害锛屽姞瀵嗘椂1-64瀛楄妭锛� 瑙ｅ瘑鏃�97-160瀛楄妭
      calcFlag[in]      --杩愮畻鏍囪瘑: 0-鍔犲瘑锛�1-瑙ｅ瘑
      szKey[in]         --杩愮畻瀵嗛挜锛氬鏋滄槸鍔犲瘑杩愮畻锛岃鍊间负鍔犲瘑X||鍔犲瘑Y;
	      	  	  	  	  	 濡傛灉鏄В瀵嗚繍绠楋紝璇ュ�间负瑙ｅ瘑SK
      nKeyLen[in]       --杩愮畻瀵嗛挜闀垮害锛屽姞瀵嗕负64锛岃В瀵嗕负32
      szOutData[out]    --杈撳嚭杩愮畻缁撴灉
      pOutLen[out]      --杈撳嚭缁撴灉闀垮害

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x08      --璁＄畻閿欒
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x19      --瀵嗛挜闀垮害闈炴硶
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙�
******************************************************************************/
static inline vss_uint32 VssSM2Calc(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2Calc));
    return api_func_raw(szInData, nInLen, calcFlag, szKey, nKeyLen, szOutData, pOutLen);
}

/******************************************************************************
鎽樿: SM2闈炲绉板姞瑙ｅ瘑鎺ュ彛

鍙傛暟: szInData[in]      --杈撳叆鏁版嵁
      nInLen[in]        --杈撳叆鏁版嵁闀垮害锛屽姞瀵嗘椂1-64瀛楄妭锛� 瑙ｅ瘑鏃�97-160瀛楄妭
      calcFlag[in]       --杩愮畻鏍囪瘑: 0-鍔犲瘑锛�1-瑙ｅ瘑
      szOutData[out]    --杈撳嚭杩愮畻缁撴灉
      pOutLen[out]      --杈撳嚭缁撴灉闀垮害

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x05      --瀵嗛挜涓嶅瓨鍦�
        0x08      --璁＄畻閿欒
        0x0B      --杩愮畻鏍囪瘑涓嶄负鎸囧畾鍊�
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssSM2CalcIndex(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2CalcIndex));
    return api_func_raw(szInData, nInLen, calcFlag, szOutData, pOutLen);
}

/******************************************************************************
鎽樿: SM2鏁版嵁绛惧悕

鍙傛暟: sk[in]              --绛惧悕绉侀挜锛屽畾闀�32瀛楄妭
     data[in]             --杩愮畻鏁版嵁
     dataLen[in]          --杩愮畻鏁版嵁闀垮害锛屼笉鍝堝笇鐩存帴绛惧悕鏃舵寚瀹�32锛屽搱甯岀鍚嶆椂鏁版嵁闀垮害澶т簬0
     hashFlag[in]         --鍝堝笇鏍囪瘑: 0-涓嶅搱甯岀洿鎺ョ鍚�; 1-鍏圫M3鍐嶇鍚�
     sig[out]             --绛惧悕缁撴灉锛屽畾闀�64瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍝堝笇鏍囪瘑闈炴硶
        0x08      --璁＄畻閿欒
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssSM2Sign(vss_uint8* sk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* sk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* sk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2Sign));
    return api_func_raw(sk, data, dataLen, hashFlag, sig);
}

/******************************************************************************
鎽樿: SM2鏁版嵁绛惧悕

鍙傛暟: data[in]             --杩愮畻鏁版嵁
     dataLen[in]          --杩愮畻鏁版嵁闀垮害锛屼笉鍝堝笇鐩存帴绛惧悕鏃舵寚瀹�32锛屽搱甯岀鍚嶆椂鏁版嵁闀垮害澶т簬0
     hashFlag[in]         --鍝堝笇鏍囪瘑: 0-涓嶅搱甯岀洿鎺ョ鍚�; 1-鍏圫M3鍐嶇鍚�
     sig[out]             --绛惧悕缁撴灉锛屽畾闀�64瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍝堝笇鏍囪瘑闈炴硶
        0x05      --瀵嗛挜涓嶅瓨鍦�
        0x08      --璁＄畻閿欒
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssSM2SignIndex(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2SignIndex));
    return api_func_raw(data, dataLen, hashFlag, sig);
}

/******************************************************************************
鎽樿: SM2楠岃瘉绛惧悕

鍙傛暟: pk[in]              --楠岃瘉绛惧悕鍏挜锛屽畾闀�64瀛楄妭
     data[in]             --杩愮畻鏁版嵁
     dataLen[in]          --杩愮畻鏁版嵁闀垮害锛屼笉鍝堝笇鐩存帴绛惧悕鏃舵寚瀹�32锛屽搱甯岀鍚嶆椂鏁版嵁闀垮害澶т簬0
     hashFlag[in]         --鍝堝笇鏍囪瘑: 0-涓嶅搱甯岀洿鎺ョ鍚�; 1-鍏圫M3鍐嶇鍚�
     sig[in]              --绛惧悕缁撴灉锛屽畾闀�64瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍝堝笇鏍囪瘑闈炴硶
        0x08      --楠岀澶辫触
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssSM2Verify(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSM2Verify));
    return api_func_raw(pk, data, dataLen, hashFlag, sig);
}

/******************************************************************************
鎽樿: 璇诲彇璇佷功淇℃伅锛屾敮鎸佹牴璇佷功鍜岃韩浠借瘉涔﹁鍙栦俊鎭�

鍙傛暟: cert[in]          --璇佷功鏁版嵁
     certLen[in]       --璇佷功闀垮害
	info[out]         --杈撳嚭16瀛楄妭鎸囧畾淇℃伅鏍煎紡
                     璇佷功绫诲瀷  4B
                     鏈夋晥鏃ユ湡  4B 鏍煎紡涓篩YMMDD
                     璇佷功搴忓彿  4B
                     璇佷功灞炴��  4B [绠楁硶锛屾牴绱㈠紩锛岄鐣欙紝棰勭暀]
                               绠楁硶锛�0-鍥藉瘑锛�1-鍥介檯
                               鏍圭储寮曪細0x10-娴嬭瘯鍥藉瘑鏍�
                                       0x11-娴嬭瘯鍥介檯鏍�
                                       0x20-棰勭敓浜у浗瀵嗘牴
                                       0x21-棰勭敓浜у浗闄呮牴
                                       0x30-鐢熶骇鍥藉瘑鏍�
                                       0x31-鐢熶骇鍥介檯鏍�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x12      --璇佷功闀垮害闈炴硶
	 0x1A      --璇佷功鏍煎紡闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙�
******************************************************************************/
static inline vss_uint32 VssGetCertInfo(vss_uint8* cert, vss_uint32 certLen, vss_uint8* info)
{
    vss_uint32 (*api_func_raw)(vss_uint8* cert, vss_uint32 certLen, vss_uint8* info) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* cert, vss_uint32 certLen, vss_uint8* info))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetCertInfo));
    return api_func_raw(cert, certLen, info);
}

/******************************************************************************
鎽樿: 璇佷功鍏挜鍔犲瘑

鍙傛暟: in[in]         --寰呭姞瀵嗘暟鎹�
      inLen[in]      --寰呭姞瀵嗘暟鎹暱搴︼紝 鏈�澶�64瀛楄妭
      cert[in]       --璇佷功
      certLen[in]    --璇佷功闀垮害
      out[out]       --鍔犲瘑鍚庢暟鎹�
      pOutLen[out]  --鍔犲瘑鍚庢暟鎹暱搴�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x08      --璁＄畻閿欒
        0x0B      --涓嶆槸姝ｇ‘鐨勮瘉涔︽暟鎹�
	 0x12      --寰呭姞瀵嗘暟鎹暱搴﹁秴杩�64
	 0x1A      --璇佷功鏍煎紡闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙�
******************************************************************************/
static inline vss_uint32 VssCertPkEnc(vss_uint8* in, vss_uint32 inLen, vss_uint8 *cert, vss_uint32 certLen, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *cert, vss_uint32 certLen, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *cert, vss_uint32 certLen, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCertPkEnc));
    return api_func_raw(in, inLen, cert, certLen, out, pOutLen);
}

/******************************************************************************
鎽樿: 璇佷功鍚堟硶鎬ч獙璇�

鍙傛暟: cert[in]              --寰呴獙璇佽瘉涔�
      len[in]              --寰呴獙璇佽瘉涔﹂暱搴�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x08      --璇佷功绛惧悕鏍￠獙澶辫触
	 0x1A      --璇佷功鏍煎紡闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssVerifyCert(vss_uint8* cert, vss_uint32 len)
{
    vss_uint32 (*api_func_raw)(vss_uint8* cert, vss_uint32 len) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* cert, vss_uint32 len))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssVerifyCert));
    return api_func_raw(cert, len);
}

/******************************************************************************
鎽樿: 楠岃瘉璇佷功鏈夋晥鏈�

鍙傛暟: cert[in]              --寰呴獙璇佽瘉涔�
      len[in]              --寰呴獙璇佽瘉涔﹂暱搴�
      szDataNow[in]       --褰撳墠鏃ユ湡鈥漎YYYMMDD鈥濇牸寮忕殑瀛楃涓�

杩斿洖鍊硷細0         --璇佷功鏍￠獙閫氳繃锛屾湁鏁堟湡鍦ㄥ綋鍓嶆棩鏈熶箣鍓嶏紝宸茶繃鏈�
        1         --璇佷功鏍￠獙閫氳繃锛屼笖鏈繃鏈�
        0x04      --浼犲叆绌烘寚閽�
        0x08      --璇佷功绛惧悕鏍￠獙澶辫触
	 0x1A      --璇佷功鏍煎紡闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x1A      --璇佷功杩囨湡
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssVerifyCertValid(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow)
{
    vss_uint32 (*api_func_raw)(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssVerifyCertValid));
    return api_func_raw(cert, len, szDataNow);
}

/******************************************************************************
鎽樿: 楠岃瘉璇婃柇璇佷功鏈夋晥鏈�

鍙傛暟: cert[in]              --寰呴獙璇佽瘉涔�
      len[in]              --寰呴獙璇佽瘉涔﹂暱搴�
      szDataNow[in]       --褰撳墠鏃ユ湡鈥漎YYYMMDD鈥濇牸寮忕殑瀛楃涓�

杩斿洖鍊硷細0         --璇佷功鏍￠獙閫氳繃锛屾湁鏁堟湡鍦ㄥ綋鍓嶆棩鏈熶箣鍓嶏紝宸茶繃鏈�
        1         --璇佷功鏍￠獙閫氳繃锛屼笖鏈繃鏈�
        0x04      --浼犲叆绌烘寚閽�
        0x08      --璇佷功绛惧悕鏍￠獙澶辫触
	 0x1A      --璇佷功鏍煎紡闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x1A      --璇佷功杩囨湡
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssVerifyToolCert(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow)
{
    vss_uint32 (*api_func_raw)(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssVerifyToolCert));
    return api_func_raw(cert, len, szDataNow);
}

/******************************************************************************
鎽樿: 浜х敓浼氳瘽瀵嗛挜骞跺瓨鍌�

鍙傛暟: M1[in]               --鍏变韩瀵嗛挜锛屽畾闀�32瀛楄妭
     r1[in]                --瀹㈡埛绔殢鏈烘暟锛屽畾闀�32瀛楄妭
     r2[in]                --鏈嶅姟鍣ㄩ殢鏈烘暟锛屽畾闀�32瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1D      --鍐欑紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssGenSessionKey(vss_uint8* M1, vss_uint8* r1, vss_uint8* r2)
{
    vss_uint32 (*api_func_raw)(vss_uint8* M1, vss_uint8* r1, vss_uint8* r2) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* M1, vss_uint8* r1, vss_uint8* r2))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGenSessionKey));
    return api_func_raw(M1, r1, r2);
}

/******************************************************************************
鎽樿: 閲嶇疆浼氳瘽瀵嗛挜

鍙傛暟: 鏃�

杩斿洖鍊硷細0         --鎴愬姛
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1D      --鍐欑紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssResetSessionKey(void)
{
    vss_uint32 (*api_func_raw)() = 0;

    api_func_raw = (vss_uint32 (*)())(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssResetSessionKey));
    return api_func_raw();
}

/******************************************************************************
鎽樿: 浜х敓璇佷功璇锋眰锛岀畻娉曠敱绯荤粺绠楁硶鏍囪瘑鎸囧畾

鍙傛暟: ecuType[in]            --ecu绫诲瀷锛�1瀛楄妭
VIN [in]                  --杞﹁締璇嗗埆鍙凤紝17瀛楄妭
     info[in]                   --璇佷功璇嗗埆淇℃伅锛�16瀛楄妭
     pk[out]                  --瀵嗛挜瀵癸紝64瀛楄妭锛圶+Y锛�
     sig[out]                 --鐢宠璇佷功鐢ㄧ鍚嶏紝64瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x08      --璁＄畻澶辫触
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
        0x1D      --鍐欑紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssGenCertReq(vss_uint8 ecuType, vss_char8* VIN, vss_char8* info, vss_uint8* pk, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8 ecuType, vss_char8* VIN, vss_char8* info, vss_uint8* pk, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 ecuType, vss_char8* VIN, vss_char8* info, vss_uint8* pk, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGenCertReq));
    return api_func_raw(ecuType, VIN, info, pk, sig);
}

/******************************************************************************
鎽樿: 瀹夊叏閫氶亾-瀵圭О鍔犺В瀵嗚繍绠楋紝鍏蜂綋绠楁硶鏍规嵁CryptoInit鐨刟lg鍙傛暟鎸囧畾

鍙傛暟: in[in]         --寰呰繍绠楁暟鎹�
      inLen[in]      --寰呰繍绠楁暟鎹暱搴�
      calcFlag[in]   --鍔犺В瀵嗘爣璇嗭細0-鍔犲瘑锛�1-瑙ｅ瘑
      padding[in]   --琛ヤ綅鏍囪瘑锛�0-涓嶅己鍒惰ˉ浣嶏紱1-寮哄埗琛ヤ綅
      lastKey[in]    --鏄惁浣跨敤澶囦唤鐨勫瘑閽ワ細0-浣跨敤褰撳墠瀵嗛挜锛�1-浣跨敤澶囦唤瀵嗛挜
      out[out]      --鍔犲瘑鍚庢暟鎹�
      pOutLen[out]  --鍔犲瘑鍚庢暟鎹暱搴�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鑰呭姞瑙ｅ瘑鏍囪瘑銆佽ˉ浣嶆爣璇嗐�佸浠藉瘑閽ユ爣璇嗗�奸潪娉�
        0x05      --鎸囧畾瀵嗛挜涓嶅瓨鍦�
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssStSymmCalc(vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint32 lastKey, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint32 lastKey, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint32 lastKey, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssStSymmCalc));
    return api_func_raw(in, inLen, calcFlag, padding, lastKey, out, pOutLen);
}

/******************************************************************************
鎽樿: 鏁版嵁绛惧悕

鍙傛暟: data[in]             --杩愮畻鏁版嵁
     dataLen[in]          --杩愮畻鏁版嵁闀垮害
     out[out]             --绛惧悕缁撴灉
     pOutLen[out]        --缁撴灉闀垮害

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x06      --鏈敓鎴愬瘑閽ュ
        0x08      --绛惧悕璁＄畻閿欒
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssSignData(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* data, vss_uint32 dataLen, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSignData));
    return api_func_raw(data, dataLen, out, pOutLen);
}

/********************************************************************************
鎽樿: 璁＄畻瀹夊叏鎻℃墜鏍￠獙鍊�
鍙傛暟: R1[in]            --R1鏁版嵁锛�33瀛楄妭
     R2[in]            --R2鏁版嵁锛�33瀛楄妭
     M1[in]           --M1鏁版嵁锛�32瀛楄妭
     S1[in]            --S1鏁版嵁锛�64瀛楄妭
     E1[in]            --E1鏁版嵁锛孧1鐨勯潪瀵圭О鍔犲瘑鏁版嵁
     E1Len[in]        --E1鏁版嵁闀垮害
     KeyWord[in]      --璁＄畻F1鐢ㄢ�漇ERVER鈥濓紱璁＄畻F2鐢ㄢ�滳LIENT鈥�
     siteCert[in]        --鏈嶅姟鍣ㄨ瘉涔︽暟鎹紝璇佷功瀹氶暱176瀛楄妭
     siteLen[in]         --鏈嶅姟鍣ㄨ瘉涔﹂暱搴︼紝搴斾负176
     vehicleCert[in]     --杞﹁締璇佷功鏁版嵁锛岃瘉涔﹀畾闀�176瀛楄妭
     vehicleLen[in]     --鏈嶅姟鍣ㄨ瘉涔﹂暱搴︼紝搴斾负176
	hmac[out]        --杈撳嚭F1鎴朏2鐨勬暟鎹紝瀹氶暱32瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x0B      --keyWord鏈紶鍏ユ寚瀹氬��
        0x12      --璇佷功闀垮害涓嶆槸176
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙�
********************************************************************************/
static inline vss_uint32 VssCalcFinishHmac(vss_uint8* R1, vss_uint8* R2, vss_uint8* M1, vss_uint8* S1,
vss_uint8* E1, vss_uint32 E1Len, vss_char8* KeyWord , vss_uint8* siteCert, vss_uint32 siteLen,
vss_uint8* vehicleCert, vss_uint32 vehicleLen, vss_uint8* hmac)
{
    vss_uint32 (*api_func_raw)(vss_uint8* R1, vss_uint8* R2, vss_uint8* M1, vss_uint8* S1,
													vss_uint8* E1, vss_uint32 E1Len, vss_char8* KeyWord , vss_uint8* siteCert, vss_uint32 siteLen,
													vss_uint8* vehicleCert, vss_uint32 vehicleLen, vss_uint8* hmac) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* R1, vss_uint8* R2, vss_uint8* M1, vss_uint8* S1,
															vss_uint8* E1, vss_uint32 E1Len, vss_char8* KeyWord , vss_uint8* siteCert, vss_uint32 siteLen,
															vss_uint8* vehicleCert, vss_uint32 vehicleLen, vss_uint8* hmac))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCalcFinishHmac));
    return api_func_raw(R1, R2, M1, S1, E1, E1Len, KeyWord , siteCert, siteLen, vehicleCert, vehicleLen, hmac);
}

/******************************************************************************
鎽樿: 鍥轰欢鏂囦欢绛惧悕楠岃瘉

鍙傛暟: szEcuFileName [in]   --鍥轰欢鏂囦欢璺緞鍚嶇О

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x08      --楠岀澶辫触
        0x0B      --鏂囦欢涓嶅瓨鍦ㄦ垨鏃犳硶璇诲彇鏁版嵁
	 0x1A      --璇佷功鏍煎紡闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssVerifyEcuSign(vss_char8* szEcuFileName)
{
    vss_uint32 (*api_func_raw)(vss_char8* szEcuFileName) = 0;

    api_func_raw = (vss_uint32 (*)(vss_char8* szEcuFileName))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssVerifyEcuSign));
    return api_func_raw(szEcuFileName);
}

/******************************************************************************
鎽樿: 鍥轰欢鏂囦欢鍔犺В瀵�

鍙傛暟: szSrcEcuFile [in]   --婧愬浐浠跺師鏂囦欢璺緞鍚嶇О
     szDestEcuFile [in]  --鐩爣鍥轰欢鏂囦欢璺緞鍚嶇О
     calcFlag[in]       --杩愮畻鏍囪瘑: 0-鍔犲瘑锛�1-瑙ｅ瘑
     key[in]           --杩愮畻瀵嗛挜鏄庢枃锛屽畾闀�16瀛楄妭
     srcOffset[in]	   --婧愬浐浠跺師鏂囦欢鏁版嵁鍋忕Щ閲�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨杩愮畻鏍囪瘑闈炴硶
        0x0B      --鏂囦欢涓嶅瓨鍦ㄦ垨鏃犳硶璇诲彇鏁版嵁鎴栨暟鎹亸绉婚噺瓒婄晫
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙�
******************************************************************************/
static inline vss_uint32 VssCalcEcu(vss_char8* szSrcEcuFile, vss_char8* szDestEcuFile, vss_uint32 calcFlag, vss_uint8* key,  vss_uint32 srcOffset)
{
    vss_uint32 (*api_func_raw)(vss_char8* szSrcEcuFile, vss_char8* szDestEcuFile, vss_uint32 calcFlag, vss_uint8* key,  vss_uint32 srcOffset) = 0;

    api_func_raw = (vss_uint32 (*)(vss_char8* szSrcEcuFile, vss_char8* szDestEcuFile, vss_uint32 calcFlag, vss_uint8* key,  vss_uint32 srcOffset))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCalcEcu));
    return api_func_raw(szSrcEcuFile, szDestEcuFile, calcFlag, key, srcOffset);
}

/********************************************************************************
鎽樿: 鑾峰彇鑺墖鍙�
鍙傛暟: szKeyX[out]      --鑺墖鐨勫叕閽锛屽畾闀�32瀛楄妭
	szKeyY[out]      --鑺墖鐨勫叕閽锛屽畾闀�32瀛楄妭
	szChipCode[out]  --瀹氶暱12瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x06      --瀵嗛挜涓嶅悎娉�
        0x10      --璇诲彇OTP澶辫触
        0xE1      --鍙傛暟涓虹┖
        0xE4      --閫氫俊澶辫触
        0xE5      --娑堟伅鏍￠獙澶辫触
        0xE6      --娑堟伅浣撻暱搴﹂敊璇�
********************************************************************************/
static inline vss_uint32 VssGetChipID(vss_uint8 * szKeyX, vss_uint8 * szKeyY, vss_uint8* szChipCode)
{
    vss_uint32 (*api_func_raw)(vss_uint8 * szKeyX, vss_uint8 * szKeyY, vss_uint8* szChipCode) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 * szKeyX, vss_uint8 * szKeyY, vss_uint8* szChipCode))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetChipID));
    return api_func_raw(szKeyX, szKeyY, szChipCode);
}

/********************************************************************************
鎽樿: 鏇存柊鑺墖涓绘帶瀵嗛挜KMC
鍙傛暟:szMkey[in]   --鏂扮殑KMC瀵嗛挜瀵嗘枃锛屽畾闀�16瀛楄妭
	szMac[in]    --瀵嗛挜鏍￠獙MAC锛屽畾闀�8瀛楄妭
	szCV[in]     --鏂扮殑KMC瀵嗛挜鏍￠獙鍊硷紝瀹氶暱3瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x06      --瀵嗛挜涓嶅悎娉�
        0x08      --璁＄畻澶辫触
        0x09      --FLASH鏍￠獙澶辫触
        0x0B      --鏁版嵁涓嶅悎娉�
        0xE1      --鍙傛暟涓虹┖
        0xE4      --閫氫俊澶辫触
        0xE5      --娑堟伅鏍￠獙澶辫触
        0xE6      --娑堟伅浣撻暱搴﹂敊璇�
********************************************************************************/
static inline vss_uint32 VssUpdateMasterKey(vss_uint8* szMkey, vss_uint8* szMac, vss_uint8* szCV)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szMkey, vss_uint8* szMac, vss_uint8* szCV) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szMkey, vss_uint8* szMac, vss_uint8* szCV))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssUpdateMasterKey));
    return api_func_raw(szMkey, szMac, szCV);
}

/********************************************************************************
鎽樿: 鍐欏叆鏁版嵁锛岃姱鐗囧唴鏈夋晥瀛樺偍绌洪棿涓�0-600锛�000锛岄潪鑺墖鐜鐢卞悇鎺у埗鍣ㄧ殑flash_io_cb瀹炵幇涓帶鍒跺ぇ灏�
鍙傛暟: offset[in]       --鍦板潃鍋忕Щ閲�
	dataLen[in]     --鏁版嵁闀垮害
	data[in]        --寰呭啓鍏ユ暟鎹�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x09      --FLASH鏍￠獙澶辫触
        0x0B      --鏁版嵁涓嶅悎娉曪紝offset鍜宒ataLen涓嶅湪鏈夋晥鍦板潃鑼冨洿鍐�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0xE1      --鍙傛暟涓虹┖
        0xE4      --閫氫俊澶辫触
        0xE5      --娑堟伅鏍￠獙澶辫触
        0xE6      --娑堟伅浣撻暱搴﹂敊璇�
********************************************************************************/
static inline vss_uint32 VssChipWrite(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data)
{
    vss_uint32 (*api_func_raw)(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssChipWrite));
    return api_func_raw(offset, dataLen, data);
}

/********************************************************************************
鎽樿: 璇诲彇鏁版嵁锛岃姱鐗囧唴鏈夋晥瀛樺偍绌洪棿涓�0-600锛�000锛岄潪鑺墖鐜鐢卞悇鎺у埗鍣ㄧ殑flash_io_cb瀹炵幇涓帶鍒跺ぇ灏�

鍙傛暟: offset[in]       --鍦板潃鍋忕Щ閲�
	dataLen[in]     --鏁版嵁闀垮害
	data[out]        --杈撳嚭鏁版嵁
杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x09      --FLASH鏍￠獙澶辫触
        0x0B      --鏁版嵁涓嶅悎娉曪紝offset鍜宒ataLen涓嶅湪鏈夋晥鍦板潃鑼冨洿鍐�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0xE1      --鍙傛暟涓虹┖
        0xE4      --閫氫俊澶辫触
        0xE5      --娑堟伅鏍￠獙澶辫触
        0xE6      --娑堟伅浣撻暱搴﹂敊璇�
********************************************************************************/
static inline vss_uint32 VssChipRead(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data)
{
    vss_uint32 (*api_func_raw)(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 offset, vss_uint32 dataLen, vss_uint8* data))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssChipRead));
    return api_func_raw(offset, dataLen, data);
}

/******************************************************************************
鎽樿:闈炲绉板姞瑙ｅ瘑鎺ュ彛

鍙傛暟: szInData[in]      --杈撳叆鏁版嵁
      nInLen[in]       --杈撳叆鏁版嵁闀垮害
      calcFlag[in]      --杩愮畻鏍囪瘑: 0-鍔犲瘑锛�1-瑙ｅ瘑
      szKey[in]        --杩愮畻瀵嗛挜锛氬鏋滄槸鍔犲瘑杩愮畻锛岃鍊间负鍔犲瘑X||鍔犲瘑Y;
	      	  	  	  	  	 濡傛灉鏄В瀵嗚繍绠楋紝璇ュ�间负瑙ｅ瘑SK
      nKeyLen[in]       --杩愮畻瀵嗛挜闀垮害锛屽姞瀵嗕负64锛岃В瀵嗕负32
      szOutData[out]    --杈撳嚭杩愮畻缁撴灉
      pOutLen[out]      --杈撳嚭缁撴灉闀垮害

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍔犺В瀵嗘爣璇嗛潪娉�
        0x08      --璁＄畻閿欒
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x19      --瀵嗛挜闀垮害闈炴硶
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙�
******************************************************************************/
static inline vss_uint32 VssAsymmCalc(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szKey, vss_uint32 nKeyLen, vss_uint8 *szOutData, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAsymmCalc));
    return api_func_raw(szInData, nInLen, calcFlag, szKey, nKeyLen, szOutData, pOutLen);
}

/******************************************************************************
鎽樿:闈炲绉板姞瑙ｅ瘑鎺ュ彛

鍙傛暟: szInData[in]      --杈撳叆鏁版嵁
      nInLen[in]       --杈撳叆鏁版嵁闀垮害
      calcFlag[in]      --杩愮畻鏍囪瘑: 0-鍔犲瘑锛�1-瑙ｅ瘑
      szOutData[out]    --杈撳嚭杩愮畻缁撴灉
      pOutLen[out]      --杈撳嚭缁撴灉闀垮害

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍔犺В瀵嗘爣璇嗛潪娉�
        0x05      --瀵嗛挜涓嶅瓨鍦�
        0x08      --璁＄畻閿欒
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssAsymmCalcIndex(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* szInData, vss_uint32 nInLen, vss_uint32 calcFlag, vss_uint8 *szOutData, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAsymmCalcIndex));
    return api_func_raw(szInData, nInLen, calcFlag, szOutData, pOutLen);
}

/******************************************************************************
鎽樿: 鏁版嵁绛惧悕

鍙傛暟:  key[in]              --绉侀挜
      keyLen[in]          --绛惧悕瀵嗛挜闀垮害锛屽畾闀�32瀛楄妭
data[in]             --杩愮畻鏁版嵁
     dataLen[in]          --杩愮畻鏁版嵁闀垮害锛屼笉鍝堝笇鐩存帴绛惧悕鏃舵寚瀹�32锛屽搱甯岀鍚嶆椂鏁版嵁闀垮害澶т簬0
hashFlag[in]          --鍝堝笇鏍囪瘑: 0-涓嶅搱甯岀洿鎺ョ鍚�; 1-鍏堝搱甯屽啀绛惧悕
      sig[out]             --绛惧悕缁撴灉锛屽畾闀�64瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍝堝笇鏍囪瘑闈炴硶
        0x08      --璁＄畻閿欒
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssSign(vss_uint8* key, vss_uint32 keyLen,vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* key, vss_uint32 keyLen,vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* key, vss_uint32 keyLen,vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSign));
    return api_func_raw(key, keyLen, data, dataLen, hashFlag, sig);
}

/******************************************************************************
鎽樿: 鏁版嵁绛惧悕锛堝唴閮ㄥ瘑閽ワ級

鍙傛暟: data[in]             --杩愮畻鏁版嵁
     dataLen[in]          --杩愮畻鏁版嵁闀垮害锛屼笉鍝堝笇鐩存帴绛惧悕鏃舵寚瀹�32锛屽搱甯岀鍚嶆椂鏁版嵁闀垮害澶т簬0
     hashFlag[in]         --鍝堝笇鏍囪瘑: 0-涓嶅搱甯岀洿鎺ョ鍚�; 1-鍏堝搱甯屽啀绛惧悕
     sig[out]             --绛惧悕缁撴灉锛屽畾闀�64瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍝堝笇鏍囪瘑闈炴硶
        0x05      --瀵嗛挜涓嶅瓨鍦�
        0x08      --璁＄畻閿欒
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssSignIndex(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSignIndex));
    return api_func_raw(data, dataLen, hashFlag, sig);
}

/******************************************************************************
鎽樿: 楠岃瘉绛惧悕

鍙傛暟: pk[in]              --楠岃瘉绛惧悕鍏挜锛屽畾闀�64瀛楄妭
     data[in]             --杩愮畻鏁版嵁
     dataLen[in]          --杩愮畻鏁版嵁闀垮害锛屼笉鍝堝笇鐩存帴绛惧悕鏃舵寚瀹�32锛屽搱甯岀鍚嶆椂鏁版嵁闀垮害澶т簬0
     hashFlag[in]         --鍝堝笇鏍囪瘑: 0-涓嶅搱甯岀洿鎺ョ鍚�; 1-鍏堝搱甯屽啀绛惧悕
     sig[in]              --绛惧悕缁撴灉锛屽畾闀�64瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍝堝笇鏍囪瘑闈炴硶
        0x08      --楠岀澶辫触
        0x12      --鏁版嵁闀垮害闈炴硶
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssSignVerify(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig)
{
    vss_uint32 (*api_func_raw)(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* pk, vss_uint8* data, vss_uint32 dataLen, vss_uint8 hashFlag, vss_uint8* sig))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSignVerify));
    return api_func_raw(pk, data, dataLen, hashFlag, sig);
}

/******************************************************************************
鎽樿: 瀵圭О绠楁硶鏁版嵁杩愮畻

鍙傛暟:  in[in]         --寰呰繍绠楁暟鎹�
      inLen[in]      --寰呰繍绠楁暟鎹暱搴�
      key[in]        --瀵嗛挜
      keyLen[in]    --瀵嗛挜闀垮害锛屽畾闀�16
      calcFlag[in]   --鍔犺В瀵嗘爣璇嗭細0-鍔犲瘑锛�1-瑙ｅ瘑
      padding[in]   --琛ヤ綅鏍囪瘑锛�0-涓嶅己鍒惰ˉ浣嶏紱1-寮哄埗琛ヤ綅
      out[out]      --鍔犲瘑鍚庢暟鎹�
      pOutLen[out]  --鍔犲瘑鍚庢暟鎹暱搴�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍔犺В瀵嗘爣璇嗐�佽ˉ浣嶆爣璇嗛潪娉�
        0x18      --绠楁硶涓嶆敮鎸�
        0x19      --瀵嗛挜闀垮害闈炴硶
******************************************************************************/
static inline vss_uint32 VssSymmCalc(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSymmCalc));
    return api_func_raw(in, inLen, key, keyLen, calcFlag, padding, out, pOutLen);
}

/******************************************************************************
鎽樿: 瀵圭О绠楁硶鏁版嵁杩愮畻

鍙傛暟:  index[in]     --瀵嗛挜绱㈠紩锛屾寚瀹�0-13
in[in]         --寰呰繍绠楁暟鎹�
      inLen[in]      --寰呰繍绠楁暟鎹暱搴�
      calcFlag[in]   --鍔犺В瀵嗘爣璇嗭細0-鍔犲瘑锛�1-瑙ｅ瘑
      padding[in]   --琛ヤ綅鏍囪瘑锛�0-涓嶅己鍒惰ˉ浣嶏紱1-寮哄埗琛ヤ綅
      out[out]      --鍔犲瘑鍚庢暟鎹�
      pOutLen[out]  --鍔犲瘑鍚庢暟鎹暱搴�

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽堟垨鍔犺В瀵嗘爣璇嗐�佽ˉ浣嶆爣璇嗛潪娉�
        0x05      --瀵嗛挜涓嶅瓨鍦�
        0x0F      --瀵嗛挜绱㈠紩闈炴硶锛屼笉涓�0-13
        0x18      --绠楁硶涓嶆敮鎸�
        0x19      --瀵嗛挜闀垮害涓嶄负2锛�4锛�8锛�12锛�16瀛楄妭
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssSymmCalcIndex(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen)
{
    vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint8* in, vss_uint32 inLen, vss_uint32 calcFlag, vss_uint32 padding, vss_uint8* out, vss_uint32* pOutLen))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSymmCalcIndex));
    return api_func_raw(index, in, inLen, calcFlag, padding, out, pOutLen);
}

/******************************************************************************
鎽樿: 璁＄畻MAC锛屽垵濮嬪寲鍚戦噺涓哄叏0锛屽鍘熸枃鏁版嵁寮哄埗琛ヤ綅0x8000...锛屾墽琛屽绉癈BC璁＄畻锛屽彇鏈�鍚�16瀛楄妭涓篗AC缁撴灉鍊�

鍙傛暟: in[in]         --寰呰繍绠楁暟鎹�
     inLen[in]      --寰呰繍绠楁暟鎹暱搴�
key[in]      --瀵嗛挜
      keyLen[in]   --瀵嗛挜闀垮害锛屽畾闀�16
      out[out]      --mac缁撴灉锛屽畾闀�16瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x18      --绠楁硶涓嶆敮鎸�
        0x19      --瀵嗛挜闀垮害涓嶄负16
******************************************************************************/
static inline vss_uint32 VssMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssMac));
    return api_func_raw(in, inLen, key, keyLen, out);
}

/******************************************************************************
鎽樿: 璁＄畻CMAC

鍙傛暟: in[in]         --寰呰繍绠楁暟鎹�
      inLen[in]      --寰呰繍绠楁暟鎹暱搴�
      key[in]      --瀵嗛挜
      keyLen[in]   --瀵嗛挜闀垮害锛屽畾闀�16
      out[out]      --mac缁撴灉锛屽畾闀�16瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x18      --绠楁硶涓嶆敮鎸�
        0x19      --瀵嗛挜闀垮害涓嶄负16
******************************************************************************/
static inline vss_uint32 VssCMac(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint8 *key, vss_uint32 keyLen, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCMac));
    return api_func_raw(in, inLen, key, keyLen, out);
}

/******************************************************************************
鎽樿:璁＄畻CMAC

鍙傛暟: in[in]         --寰呰繍绠楁暟鎹�
      inLen[in]      --寰呰繍绠楁暟鎹暱搴�
      index[in]      --瀵嗛挜绱㈠紩锛屾寚瀹�0-13
      out[out]      --mac缁撴灉锛屽畾闀�16瀛楄妭

杩斿洖鍊硷細0         --鎴愬姛
        0x04      --浼犲叆绌烘寚閽�
        0x05      --鎸囧畾瀵嗛挜涓嶅瓨鍦�
        0x0F      --瀵嗛挜绱㈠紩闈炴硶锛屼笉涓�0-13
        0x18      --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssCMacIndex(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out)
{
    vss_uint32 (*api_func_raw)(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* in, vss_uint32 inLen, vss_uint32 index, vss_uint8* out))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssCMacIndex));
    return api_func_raw(in, inLen, index, out);
}

/******************************************************************************
鎽樿: 鍐欏叆鐮佸崟鍐欏叆纭鏍囧織浣嶏紝鑷姩缃�1

鍙傛暟: 鏃�

杩斿洖鍊硷細0       --鎴愬姛
        0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1D    --鍐欑紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssSetWroteFlag(void)
{
    vss_uint32 (*api_func_raw)() = 0;

    api_func_raw = (vss_uint32 (*)())(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssSetWroteFlag));
    return api_func_raw();
}

/******************************************************************************
鎽樿: 璇诲彇鐮佸崟鍐欏叆纭鏍囧織浣�

鍙傛暟: wroteFlag[out]  --杩斿洖wroteFlag鍊硷紝0-鐮佸崟鏈啓鍏ワ紱1-鐮佸崟宸插啓鍏�

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --浼犲叆绌烘寚閽�
        0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C    --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssGetWroteFlag(vss_uint8* wroteFlag)
{
    vss_uint32 (*api_func_raw)(vss_uint8* wroteFlag) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* wroteFlag))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetWroteFlag));
    return api_func_raw(wroteFlag);
}

/******************************************************************************
鎽樿: 闅忔満鐢熸垚闈炲绉板瘑閽ュ骞跺鍑烘帴鍙�

鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
      szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�
      szSK[out]     --绉侀挜SK锛堝畾闀�32瀛楄妭锛�

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --浼犲叆绌烘寚閽�
        0x08    --璁＄畻澶辫触
        0x18    --绠楁硶涓嶆敮鎸�
******************************************************************************/
static inline vss_uint32 VssAsymGenKey(vss_uint8 *szX, vss_uint8 *szY, vss_uint8 *szSK)
{
    vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8 *szY, vss_uint8 *szSK) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8 *szY, vss_uint8 *szSK))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAsymGenKey));
    return api_func_raw(szX, szY, szSK);
}

/******************************************************************************
鎽樿: 闅忔満鐢熸垚闈炲绉板瘑閽ュ骞跺瓨鍌ㄦ帴鍙�

鍙傛暟: szX[out]      --鍏挜X锛堝畾闀�32瀛楄妭锛�
      szY[out]      --鍏挜Y锛堝畾闀�32瀛楄妭锛�

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --szX鎴杝zY浼犲叆绌烘寚閽�
        0x08    --璁＄畻澶辫触
        0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1D    --鍐欑紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssAsymGenKeyIndex(vss_uint8 *szX, vss_uint8 *szY)
{
    vss_uint32 (*api_func_raw)(vss_uint8 *szX, vss_uint8 *szY) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8 *szX, vss_uint8 *szY))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssAsymGenKeyIndex));
    return api_func_raw(szX, szY);
}

/******************************************************************************
鎽樿: 璇诲彇9-13鍙峰浠藉瘑閽ユ槑鏂�

鍙傛暟: index[in]    --瀵嗛挜绱㈠紩锛屾湁鏁堢储寮曚负9-13
    nKeyLen[out]  --瀵嗛挜鐨勯暱搴�
    szKey[out]    --瀵嗛挜鍊�

杩斿洖鍊硷細0        --鎴愬姛
        0x04     --瀵嗛挜鎴栧瘑閽ラ暱搴︿紶鍏ョ┖鎸囬拡
        0x05     --鎸囧畾瀵嗛挜涓嶅瓨鍦�
        0x0F     --绱㈠紩鍊奸潪娉曪紝涓嶄负9-13
        0x1B      --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C      --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssExportKey(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey)
{
    vss_uint32 (*api_func_raw)(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 index, vss_uint32* nKeyLen, vss_uint8* szKey))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssExportKey));
    return api_func_raw(index, nKeyLen, szKey);
}

/******************************************************************************
鎽樿: 鏍规嵁鐜闈炲绉板瘑閽ュ骞跺鍑烘帴鍙�

鍙傛暟: env[in]      --鐜ID锛�1-娴嬭瘯鐜锛�2-棰勭敓浜х幆澧冿紱3-鐢熶骇鐜

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --浼犲叆绌烘寚閽�
        0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1D    --鍐欑紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssUpdateEnvironment(vss_uint32 env)
{
    vss_uint32 (*api_func_raw)(vss_uint32 env) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 env))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssUpdateEnvironment));
    return api_func_raw(env);
}

/********************************************************************************
鎽樿: 璇诲彇鑺墖鐗堟湰鍙�

鍙傛暟: version[out]	--杈撳嚭鑺墖鐗堟湰鍙凤紝鏈�闀�16瀛楄妭瀛楃涓�
杩斿洖鍊硷細0		  --鎴愬姛
		0x04	  --浼犲叆绌烘寚閽�
		0xE1	  --鍙傛暟涓虹┖
		0xE4	  --閫氫俊澶辫触
		0xE5	  --娑堟伅鏍￠獙澶辫触
		0xE6	  --娑堟伅浣撻暱搴﹂敊璇�
********************************************************************************/
static inline vss_uint32 VssGetChipVersion(vss_char8* version)
{
    vss_uint32 (*api_func_raw)(vss_char8* version) = 0;

    api_func_raw = (vss_uint32 (*)(vss_char8* version))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetChipVersion));
    return api_func_raw(version);
}

/********************************************************************************
鎽樿: 璇诲彇鑺墖鐘舵��

鍙傛暟: state[out]	   --杩斿洖褰撳績鑺墖鐘舵�侊紝0-绌洪棽; 1-杩愮畻涓�; 2-鏁呴殰; 3-鏈垵濮嬪寲
杩斿洖鍊硷細0		  --鎴愬姛
		0x04	  --浼犲叆绌烘寚閽�
		0xE1	  --鍙傛暟涓虹┖
		0xE4	  --閫氫俊澶辫触
		0xE5	  --娑堟伅鏍￠獙澶辫触
		0xE6	  --娑堟伅浣撻暱搴﹂敊璇�
********************************************************************************/
static inline vss_uint32 VssGetChipState(vss_uint32* state)
{
    vss_uint32 (*api_func_raw)(vss_uint32* state) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32* state))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetChipState));
    return api_func_raw(state);
}

/********************************************************************************
鎽樿: 璇诲彇杞欢鐗堟湰鍙�

鍙傛暟: version[out]	--杈撳嚭鑺墖鐗堟湰鍙凤紝鏈�闀�16瀛楄妭瀛楃涓�
杩斿洖鍊硷細0		  --鎴愬姛
		0x04	  --浼犲叆绌烘寚閽�
		0xE1	  --鍙傛暟涓虹┖
		0xE4	  --閫氫俊澶辫触
		0xE5	  --娑堟伅鏍￠獙澶辫触
		0xE6	  --娑堟伅浣撻暱搴﹂敊璇�
********************************************************************************/
static inline vss_uint32 VssGetVersion(vss_char8* version)
{
    vss_uint32 (*api_func_raw)(vss_char8* version) = 0;

    api_func_raw = (vss_uint32 (*)(vss_char8* version))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetVersion));
    return api_func_raw(version);
}

/******************************************************************************
鎽樿: 楠岃瘉绛惧悕璇佷功鏈夋晥鏈燂紙瀹夊叏鍒锋柊涓氬姟涓撶敤锛�

鍙傛暟: cert[in]			  --寰呴獙璇佽瘉涔�
	  len[in]			   --寰呴獙璇佽瘉涔﹂暱搴�
	  szDataNow[in] 	  --褰撳墠鏃ユ湡鈥漎YYYMMDD鈥濇牸寮忕殑瀛楃涓�

杩斿洖鍊硷細0		  --璇佷功鏍￠獙閫氳繃锛屾湁鏁堟湡鍦ㄥ綋鍓嶆棩鏈熶箣鍓嶏紝宸茶繃鏈�
		1		  --璇佷功鏍￠獙閫氳繃锛屼笖鏈繃鏈�
		0x04	  --浼犲叆绌烘寚閽�
		0x08	  --璇佷功绛惧悕鏍￠獙澶辫触
	 0x1A	   --璇佷功鏍煎紡闈炴硶
		0x18	  --绠楁硶涓嶆敮鎸�
		0x1A	  --璇佷功杩囨湡
		0x1B	  --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
		0x1C	  --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssVerifySignCertValid(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow)
{
    vss_uint32 (*api_func_raw)(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint8* cert, vss_uint32 len, vss_char8* szDataNow))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssVerifySignCertValid));
    return api_func_raw(cert, len, szDataNow);
}

/******************************************************************************
鎽樿: 璇诲彇鐜ID鎺ュ彛

鍙傛暟: env[out]      --鐜ID锛�1-娴嬭瘯鐜锛�2-棰勭敓浜х幆澧冿紱3-鐢熶骇鐜

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --浼犲叆绌烘寚閽�
        0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C    --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssGetEnvironment(vss_uint32* env)
{
    vss_uint32 (*api_func_raw)(vss_uint32* env) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32* env))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetEnvironment));
    return api_func_raw(env);
}

/******************************************************************************
鎽樿: 淇敼瀵嗛挜鐨勬縺娲诲睘鎬�

鍙傛暟: keyId[in]   --瀵嗛挜ID锛堝彧閽堝5-8鍙烽�氫俊瀵嗛挜鏈夋晥锛�
     valid[out]    --鍙敤鏍囪瘑: 0-涓嶅彲鐢紱1-鍙敤

杩斿洖鍊硷細0       --鎴愬姛
        0x04    --鍙敤鏍囪瘑涓嶆槸鎸囧畾鍊�
        0x05    --瀵嗛挜涓嶅瓨鍦�
        0x0F    --瀵嗛挜ID闈炴硶锛屼笉涓�5-8
        0x1B    --鏈皟鐢ㄧ畻娉曞簱鍒濆鍖栨帴鍙ｆ垨鏈寚瀹欼O鎿嶄綔鍥炶皟鏂规硶
        0x1C    --璇荤紦瀛樻暟鎹け璐�
******************************************************************************/
static inline vss_uint32 VssGetKeyActive(vss_uint32 keyId, vss_uint8* valid)
{
    vss_uint32 (*api_func_raw)(vss_uint32 keyId, vss_uint8* valid) = 0;

    api_func_raw = (vss_uint32 (*)(vss_uint32 keyId, vss_uint8* valid))(*((vss_uint32 *)(VSSAPI_FUNC_TABLE_BASE) + VSSAPI_VssGetKeyActive));
    return api_func_raw(keyId, valid);
}

/**
 * @ Revision history
 * @ -------------------------------------------------------------------------
 * @ Version      Date              Author               Note
 * @ -------------------------------------------------------------------------
 * @ 1.0          2020-02-01  wujialin         鍒涘缓鐗堟湰
 * @ 1.1          2021-02-04  wujialin         	瀵瑰簲VSS鎺ュ彛璇存槑涔1.1鍐呭鏇存柊
 * @ 1.2          2021-03-10  wujialin         	瀵瑰簲VSS鎺ュ彛璇存槑涔1.2鍐呭鏇存柊
 												1. 杩藉姞SAIC TLS V1.1鐩稿叧鍐呭
 													1锛変慨鏀笷TLS_ctx_init鎺ュ彛锛岃拷鍔爒ersion鍙傛暟鍖哄垎搴旂敤鐨勭増鏈俊鎭紝鎷嗗垎SOMEIP鍥炶皟鎺ュ彛鍒濆鍖栧埌FTLS_init_someipcb
													2锛塖AIC TLS 鐗堟湰鏋氫妇瀹氫箟
												2. 杩藉姞瀹夊叏鍒锋柊涓撶敤楠岀鎺ュ彛
 * @ 1.3          2021-04-14  wujialin         	瀵瑰簲VSS鎺ュ彛璇存槑涔1.3鍐呭鏇存柊
												1. 杩藉姞VssGetKeyActive鎺ュ彛
												2. 杩藉姞VssVerifySignCertValid鎺ュ彛
 * @
 */



#endif /* __VSS_API_H__ */
